@echo off
chcp 65001 >nul
echo ================================
echo    Git 项目初始化脚本
echo ================================
echo.

echo 正在检查Git是否已安装...
git --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Git未安装，请先安装Git！
    echo 下载地址：https://git-scm.com/download/win
    pause
    exit /b 1
)
echo ✅ Git已安装

echo.
echo 正在检查Git配置...
git config --global user.name >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Git用户信息未配置
    set /p username="请输入你的用户名: "
    set /p email="请输入你的邮箱: "
    git config --global user.name "%username%"
    git config --global user.email "%email%"
    echo ✅ Git用户信息配置完成
) else (
    echo ✅ Git用户信息已配置
)

echo.
echo 正在初始化Git仓库...
if exist .git (
    echo ⚠️  Git仓库已存在
) else (
    git init
    echo ✅ Git仓库初始化完成
)

echo.
echo 正在创建.gitignore文件...
if exist .gitignore (
    echo ⚠️  .gitignore文件已存在
) else (
    (
        echo # Python
        echo __pycache__/
        echo *.py[cod]
        echo .venv/
        echo *.egg-info/
        echo.
        echo # Node.js
        echo node_modules/
        echo npm-debug.log*
        echo npm-cache/
        echo.
        echo # 输出和临时文件
        echo outputs/
        echo 音频输出/
        echo logs/
        echo *.log
        echo tmp/
        echo temp/
        echo.
        echo # 数据库和会话
        echo *.db
        echo *.sqlite
        echo sessions.json
        echo.
        echo # 系统和IDE
        echo .DS_Store
        echo .vscode/
        echo .idea/
        echo Thumbs.db
        echo desktop.ini
        echo.
        echo # 大文件和模型
        echo *.pt
        echo *.pyd
        echo checkpoints/
        echo cache/
        echo *.exe
        echo.
        echo # Chrome用户数据
        echo chrome_user_data/
        echo.
        echo # 其他
        echo *.tmp
        echo *.temp
        echo .env
    ) > .gitignore
    echo ✅ .gitignore文件创建完成
)

echo.
echo 正在添加文件到Git...
git add .
echo ✅ 文件添加完成

echo.
echo 正在进行首次提交...
git commit -m "初始化AI语音直播系统项目"
echo ✅ 首次提交完成

echo.
echo ================================
echo    初始化完成！
echo ================================
echo.
echo 接下来你需要：
echo 1. 在GitHub或Gitee创建远程仓库
echo 2. 运行以下命令连接远程仓库：
echo    git remote add origin https://github.com/你的用户名/仓库名.git
echo    git push -u origin main
echo.
echo 或者运行 git_push.bat 脚本来推送到远程仓库
echo.
pause
