# IndexTTS-FAST AI语音直播系统

## 项目概述

IndexTTS-FAST是一款基于先进文本转语音(TTS)技术的AI直播助手系统，专为直播行业设计。系统支持实时语音合成、声音模型训练、智能对话、直播监控等功能，能够让虚拟主播以自然流畅的声音进行直播互动。

### 核心特性

- 🎙️ **实时语音合成** - 基于IndexTTS引擎的高质量语音生成
- 🎯 **智能直播助手** - 支持抖音、微信等平台的直播监控和自动回复
- 🎨 **声音模型训练** - 仅需10秒音频即可训练个性化声音模型
- 🤖 **AI对话集成** - 集成多种大语言模型(智谱AI、通义千问等)
- 📱 **现代化界面** - 基于React+TypeScript的响应式Web界面
- 🔄 **实时通信** - WebSocket支持的实时音频流传输
- 🛡️ **用户认证** - 完整的用户注册、登录和权限管理系统

本项目采用前后端分离架构，前端使用React+TypeScript开发，后端使用FastAPI构建，并集成IndexTTS作为核心语音合成引擎。

## 📚 文档导航

| 文档 | 描述 | 链接 |
|------|------|------|
| 🚀 **快速开始** | 项目概述和快速上手 | [README.md](./README.md) |
| 📖 **功能特性** | 详细的功能介绍和使用场景 | [FEATURES.md](./FEATURES.md) |
| 🛠️ **安装指南** | 完整的安装和配置教程 | [INSTALLATION.md](./INSTALLATION.md) |
| 🔧 **API文档** | 详细的API使用说明 | [API_DOCS.md](./TTS_播放逻辑整理/API_DOCS.md) |
| 📊 **技术栈** | 技术架构和选型说明 | [技术栈.md](./TTS_播放逻辑整理/技术栈.md) |
| 📝 **更新日志** | 版本更新和变更记录 | [CHANGELOG.md](./CHANGELOG.md) |
| 🤝 **贡献指南** | 如何参与项目贡献 | [CONTRIBUTING.md](./CONTRIBUTING.md) |

## 🚀 快速开始

### 方法一：一键部署（推荐）
1. 克隆项目：`git clone https://gitee.com/chen-duohao/index-tts-fast.git`
2. 进入项目目录：`cd index-tts-fast`
3. 双击运行 `一键部署.bat` 脚本
4. 等待所有依赖安装完成

### 方法二：手动安装
1. 克隆项目
2. 安装主项目依赖：`npm install`
3. 安装AI模块依赖：`cd AI && npm install`
4. 安装自动化后端依赖：`cd automation-backend && npm install`
5. 安装Python依赖：`cd AI && pip install -r requirements.txt`

### 启动服务
1. 启动后端服务：`cd AI/backend && python main.py`
2. 启动AI前端：`cd AI && npm start`
3. 启动IndexTTS：`cd index-tts-liuyue && python app.py`

## 系统架构

项目采用微服务架构设计，包含以下核心组件：

### 架构组件

1. **前端应用** (React + TypeScript)
   - 用户界面和交互逻辑
   - 实时音频播放和控制
   - WebSocket客户端通信

2. **后端服务** (FastAPI)
   - RESTful API和WebSocket服务
   - 用户认证和会话管理
   - 业务逻辑处理和数据持久化

3. **TTS引擎** (IndexTTS + Gradio)
   - 文本到语音转换
   - 声音模型训练和管理
   - 音频生成和处理

4. **直播监控服务** (Playwright)
   - 直播平台数据抓取
   - 弹幕实时监控
   - 自动化交互控制

5. **桌面启动器** (PyQt5)
   - 服务协调和管理
   - 系统状态监控
   - 一键启动所有服务

### 数据流架构图

```mermaid
graph TB
    A[用户界面] --> B[前端应用<br/>React + TypeScript]
    B --> C[后端服务<br/>FastAPI]
    C --> D[TTS引擎<br/>IndexTTS]
    C --> E[直播监控<br/>Playwright]
    C --> F[数据库<br/>SQLite]

    B -.->|WebSocket| C
    C -.->|HTTP API| D
    E -.->|数据抓取| G[直播平台]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
    style F fill:#f1f8e9
```

## 主要功能

### 🎙️ 语音合成工作区
- **实时文本转语音** - 支持多种声音模型的实时语音生成
- **语音参数调节** - 可调节语速、音调、音量等参数
- **音频队列管理** - 支持音频队列播放和管理
- **音频导出下载** - 支持WAV格式音频文件导出

### 🎯 直播助手工作区
- **实时直播语音** - 将文本内容实时转换为直播语音
- **弹幕监控回复** - 自动监控直播间弹幕并智能回复
- **话术模板管理** - 预设多种直播场景的话术模板
- **直播数据统计** - 实时显示观众数量、互动数据等

### 🎨 模型训练工作区
- **一键声音克隆** - 仅需10秒WAV音频即可训练个性化声音模型
- **模型管理** - 支持模型的创建、重命名、删除、预览等操作
- **训练进度监控** - 实时显示模型训练进度和状态
- **模型质量评估** - 提供模型试听和质量评估功能

### 🤖 AI对话工作区
- **多模型支持** - 集成智谱AI、通义千问、OpenAI等多种大语言模型
- **智能对话生成** - 基于上下文的智能对话生成
- **提示词管理** - 支持自定义提示词模板和场景配置
- **对话历史记录** - 完整的对话历史记录和管理

### 📱 智能场控工作区
- **多平台支持** - 支持抖音小店、巨量百应、小红书、微信视频号等7个主流平台
- **真实浏览器自动化** - 基于Playwright的真实浏览器控制，避免检测
- **智能连接管理** - 自动登录检测、状态同步、错误恢复
- **自动化交互** - 自动发言、商品弹窗、智能回复等完整功能
- **实时状态监控** - 连接状态、任务状态、平台信息实时显示

### 🛠️ 系统管理
- **用户认证系统** - 完整的用户注册、登录、权限管理
- **服务状态监控** - 实时监控各个服务的运行状态
- **日志管理** - 详细的系统日志记录和查看
- **配置管理** - 系统参数和配置的统一管理

## 界面结构

### 工作区导航
系统采用侧边栏导航设计，包含以下主要工作区：

1. **🎙️ 语音合成** (`/voice`) - 文本转语音功能
2. **🎯 直播助手** (`/live`) - 实时直播语音生成
3. **📝 文字直播** (`/text-live`) - 文字内容直播管理
4. **🎨 模型训练** (`/training`) - 声音模型训练和管理
5. **🤖 AI对话** (`/ai-model`) - 智能对话生成
6. **💬 互动管理** (`/interaction`) - 直播互动管理
7. **🛍️ 商品管理** (`/product`) - 商品信息管理
8. **📱 智能场控** (`/smart-control`) - 直播平台自动化控制

### 核心组件架构

#### 工作区组件 (Workspace Components)
- **VoiceWorkspace** - 语音合成工作区
- **LiveStreamWorkspace** - 直播助手工作区
- **TextLiveWorkspace** - 文字直播工作区
- **TrainingWorkspace** - 模型训练工作区
- **AIModelWorkspace** - AI对话工作区
- **InteractionWorkspace** - 互动管理工作区
- **ProductWorkspace** - 商品管理工作区
- **SmartControlWorkspace** - 智能场控工作区

#### 通用组件 (Common Components)
- **Sidebar** - 侧边栏导航组件
- **AudioPlayer** - 音频播放控制组件
- **ModelSelector** - 声音模型选择组件
- **TextInput** - 文本输入组件
- **ProgressBar** - 进度条组件
- **StatusIndicator** - 状态指示器组件

#### 上下文管理 (Context Providers)
- **WorkspaceContext** - 工作区状态管理
- **AuthContext** - 用户认证状态管理
- **LiveConnectionContext** - 直播连接状态管理
- **LiveReplyContext** - 直播回复功能管理

## 数据流

### 前端到后端
1. **文本转语音流程**:\
   ```
   用户输入文本 → React组件状态更新 → API请求(/api/tts) → 后端处理 → IndexTTS服务生成音频 → 返回音频数据 → 前端播放
   ```

2. **WebSocket实时通信流程**:\
   ```
   用户发送文本 → WebSocket连接(/ws/tts) → 后端处理 → 分句处理 → IndexTTS服务生成音频 → 实时返回音频片段 → 前端连续播放
   ```

3. **模型训练流程**:\
   ```
   用户上传音频 → API请求(/train) → 后端处理 → IndexTTS服务训练模型 → 保存模型 → 返回状态 → 前端更新模型列表
   ```

### 后端处理流程
1. **请求处理**:\
   - FastAPI接收HTTP/WebSocket请求
   - 请求验证和参数处理
   - 业务逻辑处理

2. **TTS服务调用**:\
   - 通过Gradio客户端接口调用IndexTTS服务
   - 处理模型选择、文本处理、参数配置等
   - 获取生成的音频数据

3. **音频处理**:\
   - 处理音频格式和编码
   - 处理长文本分句
   - 处理并发请求

## 技术栈

### 前端技术栈
- **核心框架**: React 18.2.0
- **语言**: TypeScript 4.9.5
- **构建工具**: React Scripts 5.0.1 + react-app-rewired
- **状态管理**: Zustand 4.5.2
- **UI组件库**:
  - Ant Design 5.24.8
  - Material-UI 5.15.6
- **样式方案**:
  - Tailwind CSS 3.3.2
  - Emotion (CSS-in-JS)
- **网络请求**:
  - Axios 1.9.0
  - Socket.io-client 4.8.1
- **路由**: React Router DOM 7.5.1
- **其他工具**:
  - QRCode.react 4.2.0 (二维码生成)
  - throttle-debounce 5.0.2 (防抖节流)

### 后端技术栈
- **Web框架**: FastAPI (最新版)
- **ASGI服务器**: Uvicorn 0.24.0
- **异步处理**: asyncio + WebSockets
- **数据库**: SQLite + SQLAlchemy
- **认证授权**:
  - python-jose (JWT)
  - passlib (密码加密)
- **TTS集成**: Gradio Client
- **音频处理**:
  - NumPy ≥1.24.3
  - PyTorch ≥2.0.0
  - torchaudio ≥2.0.0
  - SoundFile ≥0.12.1
  - librosa ≥0.10.1
  - scipy ≥1.11.3
- **自动化工具**: Playwright (浏览器自动化)
- **环境管理**: python-dotenv 1.0.0

### TTS引擎
- **核心引擎**: IndexTTS (基于Transformer架构)
- **深度学习框架**: PyTorch 2.0+
- **Web接口**: Gradio 3.50+
- **音频处理**: 支持WAV格式音频的训练和推理

### 桌面启动器
- **GUI框架**: PyQt5
- **进程管理**: Python subprocess
- **系统监控**: 多进程协调管理

### 开发工具
- **包管理**: npm/yarn (前端) + pip (后端)
- **代码规范**: ESLint + TypeScript
- **构建优化**: Webpack 5 + Node.js Polyfills

## 项目目录结构

```
IndexTTS-FAST/
├── 📁 AI/                           # 主应用目录
│   ├── 📁 backend/                  # 后端服务
│   │   ├── 📄 main.py              # FastAPI主应用
│   │   ├── 📄 database.py          # 数据库配置
│   │   ├── 📄 models.py            # 数据模型
│   │   ├── 📄 security.py          # 安全认证
│   │   ├── 📁 douyin_monitor/      # 抖音监控模块
│   │   ├── 📁 smart_control_monitor/ # 智能场控模块
│   │   ├── 📁 sessions/            # 会话数据
│   │   ├── 📁 logs/                # 日志文件
│   │   └── 📄 requirements.txt     # Python依赖
│   ├── 📁 src/                     # 前端源码
│   │   ├── 📄 App.tsx              # 主应用组件
│   │   ├── 📄 index.tsx            # 应用入口
│   │   ├── 📁 components/          # React组件
│   │   │   ├── 📁 Layout/          # 布局组件
│   │   │   ├── 📁 Workspace/       # 工作区组件
│   │   │   ├── 📁 Common/          # 通用组件
│   │   │   └── 📁 UI/              # UI组件
│   │   ├── 📁 contexts/            # React上下文
│   │   ├── 📁 hooks/               # 自定义Hook
│   │   ├── 📁 pages/               # 页面组件
│   │   ├── 📁 services/            # API服务
│   │   ├── 📁 store/               # 状态管理
│   │   ├── 📁 types/               # TypeScript类型
│   │   ├── 📁 utils/               # 工具函数
│   │   └── 📁 styles/              # 样式文件
│   ├── 📁 public/                  # 静态资源
│   │   ├── 🖼️ beijing.jpg          # 登录背景图
│   │   ├── 🖼️ logo.png             # 应用Logo
│   │   └── 🖼️ border.png           # 边框装饰
│   ├── 📄 package.json             # 前端依赖配置
│   ├── 📄 tsconfig.json            # TypeScript配置
│   ├── 📄 tailwind.config.js       # Tailwind CSS配置
│   └── 📄 config-overrides.js      # React配置覆盖
├── 📁 index-tts-liuyue/            # IndexTTS引擎
│   ├── 📄 app.py                   # Gradio应用入口
│   ├── 📁 indextts/                # TTS核心代码
│   ├── 📁 voices/                  # 声音模型存储
│   ├── 📁 outputs/                 # 音频输出目录
│   ├── 📄 运行.bat                 # TTS服务启动脚本
│   └── 📄 qidong.bat               # 快速启动脚本
├── 📁 automation-backend/          # 自动化后端服务
├── 📁 TTS_播放逻辑整理/            # 技术文档
│   ├── 📄 API_DOCS.md              # API文档
│   ├── 📄 技术栈.md                # 技术栈说明
│   └── 📄 README.md                # 模块说明
├── 📁 logs/                        # 系统日志
├── 📁 outputs/                     # 输出文件
├── 📄 launcher_preview.py          # PyQt5启动器
├── 📄 AI语音直播系统.exe           # Windows可执行文件
├── 📄 package.json                 # 项目依赖配置
├── 📄 sessions.json                # 会话数据
├── 📄 sql_app.db                   # SQLite数据库
└── 📄 README.md                    # 项目说明文档
```

## 快速开始

### 系统要求

| 组件 | 最低要求 | 推荐配置 |
|------|----------|----------|
| **操作系统** | Windows 10 | Windows 11 |
| **Python** | 3.10+ | 3.12+ |
| **Node.js** | 16.0+ | 18.0+ |
| **内存** | 8GB RAM | 16GB+ RAM |
| **存储** | 10GB 可用空间 | 20GB+ SSD |
| **GPU** | 可选 | NVIDIA GPU (CUDA支持) |

### 环境准备

#### 1. 安装Python环境
```bash
# 下载Python 3.12: https://www.python.org/downloads/
# 安装时勾选 "Add Python to PATH"
python --version  # 验证安装
pip --version      # 验证pip
```

#### 2. 安装Node.js环境
```bash
# 下载Node.js: https://nodejs.org/
node --version     # 验证安装
npm --version      # 验证npm
```

#### 3. 安装CUDA (可选，GPU加速)
```bash
# 下载CUDA: https://developer.nvidia.com/cuda-downloads
nvcc --version     # 验证安装
```

### 安装步骤

#### 1. 克隆项目
```bash
git clone <项目仓库地址>
cd IndexTTS-FAST
```

#### 2. 安装后端依赖
```bash
# 进入后端目录
cd AI/backend

# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境
# Windows:
.venv\Scripts\activate
# Linux/macOS:
source .venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

#### 3. 安装前端依赖
```bash
# 进入前端目录
cd ../  # 回到AI目录

# 安装依赖
npm install
# 或者使用yarn
yarn install
```

#### 4. 配置IndexTTS服务
```bash
# 进入TTS目录
cd ../index-tts-liuyue

# 确保Python环境正确
python --version

# 安装TTS相关依赖（如果需要）
pip install gradio torch torchaudio
```

### 启动方式

#### 方式一：一键启动 (推荐)
```bash
# 使用桌面启动器
python launcher_preview.py

# 或直接运行可执行文件
./AI语音直播系统.exe
```

#### 方式二：手动启动 (开发调试)

**1. 启动IndexTTS服务**
```bash
cd index-tts-liuyue
# Windows
qidong.bat
# 或
运行.bat

# 服务启动后访问: http://localhost:7860
```

**2. 启动后端服务**
```bash
cd AI/backend
# 激活虚拟环境
.venv\Scripts\activate  # Windows
source .venv/bin/activate  # Linux/macOS

# 启动FastAPI服务
uvicorn main:app --host 0.0.0.0 --port 8088 --reload

# 服务启动后访问: http://localhost:8088
```

**3. 启动前端服务**
```bash
cd AI
# 启动React开发服务器
npm start
# 或
yarn start

# 服务启动后访问: http://localhost:3000
```

**4. 启动直播监控服务 (可选)**
```bash
cd AI/backend/douyin_monitor
python douyin_monitor.py
```

### 配置说明

#### 环境变量配置
创建 `.env` 文件在 `AI/backend/` 目录下：
```env
# 数据库配置
DATABASE_URL=sqlite:///./sql_app.db

# JWT配置
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# LLM API配置
ZHIPU_API_KEY=your-zhipu-api-key
QWEN_API_KEY=your-qwen-api-key
OPENAI_API_KEY=your-openai-api-key

# TTS服务配置
TTS_SERVICE_URL=http://localhost:7860
```

#### 服务端口配置
| 服务 | 默认端口 | 访问地址 |
|------|----------|----------|
| 前端应用 | 3000 | http://localhost:3000 |
| 后端API | 8088 | http://localhost:8088 |
| IndexTTS | 7860 | http://localhost:7860 |
| 文档页面 | 8088/docs | http://localhost:8088/docs |

### 使用说明

#### 首次使用
1. **注册账户** - 访问前端应用，点击注册创建账户
2. **登录系统** - 使用注册的账户登录
3. **检查服务状态** - 确认所有服务正常运行
4. **上传声音模型** - 在模型训练工作区上传音频文件训练模型
5. **开始使用** - 在各个工作区体验不同功能

#### 常见问题
- **服务连接失败** - 检查对应服务是否启动，端口是否被占用
- **音频生成失败** - 确认IndexTTS服务正常，模型文件存在
- **直播监控异常** - 检查浏览器驱动版本，网络连接状态
- **登录失败** - 确认用户名密码正确，后端服务正常

## API文档

### 核心API接口

#### 用户认证
- `POST /register` - 用户注册
- `POST /login` - 用户登录
- `POST /logout` - 用户登出

#### TTS服务
- `POST /api/tts` - 文本转语音
- `GET /api/models` - 获取可用模型列表
- `POST /api/train` - 训练新的声音模型
- `WebSocket /ws/tts` - 实时语音生成

#### 直播功能
- `POST /api/live/start` - 开始直播监控
- `POST /api/live/stop` - 停止直播监控
- `GET /api/live/status` - 获取直播状态
- `WebSocket /ws/live` - 实时直播数据

#### AI对话
- `POST /api/ai/chat` - AI对话生成
- `GET /api/ai/models` - 获取AI模型列表
- `POST /api/ai/config` - 配置AI参数

详细的API文档请参考：
- [API使用文档](./TTS_播放逻辑整理/API_DOCS.md)
- [在线API文档](http://localhost:8088/docs) (启动后端服务后访问)

## 开发指南

### 开发环境设置
```bash
# 安装开发依赖
cd AI
npm install --dev

# 启动开发模式
npm run start

# 代码格式化
npm run lint
```

### 项目结构说明
- **前端组件开发** - 在 `AI/src/components/` 目录下开发新组件
- **API接口开发** - 在 `AI/backend/main.py` 中添加新的API端点
- **状态管理** - 使用Zustand进行全局状态管理
- **样式开发** - 使用Tailwind CSS + Emotion进行样式开发

### 贡献指南
1. Fork项目到个人仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 更新日志

### v1.0.0 (2024-12-XX)
- ✨ 初始版本发布
- 🎙️ 实现基础TTS功能
- 🎯 添加直播助手功能
- 🎨 支持声音模型训练
- 🤖 集成AI对话功能
- 📱 完成智能场控功能
- 🛡️ 实现用户认证系统

### 计划中的功能
- [ ] 支持更多直播平台
- [ ] 增加语音情感控制
- [ ] 优化模型训练速度
- [ ] 添加批量处理功能
- [ ] 支持多语言TTS
- [ ] 增加语音克隆质量评估
- [ ] 实现云端模型同步

## 常见问题 (FAQ)

### Q: 如何训练自定义声音模型？
A: 在模型训练工作区上传10秒以上的WAV格式音频文件，系统会自动进行训练。

### Q: 支持哪些直播平台？
A: 目前支持抖音和微信视频号，后续会添加更多平台支持。

### Q: 如何配置AI模型？
A: 在AI对话工作区可以选择不同的AI模型，并配置相应的API密钥。

### Q: 系统对硬件有什么要求？
A: 最低8GB内存，推荐16GB以上。GPU可选但建议使用以提升性能。

## 技术支持

- 📧 邮箱支持: [<EMAIL>](mailto:<EMAIL>)
- 💬 在线交流: [项目讨论区](https://github.com/your-repo/discussions)
- 📖 技术文档: [详细文档](./TTS_播放逻辑整理/)
- 🐛 问题反馈: [Issues](https://github.com/your-repo/issues)

## 许可证

本项目基于 **ISC许可证** 开源发布。详细信息请参阅 [LICENSE](./LICENSE) 文件。

---

<div align="center">

**IndexTTS-FAST AI语音直播系统**

让AI语音直播变得简单高效 🚀

[⭐ Star](https://github.com/your-repo) | [🍴 Fork](https://github.com/your-repo/fork) | [📝 Issues](https://github.com/your-repo/issues) | [💬 Discussions](https://github.com/your-repo/discussions)

</div>
