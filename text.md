文本直播页面完整实现计划（对标实时直播）	
1. 启动/停止文本直播
入口按钮：
“启动文本直播”按钮，样式、位置、交互与实时直播一致。
仅当有“使用中”分类时可用，点击后变为“停止文本直播”。
状态管理：
维护 isTextLiveStreaming 状态，控制UI和循环逻辑。
2. 文本组内容循环与播放队列
内容来源：
取“使用中”分类下所有文本组，按页面顺序排列。
循环逻辑：
每一轮遍历所有文本组，每组随机抽取一条话术（如组1有10条，随机选1条）。
组内无内容则跳过，所有组遍历完后回到第一组继续循环。
队列管理：
维护播放队列，队列为空时自动补充下一轮内容。
支持暂停、恢复、停止、跳转、音频下载等操作。
3. 变量、助播、TTS处理
变量支持：
话术内容支持 [昵称]、[人数]、[时间]、[整点]、{选项1|选项2|选项3} 等变量，自动替换。
助播支持：
支持 [助:内容] 标记，自动用助播音色生成。
TTS生成：
复用实时直播的 TTS 生成、播放、进度条、音量、下载等全部逻辑。
4. 插话与插话预设
插话输入：
插话输入框、发送按钮、预设按钮、弹窗、交互与实时直播完全一致。
插话内容可随时插入播放队列，立即播报。
插话预设：
预设内容 localStorage 共享，增删改、同步、弹窗UI全部一致。
5. 欢迎、回复、报时、人数播报
自动播报：
支持欢迎新观众、自动回复、报时、人数变化等播报，开关、内容、变量处理与实时直播一致。
播报内容插入队列，优先级高于普通文本组内容。
6. 音色选择与轮换
主播/助播音色：
支持主播/助播音色选择，弹窗、下拉、样式与实时直播一致。
音色轮换：
支持定时/随机/标签组轮换，所有交互、动画、弹窗、Popover等全部一致。
7. 语速调节与随机
语速调节：
支持语速滑块、随机范围设置、Popover弹窗，UI与实时直播一致。
8. 播放队列与控制
队列管理：
维护播放队列，支持暂停、恢复、跳转、音频下载、进度条、音量调节等。
队列为空时自动补充下一轮文本组内容。
9. 状态与本地持久化
本地存储：
分类、文本组、使用状态、音色、语速、插话预设等全部本地持久化，刷新不丢失。
状态恢复：
支持页面刷新后恢复上次的直播状态（可选）。
10. UI/交互/动画/风格
UI一致性：
所有按钮、弹窗、输入框、进度条、动画、主色、描边、hover/active 效果、圆角、间距、字体、布局等全部与实时直播页面保持一致。
只要文本直播已有的UI不冲突，就不做多余改动。
11. 其它高级能力（如已实现）
批量生成/导入/新建文本组：
保持现有能力，UI风格与实时直播一致。
本地持久化与同步：
插话预设、分类、文本组等数据同步与实时直播一致。
主要技术实现点
核心循环：维护当前分类、文本组索引、每组内容索引，循环抽取，自动补充队列。
TTS与变量处理：完全复用实时直播的 TTS/变量/助播/插话/播报处理逻辑。
UI组件复用：能复用的组件/样式全部直接用实时直播的实现。
状态管理：用 useState/useWorkspaceState 管理所有状态，支持本地持久化。
插话/播报/音色/语速/队列/控制：所有功能、交互、体验、动画、细节全部对齐实时直播