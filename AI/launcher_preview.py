import sys
import subprocess
import signal
import os
import psutil
import threading
from PyQt5.QtWidgets import (
    QApplication, QWidget, QPushButton, QVBoxLayout, QHBoxLayout, QGroupBox, QLabel, QPlainTextEdit, QMessageBox, QSystemTrayIcon, QMenu, QAction, QMainWindow
)
from PyQt5.QtCore import Qt, QTimer, QRectF
from PyQt5.QtGui import QIcon, QColor, QTextCharFormat, QTextCursor, QPixmap, QPainter, QLinearGradient, QFont, QBrush, QPen

# 服务配置
SERVICES = [
    {
        "name": "抓取公屏",
        "desc": "弹幕监控",
        "cmd": lambda base: [
            "cmd.exe", "/k",
            ".venv\\Scripts\\activate & cd douyin_monitor & python live_server.py"
        ],
        "shell": False,
        "cwd": lambda base: os.path.join(base, "..", "AI", "backend")
    },
    {
        "name": "前端",
        "desc": "AI前端界面",
        "cmd": lambda base: "npm start",
        "shell": True,
        "cwd": lambda base: os.path.join(base, "..", "AI")
    },
    {
        "name": "TTS服务器",
        "desc": "语音合成",
        "cmd": lambda base: "qidong.bat",
        "shell": True,
        "cwd": lambda base: os.path.join(base, "..", "index-tts-liuyue")
    },
    {
        "name": "后端主服务",
        "desc": "AI主后端",
        "cmd": lambda base: [
            "cmd.exe", "/k",
            ".venv\\Scripts\\activate & uvicorn main:app --host 0.0.0.0 --port 8088"
        ],
        "shell": False,
        "cwd": lambda base: os.path.join(base, "..", "AI", "backend")
    },
]

class GradientLabel(QLabel):
    """
    支持蓝紫渐变色文字和下划线的自定义QLabel
    """
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.setFont(QFont('微软雅黑', 28, QFont.Bold))
        self.setMinimumHeight(60)
        self.setStyleSheet("letter-spacing: 6px; padding: 8px 0;")
        self.setAlignment(Qt.AlignVCenter)

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        font = self.font()
        painter.setFont(font)
        rect = self.rect()
        # 计算文字实际宽度
        metrics = painter.fontMetrics()
        text = self.text()
        text_width = metrics.horizontalAdvance(text)
        text_height = metrics.height()
        # 居中对齐
        x = (rect.width() - text_width) // 2
        y = (rect.height() + text_height) // 2 - 10
        # 蓝紫渐变色
        grad = QLinearGradient(x, 0, x + text_width, 0)
        grad.setColorAt(0, QColor("#6ad6ff"))
        grad.setColorAt(1, QColor("#c97cff"))
        painter.setPen(QPen(QBrush(grad), 0))
        # 发光阴影（蓝紫）
        for offset in [2, 4, 8]:
            glow_grad = QLinearGradient(x, 0, x + text_width, 0)
            glow_grad.setColorAt(0, QColor(106,214,255,60))
            glow_grad.setColorAt(1, QColor(201,124,255,60))
            painter.setPen(QPen(QBrush(glow_grad), offset))
            painter.drawText(x, y, text)
        # 主渐变文字
        painter.setPen(QPen(QBrush(grad), 0))
        painter.drawText(x, y, text)
        # 渐变横线
        line_y = y + 8
        line_height = 4
        radius = 2
        line_rect = QRectF(x, line_y, text_width, line_height)
        painter.setBrush(QBrush(grad))
        painter.setPen(Qt.NoPen)
        painter.drawRoundedRect(line_rect, radius, radius)

class ServiceCard(QGroupBox):
    """
    单个服务卡片，包含状态灯、描述、启动/关闭按钮
    """
    def __init__(self, name, desc, parent=None):
        super().__init__(parent)
        self.setTitle("")
        self.name = name
        self.desc = desc
        self.status = 'stopped'  # running, stopped, starting
        self.proc = None
        self.init_ui()

    def init_ui(self):
        layout = QHBoxLayout()
        layout.setSpacing(8)
        layout.setContentsMargins(8, 6, 8, 6)
        self.status_light = QLabel()
        self.status_light.setFixedSize(16, 16)
        self.status_light.setObjectName("statusLight")
        self.set_status('stopped')
        info = QVBoxLayout()
        self.name_label = QLabel(f"<b>{self.name}</b>")
        self.name_label.setWordWrap(True)
        self.name_label.setMaximumWidth(16777215)  # 无限宽度
        self.desc_label = QLabel(self.desc)
        self.desc_label.setStyleSheet("color:#888;font-size:13px;")
        self.desc_label.setWordWrap(True)
        self.desc_label.setMaximumWidth(16777215)
        info.addWidget(self.name_label)
        info.addWidget(self.desc_label)
        info.setSpacing(2)
        info.setContentsMargins(0, 0, 0, 0)
        btns = QHBoxLayout()
        self.btn_start = QPushButton("启动")
        self.btn_stop = QPushButton("关闭")
        self.btn_start.setObjectName("startBtn")
        self.btn_stop.setObjectName("stopBtn")
        self.btn_start.setFixedWidth(80)
        self.btn_stop.setFixedWidth(80)
        btns.addWidget(self.btn_start)
        btns.addWidget(self.btn_stop)
        btns.setSpacing(6)
        btns.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.status_light, 0, Qt.AlignVCenter)
        layout.addLayout(info, 10)
        layout.addStretch(1)
        layout.addLayout(btns)
        self.setLayout(layout)
        self.setStyleSheet("QGroupBox{border-radius:16px;background:#23242b;margin-top:8px;padding:8px 4px 8px 4px;}")

    def set_status(self, status):
        self.status = status
        color = {"running": "#00eaff", "stopped": "#444", "starting": "#ffd700"}.get(status, "#444")
        self.status_light.setStyleSheet(f"border-radius:9px;background:{color};border:2px solid #222;")

class ServiceLogReader(threading.Thread):
    def __init__(self, stream, log_callback):
        threading.Thread.__init__(self)
        self.stream = stream
        self.log_callback = log_callback
        self.daemon = True # Allow the main program to exit even if this thread is still running

    def run(self):
        try:
            while not self.stream.closed:
                line = self.stream.readline()
                if not line:
                    break
                self.log_callback(line.strip())
        except Exception as e:
            # Optionally log thread error, but avoid recursive logging issues
            pass

class Launcher(QMainWindow):
    """
    主启动器窗口，暗黑卡片风格，支持托盘，顶部集成LOGO
    """
    def __init__(self):
        super().__init__()
        self.setWindowTitle("AI语音直播系统")
        
        # 创建中心部件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 设置窗口图标
        if getattr(sys, 'frozen', False):
            base_dir = os.path.dirname(sys.executable)
        else:
            base_dir = os.path.abspath(os.path.dirname(__file__))
        self.base_dir = base_dir.replace("/", os.sep)
        
        icon_path = os.path.join(self.base_dir, "app.ico")
        self.app_icon = QIcon(icon_path)
        if self.app_icon.isNull():
            # print("图标加载失败，请检查路径：", icon_path)
            pass
        else:
            # 设置窗口图标
            self.setWindowIcon(self.app_icon)
            # 设置应用程序图标
            QApplication.instance().setWindowIcon(self.app_icon)
            # 设置任务栏图标
            import ctypes
            myappid = 'mycompany.myproduct.subproduct.version'
            ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(myappid)
        
        self.setFixedSize(600, 540)
        self.cards = []
        self.processes = [None]*len(SERVICES)
        self.init_ui()
        self.init_tray()
        self.apply_dark_theme()

    def init_ui(self):
        main = QVBoxLayout()
        # 顶部LOGO+标题横向并排
        logo_title_box = QHBoxLayout()
        logo = QLabel()
        logo_path = os.path.join(os.path.dirname(__file__), "logo.png")
        pix = QPixmap(logo_path)
        if pix.isNull():
            # print("LOGO图片加载失败，请检查路径和图片格式：", logo_path)
            pass
        pix = pix.scaled(128, 128, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        logo.setPixmap(pix)
        logo.setAlignment(Qt.AlignVCenter)
        logo.setStyleSheet("border-radius:20px;box-shadow:0 0 24px #00eaff;")
        title = GradientLabel("AI语音直播系统")
        logo_title_box.addStretch(1)
        logo_title_box.addWidget(logo)
        logo_title_box.addSpacing(32)
        logo_title_box.addWidget(title)
        logo_title_box.addStretch(1)
        main.addLayout(logo_title_box)
        # 服务卡片区
        for idx, svc in enumerate(SERVICES):
            card = ServiceCard(svc["name"], svc["desc"])
            card.btn_start.clicked.connect(lambda _, i=idx: self.start_service(i))
            card.btn_stop.clicked.connect(lambda _, i=idx: self.stop_service(i))
            self.cards.append(card)
            main.addWidget(card)
        # 日志区
        self.log = QPlainTextEdit()
        self.log.setReadOnly(True)
        self.log.setObjectName("logBox")
        self.log.setMaximumBlockCount(500)
        main.addWidget(self.log, stretch=1)
        # 全局操作按钮
        btns = QHBoxLayout()
        self.btn_start_all = QPushButton("启动全部")
        self.btn_stop_all = QPushButton("关闭全部")
        self.btn_restart_all = QPushButton("重启全部")
        btns.addWidget(self.btn_start_all)
        btns.addWidget(self.btn_stop_all)
        btns.addWidget(self.btn_restart_all)
        main.addLayout(btns)
        self.central_widget.setLayout(main)
        self.btn_start_all.clicked.connect(self.start_all)
        self.btn_stop_all.clicked.connect(self.stop_all)
        self.btn_restart_all.clicked.connect(self.restart_all)

    def init_tray(self):
        self.tray = QSystemTrayIcon(self)
        self.tray.setIcon(self.app_icon)
        menu = QMenu()
        act_show = QAction("显示主界面", self)
        act_quit = QAction("退出并关闭所有服务", self)
        menu.addAction(act_show)
        menu.addAction(act_quit)
        self.tray.setContextMenu(menu)
        act_show.triggered.connect(self.showNormal)
        act_quit.triggered.connect(self.exit_all)
        self.tray.activated.connect(lambda r: self.showNormal() if r==QSystemTrayIcon.Trigger else None)
        self.tray.show()

    def apply_dark_theme(self):
        self.setStyleSheet("""
        QWidget { background: #181A20; color: #e0e0e0; font-family: '微软雅黑', 'Arial', 'Roboto', sans-serif; }
        QPushButton { background: #23242b; color: #fff; border-radius: 8px; padding: 6px 18px; font-size: 15px; border: 1.5px solid #00eaff; transition:all 0.2s; }
        QPushButton:hover { background: #232b3a; border-color: #ffd700; box-shadow:0 0 8px #00eaff; }
        #startBtn { border-color: #00eaff; }
        #stopBtn { border-color: #ff00ff; }
        #logBox { background: #101014; color: #b0eaff; border-radius: 12px; font-size: 14px; padding: 8px; }
        QGroupBox { border: none; }
        """)

    def log_msg(self, msg, color="#00eaff"):
        fmt = QTextCharFormat()
        fmt.setForeground(QColor(color))
        self.log.moveCursor(QTextCursor.End)
        self.log.setCurrentCharFormat(fmt)
        self.log.appendPlainText(msg)
        self.log.setCurrentCharFormat(QTextCharFormat())

    def start_service(self, idx):
        card = self.cards[idx]
        svc = SERVICES[idx]
        if self.processes[idx] and self.processes[idx].poll() is None:
            self.log_msg(f"{svc['name']} 已在运行中。", "#ffd700")
            return
        card.set_status('starting')
        self.log_msg(f"启动 {svc['name']}...", "#ffd700")
        try:
            cmd = svc["cmd"](self.base_dir)
            shell = svc["shell"]
            cwd = svc["cwd"](self.base_dir) if callable(svc["cwd"]) else svc["cwd"]
            
            # 对于抓取公屏服务，捕获 stdout 和 stderr
            if svc['name'] == '抓取公屏':
                p = subprocess.Popen(
                    cmd,
                    shell=shell,
                    cwd=cwd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    creationflags=subprocess.CREATE_NO_WINDOW,
                    text=True,
                    bufsize=1,
                    universal_newlines=True
                )
                # 启动日志读取线程
                log_reader = ServiceLogReader(p.stdout, lambda line: self.log_msg(f"[抓取公屏] {line}", "#c97cff"))
                log_reader.start()
                self.processes[idx] = p # Store the process object
            elif svc['name'] == '后端主服务':
                # 后端主服务捕获日志并输出到启动器控制台
                p = subprocess.Popen(
                    cmd,
                    shell=shell,
                    cwd=cwd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    creationflags=subprocess.CREATE_NO_WINDOW,
                    text=True,
                    bufsize=1,
                    universal_newlines=True
                )
                # 启动日志读取线程，输出到控制台（处理编码问题）
                import sys
                def safe_print(line):
                    try:
                        # 移除或替换emoji字符
                        safe_line = line.encode('gbk', errors='replace').decode('gbk')
                        print(f"[后端] {safe_line}", flush=True)
                    except Exception as e:
                        # 如果还是有问题，就只输出ASCII字符
                        ascii_line = ''.join(c if ord(c) < 128 else '?' for c in line)
                        print(f"[后端] {ascii_line}", flush=True)

                log_reader = ServiceLogReader(p.stdout, safe_print)
                log_reader.start()
            else:
                # 对于其他服务，也捕获日志输出
                p = subprocess.Popen(
                    cmd,
                    shell=shell,
                    cwd=cwd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    creationflags=subprocess.CREATE_NO_WINDOW,
                    text=True,
                    bufsize=1,
                    universal_newlines=True
                )
                # 启动日志读取线程
                service_name = svc['name']
                color = "#6ad6ff" if service_name == "前端" else "#ffd700"
                log_reader = ServiceLogReader(p.stdout, lambda line, name=service_name: self.log_msg(f"[{name}] {line}", color))
                log_reader.start()
            self.processes[idx] = p

            card.set_status('running')
            self.log_msg(f"{svc['name']} 已启动。", "#00ff99")

        except Exception as e:
            card.set_status('stopped')
            self.log_msg(f"{svc['name']} 启动失败: {e}", "#ff5555")

    def stop_service(self, idx):
        card = self.cards[idx]
        svc = SERVICES[idx]
        p = self.processes[idx]
        if not p or p.poll() is not None:
            card.set_status('stopped')
            self.log_msg(f"{svc['name']} 未在运行。", "#888888")
            return
        self.log_msg(f"关闭 {svc['name']}...", "#ffd700")
        try:
            self.kill_proc_tree(p.pid)
            card.set_status('stopped')
            self.log_msg(f"{svc['name']} 已关闭。", "#00eaff")
        except Exception as e:
            self.log_msg(f"{svc['name']} 关闭失败: {e}", "#ff5555")
        self.processes[idx] = None

    def start_all(self):
        for i in range(len(SERVICES)):
            self.start_service(i)

    def stop_all(self):
        for i in range(len(SERVICES)):
            self.stop_service(i)

    def restart_all(self):
        self.stop_all()
        QTimer.singleShot(1200, self.start_all)

    def kill_proc_tree(self, pid):
        try:
            parent = psutil.Process(pid)
            for child in parent.children(recursive=True):
                child.kill()
            parent.kill()
        except Exception as e:
            self.log_msg(f'关闭进程树失败: {e}', "#ff5555")

    def exit_all(self):
        self.stop_all()
        QApplication.quit()

    def closeEvent(self, event):
        if any(p and p.poll() is None for p in self.processes):
            reply = QMessageBox.question(self, '退出', '确定要关闭所有服务并退出吗？',
                                         QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.exit_all()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    
    # 确保在应用程序级别设置图标
    if getattr(sys, 'frozen', False):
        base_dir = os.path.dirname(sys.executable)
    else:
        base_dir = os.path.abspath(os.path.dirname(__file__))
    base_dir = base_dir.replace("/", os.sep)
    
    icon_path = os.path.join(base_dir, "app.ico")
    app_icon = QIcon(icon_path)
    if not app_icon.isNull():
        # 设置应用程序图标
        app.setWindowIcon(app_icon)
        # 同时设置任务栏图标
        import ctypes
        myappid = 'mycompany.myproduct.subproduct.version'  # 任意字符串
        ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(myappid)
    
    win = Launcher()
    # 确保窗口级别也设置图标
    win.setWindowIcon(app_icon)
    win.show()
    sys.exit(app.exec_()) 