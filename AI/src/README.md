# AI模型集成系统

这个系统实现了AI大模型的无缝集成，使应用程序的任何部分都能轻松调用AI服务，共享相同的配置和API密钥。

## 主要功能

1. **全局AI模型上下文**：
   - 使用React上下文API创建了全局可访问的AI模型配置
   - 在应用程序的任何部分都可以使用相同的AI模型配置
   - 配置自动保存到localStorage，确保会话之间的持久性

2. **统一的模型接口**：
   - 支持多种模型：智谱AI、通义千问、火山引擎、DeepSeek等
   - 通过统一的`generateText`函数简化API调用
   - 共享错误处理和状态管理

3. **通用AI助手组件**：
   - 可在应用程序的任何部分使用的可重用组件
   - 自动适应当前配置的模型和API密钥
   - 支持自定义系统提示词和回调函数

## 使用方法

### 在组件中使用AI上下文

```tsx
import { useAIModel } from '../contexts/AIModelContext';

const MyComponent = () => {
  // 获取AI上下文
  const { config, generateText, isProcessing, error } = useAIModel();
  
  // 使用AI生成文本
  const handleGenerate = async () => {
    const prompt = "请生成一段话术...";
    const systemPrompt = "你是一个直播助手...";
    const result = await generateText(prompt, systemPrompt);
    
    if (result) {
      console.log("生成的文本:", result);
    }
  };
  
  return (
    <div>
      {/* 组件内容 */}
      <button 
        onClick={handleGenerate}
        disabled={isProcessing || !config.aiModelEnabled}
      >
        生成文本
      </button>
      {error && <p>错误: {error}</p>}
    </div>
  );
};
```

### 使用AIHelper组件

```tsx
import AIHelper from '../components/AIHelper';

const MyComponent = () => {
  const handleResult = (result: string) => {
    console.log("AI生成的结果:", result);
  };
  
  const playAudio = async (text: string) => {
    // 实现语音播放逻辑
    console.log("播放文本:", text);
  };
  
  return (
    <div>
      {/* 其他组件内容 */}
      
      <AIHelper
        systemPrompt="你是一个专业的客服，请回答用户的问题"
        placeholder="输入您的问题..."
        onResultGenerated={handleResult}
        canPlayAudio={true}
        playAudio={playAudio}
      />
    </div>
  );
};
```

## 目录结构

```
src/
├── contexts/
│   └── AIModelContext.tsx    # 全局AI模型上下文
├── components/
│   ├── AIHelper.tsx          # 通用AI助手组件
│   └── Workspace/
│       ├── AIModelWorkspace.tsx  # AI模型配置页面
│       └── ProductWorkspace.tsx  # 产品话术生成页面（使用AIHelper）
└── App.tsx                   # 应用程序入口
```

## 配置说明

在`AIModelWorkspace`页面中，您可以：

1. 选择要使用的AI模型
2. 配置各模型的API密钥
3. 设置模型昵称/版本
4. 启用/禁用AI功能
5. 管理提示词模板

所有配置将保存到localStorage，在应用程序的其他部分可以通过`useAIModel`钩子访问。 