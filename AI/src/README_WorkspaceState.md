# 工作区状态保留实现说明

## 概述

本文档说明了如何实现工作区组件状态保留功能，使用户在切换工作区后再切回时能够保留之前的操作状态，而不是恢复到默认状态。

## 实现方案

我们采用了以下方案来保留工作区状态：

1. 创建了自定义Hook `useWorkspaceState`，用于替代React的`useState`，并在sessionStorage中保存状态
2. 建立了工作区上下文`WorkspaceContext`，管理工作区切换
3. 修改了App组件，使其同时渲染所有工作区，但通过CSS的display属性控制显示
4. 更新了LiveStreamWorkspace和VoiceWorkspace组件，使用useWorkspaceState管理状态

## 关键文件

- `hooks/useWorkspaceState.ts` - 状态管理Hook
- `context/WorkspaceContext.tsx` - 工作区上下文
- `App.tsx` - 修改后的App组件
- `components/Workspace/LiveStreamWorkspace.tsx` - 使用useWorkspaceState的示例
- `components/Workspace/VoiceWorkspace.tsx` - 使用useWorkspaceState的示例

## 如何对其他工作区应用相同修改

对于其他工作区组件（如InteractionWorkspace、ProductWorkspace等），可以按照以下步骤修改：

1. 引入useWorkspaceState：
```typescript
import { useWorkspaceState } from '../../hooks/useWorkspaceState';
```

2. 将useState替换为useWorkspaceState：
```typescript
// 原来的代码
const [someState, setSomeState] = useState(initialValue);

// 修改后的代码
const [someState, setSomeState] = useWorkspaceState<StateType>('workspaceId-someState', initialValue);
```

注意：
- `workspaceId` 应该使用工作区的ID，比如 'interaction', 'product', 'ai-model', 'training'
- `someState` 是状态的名称
- `StateType` 是状态的类型
- `initialValue` 是初始值

3. 确保依赖项中包含setter函数：
如果在useEffect中使用状态设置函数，确保在依赖数组中包含该函数：

```typescript
useEffect(() => {
  // 使用setSomeState的逻辑
}, [someState, setSomeState]); // 确保包含setSomeState
```

## 应用示例

以下是在组件中应用useWorkspaceState的示例：

```typescript
// 原来的代码
const [text, setText] = useState('');
const [count, setCount] = useState(0);

// 修改后的代码
const [text, setText] = useWorkspaceState<string>('interaction-text', '');
const [count, setCount] = useWorkspaceState<number>('interaction-count', 0);
```

## 优势

- 组件状态在工作区切换时被保留
- 使用sessionStorage，页面刷新后状态会被重置
- 代码修改量小，对现有功能影响小
- 没有改变现有UI或用户体验

## 注意事项

- 对于非常大的状态对象，可能需要考虑性能和存储空间的影响
- 某些复杂状态可能需要特殊处理，如含有函数或无法序列化的对象
- 此实现不会保留Audio元素的播放状态，如需保留，需要额外处理

## 未来改进

- 增加工作区状态重置功能
- 为特定工作区添加状态保存到localStorage的选项（长期保存）
- 优化性能，避免大状态对象的不必要序列化 