/**
 * 直播工作区状态管理
 * 负责管理直播状态、音色选择、语速设置、话术脚本内容、智能发挥模式和用户接待选项
 */
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface Model {
  id: string;
  name: string;
  tags?: string[];
}

interface UserGreetingOptions {
  welcome: boolean;
  reply: boolean;
  time: boolean;
}

export interface LiveStreamState {
  // 导航状态
  selectedMenuItem: string;
  
  // 直播状态
  isLiveStreaming: boolean;
  
  // 模型设置
  currentModelId: string | null;
  speed: number;
  
  // 话术内容
  scriptContent: string;
  
  // AI模式设置
  aiMode: 'full' | 'semi' | 'off';
  
  // 用户接待选项
  userGreeting: UserGreetingOptions;
  
  // 其他UI状态
  isScriptExpanded: boolean;
}

interface LiveStreamActions {
  // 设置选中的菜单项
  setSelectedMenuItem: (item: string) => void;
  
  // 直播控制
  setLiveStreaming: (isStreaming: boolean) => void;
  
  // 模型设置
  setCurrentModelId: (modelId: string | null) => void;
  setSpeed: (speed: number) => void;
  
  // 设置话术内容
  setScriptContent: (content: string) => void;
  
  // 设置AI模式
  setAiMode: (mode: 'full' | 'semi' | 'off') => void;
  
  // 设置用户接待选项
  setUserGreeting: (options: Partial<UserGreetingOptions>) => void;
  
  // 设置UI状态
  setScriptExpanded: (expanded: boolean) => void;
  
  // 重置状态
  resetState: () => void;
}

// 初始状态
const initialState: LiveStreamState = {
  selectedMenuItem: 'livestream',
  isLiveStreaming: false,
  currentModelId: null,
  speed: 1.0,
  scriptContent: '',
  aiMode: 'full',
  userGreeting: {
    welcome: true,
    reply: false,
    time: false
  },
  isScriptExpanded: true
};

/**
 * 直播工作区状态管理Store
 * 使用zustand提供状态管理，并通过persist中间件实现持久化
 */
export const useLiveStreamStore = create<LiveStreamState & LiveStreamActions>()(
  persist(
    (set) => ({
      ...initialState,
      
      setSelectedMenuItem: (item) => set({ selectedMenuItem: item }),
      
      setLiveStreaming: (isStreaming) => set({ isLiveStreaming: isStreaming }),
      
      setCurrentModelId: (modelId) => set({ currentModelId: modelId }),
      
      setSpeed: (speed) => set({ speed: speed }),
      
      setScriptContent: (content) => set({ scriptContent: content }),
      
      setAiMode: (mode) => set({ aiMode: mode }),
      
      setUserGreeting: (options) => set((state) => ({ 
        userGreeting: { ...state.userGreeting, ...options } 
      })),
      
      setScriptExpanded: (expanded) => set({ isScriptExpanded: expanded }),
      
      resetState: () => set(initialState)
    }),
    {
      name: 'livestream-storage', // localStorage的key名称
      partialize: (state) => ({
        // 只持久化以下状态
        isLiveStreaming: state.isLiveStreaming,
        currentModelId: state.currentModelId,
        speed: state.speed,
        scriptContent: state.scriptContent,
        aiMode: state.aiMode,
        userGreeting: state.userGreeting,
        isScriptExpanded: state.isScriptExpanded
      }),
    }
  )
); 