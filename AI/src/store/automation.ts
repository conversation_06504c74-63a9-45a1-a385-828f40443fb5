import create from "zustand";
import { persist } from "zustand/middleware";

export interface AutoMessage {
  text: string;
  pinned: boolean;
}
export interface KeywordRule {
  keywords: string[];
  keywordInput: string;
  replies: string[];
  replyInput: string;
}

export interface AutomationState {
  platform: string;
  autoMessages: AutoMessage[];
  goodsList: string[];
  keywordRules: KeywordRule[];
  setPlatform: (platform: string) => void;
  setAutoMessages: (autoMessages: AutoMessage[]) => void;
  setGoodsList: (goodsList: string[]) => void;
  setKeywordRules: (keywordRules: KeywordRule[]) => void;
}

export const useAutomationStore = create<AutomationState>()(
  persist(
    (set, get) => ({
      platform: "douyin",
      autoMessages: [],
      goodsList: [],
      keywordRules: [],
      // 你可以继续加其他配置项
      setPlatform: (platform) => set({ platform }),
      setAutoMessages: (autoMessages) => set({ autoMessages }),
      setGoodsList: (goodsList) => set({ goodsList }),
      setKeywordRules: (keywordRules) => set({ keywordRules }),
    }),
    { name: "automation-config" }
  )
); 