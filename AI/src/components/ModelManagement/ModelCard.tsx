import React, { useState } from 'react';
import {
  <PERSON>,
  CardContent,
  Typography,
  IconButton,
  Box,
  TextField,
  alpha,
  styled,
  keyframes,
} from '@mui/material';
import { Edit as EditIcon, Delete as DeleteIcon, Save as SaveIcon, Close as CloseIcon } from '@mui/icons-material';
import MusicNoteIcon from '@mui/icons-material/MusicNote';

// 创建发光边框动画
const glowBorderAnimation = keyframes`
  0% {
    box-shadow: 0 0 2px #0ea5e9, 0 0 4px #0ea5e9;
  }
  50% {
    box-shadow: 0 0 4px #0ea5e9, 0 0 8px #0ea5e9;
  }
  100% {
    box-shadow: 0 0 2px #0ea5e9, 0 0 4px #0ea5e9;
  }
`;

// 创建音符图标动画
const floatAnimation = keyframes`
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4px);
  }
  100% {
    transform: translateY(0);
  }
`;

// 自定义卡片组件
const StyledCard = styled(Card)(({ theme }) => ({
  height: '100%',
  background: alpha('#0f172a', 0.6),
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha('#0ea5e9', 0.2)}`,
  borderRadius: '12px',
  transition: 'all 0.3s ease',
  position: 'relative',
  overflow: 'hidden',
  '&:hover': {
    border: `1px solid ${alpha('#0ea5e9', 0.5)}`,
    animation: `${glowBorderAnimation} 2s infinite`,
    transform: 'translateY(-4px)',
    '& .model-actions': {
      opacity: 1,
      transform: 'translateY(0)',
    },
  },
  '&:before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '1px',
    background: 'linear-gradient(90deg, #0ea5e9 0%, rgba(14, 165, 233, 0) 100%)',
  },
}));

// 自定义图标按钮
const StyledIconButton = styled(IconButton)(({ theme }) => ({
  color: alpha('#94a3b8', 0.8),
  padding: '8px',
  transition: 'all 0.2s ease',
  '&:hover': {
    color: '#0ea5e9',
    background: alpha('#0ea5e9', 0.1),
  },
}));

interface ModelCardProps {
  modelName: string;
  onRename: (oldName: string, newName: string) => Promise<void>;
  onDelete: (modelName: string) => Promise<void>;
}

export const ModelCard: React.FC<ModelCardProps> = ({
  modelName,
  onRename,
  onDelete,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [newName, setNewName] = useState(modelName);

  const handleSave = async () => {
    if (newName && newName !== modelName) {
      await onRename(modelName, newName);
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setNewName(modelName);
    setIsEditing(false);
  };

  return (
    <StyledCard>
      <CardContent sx={{ p: 2 }}>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 2,
          }}
        >
          <Box
            sx={{
              width: 40,
              height: 40,
              borderRadius: '10px',
              background: alpha('#0ea5e9', 0.1),
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <MusicNoteIcon
              sx={{
                fontSize: '24px',
                color: '#0ea5e9',
                animation: `${floatAnimation} 2s ease-in-out infinite`,
              }}
            />
          </Box>
          {isEditing ? (
            <TextField
              fullWidth
              variant="standard"
              value={newName}
              onChange={(e) => setNewName(e.target.value)}
              autoFocus
              sx={{
                '& .MuiInput-root': {
                  color: '#e2e8f0',
                  fontSize: '16px',
                  '&:before': {
                    borderColor: alpha('#0ea5e9', 0.3),
                  },
                  '&:after': {
                    borderColor: '#0ea5e9',
                  },
                  '&:hover:not(.Mui-disabled):before': {
                    borderColor: alpha('#0ea5e9', 0.5),
                  },
                },
              }}
            />
          ) : (
            <Typography
              sx={{
                flexGrow: 1,
                fontSize: '16px',
                fontWeight: 500,
                color: '#e2e8f0',
                textShadow: '0 0 10px rgba(14, 165, 233, 0.2)',
              }}
            >
              {modelName}
            </Typography>
          )}
        </Box>
        <Box
          className="model-actions"
          sx={{
            display: 'flex',
            justifyContent: 'flex-end',
            gap: 1,
            mt: 2,
            opacity: 0,
            transform: 'translateY(10px)',
            transition: 'all 0.3s ease',
          }}
        >
          {isEditing ? (
            <>
              <StyledIconButton onClick={handleSave} size="small">
                <SaveIcon fontSize="small" />
              </StyledIconButton>
              <StyledIconButton onClick={handleCancel} size="small">
                <CloseIcon fontSize="small" />
              </StyledIconButton>
            </>
          ) : (
            <>
              <StyledIconButton onClick={() => setIsEditing(true)} size="small">
                <EditIcon fontSize="small" />
              </StyledIconButton>
              <StyledIconButton
                onClick={() => onDelete(modelName)}
                size="small"
                sx={{
                  '&:hover': {
                    color: '#f43f5e',
                    background: alpha('#f43f5e', 0.1),
                  },
                }}
              >
                <DeleteIcon fontSize="small" />
              </StyledIconButton>
            </>
          )}
        </Box>
      </CardContent>
    </StyledCard>
  );
}; 