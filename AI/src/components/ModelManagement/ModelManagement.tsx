import React, { useState } from 'react';
import {
  Box,
  Grid,
  Typography,
  TextField,
  CircularProgress,
  Snackbar,
  Alert,
  alpha,
  styled,
  keyframes,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from '@mui/material';
import { ModelCard } from './ModelCard';
import SearchIcon from '@mui/icons-material/Search';
import { Model } from '../../hooks/useTTS';

// 创建发光动画
const glowAnimation = keyframes`
  0% {
    text-shadow: 0 0 10px rgba(99, 102, 241, 0.4);
  }
  50% {
    text-shadow: 0 0 20px rgba(139, 92, 246, 0.6);
  }
  100% {
    text-shadow: 0 0 10px rgba(99, 102, 241, 0.4);
  }
`;

// 容器组件
const StyledContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  padding: '32px',
  background: `linear-gradient(135deg, ${alpha('#0f172a', 0.95)}, ${alpha('#1e293b', 0.9)})`,
  backdropFilter: 'blur(10px)',
}));

// 标题组件
const StyledTitle = styled(Typography)(({ theme }) => ({
  fontSize: '32px',
  fontWeight: 700,
  background: 'linear-gradient(to right, #60A5FA, #A78BFA)',
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  marginBottom: '32px',
  position: 'relative',
  display: 'inline-block',
  animation: `${glowAnimation} 2s ease-in-out infinite`,
  '&:after': {
    content: '""',
    position: 'absolute',
    bottom: '-12px',
    left: 0,
    width: '100%',
    height: '3px',
    background: 'linear-gradient(90deg, #60A5FA 0%, #A78BFA 100%)',
    boxShadow: '0 0 10px rgba(139, 92, 246, 0.5)',
  },
}));

// 搜索框容器
const SearchContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  marginBottom: '24px',
  width: '100%',
  maxWidth: '400px',
}));

// 搜索图标样式
const StyledSearchIcon = styled(SearchIcon)(({ theme }) => ({
  position: 'absolute',
  left: '12px',
  top: '50%',
  transform: 'translateY(-50%)',
  color: alpha('#94a3b8', 0.6),
}));

// 搜索输入框
const SearchInput = styled(TextField)(({ theme }) => ({
  width: '100%',
  '& .MuiOutlinedInput-root': {
    backgroundColor: alpha('#1e293b', 0.4),
    borderRadius: '12px',
    paddingLeft: '40px',
    '& fieldset': {
      border: `1px solid ${alpha('#60A5FA', 0.2)}`,
    },
    '&:hover fieldset': {
      borderColor: alpha('#60A5FA', 0.3),
    },
    '&.Mui-focused fieldset': {
      borderColor: '#60A5FA',
    },
  },
  '& .MuiOutlinedInput-input': {
    color: '#60A5FA',
    '&::placeholder': {
      color: alpha('#60A5FA', 0.6),
      opacity: 1,
    },
  },
}));

interface ModelManagementProps {
  models: Model[];
  onModelListUpdate: () => void;
}

export const ModelManagement: React.FC<ModelManagementProps> = ({
  models,
  onModelListUpdate,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error';
  }>({
    open: false,
    message: '',
    severity: 'success',
  });

  // 删除对话框状态
  const [deleteDialog, setDeleteDialog] = useState<{
    open: boolean;
    modelName: string;
  }>({
    open: false,
    modelName: '',
  });

  // 重命名对话框状态
  const [renameDialog, setRenameDialog] = useState<{
    open: boolean;
    oldName: string;
    newName: string;
  }>({
    open: false,
    oldName: '',
    newName: '',
  });

  const filteredModels = models.filter((model) =>
    !model.name.includes("参考音频") &&
    !model.name.includes("使用参考音频") &&
    model.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // 打开删除确认对话框
  const openDeleteDialog = (modelName: string): Promise<void> => {
    return new Promise((resolve) => {
      setDeleteDialog({
        open: true,
        modelName,
      });
      resolve();
    });
  };

  // 打开重命名对话框
  const openRenameDialog = (oldName: string, newName: string): Promise<void> => {
    return new Promise((resolve) => {
      setRenameDialog({
        open: true,
        oldName,
        newName,
      });
      resolve();
    });
  };

  const handleRename = async (oldName: string, newName: string) => {
    try {
      setLoading(true);
      const response = await fetch(`http://localhost:8088/models/rename?old_name=${encodeURIComponent(oldName)}&new_name=${encodeURIComponent(newName)}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        setSnackbar({
          open: true,
          message: '模型重命名成功',
          severity: 'success',
        });
        onModelListUpdate();
      } else {
        throw new Error(data.message || '重命名失败');
      }
    } catch (error) {
      console.error('重命名失败:', error);
      setSnackbar({
        open: true,
        message: error instanceof Error ? error.message : '重命名失败',
        severity: 'error',
      });
    } finally {
      setLoading(false);
      setRenameDialog({ ...renameDialog, open: false });
    }
  };

  const handleDelete = async (modelName: string) => {
    try {
      setLoading(true);
      const response = await fetch(`http://localhost:8088/models/${encodeURIComponent(modelName)}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        setSnackbar({
          open: true,
          message: '模型删除成功',
          severity: 'success',
        });
        onModelListUpdate();
      } else {
        throw new Error(data.message || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      setSnackbar({
        open: true,
        message: error instanceof Error ? error.message : '删除失败',
        severity: 'error',
      });
    } finally {
      setLoading(false);
      setDeleteDialog({ ...deleteDialog, open: false });
    }
  };

  return (
    <StyledContainer>
      <StyledTitle>模型管理</StyledTitle>
      
      <SearchContainer>
        <StyledSearchIcon />
        <SearchInput
          placeholder="搜索模型..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </SearchContainer>

      {loading && (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            my: 4,
          }}
        >
          <CircularProgress sx={{ color: '#0ea5e9' }} />
        </Box>
      )}

      {!loading && filteredModels.length === 0 && (
        <Box
          sx={{
            textAlign: 'center',
            py: 8,
            color: '#94a3b8',
            backgroundColor: alpha('#1e293b', 0.4),
            borderRadius: '12px',
            border: `1px dashed ${alpha('#0ea5e9', 0.2)}`,
          }}
        >
          <Typography>暂无模型</Typography>
        </Box>
      )}

      {!loading && filteredModels.length > 0 && (
        <Grid container spacing={3}>
          {filteredModels.map((model) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={model.name}>
              <ModelCard
                modelName={model.name}
                onRename={openRenameDialog}
                onDelete={openDeleteDialog}
              />
            </Grid>
          ))}
        </Grid>
      )}

      {/* 删除确认对话框 */}
      <Dialog
        open={deleteDialog.open}
        onClose={() => setDeleteDialog({ ...deleteDialog, open: false })}
        PaperProps={{
          sx: {
            backgroundColor: alpha('#1e293b', 0.95),
            backdropFilter: 'blur(10px)',
            border: `1px solid ${alpha('#60A5FA', 0.2)}`,
          },
        }}
      >
        <DialogTitle sx={{ 
          background: 'linear-gradient(to right, #60A5FA, #A78BFA)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
        }}>确认删除</DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ 
            background: 'linear-gradient(to right, #60A5FA, #A78BFA)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
          }}>
            确定要删除模型 "{deleteDialog.modelName}" 吗？此操作不可撤销。
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setDeleteDialog({ ...deleteDialog, open: false })}
            sx={{
              color: '#60A5FA',
              '&:hover': {
                color: '#ffffff',
                background: 'linear-gradient(to right, rgba(96, 165, 250, 0.1), rgba(167, 139, 250, 0.1))',
              },
            }}
          >
            取消
          </Button>
          <Button
            onClick={() => handleDelete(deleteDialog.modelName)}
            sx={{
              color: '#f43f5e',
              '&:hover': {
                color: '#ffffff',
                backgroundColor: alpha('#f43f5e', 0.1),
              },
            }}
          >
            删除
          </Button>
        </DialogActions>
      </Dialog>

      {/* 重命名对话框 */}
      <Dialog
        open={renameDialog.open}
        onClose={() => setRenameDialog({ ...renameDialog, open: false })}
        PaperProps={{
          sx: {
            backgroundColor: alpha('#1e293b', 0.95),
            backdropFilter: 'blur(10px)',
            border: `1px solid ${alpha('#60A5FA', 0.2)}`,
            minWidth: '400px',
          },
        }}
      >
        <DialogTitle sx={{ 
          background: 'linear-gradient(to right, #60A5FA, #A78BFA)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
        }}>确认修改</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
            <Typography sx={{ 
              background: 'linear-gradient(to right, #60A5FA, #A78BFA)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
            }}>原名称：</Typography>
            <Typography sx={{ 
              background: 'linear-gradient(to right, #60A5FA, #A78BFA)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              fontWeight: 500 
            }}>
              {renameDialog.oldName}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography sx={{ 
              background: 'linear-gradient(to right, #60A5FA, #A78BFA)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
            }}>新名称：</Typography>
            <Typography sx={{ 
              background: 'linear-gradient(to right, #60A5FA, #A78BFA)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              fontWeight: 500 
            }}>
              {renameDialog.newName}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions sx={{ padding: '16px 24px' }}>
          <Button
            onClick={() => setRenameDialog({ ...renameDialog, open: false })}
            sx={{
              color: '#60A5FA',
              '&:hover': {
                color: '#ffffff',
                background: 'linear-gradient(to right, rgba(96, 165, 250, 0.1), rgba(167, 139, 250, 0.1))',
              },
            }}
          >
            取消
          </Button>
          <Button
            onClick={() => handleRename(renameDialog.oldName, renameDialog.newName)}
            disabled={!renameDialog.newName || renameDialog.newName === renameDialog.oldName}
            sx={{
              background: 'linear-gradient(to right, #60A5FA, #A78BFA)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              '&:hover': {
                color: '#ffffff',
                background: 'linear-gradient(to right, rgba(96, 165, 250, 0.1), rgba(167, 139, 250, 0.1))',
              },
              '&.Mui-disabled': {
                opacity: 0.5,
                WebkitTextFillColor: 'transparent',
              },
            }}
          >
            确认修改
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          severity={snackbar.severity}
          variant="filled"
          sx={{
            minWidth: '300px',
            backgroundColor: snackbar.severity === 'success'
              ? alpha('#10b981', 0.9)  // 成功时使用绿色
              : alpha('#ef4444', 0.9), // 错误时使用红色
            color: '#ffffff',
            fontSize: '16px',
            display: 'flex',
            alignItems: 'center',
            '& .MuiAlert-icon': {
              fontSize: '24px',
              opacity: 0.9,
            },
            '& .MuiAlert-message': {
              padding: '8px 0',
            },
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
            border: '1px solid',
            borderColor: snackbar.severity === 'success'
              ? alpha('#10b981', 0.3)
              : alpha('#ef4444', 0.3),
          }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </StyledContainer>
  );
}; 