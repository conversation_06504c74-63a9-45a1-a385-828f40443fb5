import React, { useState } from 'react';
import { Box, Button, TextField, Typography, Paper, Container, Alert, Link } from '@mui/material';
import { Link as RouterLink, useNavigate } from 'react-router-dom';

/**
 * 注册组件
 * @returns 注册页面
 */
const Register: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    
    if (password !== confirmPassword) {
      setError('两次输入的密码不一致');
      return;
    }
    
    try {
      // 简化的注册逻辑，只检查用户名是否为空
      if (username && password) {
        // 在实际应用中，这里应该调用后端API进行用户注册
        navigate('/login');
      } else {
        setError('用户名和密码不能为空');
      }
    } catch (err) {
      setError('注册失败，请稍后再试');
    }
  };

  return (
    <Container component="main" maxWidth="xs" className="h-screen flex items-center justify-center">
      <Paper 
        elevation={3} 
        className="p-8 bg-gradient-to-br from-[#1a1f36]/90 to-[#232842]/90 backdrop-blur-md shadow-[0_0_25px_rgba(0,0,0,0.2),0_0_0_1px_rgba(255,255,255,0.05)_inset] border border-[#2a3153]/50"
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          <div className="relative mb-6">
            <h1 className="text-2xl font-bold tracking-wider">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-500">
                AI语音直播
              </span>
            </h1>
            <div className="mt-2 h-0.5 w-20 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full"></div>
          </div>
          
          <Typography component="h1" variant="h5" className="mb-4 text-white">
            注册
          </Typography>
          
          {error && <Alert severity="error" className="mb-4 w-full">{error}</Alert>}
          
          <Box component="form" onSubmit={handleSubmit} noValidate sx={{ mt: 1 }} className="w-full">
            <TextField
              margin="normal"
              required
              fullWidth
              id="username"
              label="用户名"
              name="username"
              autoComplete="username"
              autoFocus
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              className="mb-4"
              sx={{
                '& .MuiInputBase-root': {
                  color: 'white',
                },
                '& .MuiInputLabel-root': {
                  color: 'rgba(255, 255, 255, 0.7)',
                },
                '& .MuiOutlinedInput-root': {
                  '& fieldset': {
                    borderColor: 'rgba(255, 255, 255, 0.23)',
                  },
                  '&:hover fieldset': {
                    borderColor: 'rgba(255, 255, 255, 0.5)',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#90caf9',
                  },
                },
              }}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label="密码"
              type="password"
              id="password"
              autoComplete="new-password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="mb-4"
              sx={{
                '& .MuiInputBase-root': {
                  color: 'white',
                },
                '& .MuiInputLabel-root': {
                  color: 'rgba(255, 255, 255, 0.7)',
                },
                '& .MuiOutlinedInput-root': {
                  '& fieldset': {
                    borderColor: 'rgba(255, 255, 255, 0.23)',
                  },
                  '&:hover fieldset': {
                    borderColor: 'rgba(255, 255, 255, 0.5)',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#90caf9',
                  },
                },
              }}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="confirmPassword"
              label="确认密码"
              type="password"
              id="confirmPassword"
              autoComplete="new-password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              className="mb-6"
              sx={{
                '& .MuiInputBase-root': {
                  color: 'white',
                },
                '& .MuiInputLabel-root': {
                  color: 'rgba(255, 255, 255, 0.7)',
                },
                '& .MuiOutlinedInput-root': {
                  '& fieldset': {
                    borderColor: 'rgba(255, 255, 255, 0.23)',
                  },
                  '&:hover fieldset': {
                    borderColor: 'rgba(255, 255, 255, 0.5)',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#90caf9',
                  },
                },
              }}
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2 }}
              className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white py-2 rounded-lg transition-all duration-300"
            >
              注册
            </Button>
            <Box textAlign="center" mt={2}>
              <Link component={RouterLink} to="/login" variant="body2" className="text-blue-400 hover:text-blue-300">
                已有账号？登录
              </Link>
            </Box>
          </Box>
        </Box>
      </Paper>
    </Container>
  );
};

export default Register; 