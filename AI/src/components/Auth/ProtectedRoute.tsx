import React, { useEffect, useState } from 'react';
import { Navigate, Outlet } from 'react-router-dom';

/**
 * 受保护路由组件
 * @returns 如果用户已登录，则渲染子组件；否则重定向到登录页面
 */
const ProtectedRoute: React.FC = () => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    // 检查本地存储中是否有用户信息
    const storedUser = localStorage.getItem('user');
    setIsAuthenticated(!!storedUser);
    setIsLoading(false);
  }, []);

  if (isLoading) {
    // 加载中显示空白页面
    return null;
  }

  return isAuthenticated ? <Outlet /> : <Navigate to="/login" replace />;
};

export default ProtectedRoute; 