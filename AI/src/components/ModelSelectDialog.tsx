import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog, DialogTitle, DialogContent, DialogActions, IconButton, Button, TextField, Box, Typography, Grid, Chip, Tabs, Tab, Tooltip, CircularProgress, InputAdornment, alpha
} from '@mui/material';
import { Close, Search, PlayCircle, Stop, CheckCircle } from '@mui/icons-material';
import { TagStorage, Tag } from '../utils/tagStorage';
import { useIndexTTS } from '../hooks/useIndexTTS';

interface ModelSelectDialogProps {
  open: boolean;
  onClose: () => void;
  models: any[];
  currentModel: any;
  onSelect: (model: any) => void;
  mode?: 'main' | 'assistant';
}

export const ModelSelectDialog: React.FC<ModelSelectDialogProps> = ({
  open,
  onClose,
  models,
  currentModel,
  onSelect,
  mode = 'main',
}) => {
  const [modelSearchTerm, setModelSearchTerm] = useState('');
  const [tags, setTags] = useState<Tag[]>([]);
  const [modelTags, setModelTags] = useState<{ [modelId: string]: string[] }>({});
  const [activeTagFilters, setActiveTagFilters] = useState<string[]>([]);
  const [previewAudio, setPreviewAudio] = useState<any>(null);
  const [previewLoading, setPreviewLoading] = useState<string | null>(null);
  const { generateAudio, setAudioState } = useIndexTTS();

  useEffect(() => {
    setTags(TagStorage.getTags());
    setModelTags(TagStorage.getModelTags());
  }, [open]);

  const filteredModels = models.filter(model => {
    if (model.id.includes('使用参考音频') || (model.name && model.name.includes('使用参考音频'))) return false;
    const matchesSearch = !modelSearchTerm || model.name.toLowerCase().includes(modelSearchTerm.toLowerCase()) || model.id.toLowerCase().includes(modelSearchTerm.toLowerCase());
    if (activeTagFilters.length > 0) {
      const modelTagIds = modelTags[model.id] || [];
      return matchesSearch && activeTagFilters.every(tagId => modelTagIds.includes(tagId));
    }
    return matchesSearch;
  });

  const handlePlayModelPreview = async (modelId: string, modelName: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (previewAudio && previewAudio.modelId === modelId && previewAudio.isPlaying) {
      if (previewAudio.audio) previewAudio.audio.pause();
      setPreviewAudio((prev: typeof previewAudio) => prev ? { ...prev, isPlaying: false } : null);
      return;
    }
    if (previewAudio && previewAudio.audio) {
      previewAudio.audio.pause();
      setPreviewAudio(null);
    }
    try {
      setPreviewLoading(modelId);
      setAudioState((prev: any) => ({ ...prev, error: null }));
      const cleanModelName = modelName.replace('使用参考音频', '').replace(/\.pt$/, '').trim();
      const previewText = `你好，我是${cleanModelName}，期待和你一起直播`;
      const modelWithSuffix = modelId.endsWith('.pt') ? modelId : `${modelId}.pt`;
      const audioUrl = await generateAudio({ model: modelWithSuffix, text: previewText, speed: 1, isInterrupt: true });
      setPreviewLoading(null);
      if (audioUrl) {
        const audio = new Audio(audioUrl);
        audio.addEventListener('ended', () => setPreviewAudio((prev: typeof previewAudio) => prev ? { ...prev, isPlaying: false } : null));
        await audio.play();
        setPreviewAudio({ url: audioUrl, modelId, isPlaying: true, audio });
      }
    } catch (error) {
      setPreviewLoading(null);
      setAudioState((prev: any) => ({ ...prev, error: '试听失败，请重试' }));
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          bgcolor: '#232842',
          borderRadius: 2,
          boxShadow: '0 8px 32px rgba(0,0,0,0.3)',
          p: '16px 12px 16px 16px',
          maxHeight: '85vh',
          minHeight: '70vh',
          width: '100%',
          height: 'auto',
          display: 'flex',
          flexDirection: 'column',
        },
      }}
    >
      <DialogTitle sx={{ color: '#fff', fontWeight: 600, pb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6">
            选择{mode === 'main' ? '主播' : '助播'}音色
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <IconButton onClick={onClose}>
              <Close />
            </IconButton>
          </Box>
        </Box>
      </DialogTitle>
      <DialogContent sx={{ py: 2, flex: 1, display: 'flex', flexDirection: 'column', overflow: 'hidden', pr: 2 }}>
        {/* 搜索框 */}
        <TextField
          placeholder="搜索模型..."
          variant="outlined"
          size="small"
          fullWidth
          value={modelSearchTerm}
          onChange={(e) => setModelSearchTerm(e.target.value)}
          sx={{ mb: 2, '& .MuiOutlinedInput-root': { bgcolor: 'rgba(42,49,83,0.3)', borderRadius: '10px', height: '40px', '& fieldset': { borderColor: 'rgba(255,255,255,0.1)' }, '&:hover fieldset': { borderColor: 'rgba(46,192,255,0.3)' }, '&.Mui-focused fieldset': { borderColor: '#2ec0ff' }, '& input': { color: '#fff', height: '40px', boxSizing: 'border-box', padding: '0 14px' } } }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search sx={{ color: 'rgba(255,255,255,0.7)' }} />
              </InputAdornment>
            ),
          }}
        />
        {/* 标签Tabs */}
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mb: 2, position: 'relative', width: '100%', height: '40px', minHeight: '40px' }}>
          <Tabs
            value={activeTagFilters.length === 0 ? 'all' : activeTagFilters[0]}
            onChange={(_, value) => {
              if (value === 'all') {
                setActiveTagFilters([]);
              } else {
                if (activeTagFilters.length === 1 && activeTagFilters[0] === value) {
                  setActiveTagFilters([]);
                } else {
                  setActiveTagFilters([value]);
                }
              }
            }}
            variant="scrollable"
            scrollButtons="auto"
            sx={{ minHeight: '40px', '& .MuiTab-root': { minHeight: '40px', height: '40px', color: 'rgba(255,255,255,0.7)', fontSize: '0.85rem', textTransform: 'none', py: 0.5, px: 2, borderRadius: '8px', transition: 'all 0.2s ease', mr: 1, '&.Mui-selected': { color: '#2ec0ff', bgcolor: 'rgba(46,192,255,0.1)' } }, '& .MuiTabs-indicator': { display: 'none' } }}
          >
            <Tab label="全部" value="all" sx={{ borderBottom: activeTagFilters.length === 0 ? '2px solid #2ec0ff' : 'none' }} />
            {tags.map(tag => (
              <Tab
                key={tag.id}
                label={tag.name}
                value={tag.id}
                icon={<Box sx={{ width: 8, height: 8, borderRadius: '50%', bgcolor: tag.color, mr: 1, display: 'inline-block' }} />}
                iconPosition="start"
                sx={{ borderBottom: activeTagFilters.includes(tag.id) ? `2px solid ${tag.color}` : 'none', color: activeTagFilters.includes(tag.id) ? tag.color : 'rgba(255,255,255,0.7)', '&.Mui-selected': { color: tag.color, bgcolor: alpha(tag.color, 0.1) } }}
              />
            ))}
          </Tabs>
        </Box>
        {/* 多标签筛选器 */}
        <Box sx={{ display: activeTagFilters.length > 1 ? 'flex' : 'none', flexWrap: 'wrap', gap: 0.5, mb: 2, minHeight: '28px', height: 'auto' }}>
          <Typography variant="caption" sx={{ color: 'rgba(201,209,240,0.8)', mr: 1, display: 'flex', alignItems: 'center' }}>多重筛选:</Typography>
          {activeTagFilters.map(tagId => {
            const tag = tags.find(t => t.id === tagId);
            if (!tag) return null;
            return (
              <Chip
                key={tag.id}
                label={tag.name}
                size="small"
                onDelete={() => setActiveTagFilters(prev => prev.filter(id => id !== tag.id))}
                sx={{ bgcolor: alpha(tag.color, 0.15), color: tag.color, borderRadius: '4px', fontSize: '0.75rem', height: '24px', '& .MuiChip-deleteIcon': { color: alpha(tag.color, 0.7), '&:hover': { color: tag.color } } }}
              />
            );
          })}
        </Box>
        {/* 模型网格 */}
        <Box sx={{ flex: 1, overflow: 'auto', minHeight: '450px', maxHeight: 'calc(70vh - 180px)', '&::-webkit-scrollbar': { width: '8px', backgroundColor: 'transparent', marginLeft: '5px' }, '&::-webkit-scrollbar-thumb': { backgroundColor: 'rgba(255,255,255,0.2)', borderRadius: '4px', '&:hover': { backgroundColor: 'rgba(255,255,255,0.3)' } }, '&::-webkit-scrollbar-track': { backgroundColor: 'transparent', marginRight: '10px' }, paddingRight: '10px', marginRight: '5px' }}>
          <Grid container spacing={3} sx={{ pr: 2 }}>
            {filteredModels.map(model => {
              const isSelected = currentModel?.id === model.id;
              const modelTagIds = modelTags[model.id] || [];
              const modelTagsList = modelTagIds.length > 0 ? tags.filter(tag => modelTagIds.includes(tag.id)) : [];
              if (activeTagFilters.length > 0 && !activeTagFilters.every(tagId => modelTagIds.includes(tagId))) {
                return null;
              }
              return (
                <Grid item xs={12} sm={6} md={4} key={model.id}>
                  <Box
                    onClick={() => { onSelect(model); onClose(); }}
                    sx={{ position: 'relative', cursor: 'pointer', borderRadius: '12px', background: isSelected ? `linear-gradient(135deg, rgba(21,28,50,0.7), rgba(8,14,28,0.8))` : 'rgba(21,28,50,0.6)', border: isSelected ? `2px solid rgba(46,192,255,0.8)` : `1px solid rgba(255,255,255,0.1)`, padding: '18px', height: '100%', minHeight: '200px', display: 'flex', flexDirection: 'column', overflow: 'hidden', boxShadow: isSelected ? `0 10px 20px rgba(0,0,0,0.2), 0 0 15px rgba(46,192,255,0.3)` : '0 8px 16px rgba(0,0,0,0.15)', transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)', '&:hover': { transform: 'translateY(-5px)', boxShadow: isSelected ? `0 15px 30px rgba(0,0,0,0.25), 0 0 20px rgba(46,192,255,0.4)` : '0 12px 24px rgba(0,0,0,0.2)', border: isSelected ? `2px solid rgba(46,192,255,0.8)` : `1px solid rgba(46,192,255,0.5)` }, '&::before': { content: '""', position: 'absolute', top: 0, left: 0, right: 0, height: '2px', background: isSelected ? `linear-gradient(90deg, transparent, rgba(46,192,255,0.8), transparent)` : 'transparent', opacity: 0.8 }, '&::after': { content: '""', position: 'absolute', bottom: 0, left: isSelected ? '-100%' : '-200%', width: '200%', height: '1px', background: `linear-gradient(90deg, transparent, rgba(46,192,255,0.5), transparent)`, transition: 'left 1.5s ease', animation: isSelected ? 'shimmer 3s infinite linear' : 'none' }, '@keyframes shimmer': { '0%': { left: '-100%' }, '100%': { left: '100%' } } }}
                  >
                    {isSelected && (
                      <Box sx={{ position: 'absolute', top: 12, right: 12, color: '#2ec0ff', animation: 'pulse 2s infinite ease-in-out', '@keyframes pulse': { '0%': { opacity: 0.7 }, '50%': { opacity: 1 }, '100%': { opacity: 0.7 } }, zIndex: 2 }}>
                        <CheckCircle fontSize="small" />
                      </Box>
                    )}
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle1" component="div" sx={{ fontWeight: 700, color: isSelected ? '#2ec0ff' : '#fff', mb: 1.5, display: '-webkit-box', WebkitLineClamp: 1, WebkitBoxOrient: 'vertical', overflow: 'hidden', textOverflow: 'ellipsis', textShadow: isSelected ? '0 0 8px rgba(46,192,255,0.4)' : 'none', fontSize: '1.2rem', lineHeight: '1.4', position: 'relative', paddingLeft: isSelected ? '12px' : '0', transition: 'all 0.3s ease', '&::before': { content: '""', position: 'absolute', left: 0, top: '50%', transform: 'translateY(-50%)', width: isSelected ? '4px' : '0', height: '80%', backgroundColor: '#2ec0ff', borderRadius: '2px', transition: 'all 0.3s ease' } }}>
                        {model.name.replace('使用参考音频', '').trim()}
                      </Typography>
                      <Typography sx={{ color: isSelected ? 'rgba(46,192,255,0.8)' : '#8695bb', fontSize: '0.9rem', display: 'block', fontWeight: isSelected ? 500 : 400, letterSpacing: '0.01em' }}>
                        ID: {model.id.substring(0, 8)}...
                      </Typography>
                    </Box>
                    {/* 标签显示区域 */}
                    {modelTagsList.length > 0 && (
                      <Box sx={{ mb: 2, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {modelTagsList.map(tag => (
                          <Chip
                            key={tag.id}
                            label={tag.name}
                            size="small"
                            sx={{ bgcolor: alpha(tag.color, 0.2), color: tag.color, borderRadius: '4px', fontSize: '0.75rem', height: '22px', '& .MuiChip-deleteIcon': { color: alpha(tag.color, 0.7), '&:hover': { color: tag.color } } }}
                          />
                        ))}
                      </Box>
                    )}
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 'auto', pt: 1.5, borderTop: `1px solid rgba(255,255,255,0.05)` }}>
                      <Box sx={{ height: '8px', width: '60%', background: `linear-gradient(to right, rgba(79,125,249,0.3), rgba(46,192,255,0.3))`, borderRadius: '4px', overflow: 'hidden', position: 'relative', '&::after': { content: '""', position: 'absolute', top: 0, left: '-100%', width: '100%', height: '100%', background: `linear-gradient(to right, transparent, rgba(79,125,249,0.8), rgba(46,192,255,0.8), transparent)`, animation: isSelected ? 'progressAnim 2s infinite' : 'none', '@keyframes progressAnim': { '0%': { left: '-100%' }, '100%': { left: '100%' } } } }} />
                      <IconButton
                        size="small"
                        onClick={e => handlePlayModelPreview(model.id, model.name, e)}
                        disabled={previewLoading !== null}
                        sx={{ bgcolor: previewAudio && previewAudio.modelId === model.id && previewAudio.isPlaying ? 'rgba(122,108,247,0.2)' : 'rgba(79,125,249,0.1)', color: previewAudio && previewAudio.modelId === model.id && previewAudio.isPlaying ? '#7a6cf7' : '#4f7df9', '&:hover': { bgcolor: previewAudio && previewAudio.modelId === model.id && previewAudio.isPlaying ? 'rgba(122,108,247,0.3)' : 'rgba(79,125,249,0.2)', transform: 'scale(1.1)' }, transition: 'all 0.2s ease', width: '36px', height: '36px', position: 'relative' }}
                      >
                        {previewLoading === model.id ? (
                          <CircularProgress size={16} sx={{ color: '#4f7df9' }} />
                        ) : previewAudio && previewAudio.modelId === model.id && previewAudio.isPlaying ? (
                          <Stop sx={{ fontSize: '1.3rem' }} />
                        ) : (
                          <PlayCircle sx={{ fontSize: '1.3rem' }} />
                        )}
                      </IconButton>
                    </Box>
                  </Box>
                </Grid>
              );
            })}
          </Grid>
        </Box>
        <Box sx={{ py: 4, textAlign: 'center', bgcolor: 'rgba(42,49,83,0.2)', borderRadius: '10px', border: `1px dashed rgba(255,255,255,0.1)`, display: filteredModels.length === 0 ? 'block' : 'none', minHeight: '100px' }}>
          <Typography sx={{ color: 'rgba(255,255,255,0.7)' }}>
            没有找到匹配的模型
          </Typography>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} sx={{ color: '#c9d1f0' }}>取消</Button>
      </DialogActions>
    </Dialog>
  );
}; 