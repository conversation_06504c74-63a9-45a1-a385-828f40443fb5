import React, { useState, useEffect, useRef } from 'react';
import { Progress, Button, Space, message } from 'antd';
import { PlayCircleOutlined, PauseCircleOutlined, DownloadOutlined } from '@ant-design/icons';
import { Box, styled } from '@mui/material';
import { convertToMP3 } from '../../utils/audioConverter';

const AudioControlsWrapper = styled(Box)(({ theme }) => ({
  margin: '20px 0',
  padding: '15px',
  borderRadius: '8px',
  background: '#f5f5f5'
}));

const ProgressWrapper = styled(Box)(({ theme }) => ({
  position: 'relative',
  marginBottom: '15px'
}));

const TextOverlay = styled(Box)(({ theme }) => ({
  position: 'absolute',
  width: '100%',
  textAlign: 'center',
  top: '-25px',
  color: '#333',
  fontSize: '14px'
}));

interface AudioControlsProps {
  audioData: string;  // base64音频数据
  text: string;       // 显示的文字
}

export const AudioControls: React.FC<AudioControlsProps> = ({ audioData, text }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [progress, setProgress] = useState(0);
  const [duration, setDuration] = useState(0);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const sourceNodeRef = useRef<AudioBufferSourceNode | null>(null);
  const gainNodeRef = useRef<GainNode | null>(null);
  // 添加组件ID，确保每个实例都是独立的
  const instanceIdRef = useRef<string>(Math.random().toString(36).substring(2, 9));

  // 将base64转换为ArrayBuffer
  const base64ToArrayBuffer = async (base64: string): Promise<ArrayBuffer> => {
    try {
      // 移除可能存在的data URI前缀
      const base64Data = base64.replace(/^data:audio\/\w+;base64,/, '');
      
      // 解码base64
      const binaryString = window.atob(base64Data);
      const bytes = new Uint8Array(binaryString.length);
      
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      
      return bytes.buffer;
    } catch (error) {
      console.error('Base64转换失败:', error);
      throw new Error('音频数据格式错误');
    }
  };

  // 创建/获取音频上下文
  const getAudioContext = (): AudioContext => {
    if (!audioContextRef.current) {
      console.log(`[AudioControls ${instanceIdRef.current}] 创建新的音频上下文`);
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
    }
    return audioContextRef.current;
  };

  // 清理音频资源
  const cleanupAudioResources = () => {
    console.log(`[AudioControls ${instanceIdRef.current}] 清理音频资源`);
    
    // 停止并断开音频源
    if (sourceNodeRef.current) {
      try {
        sourceNodeRef.current.stop();
        sourceNodeRef.current.disconnect();
      } catch (e) {
        // 忽略已停止的音频报错
      }
      sourceNodeRef.current = null;
    }
    
    // 断开增益节点
    if (gainNodeRef.current) {
      try {
        gainNodeRef.current.disconnect();
      } catch (e) {
        // 忽略断开连接错误
      }
      gainNodeRef.current = null;
    }
    
    // 重置状态
    setIsPlaying(false);
  };

  useEffect(() => {
    console.log(`[AudioControls ${instanceIdRef.current}] 组件挂载`);
    
    // 初始化音频上下文
    const context = getAudioContext();
    
    // 解码音频数据
    const initAudio = async () => {
      if (!audioData) return;
      
      try {
        const arrayBuffer = await base64ToArrayBuffer(audioData);
        const audioBuffer = await context.decodeAudioData(arrayBuffer);
        setDuration(audioBuffer.duration);
        console.log(`[AudioControls ${instanceIdRef.current}] 音频初始化成功，时长:`, audioBuffer.duration);
      } catch (error) {
        console.error(`[AudioControls ${instanceIdRef.current}] 音频初始化失败:`, error);
        message.error('音频初始化失败');
      }
    };

    initAudio();

    // 组件卸载时清理资源
    return () => {
      console.log(`[AudioControls ${instanceIdRef.current}] 组件卸载，释放资源`);
      cleanupAudioResources();
      
      // 关闭音频上下文
      if (audioContextRef.current) {
        try {
          audioContextRef.current.close();
        } catch (e) {
          console.error(`[AudioControls ${instanceIdRef.current}] 关闭音频上下文失败:`, e);
        }
        audioContextRef.current = null;
      }
    };
  }, [audioData]);

  // 更新进度
  useEffect(() => {
    let animationFrame: number;
    const updateProgress = () => {
      if (audioRef.current && isPlaying) {
        setProgress((audioRef.current.currentTime / duration) * 100);
        animationFrame = requestAnimationFrame(updateProgress);
      }
    };

    if (isPlaying) {
      animationFrame = requestAnimationFrame(updateProgress);
    }

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [isPlaying, duration]);

  const handlePlayPause = async () => {
    const context = getAudioContext();
    if (!context || !audioData) {
      message.error('音频数据不可用');
      return;
    }

    if (!isPlaying) {
      try {
        // 先清理现有资源
        cleanupAudioResources();
        
        // 如果音频上下文被暂停，恢复它
        if (context.state === 'suspended') {
          await context.resume();
        }

        // 创建新的音频源
        const arrayBuffer = await base64ToArrayBuffer(audioData);
        const audioBuffer = await context.decodeAudioData(arrayBuffer);
        
        sourceNodeRef.current = context.createBufferSource();
        sourceNodeRef.current.buffer = audioBuffer;
        
        // 创建音频处理节点
        gainNodeRef.current = context.createGain();
        gainNodeRef.current.gain.value = 1.0; // 设置音量
        
        // 连接节点
        sourceNodeRef.current.connect(gainNodeRef.current);
        gainNodeRef.current.connect(context.destination);
        
        // 从当前进度开始播放
        const startTime = (progress / 100) * duration;
        sourceNodeRef.current.start(0, startTime);
        setIsPlaying(true);
        console.log(`[AudioControls ${instanceIdRef.current}] 开始播放音频`);

        // 设置结束回调
        sourceNodeRef.current.onended = () => {
          setIsPlaying(false);
          setProgress(0);
          console.log(`[AudioControls ${instanceIdRef.current}] 音频播放结束`);
          sourceNodeRef.current = null;
        };
      } catch (error) {
        console.error(`[AudioControls ${instanceIdRef.current}] 播放失败:`, error);
        message.error('播放失败');
        setIsPlaying(false);
      }
    } else {
      // 停止播放
      cleanupAudioResources();
    }
  };

  const handleDownload = async () => {
    try {
      const context = getAudioContext();
      if (!context || !audioData) {
        throw new Error('音频数据不可用');
      }

      message.loading('正在准备下载...', 0);

      // 将base64转换为AudioBuffer
      const arrayBuffer = await base64ToArrayBuffer(audioData);
      const audioBuffer = await context.decodeAudioData(arrayBuffer);
      
      // 转换为MP3
      console.log(`[AudioControls ${instanceIdRef.current}] 开始转换为MP3...`);
      const mp3Blob = await convertToMP3(audioBuffer);
      console.log(`[AudioControls ${instanceIdRef.current}] MP3转换完成，大小:`, Math.round(mp3Blob.size / 1024), 'KB');
      
      // 创建下载链接
      const url = window.URL.createObjectURL(mp3Blob);
      const link = document.createElement('a');
      link.href = url;
      
      // 创建文件选择对话框
      const fileName = window.prompt('请输入保存的文件名:', '语音.mp3') || '语音.mp3';
      link.download = fileName.endsWith('.mp3') ? fileName : `${fileName}.mp3`;
      
      // 触发下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // 清理
      window.URL.revokeObjectURL(url);
      message.destroy(); // 清除加载提示
      message.success('下载成功');
    } catch (error) {
      console.error(`[AudioControls ${instanceIdRef.current}] 下载失败:`, error);
      message.destroy();
      message.error('下载失败');
    }
  };

  return (
    <AudioControlsWrapper>
      <TextOverlay>{text}</TextOverlay>
      <ProgressWrapper>
        <Progress 
          percent={progress} 
          showInfo={false} 
          strokeColor={{
            '0%': '#108ee9',
            '100%': '#87d068'
          }} 
        />
      </ProgressWrapper>
      <Space>
        <Button 
          type="primary" 
          shape="circle" 
          icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />} 
          onClick={handlePlayPause}
        />
        <Button 
          type="default" 
          shape="circle" 
          icon={<DownloadOutlined />} 
          onClick={handleDownload}
        />
      </Space>
    </AudioControlsWrapper>
  );
}; 