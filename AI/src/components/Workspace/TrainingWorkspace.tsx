import React, { useState, useRef, useCallback, useEffect } from 'react';
import { 
  Box, 
  Card, 
  CardContent, 
  Typo<PERSON>, 
  <PERSON>ack,
  Button,
  Stepper,
  Step,
  StepLabel,
  Paper,
  Grid,
  LinearProgress,
  IconButton,
  alpha,
  Tooltip,
  Alert,
  TextField,
  FormControl,
  FormControlLabel,
  RadioGroup,
  Radio,
  Chip,
  Snackbar,
  Slider,
  CircularProgress,
  Tabs,
  Tab,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  InputAdornment,
  Pagination,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import { 
  AutoAwesome, 
  Upload,
  Delete,
  Mic,
  RecordVoiceOver,
  GraphicEq,
  PlayArrow,
  Save,
  MusicNote,
  Stop,
  CheckCircle,
  MoreVert,
  Add,
  Close,
  Circle,
  StopCircle,
  PlayCircle,
  Search,
  FilterList,
  Edit,
} from '@mui/icons-material';
import { Model } from '../../hooks/useTTS';
import { TagStorage, Tag } from '../../utils/tagStorage';
import { ModelManagement } from '../ModelManagement/ModelManagement';
import { useTTS } from '../../hooks/useTTS'; // <--- 添加 useTTS 导入
import { message } from 'antd'; // <--- 添加 antd message 导入

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`training-tabpanel-${index}`}
      aria-labelledby={`training-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box>{children}</Box>
      )}
    </div>
  );
}

interface TrainingWorkspaceProps {
  models: Model[];
  currentModel: Model | null;
  setModel: (model: Model) => void;
  onModelListUpdate: () => void;
}

// 添加ModelCardProps接口定义 (从VoiceWorkspace.tsx复制)
interface ModelCardProps {
  model: {
    id: string;
    name: string;
  };
  isSelected: boolean;
  isPlaying: boolean;
  onSelect: () => void;
  onPlayPreview: () => void;
  tags?: Tag[]; // 修改为直接使用Tag类型数组
  allTags?: Tag[]; // 所有可用标签列表
  onTagClick?: (tagId: string) => void;
  onTagsManage?: (event: React.MouseEvent<HTMLElement>, modelId: string) => void;
  // 添加标签管理函数
  addTagToModel?: (modelId: string, tagId: string) => void;
  removeTagFromModel?: (modelId: string, tagId: string) => void;
  // 添加试听状态属性
  previewLoading?: string | null;
  previewAudio?: { 
    url: string;
    modelId: string;
    isPlaying: boolean;
  } | null;
  onDeleteModel?: (modelId: string, modelName: string) => void;
  // 添加重命名函数
  onRenameModel?: (modelId: string, currentModelName: string, newModelName: string) => void;
}

// 添加ModelCard组件 (从VoiceWorkspace.tsx复制)
const ModelCard: React.FC<ModelCardProps> = ({
  model,
  isSelected,
  isPlaying,
  onSelect,
  onPlayPreview,
  tags = [],
  allTags = [],
  onTagClick,
  onTagsManage,
  addTagToModel,
  removeTagFromModel,
  previewLoading,
  previewAudio,
  onDeleteModel,
  onRenameModel,
}) => {
  // 添加对话框状态管理 - 使用React.useState而不是直接的useState
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [localTagMenuAnchorEl, setLocalTagMenuAnchorEl] = React.useState<null | HTMLElement>(null);
  
  // 添加重命名相关状态
  const [isRenameDialogOpen, setIsRenameDialogOpen] = React.useState(false);
  const [newModelName, setNewModelName] = React.useState(model.name); // 初始化为当前模型名称
  
  // 检查当前模型是否正在加载或播放试听
  const isLoadingOrPlayingPreview = previewLoading === model.id || (previewAudio?.modelId === model.id && previewAudio?.isPlaying);
  const isOtherModelLoadingOrPlaying = (previewLoading !== null && previewLoading !== model.id) || (previewAudio?.isPlaying === true && previewAudio?.modelId !== model.id);
  
  // 先提取当前模型已应用的标签ID
  const modelTagIds = tags.map(t => t.id);
  
  // 处理标签菜单点击
  const handleTagMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation(); // 阻止卡片点击事件传播
    setLocalTagMenuAnchorEl(event.currentTarget); // 设置菜单锚点
  };
  
  // 处理关闭标签菜单
  const handleCloseLocalTagMenu = () => {
    setLocalTagMenuAnchorEl(null);
  };
  
  // 标签管理逻辑
  const handleToggleModelTag = (tagId: string) => {
    const hasTag = modelTagIds.includes(tagId);
    
    if (hasTag) {
      // 如果已有该标签，则移除
      if (removeTagFromModel) {
        removeTagFromModel(model.id, tagId);
        message.info(`标签已从模型移除`);
      }
    } else {
      // 如果没有该标签，则添加
      if (addTagToModel) {
        addTagToModel(model.id, tagId);
        message.success(`标签已添加到模型`);
      }
    }
  };

  // 创建新标签
  const handleCreateNewTag = () => {
    // 触发自定义事件
    const event = new CustomEvent('createTag', { detail: { modelId: model.id } });
    window.dispatchEvent(event);
    // 关闭当前菜单
    handleCloseLocalTagMenu();
  };

  // 处理标签点击
  const handleTagClick = (event: React.MouseEvent, tagId: string) => {
    event.stopPropagation();
    if (onTagClick) {
      onTagClick(tagId);
    }}

  // 处理标签删除
  const handleTagDelete = (event: React.MouseEvent, tagId: string) => {
    event.stopPropagation();
    if (removeTagFromModel) {
      removeTagFromModel(model.id, tagId);
      message.success(`标签已从模型移除`);
    }
  };
  
  return (
    <Card
      onClick={onSelect}
      sx={{
        position: 'relative',
        cursor: 'pointer',
        borderRadius: '12px',
        background: isSelected 
          ? `linear-gradient(135deg, ${alpha('#151c32', 0.7)}, ${alpha('#080e1c', 0.8)})`
          : alpha('#151c32', 0.6),
        border: isSelected 
          ? `2px solid ${alpha('#2ec0ff', 0.8)}` 
          : `1px solid ${alpha('#ffffff', 0.1)}`,
        padding: '18px',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
        boxShadow: isSelected 
          ? `0 10px 20px rgba(0,0,0,0.2), 0 0 15px ${alpha('#2ec0ff', 0.3)}` 
          : '0 8px 16px rgba(0,0,0,0.15)',
        transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        '&:hover': {
          transform: 'translateY(-5px)',
          boxShadow: isSelected 
            ? `0 15px 30px rgba(0,0,0,0.25), 0 0 20px ${alpha('#2ec0ff', 0.4)}` 
            : '0 12px 24px rgba(0,0,0,0.2)',
          border: isSelected 
            ? `2px solid ${alpha('#2ec0ff', 0.8)}` 
            : `1px solid ${alpha('#2ec0ff', 0.5)}`,
        },
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '2px',
          background: isSelected 
            ? `linear-gradient(90deg, transparent, ${alpha('#2ec0ff', 0.8)}, transparent)` 
            : 'transparent',
          opacity: 0.8,
        },
        '&::after': {
          content: '""',
          position: 'absolute',
          bottom: 0,
          left: isSelected ? '-100%' : '-200%',
          width: '200%',
          height: '1px',
          background: `linear-gradient(90deg, transparent, ${alpha('#2ec0ff', 0.5)}, transparent)`,
          transition: 'left 1.5s ease',
          animation: isSelected ? 'shimmer 3s infinite linear' : 'none',
        },
        '@keyframes shimmer': {
          '0%': { left: '-100%' },
          '100%': { left: '100%' },
        },
      }}
    >
      {isSelected && (
        <Box
          sx={{
            position: 'absolute',
            top: 12,
            right: 12,
            color: '#2ec0ff',
            animation: 'pulse 2s infinite ease-in-out',
            '@keyframes pulse': {
              '0%': { opacity: 0.7 },
              '50%': { opacity: 1 },
              '100%': { opacity: 0.7 },
            },
            zIndex: 2,
          }}
        >
          <CheckCircle fontSize="small" />
        </Box>
      )}
      
      {/* 添加重命名、删除和标签管理按钮 */}
      <Box sx={{
          position: 'absolute',
          top: 8,
          right: 8,
          zIndex: 3,
          display: 'flex',
          gap: 0.5, // 按钮之间的间距
          alignItems: 'center',
      }}>
          {/* 重命名按钮 */}
          <Tooltip title="重命名模型">
              <IconButton
                  size="small"
                  onClick={(e) => { 
                      e.stopPropagation(); // 阻止卡片点击事件
                      setIsRenameDialogOpen(true); // 打开重命名对话框
                      setNewModelName(model.name); // 初始化输入框为当前模型名称
                  }}
                  sx={{
                      bgcolor: alpha('#ffb100', 0.1),
                      color: '#ffb100',
                      width: 28,
                      height: 28,
                      '&:hover': {
                          bgcolor: alpha('#ffb100', 0.2),
                      }
                  }}
              >
                  <Edit fontSize="small" />
              </IconButton>
          </Tooltip>
          
          {/* 删除按钮 */}
          <Tooltip title="删除模型">
              <IconButton
                  size="small"
                  onClick={(e) => { 
                      e.stopPropagation(); // 阻止卡片点击事件
                      if (onDeleteModel) {
                          onDeleteModel(model.id, model.name);
                      }
                  }}
                  sx={{
                      bgcolor: alpha('#ff4d4f', 0.1),
                      color: '#ff4d4f', // 使用红色表示删除
                      width: 28,
                      height: 28,
                      '&:hover': {
                          bgcolor: alpha('#ff4d4f', 0.2),
                      }
                  }}
              >
                  <Delete fontSize="small" />
              </IconButton>
          </Tooltip>

          {/* 标签管理按钮 (保留) */}
          <Tooltip title="管理标签">
              <IconButton
                size="small"
                onClick={handleTagMenuClick}
                sx={{
                  bgcolor: alpha('#ffffff', 0.1),
                  color: '#ffffff',
                  width: 28,
                  height: 28,
                  '&:hover': {
                    bgcolor: alpha('#2ec0ff', 0.2),
                  }
                }}
              >
                <MoreVert fontSize="small" />
              </IconButton>
          </Tooltip>
      </Box>
      
      {/* 标签菜单 - 使用Menu组件替代Dialog */}
      <Menu
        anchorEl={localTagMenuAnchorEl}
        open={Boolean(localTagMenuAnchorEl)}
        onClose={handleCloseLocalTagMenu}
        PaperProps={{
          sx: {
            bgcolor: alpha('#151c32', 0.95),
            color: '#ffffff',
            borderRadius: '8px',
            boxShadow: '0 8px 16px rgba(0,0,0,0.3)',
            border: `1px solid ${alpha('#ffffff', 0.1)}`,
            minWidth: '220px',
            maxHeight: '350px',
            overflowY: 'auto'
          }
        }}
      >
        <Box sx={{ px: 2, py: 1.5, borderBottom: `1px solid ${alpha('#ffffff', 0.1)}` }}>
          <Typography sx={{ fontSize: '0.9rem', fontWeight: 600, mb: 0.5 }}>
            模型标签管理
            </Typography>
          <Typography sx={{ fontSize: '0.8rem', color: alpha('#ffffff', 0.7) }}>
            {model.name}
            </Typography>
          </Box>
        
        {/* 标签列表 */}
            {allTags && allTags.length > 0 ? (
              allTags.map((tag: Tag) => {
                // 检查这个标签是否已应用到模型上
                const hasTag = modelTagIds.includes(tag.id);
                
                return (
              <MenuItem 
                    key={tag.id}
                    onClick={() => handleToggleModelTag(tag.id)}
                    sx={{
                      py: 1.2,
                      px: 2,
                      borderLeft: hasTag ? `3px solid ${tag.color}` : `3px solid transparent`,
                      '&:hover': {
                    bgcolor: alpha('#ffffff', 0.1),
                      },
                    }}
                  >
                <ListItemIcon sx={{ minWidth: '32px' }}>
                      {hasTag ? (
                        <CheckCircle sx={{ color: tag.color }} />
                      ) : (
                        <Circle sx={{ color: alpha(tag.color, 0.5) }} />
                      )}
                </ListItemIcon>
                <ListItemText 
                  primary={tag.name}
                      sx={{ 
                    '& .MuiTypography-root': { 
                      color: hasTag ? tag.color : '#ffffff',
                        fontWeight: hasTag ? 600 : 400,
                    }
                  }}
                />
              </MenuItem>
                );
              })
            ) : (
          <MenuItem disabled>
            <ListItemText primary="暂无可用标签" />
          </MenuItem>
        )}
        
        <Divider sx={{ my: 1, borderColor: alpha('#ffffff', 0.1) }} />
        
        {/* 创建新标签选项 */}
        <MenuItem 
            onClick={handleCreateNewTag}
            sx={{
            py: 1.2,
              color: '#2ec0ff',
              '&:hover': {
              bgcolor: alpha('#2ec0ff', 0.1),
              },
            }}
          >
          <ListItemIcon sx={{ minWidth: '32px' }}>
            <Add sx={{ color: '#2ec0ff' }} />
          </ListItemIcon>
          <ListItemText primary="创建新标签" />
        </MenuItem>
      </Menu> {/* <--- 确保Menu标签正确闭合 */}

      {/* 添加重命名对话框 */}
      <Dialog
        open={isRenameDialogOpen}
        onClose={() => setIsRenameDialogOpen(false)}
        PaperProps={{
          sx: {
            bgcolor: alpha('#1a1f36', 0.9),
            color: '#ffffff',
            borderRadius: '12px',
            boxShadow: '0 8px 32px rgba(0,0,0,0.4)',
            border: `1px solid ${alpha('#2a3153', 0.5)}`,
            backdropFilter: 'blur(10px)',
          }
        }}
      >
        <DialogTitle sx={{ color: '#ffffff', fontWeight: 600 }}>重命名模型</DialogTitle>
        <DialogContent sx={{ pt: 2 }}>
          <TextField
            autoFocus
            margin="dense"
            label="新模型名称"
            type="text"
            fullWidth
            variant="outlined"
            value={newModelName}
            onChange={(e) => setNewModelName(e.target.value)}
            sx={{
              '& .MuiOutlinedInput-root': {
                bgcolor: alpha('#1a1f36', 0.5),
                color: '#ffffff',
                '& fieldset': { borderColor: alpha('#ffffff', 0.2) },
                '&:hover fieldset': { borderColor: alpha('#835df6', 0.5) },
                '&.Mui-focused fieldset': { borderColor: '#835df6' },
              },
              '& .MuiFormLabel-root': { color: alpha('#ffffff', 0.7) },
              '& .MuiFormLabel-root.Mui-focused': { color: '#835df6' },
            }}
          />
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button onClick={() => setIsRenameDialogOpen(false)} sx={{ color: alpha('#ffffff', 0.7) }}>取消</Button>
          <Button 
            onClick={() => {
              if (newModelName.trim() && onRenameModel) { // 检查新名称不为空且重命名函数存在
                onRenameModel(model.id, model.name, newModelName.trim()); // 调用重命名函数
                setIsRenameDialogOpen(false); // 关闭对话框
              } else if (!newModelName.trim()) {
                message.warning('新模型名称不能为空');
              }
            }}
            variant="contained"
            sx={{
              bgcolor: '#835df6',
              '&:hover': { bgcolor: alpha('#835df6', 0.8) }
            }}
          >
            确定
          </Button>
        </DialogActions>
      </Dialog>

      {/* 恢复卡片内容 */}
      <Box> {/* <--- 添加一个共同父元素Box */}
        <Box sx={{ mb: 2 }}> 
          <Typography 
            variant="subtitle1" 
            component="div"
            sx={{ 
              fontWeight: 700,
              color: isSelected ? '#2ec0ff' : '#ffffff',
              mb: 1.5,
              display: '-webkit-box',
              WebkitLineClamp: 1,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              textShadow: isSelected ? '0 0 8px rgba(46,192,255,0.4)' : 'none',
              fontSize: '1.2rem',
              lineHeight: '1.4',
              position: 'relative',
              paddingLeft: isSelected ? '12px' : '0',
              transition: 'all 0.3s ease',
              '&::before': {
                content: '""',
                position: 'absolute',
                left: 0,
                top: '50%',
                transform: 'translateY(-50%)',
                width: isSelected ? '4px' : '0',
                height: '80%',
                backgroundColor: '#2ec0ff',
                borderRadius: '2px',
                transition: 'all 0.3s ease',
              }
            }}
          >
            {model.name}
          </Typography>
          <Typography 
            sx={{ 
              color: isSelected ? alpha('#2ec0ff', 0.8) : '#8695bb',
              fontSize: '0.9rem',
              display: 'block',
              fontWeight: isSelected ? 500 : 400,
              letterSpacing: '0.01em',
            }}
          >
            ID: {model.id.substring(0, 8)}...
          </Typography>
        </Box>
        
        {/* 标签显示区域 */}
        {tags && tags.length > 0 && (
          <Box sx={{ mb: 2, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
            {tags.map(tag => (
              <Chip
                key={tag.id}
                label={tag.name}
                size="small"
                onDelete={(e) => {
                  e.stopPropagation(); // 阻止卡片点击事件
                  // 直接从模型中移除该标签
                  if (removeTagFromModel) {
                    removeTagFromModel(model.id, tag.id);
                    message.info(`标签"${tag.name}"已从模型移除`);
                  }
                }}
                deleteIcon={<Close fontSize="small" />}
                sx={{
                  bgcolor: alpha(tag.color, 0.2),
                  color: tag.color,
                  borderRadius: '4px',
                  fontSize: '0.75rem',
                  height: '22px',
                  '& .MuiChip-deleteIcon': {
                    color: alpha(tag.color, 0.7),
                    '&:hover': {
                      color: tag.color,
                    }
                  }
                }}
                onClick={(e) => {
                  e.stopPropagation(); // 阻止卡片点击事件
                  if (onTagClick) {
                    onTagClick(tag.id);
                  }
                }}
              />
            ))}
          </Box>
        )}

        <Typography 
          sx={{ 
            color: '#c5d0ee', 
            mb: 2,
            fontSize: '0.95rem',
            flex: 1,
            opacity: 0.9,
            lineHeight: '1.5',
          }}
        >
          模型ID: {model.id}
        </Typography>

        <Box 
          sx={{ 
            display: 'flex', 
            justifyContent: 'space-between',
            alignItems: 'center',
            mt: 'auto',
            pt: 1.5,
            borderTop: `1px solid ${alpha('#ffffff', 0.05)}`,
          }}
        >
          <Box 
            sx={{
              height: '8px',
              width: '60%', 
              background: `linear-gradient(to right, ${alpha('#4f7df9', 0.3)}, ${alpha('#2ec0ff', 0.3)})`,
              borderRadius: '4px',
              overflow: 'hidden',
              position: 'relative',
              '&::after': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: '-100%',
                width: '100%',
                height: '100%',
                background: `linear-gradient(to right, transparent, ${alpha('#4f7df9', 0.8)}, ${alpha('#2ec0ff', 0.8)}, transparent)`,
                animation: isSelected ? 'progressAnim 2s infinite' : 'none',
                '@keyframes progressAnim': {
                  '0%': { left: '-100%' },
                  '100%': { left: '100%' },
                },
              }
            }}
          />
          <IconButton
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              onPlayPreview();
            }}
            // 在其他模型加载或播放时禁用按钮
            disabled={isOtherModelLoadingOrPlaying} // <--- 添加此行
            sx={{
              bgcolor: isPlaying ? alpha('#7a6cf7', 0.2) : alpha('#4f7df9', 0.1),
              color: isPlaying ? '#7a6cf7' : '#4f7df9',
              '&:hover': {
                bgcolor: isPlaying ? alpha('#7a6cf7', 0.3) : alpha('#4f7df9', 0.2),
                transform: 'scale(1.1)',
              },
              transition: 'all 0.2s ease',
              width: '36px',
              height: '36px',
            }}
          >
            {isPlaying ? 
              <StopCircle sx={{ fontSize: '1.3rem' }} /> : 
              (isLoadingOrPlayingPreview ? 
                <CircularProgress size={20} sx={{ color: 'inherit' }} /> : // 显示加载指示器
                <PlayCircle sx={{ fontSize: '1.3rem' }} />
              )
            }
          </IconButton>
        </Box>
      </Box>
    </Card>
  );
};

export const TrainingWorkspace: React.FC<TrainingWorkspaceProps> = ({
  models,
  currentModel,
  setModel,
  onModelListUpdate
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [audioUploaded, setAudioUploaded] = useState(false);
  const [audioName, setAudioName] = useState('');
  const [modelName, setModelName] = useState('');
  const [selectedTagId, setSelectedTagId] = useState('');
  const [trainingProgress, setTrainingProgress] = useState(0);
  const [isTraining, setIsTraining] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [audioUrl, setAudioUrl] = useState<string>('');
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [isPlaying, setIsPlaying] = useState(false);
  const [trainedModelId, setTrainedModelId] = useState<string>('');
  const audioRef = useRef<HTMLAudioElement>(null);
  const [audioCurrentTime, setAudioCurrentTime] = useState(0);
  const [audioDuration, setAudioDuration] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const progressBarRef = useRef<HTMLDivElement>(null);
  const animationFrameRef = useRef<number>();
  const [tags, setTags] = useState<Tag[]>([] as Tag[]);
  const [trainedModelFileName, setTrainedModelFileName] = useState('');
  const [trainingStartTime, setTrainingStartTime] = useState<number>(0);
  const [trainingDuration, setTrainingDuration] = useState<string>('');
  const [tabValue, setTabValue] = useState(0);
  
  // 从VoiceWorkspace.tsx复制的模型管理相关状态
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [filteredModels, setFilteredModels] = useState<Model[]>([]);
  const [activeTagFilters, setActiveTagFilters] = useState<string[]>([]);
  const [modelTags, setModelTags] = useState<{[modelId: string]: string[]}>({});
  const [filterMenuAnchorEl, setFilterMenuAnchorEl] = useState<HTMLElement | null>(null);
  const [isCreateTagDialogOpen, setIsCreateTagDialogOpen] = useState(false);
  const [newTagName, setNewTagName] = useState('');
  const [newTagColor, setNewTagColor] = useState('#4f7df9');
  const [selectedModelForTag, setSelectedModelForTag] = useState<string | null>(null);

  // 试听音频状态，用于模型管理中的试听功能
  const [previewAudio, setPreviewAudio] = useState<{url: string; modelId: string; isPlaying: boolean;} | null>(null);
  const [previewLoading, setPreviewLoading] = useState<string | null>(null);

  // 每页显示的模型数量（常量）
  const itemsPerPage = 6;

  const steps = ['上传参考音频', '设置模型参数', '开始训练', '模型完成'];
  
  const updateAudioProgress = useCallback(() => {
    if (audioRef.current) {
      setAudioCurrentTime(audioRef.current.currentTime);
      animationFrameRef.current = requestAnimationFrame(updateAudioProgress);
    }
  }, []);

  const initAudioListeners = useCallback(() => {
    if (!audioRef.current) return;

    const audio = audioRef.current;
    
    audio.addEventListener('loadedmetadata', () => {
      setAudioDuration(audio.duration);
      setAudioCurrentTime(0);
    });

    audio.addEventListener('play', () => {
      animationFrameRef.current = requestAnimationFrame(updateAudioProgress);
    });

    audio.addEventListener('pause', () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    });

    audio.addEventListener('ended', () => {
      setIsPlaying(false);
      setAudioCurrentTime(0);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    });
  }, [updateAudioProgress]);

  useEffect(() => {
    return () => {
      if (audioRef.current) {
        const audio = audioRef.current;
        audio.pause();
        URL.revokeObjectURL(audio.src);
      }
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (audioFile) {
      const url = URL.createObjectURL(audioFile);
      setAudioUrl(url);
      
      // 重置播放状态
      setIsPlaying(false);
      setAudioCurrentTime(0);
      
      return () => {
        URL.revokeObjectURL(url);
      };
    }
  }, [audioFile]);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleTimeUpdate = () => {
      setAudioCurrentTime(audio.currentTime);
    };

    const handleLoadedMetadata = () => {
      setAudioDuration(audio.duration);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setAudioCurrentTime(0);
    };

    // 添加事件监听
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('ended', handleEnded);

    // 清理函数
    return () => {
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('ended', handleEnded);
    };
  }, [audioRef.current]);

  // 初始化时加载标签数据 (从VoiceWorkspace.tsx复制)
  useEffect(() => {
    const savedTags = TagStorage.getTags();
    const savedModelTags = TagStorage.getModelTags();
    
    if(savedTags.length > 0) {
      setTags(savedTags);
    } else {
      // 如果没有保存的标签，设置默认标签
      const defaultTags = [
        { id: 'female', name: '女声', color: '#FF61A6' },
        { id: 'male', name: '男声', color: '#3F84E5' },
        { id: 'special', name: '特色', color: '#8B5CF6' },
      ];
      setTags(defaultTags);
      TagStorage.saveTags(defaultTags);
    }
    
    if(Object.keys(savedModelTags).length > 0) {
      setModelTags(savedModelTags);
    }
  }, []); // 仅在组件挂载时运行

  // 当标签列表变化时保存 (从VoiceWorkspace.tsx复制)
  useEffect(() => {
    if(tags.length > 0) {
      TagStorage.saveTags(tags);
    }
  }, [tags]); // 依赖tags

  // 自动将模型分配到相应的标签类别 (从VoiceWorkspace.tsx复制)
  useEffect(() => {
    // 初始化时，根据模型名称自动分配标签
    const updatedModelTags = { ...modelTags };
    
    models.forEach(model => {
      if (!updatedModelTags[model.id]) {
        updatedModelTags[model.id] = [];
        
        // 根据模型名称特征自动分配标签
        if (model.name.includes('女') || model.name.toLowerCase().includes('f')) {
          updatedModelTags[model.id].push('female');
        } else if (model.name.includes('男') || model.name.toLowerCase().includes('m')) {
          updatedModelTags[model.id].push('male');
        } else {
          updatedModelTags[model.id].push('special');
        }
      }
    });
    
    setModelTags(updatedModelTags); // 这个状态更新可能需要细化，以避免覆盖手动添加的标签
  }, [models]); // 依赖models

  // 搜索和过滤模型 (从VoiceWorkspace.tsx复制)
  useEffect(() => {
    let result = models;
    
    // 搜索过滤
    if (searchTerm.trim()) {
      const query = searchTerm.toLowerCase().trim();
      result = result.filter(model => 
        model.name.toLowerCase().includes(query) || 
        model.id.toLowerCase().includes(query)
      );
    }
    
    // 标签筛选
    if (activeTagFilters.length > 0) {
      result = result.filter(model => {
        const modelTagIds = modelTags[model.id] || [];
        return activeTagFilters.every(tagId => modelTagIds.includes(tagId));
      });
    }
    
    setFilteredModels(result); // 更新过滤后的模型列表
    setCurrentPage(1); // 重置到第一页
  }, [searchTerm, models, modelTags, activeTagFilters]); // 依赖搜索词、模型列表、模型标签和活跃筛选器

  // 监听标签系统更改事件 (从VoiceWorkspace.tsx复制)
  useEffect(() => {
    // 定义一个处理标签系统更改的函数
    const handleTagSystemChange = () => {
      console.log('TrainingWorkspace (ModelManagement): 检测到标签系统更改，重新加载数据');
      const savedTags = TagStorage.getTags();
      const savedModelTags = TagStorage.getModelTags();
      
      if(savedTags.length > 0) {
        setTags(savedTags);
      }
      
      if(Object.keys(savedModelTags).length > 0) {
        setModelTags(savedModelTags);
      }
    };
    
    // 添加自定义事件监听器
    window.addEventListener('tag-system-change', handleTagSystemChange);
    
    // 组件卸载时移除监听器
    return () => {
      window.removeEventListener('tag-system-change', handleTagSystemChange);
    };
  }, []); // 仅在组件挂载和卸载时运行

  const handleTagChange = (tagId: string) => {
    setSelectedTagId(tagId);
  };

  const handleAudioTimeUpdate = () => {
    if (audioRef.current) {
      setAudioCurrentTime(audioRef.current.currentTime);
    }
  };

  const handleAudioLoadedMetadata = () => {
    if (audioRef.current) {
      setAudioDuration(audioRef.current.duration);
    }
  };

  const handlePlayPause = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
    } else {
      audio.play().catch(error => {
        console.error('播放失败:', error);
      });
    }
    setIsPlaying(!isPlaying);
  };

  const handleAudioUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (!file.name.toLowerCase().endsWith('.wav')) {
        setErrorMessage('请上传WAV格式的音频文件');
        return;
      }
      
      if (audioRef.current) {
        audioRef.current.pause();
        URL.revokeObjectURL(audioRef.current.src);
      }
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      
      setAudioFile(file);
      setAudioUploaded(true);
      setAudioName(file.name);
      setErrorMessage('');
      setIsPlaying(false);
      setAudioCurrentTime(0);
      setAudioDuration(0);
    }
  }, []);
  
  const handleDeleteAudio = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause();
      URL.revokeObjectURL(audioRef.current.src);
    }
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
    setIsPlaying(false);
    setAudioUploaded(false);
    setAudioName('');
    setAudioFile(null);
    setAudioUrl('');
    setAudioCurrentTime(0);
    setAudioDuration(0);
  }, []);

  // 格式化时长显示
  const formatDuration = (duration: number): string => {
    const totalSeconds = Math.floor(duration / 1000); // 将毫秒转换为秒
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${String(minutes).padStart(2, '0')}分${String(seconds).padStart(2, '0')}秒`;
  };

  const startTraining = async () => {
    if (!audioFile || !modelName || !selectedTagId) {
      setSnackbarMessage('请确保已上传音频文件、设置模型名称和选择声音类型');
      setSnackbarOpen(true);
      return;
    }
    
    setIsTraining(true);
    const startTime = Date.now(); // 记录开始时间
    setTrainingStartTime(startTime);
    
    try {
      console.log('开始准备训练数据:', {
        audioFileName: audioFile.name,
        modelName: modelName,
        tagId: selectedTagId,
        fileSize: audioFile.size
      });

      const formData = new FormData();
      formData.append('audio_file', audioFile);
      formData.append('model_name', modelName);
      formData.append('tag_id', selectedTagId);
      
      console.log('正在发送训练请求...');
      const response = await fetch('http://localhost:8088/train', {
        method: 'POST',
        body: formData
      });
      
      console.log('收到服务器响应:', response.status);
      
      if (response.ok) {
        const responseData = await response.json();
        console.log('训练响应数据:', responseData);
        
        // 计算训练时长
        const endTime = Date.now();
        const duration = endTime - startTime; // 使用之前保存的startTime
        const formattedDuration = formatDuration(duration);
        setTrainingDuration(formattedDuration);
        console.log('训练时长:', formattedDuration);
        
        setIsTraining(false);
        setActiveStep(3);
        
        // 训练完成后，将标签添加到模型
        const modelFileName = `${modelName}.pt`;
        console.log('添加标签到模型:', modelFileName);
        
        try {
          TagStorage.addTagToModel(modelFileName, selectedTagId);
          console.log('标签添加成功');
        } catch (error) {
          console.error('添加标签失败:', error);
          setSnackbarMessage('模型标签添加失败，请稍后在模型管理中手动添加');
          setSnackbarOpen(true);
        }
        
        if (onModelListUpdate) {
          onModelListUpdate();
        }
      } else {
        const errorData = await response.json().catch(() => ({ message: '未知错误' }));
        console.error('训练失败响应:', errorData);
        throw new Error(errorData.message || '训练失败，请检查服务器状态');
      }
    } catch (error) {
      console.error('训练失败:', error);
      setErrorMessage(error instanceof Error ? error.message : '训练失败');
      setSnackbarMessage('训练失败，请重试');
      setSnackbarOpen(true);
      setIsTraining(false);
      setTrainingStartTime(0);
    }
  };

  const handleNext = () => {
    if (activeStep === 2) {
      startTraining();
    } else {
      setActiveStep(activeStep + 1);
    }
  };
  
  const handleBack = () => {
    setActiveStep(activeStep - 1);
  };
  
  const handleReset = () => {
    setActiveStep(0);
    setAudioUploaded(false);
    setAudioName('');
    setModelName('');
    setTrainingProgress(0);
  };

  // 高级卡片样式
  const advancedCardStyle = {
    position: 'relative',
    overflow: 'hidden',
    backgroundColor: alpha('#1a1f36', 0.7),
    borderRadius: '12px',
    boxShadow: `0 8px 32px ${alpha('#000', 0.2)}`,
    border: `1px solid ${alpha('#2a3153', 0.5)}`,
    transition: 'transform 0.3s ease, box-shadow 0.3s ease',
    '&:hover': {
      boxShadow: `0 10px 40px ${alpha('#000', 0.3)}`,
      transform: 'translateY(-2px)'
    }
  };

  // 卡片内容样式
  const advancedCardContent = {
    p: 3,
    '&:last-child': {
      pb: 3
    }
  };

  // 格式化时间
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleTagFilterToggle = (tagId: string) => {
    if (activeTagFilters.includes(tagId)) {
      setActiveTagFilters(activeTagFilters.filter(id => id !== tagId));
    } else {
      setActiveTagFilters([...activeTagFilters, tagId]);
    }
  };

  // 辅助函数：将Base64音频数据转换为可播放的URL
  const base64ToAudioUrl = (base64: string): string | null => {
    try {
      const byteCharacters = atob(base64);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: 'audio/wav' }); // 假设是wav格式
      return URL.createObjectURL(blob);
    } catch (error) {
      console.error('Base64 to audio URL conversion error:', error);
      return null;
    }
  };

  const { generateSpeech, stopAudio } = useTTS(); // 使用 useTTS hook，并修正函数名称

  // 处理模型卡片试听播放 (从VoiceWorkspace.tsx复制并适配)
  const handlePlayModelPreview = async (modelId: string, modelName: string) => {
    // 如果正在试听同一个模型，则暂停
    if (previewAudio && previewAudio.modelId === modelId && previewAudio.isPlaying) {
      // 停止当前播放的音频
      setPreviewAudio({ ...previewAudio, isPlaying: false }); // 暂停，不清除URL

      return;
    }

    // 如果正在试听其他模型，先停止
    if (previewAudio && previewAudio.isPlaying) {
        // 停止播放
        if (previewAudio.url) { // 检查是否存在URL
           const audio = new Audio(previewAudio.url); // 创建Audio对象用于暂停 (仅为调用pause/revokeObjectURL)
           audio.pause();
           // 清除旧的 audio url
           URL.revokeObjectURL(previewAudio.url);
        }
        setPreviewAudio(null); // 清空previewAudio状态
    }

    // 设置加载状态
    setPreviewLoading(modelId);

    try {
      // 试听文案（去除扩展名）
      const previewText = `你好，我是${getModelDisplayName(modelName)}，期待和你一起直播`;
      // 生成试听音频（不加入队列），传递模型ID
      const audioData = await generateSpeech(previewText, true, modelId);

      if (audioData) {
        // 转换为可播放的 URL
        const audioUrl = base64ToAudioUrl(audioData);
        if (audioUrl) {

           // 创建新的Audio对象并播放
           const audio = new Audio(audioUrl);
           audio.play().catch(error => {
             console.error('试听播放失败:', error);
             message.error('试听播放失败');
             setPreviewAudio(null); // 播放失败清除状态
             setPreviewLoading(null);
           });

           // 更新试听状态
           setPreviewAudio({
             url: audioUrl,
             modelId: modelId,
             isPlaying: true,
           });

           // 监听播放结束事件
           audio.onended = () => {
             console.log('试听播放结束');
             setPreviewAudio(prev => prev ? { ...prev, isPlaying: false } : null);
             // 播放结束后可以保留URL，或者在这里释放
             // URL.revokeObjectURL(audioUrl);
           };

        } else {
           message.error('生成试听音频URL失败');
           setPreviewAudio(null);
        }
        // 音频生成成功，清除加载状态
        setPreviewLoading(null);
      } else {
        // 音频生成失败，清除加载状态
        message.error('生成试听音频失败');
        setPreviewAudio(null);
        setPreviewLoading(null);
      }
    } catch (error) {
      console.error('试听音频生成错误:', error);
      message.error(`试听音频错误: ${error instanceof Error ? error.message : String(error)}`);
      setPreviewAudio(null);
    } finally {
      // 无论成功或失败，清除加载状态 (已在try/catch块内处理)
      // setPreviewLoading(null);
    }
  };
  
  // 处理模型标签添加 (从VoiceWorkspace.tsx复制并适配)
  const addTagToModel = (modelId: string, tagId: string) => {
    TagStorage.addTagToModel(modelId, tagId);
    // 更新本地状态以反映变化
    setModelTags(prevModelTags => {
      const newModelTags = { ...prevModelTags };
      if (!newModelTags[modelId]) {
        newModelTags[modelId] = [];
      }
      if (!newModelTags[modelId].includes(tagId)) {
        newModelTags[modelId].push(tagId);
      }
      return newModelTags;
    });
    // 触发事件通知其他组件 (可选，如果需要跨组件同步)
    window.dispatchEvent(new CustomEvent('tag-system-change'));
  };
  
  // 处理模型标签移除 (从VoiceWorkspace.tsx复制并适配)
  const removeTagFromModel = (modelId: string, tagId: string) => {
    TagStorage.removeTagFromModel(modelId, tagId);
    // 更新本地状态以反映变化
    setModelTags(prevModelTags => {
      const newModelTags = { ...prevModelTags };
      if (newModelTags[modelId]) {
        newModelTags[modelId] = newModelTags[modelId].filter(id => id !== tagId);
        // 如果模型没有任何标签了，可以考虑移除这个modelId的key或者保留空数组
        // delete newModelTags[modelId]; // 根据需要决定是否移除
      }
      return newModelTags;
    });
    // 触发事件通知其他组件 (可选，如果需要跨组件同步)
    window.dispatchEvent(new CustomEvent('tag-system-change'));
  };
  
    // 获取模型显示名称 (从VoiceWorkspace.tsx复制)
  const getModelDisplayName = (modelName: string) => {
    // 移除文件扩展名 .pt
    return modelName.endsWith('.pt') ? modelName.slice(0, -3) : modelName;
  };

  
  
  // 处理模型删除
  const handleDeleteModel = async (modelId: string, modelName: string) => {
      if (window.confirm(`确定要删除模型 "${modelName}" 吗？`)) {
          try {
              const response = await fetch(`http://localhost:8088/models/${modelName}`, {
                  method: 'DELETE',
              });
  
              if (!response.ok) {
                  const errorText = await response.text();
                  throw new Error(`删除失败: ${errorText}`);
              }
  
              message.success(`模型 "${modelName}" 删除成功`);
              // 从模型列表中移除已删除的模型
              // setModels(models.filter(model => model.id !== modelId)); // <--- 删除此行
              if (onModelListUpdate) { // <--- 调用 onModelListUpdate
                onModelListUpdate();
              }
              // 同时从模型标签关联中移除
              setModelTags(prevModelTags => {
                  const newModelTags = { ...prevModelTags };
                  delete newModelTags[modelId];
                  return newModelTags;
              });
              // 刷新前端模型列表 (如果后端没有自动推送更新的话)
              // handleFetchModels(); // 可选，如果需要强制刷新
          } catch (error) {
              console.error('模型删除失败:', error);
              message.error(`删除失败: ${error instanceof Error ? error.message : String(error)}`);
          }
      }
  };

  // 创建标签对话框相关状态和处理函数 (从VoiceWorkspace.tsx复制)
  const handleOpenCreateTagDialog = (modelId: string | null = null) => {
    setSelectedModelForTag(modelId); // 记录是从哪个模型触发的创建
    setIsCreateTagDialogOpen(true);
  };

  const handleCloseCreateTagDialog = () => {
    setIsCreateTagDialogOpen(false);
    setNewTagName('');
    setNewTagColor('#4f7df9');
    setSelectedModelForTag(null);
  };

  const handleCreateTagConfirm = () => {
    if (newTagName.trim()) {
      const newTag: Tag = {
        id: `tag_${Date.now()}_${Math.floor(Math.random() * 1000)}`, // 简单的唯一ID生成
        name: newTagName.trim(),
        color: newTagColor,
      };
      TagStorage.addTag(newTag);
      setTags([...tags, newTag]); // 更新本地标签列表
      message.success(`标签 "${newTag.name}" 创建成功`);
      
      // 如果是从模型卡片触发的创建，且成功创建标签，则自动将该标签添加到模型
      if (selectedModelForTag) {
        addTagToModel(selectedModelForTag, newTag.id);
        // message.success(`标签 "${newTag.name}" 已添加到模型`); // addTagToModel中已经有提示
      }
      
      handleCloseCreateTagDialog();
    } else {
      message.warning('标签名称不能为空');
    }
  };

  // 监听创建标签的自定义事件 (从VoiceWorkspace.tsx复制)
  useEffect(() => {
    const handleCreateTagEvent = (event: CustomEvent) => {
      const modelId = event.detail.modelId;
      handleOpenCreateTagDialog(modelId);
    };

    window.addEventListener('createTag', handleCreateTagEvent as EventListener);

    return () => {
      window.removeEventListener('createTag', handleCreateTagEvent as EventListener);
    };
  }, []); // 仅在组件挂载和卸载时运行

  // 添加重命名模型函数
  const handleRenameModel = async (modelId: string, currentModelName: string, newModelName: string) => {
      if (currentModelName === newModelName) {
          message.info('新名称与旧名称相同，无需重命名');
          return;
      }
      if (!newModelName.trim()) {
          message.warning('新模型名称不能为空');
          return;
      }

      if (window.confirm(`确定要将模型 "${currentModelName}" 重命名为 "${newModelName}" 吗？`)) {
          try {
              // 根据错误信息，将旧名称和新名称作为查询参数发送
              const response = await fetch(`http://localhost:8088/models/rename?old_name=${encodeURIComponent(currentModelName)}&new_name=${encodeURIComponent(newModelName.trim())}`, {
                  method: 'POST', // 方法保持POST
                  headers: {
                      // 不需要Content-Type: application/json，因为数据在URL中
                      // 'Content-Type': 'application/json',
                  },
                  // 不需要请求体
                  // body: JSON.stringify({
                  //     current_name: currentModelName, // 发送当前模型名称
                  //     new_name: newModelName.trim(), // 发送新模型名称
                  // }),
              });

              if (!response.ok) {
                  const errorText = await response.text();
                  throw new Error(`重命名失败: ${errorText}`);
              }

              message.success(`模型 "${currentModelName}" 已成功重命名为 "${newModelName}"`);
              
              // 更新本地模型列表，确保名称立即反映
              // 注意：如果后端返回了更新后的模型列表，可以使用后端的数据
              // 这里假设需要手动更新或等待onModelListUpdate触发
              if (onModelListUpdate) { // 调用 onModelListUpdate 刷新整个列表
                onModelListUpdate();
              } else { // 或者尝试手动更新当前组件的状态，但这依赖于models state
                // 如果 TrainingWorkspace 内部维护了 models 状态并且这个状态是可写的
                // setModels(prevModels => prevModels.map(m => m.id === modelId ? { ...m, name: newModelName.trim() } : m));
                // 如果 models 是 props 传递且不可直接修改，依赖 onModelListUpdate 是更推荐的方式
              }
              
          } catch (error) {
              console.error('模型重命名失败:', error);
              message.error(`重命名失败: ${error instanceof Error ? error.message : String(error)}`);
          }
      }
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 4 }}>
        <Tabs 
          value={tabValue} 
          onChange={handleTabChange}
          sx={{
            '& .MuiTab-root': {
              fontSize: '18px',
              fontWeight: 600,
              color: alpha('#94a3b8', 0.7),
              transition: 'all 0.3s ease',
              minWidth: '160px',
              padding: '16px 32px',
              marginRight: '24px',
              '&.Mui-selected': {
                background: 'linear-gradient(to right, #60A5FA, #A78BFA)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontWeight: 700,
              },
              '&:hover': {
                color: alpha('#60A5FA', 0.9),
              },
              '&:not(:last-child)': {
                marginRight: '32px',
              },
            },
            '& .MuiTabs-indicator': {
              height: '3px',
              background: 'linear-gradient(90deg, #60A5FA 0%, #A78BFA 100%)',
              boxShadow: '0 0 10px rgba(139, 92, 246, 0.5)',
            },
          }}
        >
          <Tab 
            label="模型训练" 
            sx={{
              '&::after': {
                content: '""',
                position: 'absolute',
                right: '-16px',
                top: '50%',
                transform: 'translateY(-50%)',
                width: '1px',
                height: '20px',
                background: alpha('#94a3b8', 0.2),
              }
            }}
          />
          <Tab label="模型管理" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Typography
          variant="h4"
          sx={{
            fontWeight: 600,
            mb: 3,
            background: 'linear-gradient(90deg, #4f7df9, #835df6)',
            backgroundClip: 'text',
            textFillColor: 'transparent',
            display: 'inline-block',
          }}
        >
          模型训练
        </Typography>

        <Stack spacing={2.5}>
          {/* 训练进度卡片 */}
          <Card sx={advancedCardStyle}>
            <CardContent sx={advancedCardContent}>
              <Stack direction="row" alignItems="center" spacing={1.5} mb={4}>
                <AutoAwesome sx={{ 
                  color: '#835df6',
                  animation: 'pulse 1.5s infinite',
                  '@keyframes pulse': {
                    '0%': { opacity: 0.7 },
                    '50%': { opacity: 1 },
                    '100%': { opacity: 0.7 }
                  }
                }} />
                <Typography variant="h6" sx={{ 
                  color: '#ffffff',
                  fontWeight: 600,
                  letterSpacing: '0.05em',
                }}>
                  声音模型训练
                </Typography>
              </Stack>
              
              <Stepper activeStep={activeStep} alternativeLabel sx={{ mb: 4 }}>
                {steps.map((label) => (
                  <Step key={label}>
                    <StepLabel
                      sx={{
                        '& .MuiStepLabel-label': {
                          color: alpha('#ffffff', 0.7),
                          '&.Mui-active': {
                            color: '#835df6',
                          },
                          '&.Mui-completed': {
                            color: alpha('#ffffff', 0.9),
                          }
                        },
                        '& .MuiStepIcon-root': {
                          color: alpha('#1f2747', 0.7),
                          '&.Mui-active': {
                            color: '#835df6',
                          },
                          '&.Mui-completed': {
                            color: '#835df6',
                          }
                        }
                      }}
                    >
                      {label}
                    </StepLabel>
                  </Step>
                ))}
              </Stepper>
              
              <Box sx={{ mt: 2, mb: 4 }}>
                {activeStep === 0 && (
                  <Paper
                    sx={{
                      backgroundColor: alpha('#1f2747', 0.5),
                      borderRadius: '10px',
                      p: 3,
                      border: `1px solid ${alpha('#ffffff', 0.1)}`
                    }}
                  >
                    <Stack spacing={3}>
                      <Alert 
                        severity="info" 
                        sx={{
                          bgcolor: alpha('#4f7df9', 0.1),
                          color: '#ffffff',
                          border: `1px solid ${alpha('#4f7df9', 0.3)}`,
                          '& .MuiAlert-icon': {
                            color: '#4f7df9'
                          }
                        }}
                      >
                        请上传一段干净清晰的人声WAV音频，长度约10秒钟，用于训练您的专属语音模型
                      </Alert>
                      
                      {!audioUploaded ? (
                        <Box
                          component="label"
                          sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            justifyContent: 'center',
                            p: 5,
                            bgcolor: alpha('#1a1f36', 0.5),
                            borderRadius: '10px',
                            border: `2px dashed ${alpha('#835df6', 0.5)}`,
                            cursor: 'pointer',
                            transition: 'all 0.2s ease',
                            '&:hover': {
                              bgcolor: alpha('#1a1f36', 0.7),
                              borderColor: alpha('#835df6', 0.7),
                            }
                          }}
                        >
                          <Upload sx={{ color: '#835df6', fontSize: 48, mb: 2 }} />
                          <Typography sx={{ color: '#ffffff', fontWeight: 500, mb: 1 }}>
                            点击上传音频文件
                          </Typography>
                          <Typography sx={{ color: alpha('#ffffff', 0.7), fontSize: '0.875rem' }}>
                            支持WAV格式，建议音频长度10-30秒
                          </Typography>
                          <input
                            type="file"
                            hidden
                            accept=".wav"
                            onChange={handleAudioUpload}
                          />
                        </Box>
                      ) : (
                        <Box
                          sx={{
                            p: 3,
                            bgcolor: alpha('#1a1f36', 0.5),
                            borderRadius: '10px',
                            border: `1px solid ${alpha('#835df6', 0.3)}`,
                            backdropFilter: 'blur(10px)',
                            transition: 'all 0.3s ease',
                            '&:hover': {
                              border: `1px solid ${alpha('#835df6', 0.5)}`,
                              boxShadow: `0 0 20px ${alpha('#835df6', 0.2)}`,
                            }
                          }}
                        >
                          <Stack direction="row" alignItems="center" spacing={2}>
                            <Box
                              sx={{
                                width: 48,
                                height: 48,
                                borderRadius: '12px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                bgcolor: alpha('#835df6', 0.1),
                                border: `1px solid ${alpha('#835df6', 0.2)}`,
                              }}
                            >
                              <Mic sx={{ color: '#835df6', fontSize: 24 }} />
                            </Box>
                            <Box sx={{ flexGrow: 1 }}>
                              <Typography sx={{ 
                                color: '#ffffff', 
                                fontWeight: 500, 
                                mb: 0.5,
                                fontSize: '1rem',
                                letterSpacing: '0.02em'
                              }}>
                                {audioName}
                              </Typography>
                              <Stack direction="row" spacing={1} alignItems="center">
                                <Chip 
                                  label="WAV" 
                                  size="small"
                                  sx={{
                                    bgcolor: alpha('#835df6', 0.15),
                                    color: '#835df6',
                                    fontWeight: 600,
                                    fontSize: '0.7rem',
                                    height: 20,
                                    border: `1px solid ${alpha('#835df6', 0.3)}`,
                                  }}
                                />
                                <Typography sx={{ 
                                  color: alpha('#ffffff', 0.7), 
                                  fontSize: '0.75rem',
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 0.5
                                }}>
                                  <span style={{ color: '#835df6' }}>{formatTime(audioCurrentTime)}</span>
                                  <span style={{ color: alpha('#ffffff', 0.5) }}>/</span>
                                  <span>{formatTime(audioDuration)}</span>
                                </Typography>
                              </Stack>
                            </Box>
                            <Box sx={{ flexGrow: 1, mx: 2 }}>
                              <Slider
                                value={audioCurrentTime}
                                min={0}
                                max={audioDuration || 100}
                                onChange={(_, value) => {
                                  if (audioRef.current) {
                                    audioRef.current.currentTime = value as number;
                                    setAudioCurrentTime(value as number);
                                  }
                                }}
                                sx={{
                                  color: '#835df6',
                                  height: 4,
                                  padding: '15px 0',
                                  '& .MuiSlider-thumb': {
                                    width: 12,
                                    height: 12,
                                    backgroundColor: '#835df6',
                                    boxShadow: `0 0 10px ${alpha('#835df6', 0.5)}`,
                                    transition: 'all 0.2s cubic-bezier(.47,1.64,.41,.8)',
                                    '&:before': {
                                      boxShadow: '0 2px 12px 0 rgba(0,0,0,0.4)',
                                    },
                                    '&:hover, &.Mui-focusVisible': {
                                      boxShadow: `0 0 0 8px ${alpha('#835df6', 0.16)}`,
                                    },
                                    '&.Mui-active': {
                                      width: 16,
                                      height: 16,
                                    },
                                  },
                                  '& .MuiSlider-rail': {
                                    opacity: 0.2,
                                    backgroundColor: alpha('#ffffff', 0.2),
                                  },
                                  '& .MuiSlider-track': {
                                    background: 'linear-gradient(90deg, #835df6, #4f7df9)',
                                    border: 'none',
                                    boxShadow: `0 0 8px ${alpha('#835df6', 0.4)}`,
                                  },
                                }}
                              />
                            </Box>
                            <Stack direction="row" spacing={1}>
                              <IconButton 
                                size="small" 
                                sx={{ 
                                  color: '#835df6',
                                  width: 36,
                                  height: 36,
                                  bgcolor: alpha('#835df6', 0.1),
                                  border: `1px solid ${alpha('#835df6', 0.2)}`,
                                  transition: 'all 0.3s ease',
                                  '&:hover': {
                                    bgcolor: alpha('#835df6', 0.2),
                                    border: `1px solid ${alpha('#835df6', 0.4)}`,
                                    transform: 'scale(1.05)',
                                  },
                                  '&:active': {
                                    transform: 'scale(0.95)',
                                  }
                                }}
                                onClick={handlePlayPause}
                              >
                                {isPlaying ? <Stop sx={{ fontSize: 20 }} /> : <PlayArrow sx={{ fontSize: 20 }} />}
                              </IconButton>
                              <IconButton 
                                size="small" 
                                sx={{ 
                                  color: '#ff5a7d',
                                  width: 36,
                                  height: 36,
                                  bgcolor: alpha('#ff5a7d', 0.1),
                                  border: `1px solid ${alpha('#ff5a7d', 0.2)}`,
                                  transition: 'all 0.3s ease',
                                  '&:hover': {
                                    bgcolor: alpha('#ff5a7d', 0.2),
                                    border: `1px solid ${alpha('#ff5a7d', 0.4)}`,
                                    transform: 'scale(1.05)',
                                  },
                                  '&:active': {
                                    transform: 'scale(0.95)',
                                  }
                                }} 
                                onClick={handleDeleteAudio}
                              >
                                <Delete sx={{ fontSize: 20 }} />
                              </IconButton>
                            </Stack>
                          </Stack>
                        </Box>
                      )}
                    </Stack>
                  </Paper>
                )}
                
                {activeStep === 1 && (
                  <Paper
                    sx={{
                      backgroundColor: alpha('#1f2747', 0.5),
                      borderRadius: '10px',
                      p: 3,
                      border: `1px solid ${alpha('#ffffff', 0.1)}`
                    }}
                  >
                    <Grid container spacing={3}>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="模型名称"
                          value={modelName}
                          onChange={(e) => setModelName(e.target.value)}
                          placeholder="请输入您的模型名称，例如：小红的声音"
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              bgcolor: alpha('#1a1f36', 0.5),
                              color: '#ffffff',
                              '& fieldset': {
                                borderColor: alpha('#ffffff', 0.2),
                              },
                              '&:hover fieldset': {
                                borderColor: alpha('#835df6', 0.5),
                              },
                              '&.Mui-focused fieldset': {
                                borderColor: '#835df6',
                              }
                            },
                            '& .MuiFormLabel-root': {
                              color: alpha('#ffffff', 0.7),
                            },
                            '& .MuiFormLabel-root.Mui-focused': {
                              color: '#835df6',
                            }
                          }}
                        />
                      </Grid>
                      
                      <Grid item xs={12}>
                        <FormControl fullWidth>
                          <Typography sx={{ color: '#ffffff', mb: 1 }}>
                            选择声音类型
                          </Typography>
                          <RadioGroup
                            value={selectedTagId}
                            onChange={(e) => handleTagChange(e.target.value)}
                            sx={{ display: 'flex', flexDirection: 'row', gap: 2 }}
                          >
                            {tags.map((tag) => (
                              <FormControlLabel
                                key={tag.id}
                                value={tag.id}
                                control={
                                  <Radio
                                    sx={{
                                      color: alpha(tag.color, 0.7),
                                      '&.Mui-checked': {
                                        color: tag.color,
                                      },
                                    }}
                                  />
                                }
                                label={
                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                    <Typography sx={{ color: '#ffffff' }}>
                                      {tag.name}
                                    </Typography>
                                    <Box
                                      sx={{
                                        width: 8,
                                        height: 8,
                                        borderRadius: '50%',
                                        bgcolor: tag.color
                                      }}
                                    />
                                  </Box>
                                }
                              />
                            ))}
                          </RadioGroup>
                        </FormControl>
                      </Grid>
                      
                      <Grid item xs={12}>
                        <Alert 
                          severity="info" 
                          sx={{
                            bgcolor: alpha('#835df6', 0.1),
                            color: '#ffffff',
                            border: `1px solid ${alpha('#835df6', 0.3)}`,
                            '& .MuiAlert-icon': {
                              color: '#835df6'
                            }
                          }}
                        >
                          模型训练预计耗时5-10分钟，过程中请勿关闭窗口，训练完成后模型将自动添加到您的模型列表中
                        </Alert>
                      </Grid>
                    </Grid>
                  </Paper>
                )}
                
                {activeStep === 2 && (
                  <Paper
                    sx={{
                      backgroundColor: alpha('#1f2747', 0.5),
                      borderRadius: '10px',
                      p: 3,
                      border: `1px solid ${alpha('#ffffff', 0.1)}`
                    }}
                  >
                    <Stack spacing={3} alignItems="center">
                      <Box sx={{ textAlign: 'center', width: '100%' }}>
                        <Typography 
                          variant="h5" 
                          sx={{ 
                            color: '#ffffff', 
                            mb: 1,
                            fontWeight: 600
                          }}
                        >
                          {isTraining ? '模型训练中...' : '准备开始训练'}
                        </Typography>
                        <Typography sx={{ color: alpha('#ffffff', 0.7), mb: 3 }}>
                          {isTraining 
                            ? '正在处理您的音频数据并训练模型，请耐心等待' 
                            : '点击下方按钮开始训练您的专属语音模型'
                          }
                        </Typography>
                      </Box>
                      
                      {isTraining && (
                        <Box 
                          sx={{ 
                            display: 'flex', 
                            alignItems: 'center',
                            justifyContent: 'center',
                            flexDirection: 'column',
                            gap: 2,
                            p: 4,
                            width: '100%',
                            bgcolor: alpha('#1a1f36', 0.5),
                            borderRadius: 2,
                            border: `1px solid ${alpha('#835df6', 0.3)}`,
                          }}
                        >
                          <CircularProgress 
                            size={48}
                            sx={{ 
                              color: '#835df6'
                            }}
                          />
                          <Box sx={{ textAlign: 'center' }}>
                            <Typography sx={{ color: '#ffffff', fontWeight: 500, mb: 1 }}>
                              正在训练模型
                            </Typography>
                            <Typography sx={{ color: alpha('#ffffff', 0.7), fontSize: '0.875rem' }}>
                              请耐心等待，训练完成后会自动进入下一步
                            </Typography>
                          </Box>
                        </Box>
                      )}
                    </Stack>
                  </Paper>
                )}
                
                {activeStep === 3 && (
                  <Paper
                    sx={{
                      backgroundColor: alpha('#1f2747', 0.5),
                      borderRadius: '10px',
                      p: 3,
                      border: `1px solid ${alpha('#ffffff', 0.1)}`
                    }}
                  >
                    <Stack spacing={4} alignItems="center">
                      <Box
                        sx={{
                          width: 80,
                          height: 80,
                          borderRadius: '50%',
                          bgcolor: alpha('#835df6', 0.1),
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          border: `2px solid ${alpha('#835df6', 0.3)}`,
                          boxShadow: `0 0 20px ${alpha('#835df6', 0.2)}`
                        }}
                      >
                        <CheckCircle 
                          sx={{ 
                            fontSize: 40,
                            color: '#835df6',
                            filter: 'drop-shadow(0 0 8px rgba(131, 93, 246, 0.4))'
                          }} 
                        />
                      </Box>
                      
                      <Stack spacing={2} alignItems="center">
                        <Typography 
                          variant="h5" 
                          sx={{ 
                            color: '#ffffff', 
                            fontWeight: 600,
                            textAlign: 'center'
                          }}
                        >
                          恭喜! 您的专属语音模型已创建成功
                        </Typography>
                        
                        {trainingDuration && (
                          <Typography 
                            sx={{ 
                              color: alpha('#ffffff', 0.7),
                              textAlign: 'center',
                              fontSize: '1rem',
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1
                            }}
                          >
                            训练花费时长：
                            <Box component="span" sx={{ color: '#835df6', fontWeight: 500 }}>
                              {trainingDuration}
                            </Box>
                          </Typography>
                        )}
                      </Stack>
                      
                      <Box 
                        sx={{ 
                          p: 3, 
                          bgcolor: alpha('#1a1f36', 0.7),
                          borderRadius: 2,
                          border: `1px solid ${alpha('#835df6', 0.3)}`,
                          width: '100%',
                          maxWidth: 400,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          gap: 2
                        }}
                      >
                        <Stack direction="row" alignItems="center" spacing={2}>
                          <Box
                            sx={{
                              width: 40,
                              height: 40,
                              borderRadius: '12px',
                              bgcolor: alpha('#835df6', 0.1),
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              border: `1px solid ${alpha('#835df6', 0.2)}`
                            }}
                          >
                            <MusicNote sx={{ color: '#835df6' }} />
                          </Box>
                          <Typography sx={{ color: '#ffffff', fontWeight: 500 }}>
                            {modelName || "我的专属声音"}
                          </Typography>
                        </Stack>
                        
                        <Button
                          variant="outlined"
                          size="small"
                          startIcon={<PlayArrow />}
                          sx={{
                            borderColor: alpha('#835df6', 0.5),
                            color: '#835df6',
                            '&:hover': {
                              borderColor: '#835df6',
                              bgcolor: alpha('#835df6', 0.1),
                            }
                          }}
                        >
                          试听
                        </Button>
                      </Box>
                      
                      <Alert 
                        severity="success" 
                        sx={{
                          bgcolor: alpha('#18cd94', 0.1),
                          color: '#ffffff',
                          border: `1px solid ${alpha('#18cd94', 0.3)}`,
                          '& .MuiAlert-icon': {
                            color: '#18cd94'
                          },
                          width: '100%'
                        }}
                      >
                        模型已自动添加到您的模型列表中，您可以在语音合成或直播时选择使用
                      </Alert>
                    </Stack>
                  </Paper>
                )}
              </Box>
              
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
                <Button
                  disabled={activeStep === 0}
                  onClick={handleBack}
                  sx={{
                    color: alpha('#ffffff', 0.7),
                    '&:hover': {
                      color: '#ffffff',
                      bgcolor: alpha('#ffffff', 0.1),
                    },
                    '&.Mui-disabled': {
                      color: alpha('#ffffff', 0.3),
                    }
                  }}
                >
                  上一步
                </Button>
                
                <Box>
                  {activeStep === steps.length - 1 ? (
                    <Button 
                      variant="contained"
                      onClick={handleReset}
                      sx={{
                        ml: 1,
                        bgcolor: '#835df6',
                        '&:hover': {
                          bgcolor: alpha('#835df6', 0.8),
                        }
                      }}
                    >
                      训练新模型
                    </Button>
                  ) : (
                    <Button
                      variant="contained"
                      onClick={activeStep === 2 ? startTraining : handleNext}
                      disabled={(activeStep === 0 && !audioUploaded) || 
                               (activeStep === 1 && !modelName) ||
                               (activeStep === 2 && isTraining)}
                      sx={{
                        ml: 1,
                        minWidth: 120,
                        bgcolor: '#835df6',
                        '&:hover': {
                          bgcolor: alpha('#835df6', 0.8),
                        },
                        '&.Mui-disabled': {
                          bgcolor: alpha('#835df6', 0.3),
                          color: alpha('#ffffff', 0.5),
                        }
                      }}
                    >
                      {activeStep === 2 ? (
                        isTraining ? (
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <CircularProgress size={16} sx={{ color: alpha('#ffffff', 0.7) }} />
                            训练中
                          </Box>
                        ) : '开始训练'
                      ) : '下一步'}
                    </Button>
                  )}
                </Box>
              </Box>
            </CardContent>
          </Card>
          {errorMessage && (
            <Alert 
              severity="error" 
              sx={{
                mb: 2,
                bgcolor: alpha('#ff5a7d', 0.1),
                color: '#ffffff',
                border: `1px solid ${alpha('#ff5a7d', 0.3)}`,
                '& .MuiAlert-icon': {
                  color: '#ff5a7d'
                }
              }}
            >
              {errorMessage}
            </Alert>
          )}
        </Stack>
        
        <Snackbar
          open={snackbarOpen}
          autoHideDuration={6000}
          onClose={() => setSnackbarOpen(false)}
          message={snackbarMessage}
          anchorOrigin={{
            vertical: 'top',
            horizontal: 'center',
          }}
        />

        <audio
          ref={audioRef}
          src={audioUrl}
          style={{ display: 'none' }}
        />
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Box sx={{ p: 3 }}>
          <Box sx={{ mb: 3, display: 'flex', gap: 2, alignItems: 'center' }}>
            <TextField
              fullWidth
              label="搜索模型 (名称或ID)"
              variant="outlined"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search sx={{ color: '#8695bb' }} />
                  </InputAdornment>
                ),
                sx: {
                  borderRadius: '8px',
                  bgcolor: alpha('#151c32', 0.6),
                  '& .MuiOutlinedInput-notchedOutline': { borderColor: alpha('#ffffff', 0.1) },
                  '&:hover .MuiOutlinedInput-notchedOutline': { borderColor: alpha('#2ec0ff', 0.5) },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': { borderColor: alpha('#2ec0ff', 0.8) },
                  '& input': { color: '#ffffff' },
                  '& label': { color: '#8695bb' },
                },
              }}
              InputLabelProps={{
                sx: { color: '#8695bb' },
              }}
            />
            <Button
              variant="outlined"
              onClick={(event) => setFilterMenuAnchorEl(event.currentTarget)}
              endIcon={<FilterList />}
              sx={{
                flexShrink: 0,
                borderRadius: '8px',
                borderColor: alpha('#ffffff', 0.1),
                color: '#ffffff',
                bgcolor: alpha('#151c32', 0.6),
                '&:hover': { borderColor: alpha('#2ec0ff', 0.5) },
              }}
            >
              {activeTagFilters.length > 0 ? `筛选 (${activeTagFilters.length})` : '标签筛选'}
            </Button>
            <Menu
              anchorEl={filterMenuAnchorEl}
              open={Boolean(filterMenuAnchorEl)}
              onClose={() => setFilterMenuAnchorEl(null)}
              PaperProps={{
                sx: {
                  bgcolor: alpha('#151c32', 0.95),
                  color: '#ffffff',
                  borderRadius: '8px',
                  boxShadow: '0 8px 16px rgba(0,0,0,0.3)',
                  border: `1px solid ${alpha('#ffffff', 0.1)}`,
                  minWidth: '200px',
                  maxHeight: '300px',
                  overflowY: 'auto'
                }
              }}
            >
              <Box sx={{ px: 2, py: 1.5, borderBottom: `1px solid ${alpha('#ffffff', 0.1)}` }}>
                <Typography sx={{ fontSize: '0.9rem', fontWeight: 600 }}>
                  选择标签进行筛选
                </Typography>
              </Box>
              {tags.map(tag => (
                <MenuItem 
                  key={tag.id} 
                  onClick={() => handleTagFilterToggle(tag.id)}
                  sx={{
                    py: 1.2,
                    px: 2,
                    borderLeft: activeTagFilters.includes(tag.id) ? `3px solid ${tag.color}` : `3px solid transparent`,
                    '&:hover': {
                      bgcolor: alpha('#ffffff', 0.1),
                    }
                  }}
                >
                  <ListItemIcon sx={{ minWidth: '32px' }}>
                    {activeTagFilters.includes(tag.id) ? (
                      <CheckCircle sx={{ color: tag.color }} />
                    ) : (
                      <Circle sx={{ color: alpha(tag.color, 0.5) }} />
                    )}
                  </ListItemIcon>
                  <ListItemText 
                    primary={tag.name}
                    sx={{
                      '& .MuiTypography-root': {
                        color: activeTagFilters.includes(tag.id) ? tag.color : '#ffffff',
                        fontWeight: activeTagFilters.includes(tag.id) ? 600 : 400,
                      }
                    }}
                  />
                </MenuItem>
              ))}
               <Divider sx={{ my: 1, borderColor: alpha('#ffffff', 0.1) }} />
              <MenuItem 
                  onClick={() => setActiveTagFilters([])}
                  disabled={activeTagFilters.length === 0}
                   sx={{
                  py: 1.2,
                  color: '#ff4d4f',
                  '&:hover': {
                    bgcolor: alpha('#ff4d4f', 0.1),
                  },
                   }}
                >
                 <ListItemIcon sx={{ minWidth: '32px' }}>
                   <Close sx={{ color: activeTagFilters.length === 0 ? alpha('#ff4d4f', 0.5) : '#ff4d4f' }} />
                 </ListItemIcon>
                 <ListItemText primary="清除所有筛选" />
               </MenuItem>
            </Menu>
          </Box>

          <Grid container spacing={3}>
            {filteredModels.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage).map((model) => {
              const modelTagsList = tags.filter(tag => modelTags[model.id]?.includes(tag.id));
              
              return (
                <Grid item xs={12} sm={6} md={4} key={model.id}>
                  <Box sx={{ height: '100%' }}>
                    <ModelCard
                      model={model}
                      isSelected={false}
                      isPlaying={previewAudio?.modelId === model.id && previewAudio?.isPlaying === true}
                      onSelect={() => { /* 可选：添加其他点击行为，如显示模型详情 */ } }
                      onPlayPreview={() => handlePlayModelPreview(model.id, model.name)}
                      tags={modelTagsList}
                      allTags={tags}
                      onTagClick={handleTagFilterToggle}
                      addTagToModel={addTagToModel}
                      removeTagFromModel={removeTagFromModel}
                      previewLoading={previewLoading}
                      previewAudio={previewAudio}
                      onDeleteModel={handleDeleteModel}
                      onRenameModel={handleRenameModel}
                    />
                  </Box>
                </Grid>
              );
            })}
          </Grid>

          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center' }}>
            <Pagination
              count={Math.ceil(filteredModels.length / itemsPerPage)}
              page={currentPage}
              onChange={(event, value) => setCurrentPage(value)}
              color="primary"
              sx={{
                '& .MuiPaginationItem-root': {
                  color: '#ffffff',
                  '&.Mui-selected': {
                    bgcolor: alpha('#2ec0ff', 0.8),
                    color: '#151c32',
                    '&:hover': { bgcolor: alpha('#2ec0ff', 0.9) }
                  }
                }
              }}
            />
          </Box>
        </Box>
      </TabPanel>
    </Box>
  );
};

export default TrainingWorkspace; 