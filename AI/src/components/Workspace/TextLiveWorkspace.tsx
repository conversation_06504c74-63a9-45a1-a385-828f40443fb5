import React, { useState, useRef, useEffect, useMemo } from 'react';
import {
  Box,
  Card,
  CardContent,
  <PERSON>po<PERSON>,
  Stack,
  TextField,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Divider,
  Tabs,
  Tab,
  MenuItem,
  Select,
  InputLabel,
  FormControl,
  Collapse,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Tooltip,
  Snackbar,
  Alert,
  Switch,
  FormControlLabel,
  Menu,
  LinearProgress,
  CircularProgress,
  Slider,
  InputAdornment,
  Chip,
  Grid,
  Popover
} from '@mui/material';
import { Settings, Save, Upload, Add, Edit, Delete, Close, ArrowUpward, ArrowDownward, Check, Cancel, ImportExport, PlaylistAdd, ExpandMore, ExpandLess, Mic, PlayArrow, VolumeUp, Download, Send, KeyboardArrowDown, GraphicEq, VolumeOff } from '@mui/icons-material';
import { alpha } from '@mui/material/styles';
import LiveStatusPanel from './LiveStatusPanel';
import { useIndexTTS } from '../../hooks/useIndexTTS';
import { useTTS } from '../../hooks/useTTS';
import { useWorkspaceState } from '../../hooks/useWorkspaceState';
import { TagStorage, Tag } from '../../utils/tagStorage';
import { ModelSelectDialog } from '../../components/ModelSelectDialog';
import { useAIModel } from '../../contexts/AIModelContext';
import { getRandomString, replacePlaceholders, getVariablesForTemplate } from '../../utils/templateUtils';
import Stop from '@mui/icons-material/Stop';
import { splitTextToSentences } from '../../hooks/useIndexTTS';
import { useLiveReply } from '../../contexts/LiveReplyContext';
import { useLiveConnection } from '../../contexts/LiveConnectionContext';

// 统一主题和样式对象，复制自 LiveStreamWorkspace.tsx
const theme = {
  primary: '#4f7df9',
  secondary: '#7a6cf7',
  accent: '#2ec0ff',
  gradient: {
    primary: 'linear-gradient(135deg, #4f7df9 0%, #7a6cf7 100%)',
    secondary: 'linear-gradient(135deg, #7a6cf7 0%, #9b6cf7 100%)',
    accent: 'linear-gradient(135deg, #2ec0ff 0%, #4f7df9 100%)'
  },
  background: {
    main: '#1a1f36',
    paper: '#232842',
    light: '#2a3153',
    dark: '#151b30',
    highlight: '#323c6b'
  },
  text: {
    primary: '#ffffff',
    secondary: '#c9d1f0',
    muted: '#8a94b8'
  },
  success: '#4caf50',
  warning: '#ff9800',
  error: '#f44336',
  border: '#373e59'
};
const advancedCardStyle = {
  backgroundColor: alpha(theme.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  borderRadius: '12px',
  border: `1px solid ${alpha(theme.border, 0.2)}`,
  boxShadow: `0 8px 32px ${alpha(theme.background.dark, 0.5)}`,
  transition: 'all 0.3s ease',
  '&:hover': {
    boxShadow: `0 12px 40px ${alpha(theme.background.dark, 0.6)}`,
    transform: 'translateY(-2px)',
  }
};
const advancedCardContent = {
  p: 3,
  '&:last-child': {
    pb: 3
  }
};
const advancedButtonStyle = {
  background: theme.gradient.primary,
  color: theme.text.primary,
  fontWeight: 600,
  borderRadius: '10px',
  padding: '10px 24px',
  boxShadow: `0 4px 15px ${alpha(theme.primary, 0.3)}`,
  transition: 'all 0.3s ease',
  '&:hover': {
    background: theme.gradient.secondary,
    boxShadow: `0 6px 20px ${alpha(theme.secondary, 0.4)}`,
    transform: 'translateY(-2px)',
  },
  '&:active': {
    transform: 'translateY(0)',
  },
  '&.Mui-disabled': {
    background: alpha(theme.background.dark, 0.5),
    color: alpha(theme.text.primary, 0.3),
  }
};

// 统一描边按钮样式
const outlineBtnStyle = {
  background: 'none',
  color: theme.primary,
  border: `1px solid ${theme.primary}`,
  borderRadius: '8px',
  fontWeight: 600,
  boxShadow: 'none',
  transition: 'all 0.15s',
  '&:hover': {
    background: 'none',
    color: theme.primary,
    border: `1.5px solid ${theme.primary}`,
  },
  '&:active, &:focus': {
    background: 'none',
    color: theme.primary,
    border: `1px solid ${theme.primary}`,
    boxShadow: 'none',
  },
};
const outlineDangerBtnStyle = {
  background: 'none',
  color: theme.error,
  border: `1px solid ${theme.error}`,
  borderRadius: '8px',
  fontWeight: 600,
  boxShadow: 'none',
  transition: 'all 0.15s',
  '&:hover': {
    background: 'none',
    color: theme.error,
    border: `1.5px solid ${theme.error}`,
  },
  '&:active, &:focus': {
    background: 'none',
    color: theme.error,
    border: `1px solid ${theme.error}`,
    boxShadow: 'none',
  },
};

// 分类、文本组、话术的数据结构
function generateId() { return Date.now().toString() + Math.random().toString(36).slice(2); }

const defaultCategories = [
  { id: 'all', name: '所有产品', active: false },
];

interface Script {
  id: string;
  content: string;
}

interface TextGroup {
  id: string;
  name: string;
  scripts: Script[];
  categoryId: string;
}

interface Category {
  id: string;
  name: string;
  active: boolean;
}

// 系统提示词默认值
const DEFAULT_PROMPT = '帮我把以下话术，生成10段意思一样字不一样的话术。要求口语化，不要输出违禁词，只要输出话术内容，不要其他备注，也不用输出序号，一句话术一行。';

// 优化按钮样式
const dialogCancelBtnStyle = {
  background: 'none',
  color: theme.primary,
  border: `1px solid ${theme.primary}`,
  boxShadow: 'none',
  borderRadius: '8px',
  fontWeight: 600,
  transition: 'all 0.15s',
  '&:hover': {
    background: 'rgba(79,125,249,0.08)',
    color: theme.primary,
    border: `1px solid ${theme.primary}`,
  },
  '&:active, &:focus': {
    background: 'none',
    color: theme.primary,
    border: `1px solid ${theme.primary}`,
    boxShadow: 'none',
  },
};
const dialogOkBtnStyle = {
  background: theme.primary,
  color: '#fff',
  borderRadius: '8px',
  fontWeight: 600,
  boxShadow: 'none',
  transition: 'all 0.15s',
  '&:hover': {
    background: '#6a8cff',
    color: '#fff',
    boxShadow: 'none',
  },
  '&:active, &:focus': {
    background: theme.primary,
    color: '#fff',
    boxShadow: 'none',
  },
};

// IconButton高对比度样式
const iconBtnCheckStyle = {
  color: theme.primary,
  '&:hover': {
    background: 'rgba(79,125,249,0.10)',
    color: theme.primary,
  },
};
const iconBtnEditStyle = {
  color: theme.accent,
  '&:hover': {
    background: 'rgba(46,192,255,0.12)',
    color: theme.accent,
  },
};
const iconBtnDeleteStyle = {
  color: theme.error,
  '&:hover': {
    background: 'rgba(244,67,54,0.12)',
    color: theme.error,
  },
};
const iconBtnArrowStyle = (disabled: boolean) => ({
  color: disabled ? 'rgba(79,125,249,0.35)' : 'rgba(79,125,249,0.85)',
  background: 'none',
  borderRadius: '50%',
  padding: 0.5,
  transition: 'all 0.18s',
  '&:hover': {
    background: disabled ? 'none' : 'rgba(79,125,249,0.13)',
    color: theme.primary,
    filter: disabled ? 'none' : 'drop-shadow(0 1px 4px rgba(79,125,249,0.18))',
  },
  '&.Mui-disabled': {
    color: 'rgba(79,125,249,0.35)',
    background: 'none',
    filter: 'none',
  },
});

// 格式化时间（秒 -> mm:ss）
function formatTime(time: number) {
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
}

// 分段处理 [助:内容]，返回 { text, isAssistant }
function splitByAssistant(text: string) {
  const regex = /\[助:([^\]]+)\]/g;
  let result: { text: string, isAssistant: boolean }[] = [];
  let lastIndex = 0;
  let match;
  while ((match = regex.exec(text)) !== null) {
    if (match.index > lastIndex) {
      result.push({ text: text.slice(lastIndex, match.index), isAssistant: false });
    }
    result.push({ text: match[1], isAssistant: true });
    lastIndex = regex.lastIndex;
  }
  if (lastIndex < text.length) {
    result.push({ text: text.slice(lastIndex), isAssistant: false });
  }
  return result.filter(seg => seg.text.trim());
}

interface TextLiveWorkspaceProps {
  setInsertLiveReply?: (fn: (text: string, type?: 'welcome' | 'time' | 'count' | 'broadcast' | 'interactionReply') => void) => void;
}

export const TextLiveWorkspace: React.FC<TextLiveWorkspaceProps> = ({ setInsertLiveReply }) => {
  // 预缓存条数设置，和实时直播一致，必须在组件体最顶部
  const [bufferSize] = useWorkspaceState<number>('live-bufferSize', 3);
  const {
    audioState,
    setAudioState,
    isLoading: isAudioLoading,
    generateAudio,
    pauseAudio,
    resumeAudio,
    setProgress,
    reset,
    setVolume,
    audioRef,
    appendAudioToQueue,
    generateSegmentsAudio,
    appendSegmentsToQueue
  } = useIndexTTS();
  const { generateText, isProcessing, error } = useAIModel();
  const { models } = useTTS();
  // 新增：模型选择状态（与LiveStreamWorkspace保持一致，便于全局复用LiveStatusPanel）
  const [currentModel, setCurrentModel] = useWorkspaceState<any>('live-currentModel', null);
  const [assistantModel, setAssistantModel] = useWorkspaceState<any>('live-assistantModel', null);
  // 状态管理
  const [textInput, setTextInput] = useState('');
  const [promptDialogVisible, setPromptDialogVisible] = useState(false);
  const [promptInput, setPromptInput] = useState(DEFAULT_PROMPT);
  const [generatedTextDialogVisible, setGeneratedTextDialogVisible] = useState(false);
  const [generatedTextResult, setGeneratedTextResult] = useState('');
  const [importDialogVisible, setImportDialogVisible] = useState(false);
  const [selectedImportCategory, setSelectedImportCategory] = useState<string>('');
  const [selectedImportTextGroup, setSelectedImportTextGroup] = useState<string>('');
  const [categories, setCategories] = useState<Category[]>(() => {
    const saved = localStorage.getItem('textlive_categories');
    return saved ? JSON.parse(saved) : [{ id: 'all', name: '所有分类', active: false }];
  });
  const [activeCategory, setActiveCategory] = useState('all');
  const [addCategoryDialogVisible, setAddCategoryDialogVisible] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [renameCategoryDialogVisible, setRenameCategoryDialogVisible] = useState(false);
  const [renamingCategory, setRenamingCategory] = useState<Category | null>(null);
  const [newCategoryNameInput, setNewCategoryNameInput] = useState('');
  const [textGroups, setTextGroups] = useState<TextGroup[]>(() => {
    const saved = localStorage.getItem('textlive_textGroups');
    return saved ? JSON.parse(saved) : [];
  });
  const [activeTextGroupCollapse, setActiveTextGroupCollapse] = useState<string[]>([]);
  const [addScriptDialogVisible, setAddScriptDialogVisible] = useState(false);
  const [newScriptsInput, setNewScriptsInput] = useState('');
  const [activeTextGroup, setActiveTextGroup] = useState<TextGroup | null>(null);
  const [editScriptDialogVisible, setEditScriptDialogVisible] = useState(false);
  const [editingScript, setEditingScript] = useState<Script | null>(null);
  const [snackbar, setSnackbar] = useState<{open: boolean, message: string, severity: 'success'|'error'|'warning'}>({open: false, message: '', severity: 'success'});
  const fileInputRef = useRef<HTMLInputElement>(null);
  // 用户接待开关状态
  const [userGreeting, setUserGreeting] = useWorkspaceState<{
    welcome: boolean;
    reply: boolean;
    time: boolean;
    count: boolean;
  }>('live-userGreeting', {
    welcome: false,
    reply: true,
    time: false,
    count: false,
  });
  const [textGeneratorCollapsed, setTextGeneratorCollapsed] = useState(true);
  // 新增更多操作菜单的状态
  const [moreMenuAnchor, setMoreMenuAnchor] = useState<null | HTMLElement>(null);
  const handleOpenMoreMenu = (e: React.MouseEvent<HTMLElement>) => setMoreMenuAnchor(e.currentTarget);
  const handleCloseMoreMenu = () => setMoreMenuAnchor(null);

  // 音色轮换相关状态
  const [modelRotation, setModelRotation] = useWorkspaceState<{
    enabled: boolean;
    mode: 'timer' | 'random';
    interval: number;
    minInterval: number;
    maxInterval: number;
    tagIds: string[];
    lastSwitchTime: number;
    nextSwitchTime: number;
  }>('live-modelRotation', {
    enabled: false,
    mode: 'timer',
    interval: 60,
    minInterval: 30,
    maxInterval: 120,
    tagIds: [],
    lastSwitchTime: 0,
    nextSwitchTime: 0,
  });
  const [rotationState, setRotationState] = useState({
    timerId: null as NodeJS.Timeout | null,
    remainingSeconds: 0,
    availableModels: [] as any[],
  });
  const [rotationSettingsDialogOpen, setRotationSettingsDialogOpen] = useState(false);
  const [tags, setTags] = useState<Tag[]>([]);
  const [modelTags, setModelTags] = useState<{[modelId: string]: string[]}>({});
  const [tempIntervalValue, setTempIntervalValue] = useState((modelRotation.interval ?? 60).toString());
  const [tempMinIntervalValue, setTempMinIntervalValue] = useState((modelRotation.minInterval ?? 30).toString());
  const [tempMaxIntervalValue, setTempMaxIntervalValue] = useState((modelRotation.maxInterval ?? 120).toString());

  // 新增 modelSelectDialogOpen 和 assistantModelSelectDialogOpen 的 useState 定义
  const [modelSelectDialogOpen, setModelSelectDialogOpen] = useState(false);
  const [assistantModelSelectDialogOpen, setAssistantModelSelectDialogOpen] = useState(false);

  // 系统提示词可配置
  const [systemPrompt, setSystemPrompt] = useState(() => {
    // 可选：持久化，localStorage.getItem('textlive_systemPrompt') || DEFAULT_PROMPT
    return DEFAULT_PROMPT;
  });

  // 新增：批量模式开关
  const [batchMode, setBatchMode] = useState(false);
  // 新增：批量生成结果
  const [batchResults, setBatchResults] = useState<{ sentence: string, variations: string[] }[]>([]);
  // 新增：批量生成进度
  const [batchProgress, setBatchProgress] = useState({ current: 0, total: 0, loading: false });
  // 新增：批量生成loading状态
  const [batchLoading, setBatchLoading] = useState(false);
  // 新增：批量导入分类名输入
  const [batchCategoryName, setBatchCategoryName] = useState('');

  // 新增：普通模式下分条可编辑
  const [singleResults, setSingleResults] = useState<string[]>([]);

  // 新增：批量新建文本组数量输入
  const [batchCreateDialogVisible, setBatchCreateDialogVisible] = useState(false);
  const [batchCreateCount, setBatchCreateCount] = useState(1);

  // 计算属性
  const filteredTextGroups = activeCategory === 'all'
    ? textGroups
    : textGroups.filter(g => g.categoryId === activeCategory);
  const importableTextGroups = selectedImportCategory
    ? textGroups.filter(g => g.categoryId === selectedImportCategory)
    : [];

  // 分句函数
  function splitSentences(text: string) {
    const raw = text.split(/[，。！？；]/);
    const sentences = raw.map(s => s.trim()).filter(Boolean);
    const merged: string[] = [];
    let buffer = "";
    for (const s of sentences) {
      if (buffer) buffer += s;
      else buffer = s;
      if (buffer.length < 15) continue;
      merged.push(buffer);
      buffer = "";
    }
    if (buffer) merged.push(buffer);
    return merged;
  }

  // 事件处理
  const handleGenerate = async () => {
    if (!textInput.trim() || isProcessing) return;
    if (batchMode) {
      const sentences = splitSentences(textInput);
      setBatchResults([]);
      setBatchLoading(true);
      setGeneratedTextResult('');
      const results: { sentence: string, variations: string[] }[] = [];
      await Promise.all(sentences.map(async (sentence, idx) => {
        const res = await generateText(sentence, systemPrompt, false);
        results[idx] = { sentence, variations: res ? res.split(/\r?\n/).filter(Boolean) : [] };
      }));
      setBatchResults(results);
      setBatchLoading(false);
    setGeneratedTextDialogVisible(true);
    } else {
      setGeneratedTextResult('');
      const result = await generateText(textInput, systemPrompt, false);
      if (result) {
        setGeneratedTextResult(result);
        setSingleResults(result.split(/\r?\n/).filter(Boolean));
      } else {
        setGeneratedTextResult('');
        setSingleResults([]);
      }
      setGeneratedTextDialogVisible(true);
    }
  };
  const handleOpenPromptDialog = () => {
    setPromptInput(systemPrompt);
    setPromptDialogVisible(true);
  };
  const handleSavePrompt = () => {
    setSystemPrompt(promptInput);
    setPromptDialogVisible(false);
    setSnackbar({open: true, message: '提示词已保存', severity: 'success'});
    // 可选：localStorage.setItem('textlive_systemPrompt', promptInput);
  };
  const handleImportToGroup = () => {
    if (!selectedImportCategory || !selectedImportTextGroup) {
      setSnackbar({open: true, message: '请选择要导入的分类和文本组！', severity: 'warning'});
      return;
    }
    const target = textGroups.find(g => g.id === selectedImportTextGroup);
    if (target) {
      const newScripts = singleResults.filter(line => line.trim() !== '').map(content => ({ id: generateId(), content: content.trim() }));
      target.scripts.push(...newScripts);
      setTextGroups([...textGroups]);
      setSnackbar({open: true, message: '文本已成功导入到文本组！', severity: 'success'});
      setGeneratedTextDialogVisible(false);
      setImportDialogVisible(false);
      setGeneratedTextResult('');
      setSingleResults([]);
      if (!activeTextGroupCollapse.includes(target.id)) setActiveTextGroupCollapse([...activeTextGroupCollapse, target.id]);
    }
  };
  const handleAddCategory = () => {
    if (!newCategoryName.trim()) {
      setSnackbar({open: true, message: '分类名称不能为空！', severity: 'warning'});
      return;
    }
    const newCat: Category = {
      id: generateId(),
      name: newCategoryName.trim(),
      active: false,
    };
    setCategories([...categories, newCat]);
    setActiveCategory(newCat.id);
    setAddCategoryDialogVisible(false);
    setNewCategoryName('');
    setSnackbar({open: true, message: '分类添加成功！', severity: 'success'});
  };
  const handleRenameCategory = () => {
    if (!renamingCategory || !newCategoryNameInput.trim()) {
      setSnackbar({open: true, message: '分类名称不能为空！', severity: 'warning'});
      return;
    }
    setCategories(categories.map(c => c.id === renamingCategory.id ? { ...c, name: newCategoryNameInput.trim() } : c));
    setRenameCategoryDialogVisible(false);
    setRenamingCategory(null);
    setNewCategoryNameInput('');
    setSnackbar({open: true, message: '分类重命名成功！', severity: 'success'});
  };
  const handleDeleteCategory = (cat: Category) => {
    setCategories(categories.filter(c => c.id !== cat.id));
    setTextGroups(textGroups.filter(g => g.categoryId !== cat.id));
    if (activeCategory === cat.id) setActiveCategory('all');
    setSnackbar({open: true, message: `分类 "${cat.name}" 及其下的所有文本组已删除！`, severity: 'success'});
  };
  const handleAddTextGroup = () => {
    if (activeCategory === 'all') return;
    const newGroup: TextGroup = {
      id: generateId(),
      name: `组${filteredTextGroups.length + 1}`,
      scripts: [],
      categoryId: activeCategory,
    };
    setTextGroups([...textGroups, newGroup]);
  };
  const handleMoveTextGroup = (group: TextGroup, direction: 'up' | 'down') => {
    // 只对当前分类的文本组排序
    const currentCatId = group.categoryId;
    const groupsInCat = textGroups.filter(g => g.categoryId === currentCatId);
    const idx = groupsInCat.findIndex(g => g.id === group.id);
    if (idx === -1) return;
    let newGroupsInCat = [...groupsInCat];
    if (direction === 'up' && idx > 0) {
      [newGroupsInCat[idx - 1], newGroupsInCat[idx]] = [newGroupsInCat[idx], newGroupsInCat[idx - 1]];
    } else if (direction === 'down' && idx < newGroupsInCat.length - 1) {
      [newGroupsInCat[idx], newGroupsInCat[idx + 1]] = [newGroupsInCat[idx + 1], newGroupsInCat[idx]];
    }
    // 重新组装 textGroups，保持其他分类顺序不变
    const newTextGroups = textGroups.filter(g => g.categoryId !== currentCatId);
    // 插入新顺序的当前分类文本组，保持原有在 textGroups 中的插入位置
    let insertIdx = textGroups.findIndex(g => g.categoryId === currentCatId);
    if (insertIdx === -1) insertIdx = newTextGroups.length;
    newTextGroups.splice(insertIdx, 0, ...newGroupsInCat);
    setTextGroups(newTextGroups);
  };
  const handleDeleteTextGroup = (group: TextGroup) => {
    setTextGroups(textGroups.filter(g => g.id !== group.id));
    setActiveTextGroupCollapse(activeTextGroupCollapse.filter(id => id !== group.id));
    setSnackbar({open: true, message: '文本组已删除', severity: 'success'});
  };
  const handleAddScripts = (group: TextGroup) => {
    setActiveTextGroup(group);
    setNewScriptsInput('');
    setAddScriptDialogVisible(true);
  };
  const handleSaveScripts = () => {
    if (!activeTextGroup || !newScriptsInput.trim()) {
      setSnackbar({open: true, message: '话术内容不能为空！', severity: 'warning'});
      return;
    }
    const newScripts = newScriptsInput.split(/\r?\n/).filter(line => line.trim() !== '').map(content => ({ id: generateId(), content: content.trim() }));
    activeTextGroup.scripts.push(...newScripts);
    setTextGroups([...textGroups]);
    setAddScriptDialogVisible(false);
    setNewScriptsInput('');
  };
  const handleEditScript = (group: TextGroup, script: Script) => {
    setActiveTextGroup(group);
    setEditingScript({ ...script });
    setEditScriptDialogVisible(true);
  };
  const handleSaveEditedScript = () => {
    if (!editingScript || !editingScript.content.trim() || !activeTextGroup) {
      setSnackbar({open: true, message: '话术内容不能为空！', severity: 'warning'});
      return;
    }
    const idx = activeTextGroup.scripts.findIndex(s => s.id === editingScript.id);
    if (idx !== -1) {
      activeTextGroup.scripts[idx] = editingScript;
      setTextGroups([...textGroups]);
    }
    setEditScriptDialogVisible(false);
    setEditingScript(null);
  };
  const handleDeleteScript = (group: TextGroup, script: Script) => {
    group.scripts = group.scripts.filter(s => s.id !== script.id);
    setTextGroups([...textGroups]);
    setSnackbar({open: true, message: '话术删除成功！', severity: 'success'});
  };

  // 分类Tab切换
  const handleCategoryTabChange = (_: any, val: string) => {
    setActiveCategory(val);
    setActiveTextGroupCollapse([]);
  };

  // 折叠展开
  const handleCollapseToggle = (id: string) => {
    setActiveTextGroupCollapse(
      activeTextGroupCollapse.includes(id)
        ? activeTextGroupCollapse.filter(i => i !== id)
        : [...activeTextGroupCollapse, id]
    );
  };

  // 导出功能
  const handleExport = () => {
    let exportContent = '# Exported Text Live Data\n';
    exportContent += '# Format: Simple structured text for categories and text groups.\n';
    exportContent += '# Each category and text group has an ID. Scripts belong to text groups.\n\n';
    exportContent += '---CATEGORIES_START---\n';
    categories.filter(c => c.id !== 'all').forEach(category => {
      exportContent += `ID: ${category.id}\n`;
      exportContent += `Name: ${category.name}\n`;
      exportContent += `Active: ${category.active}\n`;
      exportContent += '---END_CATEGORY---\n';
    });
    exportContent += '---CATEGORIES_END---\n\n';
    exportContent += '---TEXTGROUPS_START---\n';
    textGroups.forEach(group => {
      exportContent += `ID: ${group.id}\n`;
      exportContent += `Category ID: ${group.categoryId}\n`;
      exportContent += `Name: ${group.name}\n`;
      exportContent += '---SCRIPTS_START---\n';
      group.scripts.forEach(script => {
        exportContent += `Script ID: ${script.id}\n`;
        exportContent += `Content: ${script.content.replace(/\n/g, '\\n')}\n`;
        exportContent += '---END_SCRIPT---\n';
      });
      exportContent += '---SCRIPTS_END---\n';
      exportContent += '---END_TEXTGROUP---\n';
    });
    exportContent += '---TEXTGROUPS_END---\n';
    const blob = new Blob([exportContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'text_live_data.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    setSnackbar({open: true, message: '数据已导出为 text_live_data.txt', severity: 'success'});
  };

  // 导入功能
  const handleImportClick = () => {
    if (fileInputRef.current) fileInputRef.current.value = '';
    fileInputRef.current?.click();
  };
  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        processTxtContent(content);
        setSnackbar({open: true, message: 'TXT文件导入成功！', severity: 'success'});
      } catch (error) {
        setSnackbar({open: true, message: 'TXT文件解析失败或格式不正确。', severity: 'error'});
      }
    };
    reader.readAsText(file);
  };
  // 解析TXT内容
  const processTxtContent = (content: string) => {
    const lines = content.split(/\r?\n/);
    let currentSection = '';
    let newCategories: Category[] = [];
    let newTextGroups: TextGroup[] = [];
    let currentCategory: Category | null = null;
    let currentTextGroup: TextGroup | null = null;
    let currentScripts: Script[] = [];
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (line === '---CATEGORIES_START---') { currentSection = 'categories'; continue; }
      else if (line === '---CATEGORIES_END---') { currentSection = ''; continue; }
      else if (line === '---TEXTGROUPS_START---') { currentSection = 'textgroups'; continue; }
      else if (line === '---TEXTGROUPS_END---') { currentSection = ''; continue; }
      else if (line === '---SCRIPTS_START---') { currentScripts = []; currentSection = 'scripts'; continue; }
      else if (line === '---SCRIPTS_END---') { if (currentTextGroup) { currentTextGroup.scripts = currentScripts; newTextGroups.push(currentTextGroup); } currentSection = 'textgroups'; continue; }
      else if (line === '---END_CATEGORY---') { if (currentCategory) { newCategories.push(currentCategory); currentCategory = null; } currentSection = 'categories'; continue; }
      else if (line === '---END_TEXTGROUP---') { if (currentTextGroup && currentTextGroup.scripts.length === 0) { newTextGroups.push(currentTextGroup); } currentTextGroup = null; currentSection = 'textgroups'; continue; }
      if (line.startsWith('#') || line === '') continue;
      if (currentSection === 'categories') {
        if (line.startsWith('ID:')) currentCategory = { id: line.substring(3).trim(), name: '', active: false };
        else if (line.startsWith('Name:') && currentCategory) currentCategory.name = line.substring(5).trim();
        else if (line.startsWith('Active:') && currentCategory) currentCategory.active = (line.substring(7).trim() === 'true');
      } else if (currentSection === 'textgroups') {
        if (line.startsWith('ID:')) currentTextGroup = { id: line.substring(3).trim(), categoryId: '', name: '', scripts: [] };
        else if (line.startsWith('Category ID:') && currentTextGroup) currentTextGroup.categoryId = line.substring(12).trim();
        else if (line.startsWith('Name:') && currentTextGroup) currentTextGroup.name = line.substring(5).trim();
      } else if (currentSection === 'scripts') {
        if (line.startsWith('Content:')) {
          const content = line.substring(8).trim().replace(/\\n/g, '\n');
          currentScripts.push({ id: Math.random().toString(36).slice(2), content });
        }
      }
    }
    if (currentCategory) newCategories.push(currentCategory);
    setCategories([{ id: 'all', name: '所有分类', active: false }, ...newCategories]);
    setTextGroups(newTextGroups);
    setActiveCategory('all');
  };

  // 批量导入逻辑
  const handleBatchImport = () => {
    const name = batchCategoryName.trim();
    if (!name) {
      setSnackbar({open: true, message: '请输入分类名！', severity: 'warning'});
      return;
    }
    let cat = categories.find(c => c.name === name);
    let catId = cat ? cat.id : generateId();
    let newCategories = [...categories];
    if (!cat) {
      newCategories.push({ id: catId, name, active: false });
      setCategories(newCategories);
    }
    let newGroups = [...textGroups];
    batchResults.forEach(({ sentence, variations }) => {
      const groupName = sentence.slice(0, 10) + (sentence.length > 10 ? '...' : '');
      const newGroup: TextGroup = {
        id: generateId(),
        name: groupName,
        scripts: variations.map(content => ({ id: generateId(), content })),
        categoryId: catId,
      };
      newGroups.push(newGroup);
    });
    setTextGroups(newGroups);
    setGeneratedTextDialogVisible(false);
    setImportDialogVisible(false);
    setBatchResults([]);
    setGeneratedTextResult('');
    setBatchCategoryName('');
    setSnackbar({open: true, message: '批量导入成功，已自动创建分类并导入！', severity: 'success'});
  };

  // 分类变动时保存到localStorage
  useEffect(() => {
    localStorage.setItem('textlive_categories', JSON.stringify(categories));
  }, [categories]);
  // 文本组变动时保存到localStorage
  useEffect(() => {
    localStorage.setItem('textlive_textGroups', JSON.stringify(textGroups));
  }, [textGroups]);

  // 批量新建文本组逻辑
  const handleBatchCreateTextGroups = () => {
    const count = Math.max(1, Math.floor(Number(batchCreateCount)));
    if (activeCategory === 'all') return;
    // 获取当前分类下所有文本组名中的编号
    const currentGroups = textGroups.filter(g => g.categoryId === activeCategory);
    const usedNumbers = currentGroups
      .map(g => {
        const match = g.name.match(/^组(\d+)$/);
        return match ? parseInt(match[1], 10) : null;
      })
      .filter((n): n is number => n !== null)
      .sort((a, b) => a - b);
    let start = usedNumbers.length > 0 ? Math.max(...usedNumbers) + 1 : 1;
    // 生成不重复的组名
    const newGroups: TextGroup[] = [];
    for (let i = 0; i < count; i++) {
      // 跳过已用编号
      while (usedNumbers.includes(start)) start++;
      newGroups.push({
        id: generateId(),
        name: `组${start}`,
        scripts: [],
        categoryId: activeCategory,
      });
      usedNumbers.push(start);
      start++;
    }
    setTextGroups([...textGroups, ...newGroups]);
    setBatchCreateDialogVisible(false);
    setBatchCreateCount(1);
  };

  // 插话相关 useState 必须在 getSharedPresets 之前声明
  const [interruptText, setInterruptText] = useState('');
  const [presetMenuAnchorEl, setPresetMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [newPresetText, setNewPresetText] = useState('');
  // 共享预设localStorage key
  const INTERRUPT_PRESETS_KEY = 'interruptPresets';
  const getSharedPresets = () => {
    const saved = localStorage.getItem(INTERRUPT_PRESETS_KEY);
    return saved ? JSON.parse(saved) : [
      '感谢[昵称]送出的礼物！',
      '欢迎新来的[昵称]！',
      '点赞关注主播不迷路！',
      '今天有惊喜福利哦！',
      '还有[整点]时间，大家抓紧！'
    ];
  };
  const [interruptPresets, setInterruptPresets] = useState(getSharedPresets());
  const updatePresets = (newPresets: string[]) => {
    setInterruptPresets(newPresets);
    localStorage.setItem(INTERRUPT_PRESETS_KEY, JSON.stringify(newPresets));
    window.dispatchEvent(new Event('interruptPresetsChanged'));
  };

  const handleInterrupt = async (text: string, showSuccess: boolean = true) => {
    if (!currentModel) {
      setSnackbar({open: true, message: '请先选择一个音色', severity: 'warning'});
      return;
    }
    if (!text.trim()) return;
    if (showSuccess) {
      setShowInterruptSuccess(true);
      setTimeout(() => setShowInterruptSuccess(false), 3000);
    }
    // 变量替换、随机词、符号过滤
    const variables = getVariablesForTemplate('custom', { nickname: '家人们' });
    let processedText = replacePlaceholders(text, variables);
    processedText = getRandomString(processedText);
    processedText = filterTTSUnsupportedChars(processedText);
    const modelId = currentModel.id.endsWith('.pt') ? currentModel.id : `${currentModel.id}.pt`;
    const assistantModelId = assistantModel?.id?.endsWith('.pt') ? assistantModel.id : assistantModel?.id ? `${assistantModel.id}.pt` : undefined;
    const segments = splitByAssistant(processedText);
    for (const seg of segments) {
      await generateAudio({
        model: seg.isAssistant ? assistantModelId : modelId,
        speed: getCurrentSpeed(),
        text: seg.text,
        isInterrupt: true,
        bufferSize: bufferSize
      } as any);
    }
    if (showSuccess) setInterruptText('');
  };
  const handleOpenPresetMenu = (e: React.MouseEvent<HTMLElement>) => {
    setPresetMenuAnchorEl(e.currentTarget);
  };
  const handleClosePresetMenu = () => {
    setPresetMenuAnchorEl(null);
    setNewPresetText('');
  };
  const handleSelectPreset = (text: string) => {
    setInterruptText(text);
    handleClosePresetMenu();
  };
  const handleAddPreset = () => {
    if (newPresetText.trim() && !interruptPresets.includes(newPresetText.trim())) {
      updatePresets([...interruptPresets, newPresetText.trim()]);
      setNewPresetText('');
    }
  };
  const handleDeletePreset = (textToDelete: string) => {
    updatePresets(interruptPresets.filter((text: string) => text !== textToDelete));
  };

  useEffect(() => {
    const syncPresets = () => {
      setInterruptPresets(getSharedPresets());
    };
    window.addEventListener('interruptPresetsChanged', syncPresets);
    window.addEventListener('storage', syncPresets);
    return () => {
      window.removeEventListener('interruptPresetsChanged', syncPresets);
      window.removeEventListener('storage', syncPresets);
    };
  }, []);

  // 新增：文本直播主循环状态
  const [isTextLiveStreaming, setIsTextLiveStreaming] = useWorkspaceState<boolean>('textlive-isTextLiveStreaming', false);
  const [isTextLiveLoading, setIsTextLiveLoading] = useState(false);
  const isAutoGeneratingRef = useRef(false);

  // 语速相关状态
  const [speed, setSpeed] = useWorkspaceState<number>('live-speed', 1);
  const [speedRandomEnabled, setSpeedRandomEnabled] = useWorkspaceState<boolean>('live-speedRandomEnabled', false);
  const [speedRandomMin, setSpeedRandomMin] = useWorkspaceState<number>('live-speedRandomMin', 1);
  const [speedRandomMax, setSpeedRandomMax] = useWorkspaceState<number>('live-speedRandomMax', 1.3);
  const [tempSpeedRandomMin, setTempSpeedRandomMin] = useState(speedRandomMin.toFixed(1));
  const [tempSpeedRandomMax, setTempSpeedRandomMax] = useState(speedRandomMax.toFixed(1));
  const [speedRandomAnchorEl, setSpeedRandomAnchorEl] = useState<HTMLButtonElement | null>(null);
  useEffect(() => {
    setTempSpeedRandomMin(speedRandomMin.toFixed(1));
    setTempSpeedRandomMax(speedRandomMax.toFixed(1));
  }, [speedRandomMin, speedRandomMax]);

  // 完整 getCurrentSpeed
  const getCurrentSpeed = () => {
    if (speedRandomEnabled) {
      const min = Math.min(speedRandomMin, speedRandomMax);
      const max = Math.max(speedRandomMin, speedRandomMax);
      return parseFloat((min + Math.random() * (max - min)).toFixed(2));
    }
    return speed;
  };

  // 插话变量、随机词、助播、符号过滤
  const filterTTSUnsupportedChars = (text: string) => {
    let filteredText = text.replace(/……/g, '');
    filteredText = filteredText.replace(/[—~#@\*\-]/g, '');
    return filteredText;
  };

  // 拼接文本直播内容：激活分类下所有文本组，每组随机抽一句（顺序严格按UI）
  function getTextLiveScriptContent() {
    // 找到被"使用"的分类
    const activeCat = categories.find(c => c.active && c.id !== 'all');
    if (!activeCat) return { content: '', detail: '' };
    // 只用该分类下的文本组
    const selectedGroups = textGroups.filter(g => g.categoryId === activeCat.id && g.scripts.length > 0);
    if (selectedGroups.length === 0) return { content: '', detail: '' };
    const selected = selectedGroups.map(group => {
      const idx = Math.floor(Math.random() * group.scripts.length);
      return { groupName: group.name, content: group.scripts[idx].content };
    });
    const detail = selected.map(g => `${g.groupName}: ${g.content}`).join(' | ');
    const content = selected.map(g => g.content).join(' ');
    return { content, detail };
  }

  // 记录是否是第一次拼接
  const isFirstDrawRef = useRef(true);
  // 记录抽取次数
  const drawCountRef = useRef(0);

  // 启动/停止文本直播
  const handleTextLiveToggle = async () => {
    if (!currentModel) {
      setSnackbar({open: true, message: '请先选择一个音色', severity: 'warning'});
      return;
    }
    if (!isTextLiveStreaming) {
      setIsTextLiveLoading(true);
      const { content: scriptContent } = getTextLiveScriptContent();
      drawCountRef.current += 1;
      // 日志：抽取文本前的原始拼接内容
      console.log(`[文本直播] 第${drawCountRef.current}次抽取，原始拼接内容:`, scriptContent);
      if (!scriptContent.trim()) {
        setSnackbar({open: true, message: '请先配置激活分类和文本组', severity: 'warning'});
        setIsTextLiveLoading(false);
        return;
      }
      // 新增：广播本次开播内容并写入localStorage（分模式存储）
      window.dispatchEvent(new CustomEvent('live-script-content', { detail: { type: 'textlive', content: scriptContent } }));
      localStorage.setItem('textliveScriptContent', scriptContent);
      localStorage.setItem('liveMode', 'textlive');
      // 变量替换、随机词、助播
      const variables = getVariablesForTemplate('custom', { nickname: '家人们' });
      let processedText = replacePlaceholders(scriptContent, variables);
      processedText = getRandomString(processedText);
      processedText = filterTTSUnsupportedChars(processedText);
      // 日志：变量/随机词/助播处理后的最终文本
      console.log(`[文本直播] 第${drawCountRef.current}次抽取，处理后文本:`, processedText);
      // 日志：分段结果（用hook里的splitTextToSentences）
      const segs = splitTextToSentences(processedText);
      console.log(`[文本直播] 第${drawCountRef.current}次抽取，分段结果:`, segs);
      const modelId = currentModel.id;
      const assistantModelId = assistantModel?.id;
      await generateAudio({
        model: modelId,
        assistantModel: assistantModelId,
        speed: getCurrentSpeed(),
        text: processedText,
        bufferSize: bufferSize
      } as any);
      setIsTextLiveStreaming(true);
      setIsTextLiveLoading(false);
    } else {
      pauseAudio();
      reset();
      setIsTextLiveStreaming(false);
      isFirstDrawRef.current = true;
      drawCountRef.current = 0;
    }
  };

  // 自动补充队列
  useEffect(() => {
    const remain = audioState.totalSentences - audioState.currentSentenceIndex;
    if (!isTextLiveStreaming) return;
    if (
      remain < 10 &&
      isAudioLoading === false &&
      !isAutoGeneratingRef.current
    ) {
      isAutoGeneratingRef.current = true;
      (async () => {
        const scriptResult = getTextLiveScriptContent();
        drawCountRef.current += 1;
        // 日志：抽取文本前的原始拼接内容
        console.log(`[文本直播] 队列补充第${drawCountRef.current}次抽取，原始拼接内容:`, scriptResult.content);
        if (scriptResult.content.trim()) {
          const variables = getVariablesForTemplate('custom', { nickname: '家人们' });
          let processedText = replacePlaceholders(scriptResult.content, variables);
          processedText = getRandomString(processedText);
          processedText = filterTTSUnsupportedChars(processedText);
          // 日志：变量/随机词/助播处理后的最终文本
          console.log(`[文本直播] 队列补充第${drawCountRef.current}次抽取，处理后文本:`, processedText);
          // 日志：分段结果（用hook里的splitTextToSentences）
          const segs = splitTextToSentences(processedText);
          console.log(`[文本直播] 队列补充第${drawCountRef.current}次抽取，分段结果:`, segs);
          const modelId = currentModel.id;
          const assistantModelId = assistantModel?.id;
          await appendAudioToQueue({
            model: modelId,
            assistantModel: assistantModelId,
            speed: getCurrentSpeed(),
            text: processedText,
            bufferSize: bufferSize
          } as any);
        }
        isAutoGeneratingRef.current = false;
      })();
    }
    return undefined;
  }, [isTextLiveStreaming, audioState.currentSentenceIndex, audioState.totalSentences, isAudioLoading, currentModel, assistantModel, getCurrentSpeed, getVariablesForTemplate, replacePlaceholders, getRandomString, filterTTSUnsupportedChars, appendAudioToQueue]);

  // 音色轮换主循环 useEffect
  useEffect(() => {
    if (isTextLiveStreaming && modelRotation.enabled) {
      // 初始化模型池
      const updateAvailableModelsForRotation = () => {
        if (!modelRotation.tagIds || modelRotation.tagIds.length === 0) {
          setRotationState(prev => ({ ...prev, availableModels: [...models] }));
          return;
        }
        const filteredModels = models.filter(model => {
          const modelTagIds = modelTags[model.id] || [];
          return modelRotation.tagIds.every(tagId => modelTagIds.includes(tagId));
        });
        setRotationState(prev => ({ ...prev, availableModels: filteredModels }));
      };
      updateAvailableModelsForRotation();
      if (modelRotation.nextSwitchTime === 0) {
        const now = Date.now();
        setModelRotation(prev => ({ ...prev, nextSwitchTime: now + (modelRotation.interval * 1000), lastSwitchTime: now }));
      }
      const timer = setInterval(() => {
        const now = Date.now();
        if (now >= modelRotation.nextSwitchTime && modelRotation.nextSwitchTime > 0) {
          // 切换模型
          let availableModels = [...rotationState.availableModels];
          if (availableModels.length === 0) updateAvailableModelsForRotation();
          if (availableModels.length === 1) setCurrentModel(availableModels[0]);
          else if (availableModels.length > 1) {
            availableModels = availableModels.filter(m => m.id !== currentModel?.id);
            const idx = Math.floor(Math.random() * availableModels.length);
            setCurrentModel(availableModels[idx]);
          }
          // 安排下次切换
          let nextSwitchTime = now;
          if (modelRotation.mode === 'timer') nextSwitchTime = now + (modelRotation.interval * 1000);
          else {
            const range = modelRotation.maxInterval - modelRotation.minInterval;
            const randomInterval = modelRotation.minInterval + Math.floor(Math.random() * range);
            nextSwitchTime = now + (randomInterval * 1000);
          }
          setModelRotation(prev => ({ ...prev, lastSwitchTime: now, nextSwitchTime }));
        }
        if (modelRotation.nextSwitchTime > 0) {
          const remaining = Math.max(0, Math.floor((modelRotation.nextSwitchTime - now) / 1000));
          setRotationState(prev => ({ ...prev, remainingSeconds: remaining }));
        }
      }, 1000);
      setRotationState(prev => ({ ...prev, timerId: timer }));
      return () => { if (timer) clearInterval(timer); };
    } else {
      if (rotationState.timerId) {
        clearInterval(rotationState.timerId);
        setRotationState(prev => ({ ...prev, timerId: null, remainingSeconds: 0 }));
      }
    }
    // 确保 useEffect 返回 void
    return undefined;
  }, [isTextLiveStreaming, modelRotation.enabled, modelRotation.nextSwitchTime, modelRotation.mode, modelRotation.tagIds, models, modelTags, currentModel]);

  // 插话成功提示状态
  const [showInterruptSuccess, setShowInterruptSuccess] = useState(false);

  // 判断当前分类下是否有可用内容
  const activeCat = categories.find(c => c.active && c.id !== 'all');
  const hasContent = !!activeCat && textGroups.some(g => g.categoryId === activeCat.id && g.scripts.length > 0);

  // 自动补充队列相关变量
  const lastEnqueueTimeRef = useRef(0);
  const MIN_ENQUEUE_INTERVAL = 1500; // 最小间隔1.5秒
  const MAX_QUEUE_LENGTH = 5; // 队列最大长度，前面5个就够了

  // 在 useState/useRef 之后，useEffect 之前，声明 useCallback 版本 insertLiveReply
  const { latestUsername, audienceCount } = useLiveConnection();
  const insertLiveReply = React.useCallback(async (text: string, type?: 'welcome' | 'time' | 'count' | 'broadcast' | 'interactionReply') => {
    if (!isTextLiveStreaming) return;
    if (!currentModel) return;
    if (!text.trim()) return;

    let shouldGenerate = false;
    if (type === 'welcome') shouldGenerate = userGreeting.welcome;
    else if (type === 'count') shouldGenerate = userGreeting.count;
    else if (type === 'time') shouldGenerate = userGreeting.time;
    else if (type === 'interactionReply') shouldGenerate = userGreeting.reply;
    else return;

    if (!shouldGenerate) return;

    // 变量替换逻辑（如有需要）
    // ...原有变量替换代码...

    // 插入到音频队列
    await handleInterrupt(text, false);
  }, [isTextLiveStreaming, currentModel, userGreeting, handleInterrupt]);

  // 自动回复监听 useEffect 直接引用 insertLiveReply
  useEffect(() => {
    if (setInsertLiveReply) setInsertLiveReply(insertLiveReply);
  }, [insertLiveReply, setInsertLiveReply]);

  // 组件内 state
  const [publicScreenConnected, setPublicScreenConnected] = useState(false);

  // 监听公屏连接状态事件
  useEffect(() => {
    const handleLiveConnectionChange = (event: CustomEvent<boolean>) => {
      setPublicScreenConnected(event.detail);
    };
    window.addEventListener('live-connection-change', handleLiveConnectionChange as EventListener);
    return () => {
      window.removeEventListener('live-connection-change', handleLiveConnectionChange as EventListener);
    };
  }, []);

  // 渲染
  return (
    <Box sx={{ width: '100%', minHeight: '100vh', background: `linear-gradient(135deg, ${theme.background.main} 0%, ${theme.background.dark} 100%)`, p: 3 }}>
      <Stack spacing={2.5}>
        {/* 页面头部或合适位置添加公屏连接状态指示器 */}
        {/*
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          mr: 2,
          px: 1.5,
          py: 0.5,
          borderRadius: '4px',
          bgcolor: alpha(publicScreenConnected ? theme.success : theme.error, 0.1),
          border: `1px solid ${alpha(publicScreenConnected ? theme.success : theme.error, 0.2)}`,
        }}>
          <Box sx={{
            width: 8,
            height: 8,
            borderRadius: '50%',
            bgcolor: publicScreenConnected ? theme.success : theme.error,
            boxShadow: `0 0 10px ${alpha(publicScreenConnected ? theme.success : theme.error, 0.5)}`,
          }} />
          <Typography sx={{
            color: publicScreenConnected ? theme.success : theme.error,
            fontSize: '0.85rem',
            fontWeight: 500,
          }}>
            公屏{publicScreenConnected ? '已连接' : '未连接'}
          </Typography>
        </Box>
        */}
        <Typography
          variant="h4"
          sx={{
            fontWeight: 600,
            mb: 3,
            background: theme.gradient.primary,
            backgroundClip: 'text',
            textFillColor: 'transparent',
            display: 'inline-block',
          }}
        >
          文本直播
        </Typography>
        {/* 直播状态面板 */}
        <LiveStatusPanel
          models={models}
          currentModel={currentModel}
          assistantModel={assistantModel}
          setCurrentModel={setCurrentModel}
          onOpenModelSelect={() => setModelSelectDialogOpen(true)}
          onOpenAssistantModelSelect={() => setAssistantModelSelectDialogOpen(true)}
          publicScreenConnected={publicScreenConnected}
        />
        {/* 文本生成卡片 */}
        <Card sx={advancedCardStyle}>
          <CardContent sx={advancedCardContent}>
            <Stack spacing={textGeneratorCollapsed ? 0 : 2.5}>
              {/* 标题栏 */}
              <Stack 
                direction="row" 
                alignItems="center" 
                justifyContent="space-between"
                onClick={() => setTextGeneratorCollapsed(!textGeneratorCollapsed)}
                sx={{ 
                  cursor: 'pointer',
                  '&:hover': {
                    '& .MuiTypography-root': {
                      color: theme.text.primary,
                    },
                    '& .collapse-icon': {
                      color: theme.text.primary,
                    }
                  }
                }}
              >
                <Stack direction="row" alignItems="center" spacing={1.2} sx={{ mb: 1, mt: 0.5 }}>
                  <GraphicEq sx={{ color: theme.accent, fontSize: 24, mr: 0.5 }} />
                  <Typography variant="h6" sx={{ color: theme.text.primary, fontWeight: 700, fontSize: '1.13rem', letterSpacing: '0.02em' }}>
                    文本生成
                  </Typography>
                </Stack>
                <IconButton 
                  className="collapse-icon"
                  size="small"
                  sx={{ 
                    color: theme.text.secondary,
                    transform: textGeneratorCollapsed ? 'rotate(180deg)' : 'rotate(0deg)',
                    transition: 'all 0.3s ease',
                    p: 0.5,
                  }}
                >
                  <ExpandMore />
                </IconButton>
              </Stack>

              {/* 生成区域内容 */}
              <Collapse in={!textGeneratorCollapsed} timeout="auto">
                <Stack spacing={2.5}>
                  <TextField
                    multiline
                    minRows={5}
                    maxRows={8}
                    placeholder="请输入内容"
                    value={textInput}
                    onChange={e => setTextInput(e.target.value)}
                    fullWidth
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: alpha(theme.background.light, 0.4),
                        borderRadius: '8px',
                        color: theme.text.primary,
                        fontSize: '0.95rem',
                        transition: 'all 0.2s ease',
                        '& fieldset': {
                          borderColor: alpha(theme.border, 0.1),
                          transition: 'all 0.2s ease',
                        },
                        '&:hover fieldset': {
                          borderColor: alpha(theme.accent, 0.5),
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: theme.accent,
                          borderWidth: '1px',
                        },
                        '& textarea': {
                          padding: '12px 14px',
                        },
                      },
                    }}
                  />
                  <Stack direction="row" spacing={2} alignItems="center">
                    <Button
                      variant="outlined"
                      startIcon={batchMode && batchLoading ? <CircularProgress size={18} sx={{ color: theme.accent }} /> : <Add />}
                      onClick={handleGenerate}
                      sx={{
                        ...outlineBtnStyle,
                        color: theme.accent,
                        fontWeight: 600,
                        '&.Mui-disabled': {
                          color: theme.accent,
                          opacity: 1,
                          WebkitTextFillColor: theme.accent,
                        },
                      }}
                      disabled={isProcessing || (batchMode && batchLoading)}
                    >
                      {isProcessing || (batchMode && batchLoading) ? '生成中...' : batchMode ? '批量生成' : '生成'}
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<Settings />}
                      onClick={handleOpenPromptDialog}
                      sx={{ ...outlineBtnStyle, background: 'none', color: theme.primary, border: `1px solid ${theme.primary}` }}
                    >
                      设置
                    </Button>
                    <FormControlLabel
                      control={<Switch checked={batchMode} onChange={e => setBatchMode(e.target.checked)} color="primary" />}
                      label={<Typography sx={{ color: theme.text.secondary, fontSize: '0.98rem' }}>批量模式</Typography>}
                      sx={{ ml: 2 }}
                    />
                  </Stack>
                </Stack>
              </Collapse>
            </Stack>
          </CardContent>
        </Card>

        {/* 文本组与分类管理卡片 */}
        <Card sx={advancedCardStyle}>
          <CardContent sx={advancedCardContent}>
            <Box sx={{ display: 'flex', alignItems: 'center', pl: 1, pt: 1.2, pb: 0, minHeight: 36, mb: 1.2 }}>
              <PlaylistAdd sx={{ color: theme.accent, fontSize: 20, mr: 1.2, verticalAlign: 'middle' }} />
              <Typography variant="h6" sx={{ color: theme.text.primary, fontWeight: 600, fontSize: '1.08rem', letterSpacing: '0.01em', lineHeight: 1.2 }}>
                话术管理
              </Typography>
            </Box>
            {/* Tabs + 右上角操作区 */}
            <Box sx={{ display: 'flex', alignItems: 'center', pl: 1, pr: 3, pt: 1, pb: 1 }}>
              <Tabs
                value={activeCategory}
                onChange={(_, v) => setActiveCategory(v)}
                variant="scrollable"
                scrollButtons="auto"
                sx={{
                  minHeight: '44px',
                  '& .MuiTab-root': {
                    minHeight: '44px',
                    height: '44px',
                    color: theme.text.secondary,
                    fontSize: '1.08rem',
                    textTransform: 'none',
                    px: 3,
                    borderRadius: '10px',
                    transition: 'all 0.2s',
                    mr: 1,
                    fontWeight: 600,
                    '&.Mui-selected': {
                      color: theme.accent,
                      bgcolor: alpha(theme.accent, 0.08),
                      boxShadow: `0 2px 8px 0 ${alpha(theme.accent, 0.08)}`,
                    }
                  },
                  '& .MuiTabs-indicator': { display: 'none' }
                }}
              >
                {categories.map(cat => (
                  <Tab key={cat.id} label={cat.name} value={cat.id} />
                ))}
              </Tabs>
              <Box sx={{ flex: 1 }} />
              {/* 右上角操作区 */}
              {activeCategory === 'all' ? (
                <>
                  <Button variant="outlined" color="primary" startIcon={<Add />} sx={{ borderRadius: '8px', fontWeight: 600, px: 2, mr: 1 }} onClick={() => setAddCategoryDialogVisible(true)}>
                    添加分类
                  </Button>
                  <Button variant="outlined" startIcon={<ImportExport />} sx={{ borderRadius: '8px', fontWeight: 600, px: 2, color: theme.primary, borderColor: theme.primary, mr: 1 }} onClick={handleOpenMoreMenu}>
                    更多
                  </Button>
                </>
              ) : (
                <>
                  <Button variant="outlined" startIcon={<PlaylistAdd />} sx={{ bgcolor: theme.accent, color: theme.text.primary, borderRadius: '8px', fontWeight: 600, px: 2, mr: 1 }} onClick={() => setBatchCreateDialogVisible(true)}>
                    新建文本组
                  </Button>
                  <Button variant="outlined" startIcon={<ImportExport />} sx={{ borderRadius: '8px', fontWeight: 600, px: 2, color: theme.primary, borderColor: theme.primary, mr: 1 }} onClick={handleOpenMoreMenu}>
                    更多
                  </Button>
                </>
              )}
              {/* 更多操作菜单 */}
              <Menu anchorEl={moreMenuAnchor} open={Boolean(moreMenuAnchor)} onClose={handleCloseMoreMenu}>
                <MenuItem onClick={() => { handleExport(); handleCloseMoreMenu(); }}>
                  <ImportExport fontSize="small" sx={{ mr: 1 }} /> 导出
                </MenuItem>
                <MenuItem onClick={() => { handleImportClick(); handleCloseMoreMenu(); }}>
                  <Upload fontSize="small" sx={{ mr: 1 }} /> 导入
                </MenuItem>
                {/* 可扩展更多批量操作 */}
              </Menu>
            </Box>
            <Divider sx={{ my: 0, borderColor: alpha(theme.border, 0.12) }} />
            {/* 分类管理（所有分类Tab下） */}
            {activeCategory === 'all' && (
              <Box sx={{ px: 3, py: 2 }}>
                <Typography sx={{ color: theme.text.secondary, mb: 1, fontWeight: 600, fontSize: '1.05em' }}>已创建的分类：</Typography>
                <List dense>
                  {categories.filter(c => c.id !== 'all').map(cat => (
                    <ListItem key={cat.id} sx={{ bgcolor: alpha(theme.background.light, 0.7), borderRadius: 2, mb: 1, display: 'flex', alignItems: 'center', boxShadow: '0 1px 4px 0 rgba(46,192,255,0.04)' }}>
                      <Box sx={{ flex: 1, display: 'flex', alignItems: 'center', cursor: 'pointer' }} onClick={() => setActiveCategory(cat.id)}>
                        <Typography sx={{ color: theme.text.primary, fontWeight: 500 }}>{cat.name}</Typography>
                        <Typography sx={{ color: theme.accent, ml: 2, fontWeight: 600, fontSize: '0.95em' }}>（{textGroups.filter(g => g.categoryId === cat.id).length} 个文本组）</Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {cat.active ? (
                          <>
                            <IconButton size="small" onClick={e => { e.stopPropagation(); setCategories(categories.map(c => c.id === cat.id ? { ...c, active: false } : c)); }} sx={{ color: theme.accent }}>
                              <Close />
                            </IconButton>
                            <Typography sx={{ color: theme.accent, fontWeight: 600, ml: 0.5, cursor: 'pointer' }} onClick={e => { e.stopPropagation(); setCategories(categories.map(c => c.id === cat.id ? { ...c, active: false } : c)); }}>
                              取消使用
                            </Typography>
                          </>
                        ) : (
                          <>
                            <IconButton size="small" onClick={e => { e.stopPropagation(); setCategories(categories.map(c => c.id === cat.id ? { ...c, active: true } : { ...c, active: false })); }} sx={{ color: theme.accent }}>
                              <Check />
                            </IconButton>
                            <Typography sx={{ color: theme.accent, fontWeight: 600, ml: 0.5, cursor: 'pointer' }} onClick={e => { e.stopPropagation(); setCategories(categories.map(c => c.id === cat.id ? { ...c, active: true } : { ...c, active: false })); }}>
                              使用
                            </Typography>
                          </>
                        )}
                        <IconButton size="small" onClick={e => { e.stopPropagation(); setRenamingCategory(cat); setNewCategoryNameInput(cat.name); setRenameCategoryDialogVisible(true); }} sx={iconBtnEditStyle}>
                          <Edit />
                        </IconButton>
                        <IconButton size="small" onClick={e => { e.stopPropagation(); handleDeleteCategory(cat); }} sx={iconBtnDeleteStyle}>
                          <Delete />
                        </IconButton>
                      </Box>
                    </ListItem>
                  ))}
                </List>
                {categories.filter(c => c.id !== 'all').length === 0 && (
                  <Typography sx={{ color: theme.text.muted, textAlign: 'center', mt: 2 }}>暂无已创建的分类，请点击"添加分类"按钮创建。</Typography>
                )}
              </Box>
            )}
            {/* 分类下的文本组管理 */}
            {activeCategory !== 'all' && (
              <Box sx={{ px: 3, py: 2 }}>
                {filteredTextGroups.length > 0 ? (
                  <List>
                    {filteredTextGroups.map((group, idx) => (
                      <Box key={group.id} sx={{ mb: 2, borderRadius: 2, boxShadow: '0 2px 8px rgba(0,0,0,0.08)', bgcolor: alpha(theme.background.light, 0.7) }}>
                        <ListItem button onClick={() => handleCollapseToggle(group.id)}>
                          <Typography sx={{ color: theme.accent, fontWeight: 700, mr: 1 }}>{idx + 1}.</Typography>
                          <Typography sx={{ flex: 1, color: theme.text.primary, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>{group.scripts[0]?.content?.slice(0, 50) || '（空）'}</Typography>
                          <IconButton size="small" onClick={e => { e.stopPropagation(); handleMoveTextGroup(group, 'up'); }} disabled={idx === 0} sx={iconBtnArrowStyle(idx === 0)}>
                            <ArrowUpward sx={{ fontSize: 22 }} />
                          </IconButton>
                          <IconButton size="small" onClick={e => { e.stopPropagation(); handleMoveTextGroup(group, 'down'); }} disabled={idx === filteredTextGroups.length - 1} sx={iconBtnArrowStyle(idx === filteredTextGroups.length - 1)}>
                            <ArrowDownward sx={{ fontSize: 22 }} />
                          </IconButton>
                          <IconButton size="small" onClick={e => { e.stopPropagation(); handleDeleteTextGroup(group); }} sx={iconBtnDeleteStyle}>
                            <Delete />
                          </IconButton>
                          <IconButton size="small">{activeTextGroupCollapse.includes(group.id) ? <ExpandLess /> : <ExpandMore />}</IconButton>
                        </ListItem>
                        <Collapse in={activeTextGroupCollapse.includes(group.id)} timeout="auto" unmountOnExit>
                          <Box sx={{ p: 2, bgcolor: alpha(theme.background.paper, 0.9), borderRadius: 2 }}>
                            {group.scripts.map((script, sidx) => (
                              <Stack direction="row" alignItems="center" key={script.id} sx={{ mb: 1 }}>
                                <Typography sx={{ color: theme.text.primary, mr: 2 }}>{sidx + 1}.</Typography>
                                <Typography sx={{ flex: 1, color: theme.text.secondary }}>{script.content}</Typography>
                                <IconButton size="small" onClick={() => handleEditScript(group, script)} sx={iconBtnEditStyle}><Edit /></IconButton>
                                <IconButton size="small" onClick={() => handleDeleteScript(group, script)} sx={iconBtnDeleteStyle}><Delete /></IconButton>
                              </Stack>
                            ))}
                            <Stack direction="row" justifyContent="flex-end" sx={{ mt: 1 }}>
                              <Button
                                startIcon={<Add />}
                                variant="outlined"
                                onClick={() => handleAddScripts(group)}
                                sx={{
                                  borderColor: alpha(theme.border, 0.5),
                                  color: theme.text.secondary,
                                  '&:hover': {
                                    borderColor: theme.accent,
                                    color: theme.accent,
                                    backgroundColor: 'rgba(46, 192, 255, 0.04)',
                                  },
                                }}
                              >
                                添加话术
                              </Button>
                            </Stack>
                          </Box>
                        </Collapse>
                      </Box>
                    ))}
                  </List>
                ) : (
                  <Typography sx={{ color: theme.text.muted, textAlign: 'center', mt: 4 }}>暂无文本组，请点击"新建文本组"按钮创建。</Typography>
                )}
              </Box>
            )}
          </CardContent>
        </Card>

        {/* 用户接待单选组 + 启动AI主播按钮，移入AI响应区域Card顶部 */}
        <Card sx={advancedCardStyle}>
          <CardContent sx={advancedCardContent}>
            {/* 用户接待+启动AI主播 */}
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                <Typography sx={{ color: theme.text.secondary, fontSize: '0.85rem', minWidth: 70 }}>用户接待</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, p: 0.5 }}>
                  {[
                    { key: 'welcome', label: '欢迎', tooltip: '是否播报新观众加入直播间的欢迎语' },
                    { key: 'reply', label: '回复', tooltip: '是否播报对用户互动消息的自动回复' },
                    { key: 'time', label: '报时', tooltip: '是否播报当前时间信息' },
                    { key: 'count', label: '人数', tooltip: '是否播报观众人数变化信息' }
                  ].map(({ key, label, tooltip }) => (
                    <Tooltip key={key} title={tooltip} placement="top" arrow>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={userGreeting[key as keyof typeof userGreeting]}
                            onChange={e => setUserGreeting(prev => ({ ...prev, [key]: e.target.checked }))}
                            size="small"
                            sx={{
                              '& .MuiSwitch-switchBase': {
                                padding: 0.5,
                              },
                              '& .MuiSwitch-thumb': {
                                width: 14,
                                height: 14,
                              },
                              '& .MuiSwitch-track': {
                                borderRadius: 8,
                              },
                              '& .MuiSwitch-switchBase.Mui-checked': {
                                color: theme.accent,
                                '& + .MuiSwitch-track': {
                                  backgroundColor: `${theme.accent}99`,
                                },
                              },
                            }}
                          />
                        }
                        label={<Typography sx={{ color: theme.text.secondary, fontSize: '0.95em', fontWeight: 500 }}>{label}</Typography>}
                        sx={{ margin: 0 }}
                      />
                    </Tooltip>
                  ))}
                </Box>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                <Button
                  variant="contained"
                  sx={{
                    minWidth: 160,
                    height: 36,
                    fontSize: '0.95rem',
                    fontWeight: 500,
                    borderRadius: '10px',
                    boxShadow: '0 0 20px rgba(79,125,249,0.15)',
                    color: theme.text.primary,
                    background: isTextLiveStreaming ? theme.error : theme.gradient.primary,
                    '&:hover': {
                      background: isTextLiveStreaming ? alpha(theme.error, 0.9) : theme.gradient.secondary,
                    },
                    '&.Mui-disabled': {
                      background: theme.gradient.primary,
                      opacity: 0.7,
                    },
                  }}
                  onClick={handleTextLiveToggle}
                  disabled={
                    isTextLiveLoading ||
                    (!isTextLiveStreaming && (!currentModel || !hasContent))
                  }
                >
                  {isTextLiveLoading
                    ? '启动中...'
                    : isTextLiveStreaming
                      ? '停止文本直播'
                      : '启动文本直播'}
                </Button>
              </Box>
            </Box>
            <Divider sx={{ mb: 2, borderColor: theme.border }} />
            {/* AI响应区域 */}
            <Stack direction="row" alignItems="center" spacing={1.5} mb={3}>
              <Mic sx={{
                color: theme.accent,
                filter: 'drop-shadow(0 0 3px rgba(46,192,255,0.5))',
              }} />
              <Typography variant="h6" sx={{
                color: theme.text.primary,
                fontWeight: 600,
                letterSpacing: '0.05em',
                position: 'relative',
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  bottom: -5,
                  left: 0,
                  width: '40px',
                  height: '2px',
                  background: `linear-gradient(90deg, ${theme.accent}, transparent)`,
                  borderRadius: '2px',
                },
              }}>
                AI响应区域
              </Typography>
            </Stack>
            <Box sx={{ 
              width: '100%',
              backgroundColor: alpha(theme.background.light, 0.3),
              borderRadius: '10px',
              p: 1.5,
            }}>
              {/* 音频播放器 */}
              <Box sx={{ 
                display: 'flex',
                alignItems: 'center',
                gap: 2,
                p: 2,
                bgcolor: alpha(theme.background.light, 0.4),
                borderRadius: '12px',
                backdropFilter: 'blur(10px)',
                border: `1px solid ${alpha(theme.text.primary, 0.1)}`,
                boxShadow: `0 8px 32px ${alpha(theme.background.dark, 0.2)}`,
              }}>
                <IconButton 
                  size="medium"
                  onClick={() => {
                    if (audioState.isPlaying) {
                      pauseAudio();
                    } else if (audioState.audioUrl) {
                      resumeAudio();
                    }
                  }}
                  disabled={!audioState.audioUrl}
                  sx={{
                    width: 45,
                    height: 45,
                    bgcolor: theme.primary,
                    color: theme.text.primary,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      bgcolor: alpha(theme.primary, 0.8),
                      transform: 'scale(1.05)',
                    },
                    '&.Mui-disabled': {
                      bgcolor: alpha(theme.text.primary, 0.1),
                      color: alpha(theme.text.primary, 0.3),
                    },
                    boxShadow: `0 4px 12px ${alpha(theme.primary, 0.3)}`,
                  }}
                >
                  {audioState.isPlaying ? 
                    <Stop sx={{ fontSize: 24 }} /> : 
                    <PlayArrow sx={{ fontSize: 24 }} />
                  }
                </IconButton>
                <Box sx={{ 
                  flex: 1,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2,
                }}>
                  <Typography sx={{ 
                    color: theme.text.primary,
                    fontSize: '0.85rem',
                    fontWeight: 500,
                    minWidth: 45,
                    textAlign: 'center',
                  }}>
                    {formatTime(audioState.currentTime)}
                  </Typography>
                  <Box 
                    sx={{
                      flex: 1,
                      height: 6,
                      bgcolor: alpha(theme.text.primary, 0.1),
                      borderRadius: '12px',
                      position: 'relative',
                      overflow: 'hidden',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        height: 8,
                        '& .progress-bar': {
                          background: theme.gradient.secondary,
                          boxShadow: `0 0 20px ${alpha(theme.primary, 0.5)}`,
                        },
                        '& .progress-thumb': {
                          opacity: 1,
                          transform: 'scale(1)',
                        }
                      }
                    }}
                    onClick={(e) => {
                      const rect = e.currentTarget.getBoundingClientRect();
                      const ratio = (e.clientX - rect.left) / rect.width;
                      setProgress(ratio * audioState.duration);
                    }}
                  >
                    <Box 
                      className="progress-bar"
                      sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: `${(audioState.currentTime / audioState.duration) * 100}%`,
                        height: '100%',
                        background: theme.gradient.primary,
                        borderRadius: '12px',
                        transition: 'all 0.2s ease',
                      }}
                    />
                    <Box 
                      className="progress-thumb"
                      sx={{
                        position: 'absolute',
                        top: '50%',
                        left: `${(audioState.currentTime / audioState.duration) * 100}%`,
                        width: 12,
                        height: 12,
                        bgcolor: theme.text.primary,
                        borderRadius: '50%',
                        transform: 'translate(-50%, -50%) scale(0)',
                        opacity: 0,
                        transition: 'all 0.2s ease',
                        boxShadow: `0 0 10px ${alpha(theme.primary, 0.5)}`,
                      }}
                    />
                  </Box>
                  <Typography sx={{ 
                    color: theme.text.primary,
                    fontSize: '0.85rem',
                    fontWeight: 500,
                    minWidth: 45,
                    textAlign: 'center',
                  }}>
                    {formatTime(audioState.duration)}
                  </Typography>
                </Box>
                <Box sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: 1,
                  position: 'relative',
                  '&:hover .volume-slider': {
                    width: 60,
                    opacity: 1,
                  }
                }}>
                  <IconButton 
                    size="small"
                    onClick={() => {
                      setVolume(audioState.volume === 0 ? 100 : 0);
                    }}
                    sx={{
                      color: theme.text.primary,
                      '&:hover': {
                        color: theme.primary,
                        bgcolor: alpha(theme.primary, 0.1),
                      }
                    }}
                  >
                    {audioState.volume === 0 ? (
                      <VolumeOff sx={{ fontSize: 20 }} />
                    ) : (
                      <VolumeUp sx={{ fontSize: 20 }} />
                    )}
                  </IconButton>
                  <Box 
                    className="volume-slider"
                    sx={{ 
                      width: 0,
                      opacity: 0,
                      transition: 'all 0.3s ease',
                      overflow: 'hidden',
                    }}
                  >
                    <Slider
                      size="small"
                      value={audioState.volume * 100}
                      onChange={(_, value) => {
                        setVolume(value as number);
                      }}
                      sx={{ 
                        color: theme.primary,
                        '& .MuiSlider-rail': {
                          backgroundColor: alpha(theme.text.primary, 0.1),
                        },
                        '& .MuiSlider-track': {
                          background: theme.gradient.primary,
                        },
                        '& .MuiSlider-thumb': {
                          width: 10,
                          height: 10,
                          bgcolor: theme.text.primary,
                          boxShadow: `0 0 10px ${alpha(theme.primary, 0.3)}`,
                          '&:hover, &.Mui-focusVisible': {
                            boxShadow: `0 0 0 8px ${alpha(theme.primary, 0.2)}`,
                          },
                        },
                      }} 
                    />
                  </Box>
                </Box>
                <IconButton 
                  size="small"
                  onClick={() => {
                    if (audioState.audioUrl) {
                      const a = document.createElement('a');
                      a.href = audioState.audioUrl;
                      a.download = `audio_${Date.now()}.wav`;
                      document.body.appendChild(a);
                      a.click();
                      document.body.removeChild(a);
                    }
                  }}
                  disabled={!audioState.audioUrl}
                  sx={{ 
                    color: theme.text.primary,
                    '&:hover': {
                      color: theme.primary,
                      bgcolor: alpha(theme.primary, 0.1),
                    },
                    '&.Mui-disabled': {
                      color: alpha(theme.text.primary, 0.3),
                    }
                  }}
                >
                  <Download sx={{ fontSize: 20 }} />
                </IconButton>
              </Box>
              {/* 当前播放文本 */}
              {audioState.isPlaying && (
                <Box sx={{
                  p: 2,
                  color: theme.text.primary,
                  fontSize: '0.85rem',
                  lineHeight: 1.5,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 1,
                }}>
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                  }}>
                    <Box sx={{
                      width: 4,
                      height: 4,
                      borderRadius: '50%',
                      bgcolor: theme.accent,
                      animation: 'blink 1s infinite',
                      '@keyframes blink': {
                        '0%': { opacity: 1 },
                        '50%': { opacity: 0.3 },
                        '100%': { opacity: 1 },
                      }
                    }} />
                    <Typography sx={{
                      flex: 1,
                      color: theme.text.secondary,
                      fontSize: '0.75rem',
                    }}>
                      正在播放
                    </Typography>
                  </Box>
                  <Typography sx={{
                    color: theme.text.primary,
                    fontSize: '0.85rem',
                    lineHeight: 1.6,
                    pl: 1,
                    borderLeft: `2px solid ${theme.accent}`,
                  }}>
                    {audioState.currentText || '等待播放...'}
                  </Typography>
                </Box>
              )}
            </Box>
            {/* 插话输入框+按钮 */}
            <Box sx={{ mt: 2.5, display: 'flex', gap: 1.5 }}>
              <TextField
                size="small"
                fullWidth
                placeholder="输入插话内容，支持变量"
                value={interruptText}
                onChange={e => setInterruptText(e.target.value)}
                onKeyDown={e => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    if (interruptText.trim()) handleInterrupt(interruptText, true);
                  }
                }}
                InputProps={{
                  sx: {
                    bgcolor: alpha(theme.background.light, 0.3),
                    color: theme.text.primary,
                    borderRadius: '8px',
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: alpha(theme.text.primary, 0.1),
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: theme.primary,
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: theme.primary,
                    },
                    '& input::placeholder': {
                      color: theme.text.muted,
                      opacity: 1,
                    },
                    '& input': {
                      color: theme.text.primary,
                    },
                  },
                }}
              />
              <Button
                variant="contained"
                size="small"
                endIcon={<Send sx={{ fontSize: 16 }} />}
                onClick={async () => await handleInterrupt(interruptText, true)}
                disabled={!interruptText.trim()}
                sx={{
                  minWidth: 80,
                  height: 32,
                  background: theme.gradient.primary,
                  color: theme.text.primary,
                  fontSize: '0.85rem',
                  '&:hover': {
                    background: theme.gradient.secondary,
                  },
                  '&.Mui-disabled': {
                    opacity: 0.6,
                    background: alpha(theme.text.primary, 0.1),
                  }
                }}
              >
                发送
              </Button>
              <Button
                variant="outlined"
                size="small"
                onClick={handleOpenPresetMenu}
                sx={{
                  minWidth: 80,
                  height: 32,
                  borderColor: alpha(theme.text.primary, 0.2),
                  color: theme.text.primary,
                  fontSize: '0.85rem',
                  py: 0.5,
                  '&:hover': {
                    borderColor: theme.primary,
                    backgroundColor: alpha(theme.primary, 0.1),
                  }
                }}
              >
                预设
              </Button>
              <Menu
                anchorEl={presetMenuAnchorEl}
                open={Boolean(presetMenuAnchorEl)}
                onClose={handleClosePresetMenu}
                PaperProps={{
                  sx: {
                    bgcolor: alpha(theme.background.paper, 0.95),
                    color: theme.text.primary,
                    borderRadius: '8px',
                    boxShadow: `0 8px 20px ${alpha('#000', 0.2)}`,
                    backdropFilter: 'blur(8px)',
                    minWidth: '250px',
                    maxHeight: '350px',
                    overflowY: 'auto'
                  }
                }}
              >
                <Box sx={{ px: 2, py: 1.5, borderBottom: `1px solid ${alpha(theme.text.primary, 0.1)}` }}>
                  <Typography sx={{ fontSize: '0.9rem', fontWeight: 600, mb: 0.5 }}>
                    插话预设文本
                  </Typography>
                  <Typography sx={{ fontSize: '0.8rem', color: alpha(theme.text.secondary, 0.7) }}>
                    点击选择或删除
                  </Typography>
                </Box>
                {interruptPresets.length > 0 ? (
                  interruptPresets.map((text: string, index: number) => (
                    <MenuItem
                      key={index}
                      onClick={() => handleSelectPreset(text)}
                      sx={{
                        py: 1.2,
                        px: 2,
                        '&:hover': {
                          bgcolor: alpha(theme.text.primary, 0.05),
                        },
                      }}
                    >
                      <ListItemText primary={text} sx={{ '& .MuiTypography-root': { fontSize: '0.85rem' } }} />
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeletePreset(text);
                        }}
                        sx={{ color: alpha(theme.text.secondary, 0.7), '&:hover': { color: theme.error } }}
                      >
                        <Close fontSize="small" />
                      </IconButton>
                    </MenuItem>
                  ))
                ) : (
                  <MenuItem disabled>
                    <ListItemText primary="暂无预设" sx={{ '& .MuiTypography-root': { fontSize: '0.85rem', color: theme.text.muted } }} />
                  </MenuItem>
                )}
                <Divider sx={{ my: 1, borderColor: alpha(theme.text.primary, 0.1) }} />
                <Box sx={{ px: 2, pb: 1.5 }}>
                  <TextField
                    size="small"
                    fullWidth
                    placeholder="添加新预设"
                    value={newPresetText}
                    onChange={e => setNewPresetText(e.target.value)}
                    onKeyDown={e => {
                      if (e.key === 'Enter') {
                        handleAddPreset();
                      }
                    }}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            size="small"
                            onClick={handleAddPreset}
                            disabled={!newPresetText.trim()}
                            sx={{ color: theme.primary }}
                          >
                            <Add fontSize="small" />
                          </IconButton>
                        </InputAdornment>
                      ),
                      sx: {
                        '& input': {
                          color: theme.text.primary,
                        },
                      },
                    }}
                  />
                </Box>
              </Menu>
            </Box>
          </CardContent>
        </Card>
      </Stack>

      {/* 提示词设置弹窗 */}
      <Dialog
        open={promptDialogVisible}
        onClose={() => setPromptDialogVisible(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            backgroundColor: theme.background.paper,
            borderRadius: '16px',
            boxShadow: '0 12px 40px rgba(0,0,0,0.22)',
            p: 0
          }
        }}
      >
        <DialogTitle sx={{ color: theme.text.primary, fontWeight: 700, fontSize: '1.15rem', lineHeight: 1.4, borderBottom: `1px solid ${alpha(theme.text.primary, 0.07)}`, px: 4, py: 2.5 }}>
          提示词设置
        </DialogTitle>
        <DialogContent sx={{ px: 4, py: 3, color: theme.text.primary, fontSize: '1rem', lineHeight: 1.7 }}>
          <TextField
            multiline
            minRows={5}
            placeholder="请输入提示词"
            value={promptInput}
            onChange={e => setPromptInput(e.target.value)}
            fullWidth
            sx={{
              mt: 2,
              '& .MuiOutlinedInput-root': {
                backgroundColor: alpha(theme.background.light, 0.5),
                borderRadius: '10px',
                color: theme.text.primary,
                fontSize: '1rem',
                '& fieldset': {
                  borderColor: alpha(theme.text.primary, 0.18),
                },
                '&:hover fieldset': {
                  borderColor: alpha(theme.primary, 0.4),
                },
                '&.Mui-focused fieldset': {
                  borderColor: theme.primary,
                },
              },
              '& .MuiInputLabel-root': {
                color: theme.text.secondary,
              },
              '& .MuiInputLabel-root.Mui-focused': {
                color: theme.primary,
              },
            }}
            InputLabelProps={{ style: { color: theme.text.secondary, fontSize: '0.98rem' } }}
          />
        </DialogContent>
        <DialogActions sx={{ justifyContent: 'flex-end', gap: 1.5, px: 4, py: 2.5, borderTop: `1px solid ${theme.border}` }}>
          <Button variant="outlined" sx={dialogCancelBtnStyle} onClick={() => setPromptDialogVisible(false)}>取消</Button>
          <Button variant="contained" sx={dialogOkBtnStyle} onClick={handleSavePrompt}>确定</Button>
        </DialogActions>
      </Dialog>

      {/* 生成结果对话框极简紧凑布局 */}
      <Dialog
        open={generatedTextDialogVisible}
        onClose={() => setGeneratedTextDialogVisible(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            backgroundColor: theme.background.paper,
            borderRadius: '12px',
            boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
            p: 0,
          }
        }}
      >
        <DialogTitle sx={{ 
          borderBottom: `1px solid ${theme.border}`,
          color: theme.text.primary,
          fontWeight: 700,
          fontSize: '1.08rem',
          px: 4, py: 2.5
        }}>
          生成结果
          <IconButton 
            onClick={() => setGeneratedTextDialogVisible(false)}
            size="small"
            sx={{ color: theme.text.secondary, position: 'absolute', right: 20, top: 18 }}
          >
            <Close fontSize="small" />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ py: 2.5, px: 4 }}>
          {batchMode ? (
            batchLoading ? (
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 6 }}>
                <CircularProgress sx={{ color: theme.primary }} size={40} />
                <Typography sx={{ color: theme.primary, fontWeight: 600, mt: 2 }}>批量生成中...</Typography>
              </Box>
            ) : (
              <Box>
                {batchResults.map((item, idx) => (
                  <Box key={idx} sx={{ mb: 2, p: 2, borderRadius: 2, bgcolor: alpha(theme.background.light, 0.5) }}>
                    <Typography sx={{ color: theme.accent, fontWeight: 600, mb: 1 }}>{idx + 1}. {item.sentence}</Typography>
                    {item.variations.map((v, i) => (
                      <TextField
                        key={i}
                        value={v}
                        onChange={e => {
                          const newResults = [...batchResults];
                          newResults[idx].variations[i] = e.target.value;
                          setBatchResults(newResults);
                        }}
                        fullWidth
                        size="small"
                        sx={{ mb: 1, bgcolor: alpha(theme.background.paper, 0.7), borderRadius: 1, '& .MuiOutlinedInput-root': { fontSize: '0.98rem', color: theme.text.primary } }}
                      />
                    ))}
                  </Box>
                ))}
                <Typography sx={{ color: theme.text.secondary, fontSize: '0.95rem', textAlign: 'right', mt: 1, mb: 0 }}>
                  每个分句自动创建文本组并导入，生成结果可编辑
                </Typography>
              </Box>
            )
          ) : (
            <Box>
              {singleResults.map((line, idx) => (
                <Box key={idx} sx={{ display: 'flex', alignItems: 'center', mb: 1.2 }}>
                  <Typography sx={{ color: theme.accent, fontWeight: 600, minWidth: 22, mr: 1 }}>{idx + 1}.</Typography>
                  <TextField
                    value={line}
                    onChange={e => {
                      const newLines = [...singleResults];
                      newLines[idx] = e.target.value;
                      setSingleResults(newLines);
                    }}
                    fullWidth
                    size="small"
                    sx={{ bgcolor: alpha(theme.background.paper, 0.7), borderRadius: 1, '& .MuiOutlinedInput-root': { fontSize: '0.98rem', color: theme.text.primary } }}
                  />
                </Box>
              ))}
              <Typography sx={{ color: theme.text.secondary, fontSize: '0.95rem', textAlign: 'right', mt: 1, mb: 0 }}>
                可编辑后导入到文本组，支持多行内容
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ px: 4, py: 2, borderTop: `1px solid ${theme.border}` }}>
          <Button onClick={() => setGeneratedTextDialogVisible(false)} variant="outlined" sx={dialogCancelBtnStyle}>关闭</Button>
          {batchMode ? (
            <Button variant="contained" sx={dialogOkBtnStyle} onClick={() => setImportDialogVisible(true)}>批量导入</Button>
          ) : (
            <Button variant="contained" sx={dialogOkBtnStyle} onClick={() => setImportDialogVisible(true)}>导入</Button>
          )}
        </DialogActions>
      </Dialog>
      {/* 其它弹窗和全局提示... */}
      <input type="file" ref={fileInputRef} accept=".txt" onChange={handleFileImport} style={{ display: 'none' }} />
      {/* 添加分类弹窗 */}
      <Dialog
        open={addCategoryDialogVisible}
        onClose={() => setAddCategoryDialogVisible(false)}
        maxWidth="xs"
        fullWidth
        PaperProps={{
          sx: {
            backgroundColor: theme.background.paper,
            borderRadius: '16px',
            boxShadow: '0 12px 40px rgba(0,0,0,0.22)',
            p: 0
          }
        }}
      >
        <DialogTitle sx={{ color: theme.text.primary, fontWeight: 700, fontSize: '1.15rem', lineHeight: 1.4, borderBottom: `1px solid ${alpha(theme.text.primary, 0.07)}`, px: 4, py: 2.5 }}>
          添加分类
          <IconButton onClick={() => setAddCategoryDialogVisible(false)} size="small" sx={{ position: 'absolute', right: 16, top: 16, color: theme.text.secondary }}>
            <Close fontSize="small" />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ px: 4, py: 3, color: theme.text.primary, fontSize: '1rem', lineHeight: 1.7 }}>
          <TextField
            autoFocus
            margin="dense"
            label="分类名称"
            type="text"
            fullWidth
            value={newCategoryName}
            onChange={e => setNewCategoryName(e.target.value)}
            sx={{
              mt: 2,
              '& .MuiOutlinedInput-root': {
                backgroundColor: alpha(theme.background.light, 0.5),
                borderRadius: '10px',
                color: theme.text.primary,
                fontSize: '1rem',
                '& fieldset': {
                  borderColor: alpha(theme.text.primary, 0.18),
                },
                '&:hover fieldset': {
                  borderColor: alpha(theme.primary, 0.4),
                },
                '&.Mui-focused fieldset': {
                  borderColor: theme.primary,
                },
              },
              '& .MuiInputLabel-root': {
                color: theme.text.secondary,
              },
              '& .MuiInputLabel-root.Mui-focused': {
                color: theme.primary,
              },
            }}
            InputLabelProps={{ style: { color: theme.text.secondary, fontSize: '0.98rem' } }}
          />
        </DialogContent>
        <DialogActions sx={{ justifyContent: 'flex-end', gap: 1.5, px: 4, py: 2.5, borderTop: `1px solid ${theme.border}` }}>
          <Button variant="outlined" sx={dialogCancelBtnStyle} onClick={() => setAddCategoryDialogVisible(false)}>取消</Button>
          <Button variant="contained" sx={dialogOkBtnStyle} onClick={handleAddCategory}>确定</Button>
        </DialogActions>
      </Dialog>
      {/* 导入到文本组弹窗 */}
      <Dialog
        open={importDialogVisible}
        onClose={() => setImportDialogVisible(false)}
        maxWidth="xs"
        fullWidth
        PaperProps={{
          sx: {
            backgroundColor: theme.background.paper,
            borderRadius: '16px',
            boxShadow: '0 12px 40px rgba(0,0,0,0.22)',
            p: 0
          }
        }}
      >
        <DialogTitle sx={{ color: theme.text.primary, fontWeight: 700, fontSize: '1.15rem', lineHeight: 1.4, borderBottom: `1px solid ${alpha(theme.text.primary, 0.07)}`, px: 4, py: 2.5 }}>
          导入到文本组
        </DialogTitle>
        <DialogContent sx={{ px: 4, py: 3, color: theme.text.primary, fontSize: '1rem', lineHeight: 1.7 }}>
          {batchMode ? (
            <TextField
              label="请输入分类名"
              value={batchCategoryName}
              onChange={e => setBatchCategoryName(e.target.value)}
              fullWidth
              sx={{
                borderRadius: '10px',
                backgroundColor: alpha(theme.background.light, 0.5),
                color: theme.text.primary,
                mt: 1,
                '& .MuiOutlinedInput-root': {
                  color: theme.text.primary,
                },
                '& .MuiInputLabel-root': {
                  color: theme.text.secondary,
                },
                '& .MuiInputLabel-root.Mui-focused': {
                  color: theme.accent,
                },
              }}
              InputLabelProps={{ style: { color: theme.text.secondary, fontSize: '0.98rem' } }}
            />
          ) : (
            <>
              <FormControl fullWidth sx={{ mt: 1 }}>
                <InputLabel sx={{ color: theme.text.secondary, fontSize: '0.98rem' }}>选择分类</InputLabel>
                <Select
                  value={selectedImportCategory}
                  onChange={e => {
                    setSelectedImportCategory(e.target.value);
                    setSelectedImportTextGroup('');
                  }}
                  sx={{
                    borderRadius: '10px',
                    backgroundColor: alpha(theme.background.light, 0.5),
                    color: theme.text.primary,
                    '& .MuiSelect-icon': { color: theme.text.secondary },
                  }}
                >
                  {categories.filter(c => c.id !== 'all').map(cat => (
                    <MenuItem key={cat.id} value={cat.id}>{cat.name}</MenuItem>
                  ))}
                </Select>
              </FormControl>
              <FormControl fullWidth sx={{ mt: 3 }}>
                <InputLabel sx={{ color: theme.text.secondary, fontSize: '0.98rem' }}>选择文本组</InputLabel>
                <Select
                  value={selectedImportTextGroup}
                  label="选择文本组"
                  onChange={e => setSelectedImportTextGroup(e.target.value)}
                  sx={{
                    borderRadius: '10px',
                    backgroundColor: alpha(theme.background.light, 0.5),
                    color: theme.text.primary,
                    '& .MuiSelect-icon': { color: theme.text.secondary },
                  }}
                >
                  {filteredTextGroups.filter(g => g.categoryId === selectedImportCategory).map(group => (
                    <MenuItem key={group.id} value={group.id}>{group.name}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </>
          )}
          <Typography sx={{ color: theme.text.secondary, fontSize: '0.98rem', mt: 2 }}>{batchMode ? '批量模式：输入分类名自动创建分类并导入' : '说明或提示内容'}</Typography>
        </DialogContent>
        <DialogActions sx={{ justifyContent: 'flex-end', gap: 1.5, px: 4, py: 2.5, borderTop: `1px solid ${theme.border}` }}>
          <Button variant="outlined" sx={dialogCancelBtnStyle} onClick={() => setImportDialogVisible(false)}>取消</Button>
          {batchMode ? (
            <Button variant="contained" sx={dialogOkBtnStyle} onClick={handleBatchImport}>确定批量导入</Button>
          ) : (
            <Button variant="contained" sx={dialogOkBtnStyle} onClick={handleImportToGroup}>确定导入</Button>
          )}
        </DialogActions>
      </Dialog>
      {/* 添加话术弹窗 */}
      <Dialog
        open={addScriptDialogVisible}
        onClose={() => setAddScriptDialogVisible(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            backgroundColor: theme.background.paper,
            borderRadius: '16px',
            boxShadow: '0 12px 40px rgba(0,0,0,0.22)',
            p: 0
          }
        }}
      >
        <DialogTitle sx={{ color: theme.text.primary, fontWeight: 700, fontSize: '1.15rem', lineHeight: 1.4, borderBottom: `1px solid ${alpha(theme.text.primary, 0.07)}`, px: 4, py: 2.5 }}>
          添加话术
          <IconButton onClick={() => setAddScriptDialogVisible(false)} size="small" sx={{ position: 'absolute', right: 16, top: 16, color: theme.text.secondary }}>
            <Close fontSize="small" />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ px: 4, py: 3, color: theme.text.primary, fontSize: '1rem', lineHeight: 1.7 }}>
          <TextField
            multiline
            minRows={4}
            label="请输入要添加的话术（每行一句）"
            value={newScriptsInput}
            onChange={e => setNewScriptsInput(e.target.value)}
            fullWidth
            sx={{ 
              mt: 2,
              '& .MuiOutlinedInput-root': {
                backgroundColor: alpha(theme.background.light, 0.5),
                borderRadius: '10px',
                color: theme.text.primary,
                fontSize: '1rem',
                '& fieldset': {
                  borderColor: alpha(theme.text.primary, 0.18),
                },
                '&:hover fieldset': {
                  borderColor: alpha(theme.primary, 0.4),
                },
                '&.Mui-focused fieldset': {
                  borderColor: theme.primary,
                },
              },
              '& .MuiInputLabel-root': {
              color: theme.text.secondary,
              },
              '& .MuiInputLabel-root.Mui-focused': {
                color: theme.primary,
              },
            }}
            InputLabelProps={{ style: { color: theme.text.secondary, fontSize: '0.98rem' } }}
          />
        </DialogContent>
        <DialogActions sx={{ justifyContent: 'flex-end', gap: 1.5, px: 4, py: 2.5, borderTop: `1px solid ${theme.border}` }}>
          <Button variant="outlined" sx={dialogCancelBtnStyle} onClick={() => setAddScriptDialogVisible(false)}>取消</Button>
          <Button variant="contained" sx={dialogOkBtnStyle} onClick={handleSaveScripts}>确定</Button>
        </DialogActions>
      </Dialog>
      {/* 渲染模型选择弹窗 */}
      <ModelSelectDialog
        open={modelSelectDialogOpen}
        onClose={() => setModelSelectDialogOpen(false)}
        models={models}
        currentModel={currentModel}
        onSelect={(model: any) => { setCurrentModel(model); setModelSelectDialogOpen(false); }}
      />
      <ModelSelectDialog
        open={assistantModelSelectDialogOpen}
        onClose={() => setAssistantModelSelectDialogOpen(false)}
        models={models}
        currentModel={assistantModel}
        onSelect={(model: any) => { setAssistantModel(model); setAssistantModelSelectDialogOpen(false); }}
      />
      {/* 错误提示弹窗 */}
      <Snackbar open={!!error} autoHideDuration={4000} onClose={() => {}} anchorOrigin={{ vertical: 'top', horizontal: 'center' }}>
        <Alert severity="error" sx={{ width: '100%' }}>{error}</Alert>
      </Snackbar>
      {/* 编辑话术弹窗 */}
      <Dialog
        open={editScriptDialogVisible}
        onClose={() => setEditScriptDialogVisible(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            backgroundColor: theme.background.paper,
            borderRadius: '16px',
            boxShadow: '0 12px 40px rgba(0,0,0,0.22)',
            p: 0
          }
        }}
      >
        <DialogTitle sx={{ color: theme.text.primary, fontWeight: 700, fontSize: '1.15rem', lineHeight: 1.4, borderBottom: `1px solid ${alpha(theme.text.primary, 0.07)}`, px: 4, py: 2.5 }}>
          编辑话术
          <IconButton onClick={() => setEditScriptDialogVisible(false)} size="small" sx={{ position: 'absolute', right: 16, top: 16, color: theme.text.secondary }}>
            <Close fontSize="small" />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ px: 4, py: 3, color: theme.text.primary, fontSize: '1rem', lineHeight: 1.7 }}>
          <TextField
            multiline
            minRows={4}
            label="编辑话术内容"
            value={editingScript?.content || ''}
            onChange={e => setEditingScript(editingScript ? { ...editingScript, content: e.target.value } : null)}
            fullWidth
            sx={{
              mt: 2,
              '& .MuiOutlinedInput-root': {
                backgroundColor: alpha(theme.background.light, 0.5),
                borderRadius: '10px',
                color: theme.text.primary,
                fontSize: '1rem',
                '& fieldset': {
                  borderColor: alpha(theme.text.primary, 0.18),
                },
                '&:hover fieldset': {
                  borderColor: theme.primary,
                },
                '&.Mui-focused fieldset': {
                  borderColor: theme.primary,
                },
              },
              '& .MuiInputLabel-root': {
                color: theme.text.secondary,
              },
              '& .MuiInputLabel-root.Mui-focused': {
                color: theme.primary,
              },
            }}
            InputLabelProps={{ style: { color: theme.text.secondary, fontSize: '0.98rem' } }}
          />
        </DialogContent>
        <DialogActions sx={{ justifyContent: 'flex-end', gap: 1.5, px: 4, py: 2.5, borderTop: `1px solid ${theme.border}` }}>
          <Button variant="outlined" sx={dialogCancelBtnStyle} onClick={() => setEditScriptDialogVisible(false)}>取消</Button>
          <Button variant="contained" sx={dialogOkBtnStyle} onClick={handleSaveEditedScript}>保存</Button>
        </DialogActions>
      </Dialog>
      {/* 重命名分类弹窗 */}
      <Dialog
        open={renameCategoryDialogVisible}
        onClose={() => setRenameCategoryDialogVisible(false)}
        maxWidth="xs"
        fullWidth
        PaperProps={{
          sx: {
            backgroundColor: theme.background.paper,
            borderRadius: '16px',
            boxShadow: '0 12px 40px rgba(0,0,0,0.22)',
            p: 0
          }
        }}
      >
        <DialogTitle sx={{ color: theme.text.primary, fontWeight: 700, fontSize: '1.15rem', lineHeight: 1.4, borderBottom: `1px solid ${alpha(theme.text.primary, 0.07)}`, px: 4, py: 2.5 }}>
          重命名分类
          <IconButton onClick={() => setRenameCategoryDialogVisible(false)} size="small" sx={{ position: 'absolute', right: 16, top: 16, color: theme.text.secondary }}>
            <Close fontSize="small" />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ px: 4, py: 3, color: theme.text.primary, fontSize: '1rem', lineHeight: 1.7 }}>
          <TextField
            autoFocus
            margin="dense"
            label="分类名称"
            type="text"
            fullWidth
            value={newCategoryNameInput}
            onChange={e => setNewCategoryNameInput(e.target.value)}
            sx={{
              mt: 2,
              '& .MuiOutlinedInput-root': {
                backgroundColor: alpha(theme.background.light, 0.5),
                borderRadius: '10px',
                color: theme.text.primary,
                fontSize: '1rem',
                '& fieldset': {
                  borderColor: alpha(theme.text.primary, 0.18),
                },
                '&:hover fieldset': {
                  borderColor: theme.primary,
                },
                '&.Mui-focused fieldset': {
                  borderColor: theme.primary,
                },
              },
              '& .MuiInputLabel-root': {
                color: theme.text.secondary,
              },
              '& .MuiInputLabel-root.Mui-focused': {
                color: theme.primary,
              },
            }}
            InputLabelProps={{ style: { color: theme.text.secondary, fontSize: '0.98rem' } }}
          />
        </DialogContent>
        <DialogActions sx={{ justifyContent: 'flex-end', gap: 1.5, px: 4, py: 2.5, borderTop: `1px solid ${theme.border}` }}>
          <Button variant="outlined" sx={dialogCancelBtnStyle} onClick={() => setRenameCategoryDialogVisible(false)}>取消</Button>
          <Button variant="contained" sx={dialogOkBtnStyle} onClick={handleRenameCategory}>保存</Button>
        </DialogActions>
      </Dialog>
      {/* 批量新建文本组弹窗 */}
      <Dialog
        open={batchCreateDialogVisible}
        onClose={() => setBatchCreateDialogVisible(false)}
        maxWidth="xs"
        fullWidth
        PaperProps={{
          sx: {
            backgroundColor: theme.background.paper,
            borderRadius: '16px',
            boxShadow: '0 12px 40px rgba(0,0,0,0.22)',
            p: 0
          }
        }}
      >
        <DialogTitle sx={{ color: theme.text.primary, fontWeight: 700, fontSize: '1.15rem', lineHeight: 1.4, borderBottom: `1px solid ${alpha(theme.text.primary, 0.07)}`, px: 4, py: 2.5 }}>
          批量新建文本组
          <IconButton onClick={() => setBatchCreateDialogVisible(false)} size="small" sx={{ position: 'absolute', right: 16, top: 16, color: theme.text.secondary }}>
            <Close fontSize="small" />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ px: 4, py: 3, color: theme.text.primary, fontSize: '1rem', lineHeight: 1.7 }}>
          <TextField
            type="number"
            label="请输入要创建的文本组数量"
            value={batchCreateCount}
            onChange={e => setBatchCreateCount(Number(e.target.value))}
            fullWidth
            inputProps={{ min: 1, max: 100 }}
            sx={{
              mt: 2,
              '& .MuiOutlinedInput-root': {
                backgroundColor: alpha(theme.background.light, 0.5),
                borderRadius: '10px',
                color: theme.text.primary,
                fontSize: '1rem',
              },
              '& .MuiInputLabel-root': {
                color: theme.text.secondary,
              },
              '& .MuiInputLabel-root.Mui-focused': {
                color: theme.accent,
              },
            }}
            InputLabelProps={{ style: { color: theme.text.secondary, fontSize: '0.98rem' } }}
          />
          <Typography sx={{ color: theme.text.secondary, fontSize: '0.98rem', mt: 2 }}>将自动创建对应数量的文本组，组名依次为"组N"。</Typography>
        </DialogContent>
        <DialogActions sx={{ justifyContent: 'flex-end', gap: 1.5, px: 4, py: 2.5, borderTop: `1px solid ${theme.border}` }}>
          <Button variant="outlined" sx={dialogCancelBtnStyle} onClick={() => setBatchCreateDialogVisible(false)}>取消</Button>
          <Button variant="contained" sx={dialogOkBtnStyle} onClick={handleBatchCreateTextGroups}>确定</Button>
        </DialogActions>
      </Dialog>
      {/* 插话成功提示 */}
      <Collapse in={showInterruptSuccess}>
        <Alert severity="success" sx={{ mb: 1, backgroundColor: alpha(theme.success, 0.1), color: theme.success, border: `1px solid ${alpha(theme.success, 0.2)}`, borderRadius: '8px', '& .MuiAlert-icon': { color: theme.success } }}>
          插话内容已添加到播放队列
        </Alert>
      </Collapse>
    </Box>
  );
}; 