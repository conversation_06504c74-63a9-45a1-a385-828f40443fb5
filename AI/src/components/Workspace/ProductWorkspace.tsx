import React, { useState, useEffect, useCallback } from 'react';
import { 
  <PERSON>, 
  Typography, 
  TextField, 
  Button, 
  Card, 
  CardContent, 
  Grid, 
  Select, 
  MenuItem, 
  InputLabel, 
  FormControl,
  Divider,
  SelectChangeEvent,
  Tooltip,
  IconButton,
  Alert,
  Collapse,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Paper,
  Chip,
  alpha,
  Stack,
  LinearProgress,
  Badge
} from '@mui/material';
import AIHelper from '../AIHelper';
import LightbulbOutlinedIcon from '@mui/icons-material/LightbulbOutlined';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import SettingsIcon from '@mui/icons-material/Settings';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import StopIcon from '@mui/icons-material/Stop';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import DownloadIcon from '@mui/icons-material/Download';
import LocalOfferIcon from '@mui/icons-material/LocalOffer';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import VolunteerActivismIcon from '@mui/icons-material/VolunteerActivism';
import PeopleIcon from '@mui/icons-material/People';
import InventoryIcon from '@mui/icons-material/Inventory';
import StyleIcon from '@mui/icons-material/Style';
import DeleteIcon from '@mui/icons-material/Delete';
import { useWorkspaceState } from '../../hooks/useWorkspaceState';

// 预设的产品示例
const PRODUCT_TEMPLATES = {
  "美妆": {
    name: "玫瑰精华保湿面霜",
    category: "美妆",
    features: "添加天然玫瑰精华，24小时持久保湿，温和不刺激，适合敏感肌肤，不含酒精和香精",
    price: "￥129.9",
    promotion: "限时8折，买一送一保湿喷雾",
    audience: "干性皮肤、熟龄肌、敏感肌",
    stock: "今日限量20套",
    style: "情感共鸣"
  },
  "数码": {
    name: "T9无线蓝牙耳机",
    category: "数码",
    features: "35小时超长续航，主动降噪，通透模式，IPX5防水，蓝牙5.2，双耳独立使用",
    price: "￥199",
    promotion: "送充电仓保护套，一年质保",
    audience: "学生党、通勤族、运动爱好者",
    stock: "现货发售，今日下单加急发货",
    style: "专业权威"
  },
  "厨房用品": {
    name: "多功能破壁料理机",
    category: "厨房用品",
    features: "2200W大功率，8叶钛合金刀头，12种预设模式，一键清洗，静音设计",
    price: "￥499",
    promotion: "赠送食谱一本，30天无理由退换",
    audience: "宝妈、烹饪爱好者、追求健康生活的人群",
    stock: "仅剩3台，明日涨价",
    style: "紧迫促销"
  },
  "户外运动": {
    name: "轻便折叠自行车",
    category: "户外运动",
    features: "碳纤维车架，一秒折叠，20寸轮胎，7档变速，全车重8.5kg，带USB充电口",
    price: "￥1299",
    promotion: "送骑行装备大礼包，免费保养3次",
    audience: "通勤族、户外爱好者、空间有限的城市居民",
    stock: "现货5辆，可当天发出",
    style: "幽默诙谐"
  }
};

// 自定义主题定义
interface CustomTheme {
  primary: string;
  secondary: string;
  accent: string;
  gradient: {
    primary: string;
    secondary: string;
    accent: string;
  };
  background: {
    main: string;
    paper: string;
    light: string;
    dark: string;
    highlight: string;
  };
  text: {
    primary: string;
    secondary: string;
    muted: string;
  };
  success: string;
  warning: string;
  error: string;
  border: string;
}

interface ProductWorkspaceProps {
  // 空接口，不需要任何属性
}

export const ProductWorkspace: React.FC = () => {
  // 创建主题
  const theme: CustomTheme = {
    primary: '#4f7df9', // 主色调
    secondary: '#7a6cf7', // 辅助色
    accent: '#2ec0ff',   // 强调色
    gradient: {
      primary: 'linear-gradient(135deg, #4f7df9 0%, #7a6cf7 100%)',
      secondary: 'linear-gradient(135deg, #7a6cf7 0%, #9b6cf7 100%)',
      accent: 'linear-gradient(135deg, #2ec0ff 0%, #4f7df9 100%)'
    },
    background: {
      main: '#1a1f36', // 主背景色
      paper: '#232842', // 卡片背景色
      light: '#2a3153', // 浅背景色
      dark: '#151b30',  // 深背景色
      highlight: '#323c6b' // 高亮背景色
    },
    text: {
      primary: '#ffffff',  // 主文字色
      secondary: '#c9d1f0', // 次要文字色
      muted: '#8a94b8'      // 弱化文字色
    },
    success: '#4caf50',
    warning: '#ff9800',
    error: '#f44336',
    border: '#373e59' // 边框色
  };
  
  // 使用useWorkspaceState替代useState管理状态
  const [productName, setProductName] = useWorkspaceState<string>('product-name', '');
  const [productCategory, setProductCategory] = useWorkspaceState<string>('product-category', '');
  const [customProductCategory, setCustomProductCategory] = useWorkspaceState<string>('custom-product-category', '');
  const [productFeatures, setProductFeatures] = useWorkspaceState<string>('product-features', '');
  const [productPrice, setProductPrice] = useWorkspaceState<string>('product-price', '');
  const [productPromotion, setProductPromotion] = useWorkspaceState<string>('product-promotion', '');
  const [targetAudience, setTargetAudience] = useWorkspaceState<string>('product-audience', '');
  const [stockInfo, setStockInfo] = useWorkspaceState<string>('product-stock', '');
  const [scriptStyle, setScriptStyle] = useWorkspaceState<string>('product-style', '亲切自然');
  const [customScriptStyle, setCustomScriptStyle] = useWorkspaceState<string>('custom-script-style', '');
  const [generatedDescription, setGeneratedDescription] = useWorkspaceState<string>('product-description', '');
  const [autoPrompt, setAutoPrompt] = useWorkspaceState<string>('product-prompt', '');
  const [helpExpanded, setHelpExpanded] = useWorkspaceState<boolean>('product-help-expanded', false);
  const [promptTemplateDialogOpen, setPromptTemplateDialogOpen] = useWorkspaceState<boolean>('product-template-dialog', false);
  const [wordCount, setWordCount] = useWorkspaceState<number>('product-word-count', 0);
  
  // 清空确认对话框状态
  const [clearConfirmOpen, setClearConfirmOpen] = useWorkspaceState<boolean>('product-clear-confirm', false);
  
  // 提示词模板状态
  const [promptTemplate, setPromptTemplate] = useWorkspaceState<string>('product-prompt-template', 
`1、字数一定要满足我的要求，生成2000个字以上。话术中不要出现场外音、开场白、动作描述、备注内容、环节名称等等，只生成纯直播2000字话术，不要场景备注比如（突然拍桌（咯吱声）（咯吱声），不要出现这些。也不要生成莫须有的人数，要讲库存还有多少的话就个位数去报就行，
2、现在你是一个专业的卖货主播，我会给你提供产品名称，价格，你按照要求写出话术
3、话术要口语化，有亲和力、有幽默感，禁用书面语，不要说英语，多一些自问自答的话术，如：对的、没错、怎么这一类的内容。
4、你需要知道直播间的基本信息，以及直播间中的违禁词（如：绝对、顶级、顶峰、绝无仅有、国家级、万能、首位、世界级、第一、唯一、最好、最佳、顶级、顶尖、无可比拟、无与伦比、极致、独一无二、完美、无敌、极好、极佳、超级、超强、最强、最棒、最牛、最先、最火、最热、最酷、最高、最低、最好、最佳、绝佳、最优、100%、首个、首选、首家、免检、一流、治疗、功效、疾病。）用成语的话也要避免出现极限的意思
5、话术中不要出现和直播无关的内容，如：环节名称、备注的内容
6、尽量商品2个字，用别的文字代替：如：宝贝、直播间链接、货品、咱们家这个等等。
7、不能出现有关优先发货的内容和话术，没有说有运费险就不提运费险
8、规避功效、医疗、疗效方面的说法，特别是医疗和疗效类的内容，如：能让人**样，**疗效，**功效
9、每次生成的话术，要和上一次生成的话术区别比较大，不要重复！
10、不要出现诱导性的话术，如：只要**样就**样，你**样我就**样
11、在编写之前，你需要参考同类商品优秀主播的话术，汲取其中的优点和经验来完善你的话术
12、符合抖音直播的规则话术，不能出现违规的内容
13、根据我提供的主播话术拆解，去生成你的直播带货话术，话术必须去掉所有非话术内容如：场外音、开场白、动作描述、备注内容、环节名称，只保留主播说的部分
14、不要提及抖音功效词，违禁词，不要出现我们下次直播间或者拜拜等内容

参考话术拆解框架：
一、逻辑框架设计
1. 场景引入：
· 时间紧迫感：利用"年底""年货节""快递停运"强化用户决策窗口期。
· 痛点放大：传统拔毛耗时费力（"两三个小时拔不干净""皮破被嫌弃"），引发情感共鸣（"影响过年心情"）。
2. 产品价值塑造：
· 性能参数：旗舰款转速（2万转 vs 普通款3千转）、续航（全天）、材质（食品级不锈钢）、安全设计（不伤皮肉）。
· 场景覆盖：鸡鸭鹅、猪牛羊全品类适用，强调"连根拔起"的彻底性，解决用户对残留毛发的担忧。
3. 信任背书：
· 源头厂家首播福利（低价补贴）、专业用户背书（杀鸡店使用）、长期使用案例（自家3年未坏）。
4. 限时逼单：
· 库存告急（"只剩3单"）、价格暗示（"明天恢复原价"）、物流倒计时（"快递停运"）。
二、福利与促销策略
1. 价格锚定：
· 线下对比（100-200元 vs 直播间几十元），突出"花小钱办大事"。
2. 赠品暗示：
· 运费险、1年换新、3年质保，降低用户试错成本。
3. 稀缺性设计：
· 现货仅剩、工厂停工、限时发货，制造"错过即无"的心理。
三、产品卖点深度塑造
1. 技术差异化：
· 转速对比（旗舰款2万转）、材质安全（食品级不锈钢）、防水设计（适用多场景）。
2. 使用体验：
· 省时（10分钟/只）、省力（一键操作）、普适性（老人小孩可用）。
3. 情感绑定：
· 家庭关怀（"给父母用"）、节日场景（"舒舒服服过年"），将工具升级为"家庭幸福感提升方案"。
四、售后保障与信任强化
1. 风险对冲：
· 运费险（无理由退换）、1年换新（质量问题直接换）、3年质保（长期承诺）。
2. 用户证言：
· "自家3年前旗舰款仍在用"，通过真实案例建立可信度。
五、逼单与促单技巧
1. 紧迫感话术：
· "最后3分钟下播""库存剩3单""快递停运"，倒逼用户即时决策。
2. 损失厌恶：
· 强调不购买的后果（"腰酸背痛""拔不干净"），对比购买后的便利性。
六、互动设计与人设打造
1. 人设亲和力：
· 称呼"弟弟""哥姐们"，拉近与观众距离，增强信任感。
2. 感官引导：
· 具象化描述"眨眨眼整只鸭拔好""嘎嘎快"，强化使用效果画面感。
3. 体验邀请：
· "收到货先杀两只鸡感受品质"，降低用户决策门槛。

请按照这个框架风格去生成话术，根据提供的产品特点和价格，生成与上面产品拔毛机不同的内容，确保字数达到2000字以上。必须确保内容全部是纯话术，不包含任何场外音、备注和非主播讲述内容。`
  );

  // 当结果变化时，计算字数
  useEffect(() => {
    setWordCount(generatedDescription.length);
  }, [generatedDescription, setWordCount]);

  // 处理TextField输入变化
  const handleTextInputChange = (setter: React.Dispatch<React.SetStateAction<string>>) => 
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setter(e.target.value);
    };
  
  // 处理Select输入变化
  const handleSelectChange = (setter: React.Dispatch<React.SetStateAction<string>>) => 
    (e: SelectChangeEvent) => {
      setter(e.target.value);
    };

  // 构建AI助手的系统提示词
  const getSystemPrompt = () => {
    return `你是一个专业的电商直播带货主播，擅长用生动活泼、亲和力强的语言推广产品。请根据提供的产品信息，生成一段适合直播间使用的话术，要求：
1、字数必须在2000个字以上。话术中不要出现场外音、开场白、动作描述、备注内容、环节名称等等，只生成纯直播话术，不要场景备注比如（突然拍桌）（咯吱声），不要出现这些。也不要生成莫须有的人数，要讲库存还有多少的话就个位数去报就行。
2、话术要口语化，有亲和力、有幽默感，禁用书面语，不要说英语，多一些自问自答的话术，如：对的、没错、怎么这一类的内容。
3、避免使用直播间违禁词，如：绝对、顶级、顶峰、绝无仅有、国家级、万能、首位、世界级、第一、唯一、最好、最佳、顶级、顶尖、无可比拟、无与伦比、极致、独一无二、完美、无敌、极好、极佳、超级、超强、最强、最棒、最牛、最先、最火、最热、最酷、最高、最低、最好、最佳、绝佳、最优、100%、首个、首选、首家、免检、一流、治疗、功效、疾病。用成语时也要避免出现极限的意思。
4、尽量避免使用"商品"二字，用别的词代替如：宝贝、直播间链接、货品、咱们家这个等等。
5、不能出现有关优先发货的内容和话术，除非明确提到有运费险才提运费险。
6、规避功效、医疗、疗效方面的说法，特别是医疗和疗效类的内容，如：能让人**样，**疗效，**功效。
7、不要出现诱导性的话术，如：只要**样就**样，你**样我就**样。
8、话术结构参考：
  a. 场景引入：制造时间紧迫感，放大用户痛点，引发情感共鸣
  b. 产品价值塑造：介绍性能参数、材质、场景覆盖等
  c. 信任背书：提及厂家、专业用户或长期使用案例
  d. 限时逼单：提示库存告急、价格优势、物流时效等
  e. 福利与促销：价格锚定、赠品介绍、创造稀缺性
  f. 产品差异化卖点：技术特点、使用体验、情感绑定等
  g. 售后保障：风险对冲、用户证言等
  h. 促单技巧：制造紧迫感、强调不购买的损失等
  i. 人设亲和力：使用亲切称呼，具象化描述使用效果`;
  };

  // 打开提示词模板设置对话框
  const openPromptTemplateDialog = useCallback(() => {
    setPromptTemplateDialogOpen(true);
  }, [setPromptTemplateDialogOpen]);

  // 关闭提示词模板设置对话框
  const closePromptTemplateDialog = useCallback(() => {
    setPromptTemplateDialogOpen(false);
  }, [setPromptTemplateDialogOpen]);

  // 保存提示词模板
  const savePromptTemplate = useCallback(() => {
    try {
      localStorage.setItem('customPromptTemplate', promptTemplate);
      closePromptTemplateDialog();
      // 重新生成提示词
      const newPrompt = getPromptFromProductInfo();
      setAutoPrompt(newPrompt);
    } catch (error) {
      console.error('保存提示词模板失败:', error);
    }
  }, [promptTemplate, closePromptTemplateDialog, setAutoPrompt]);

  // 加载保存的提示词模板
  useEffect(() => {
    try {
      const savedTemplate = localStorage.getItem('customPromptTemplate');
      if (savedTemplate) {
        setPromptTemplate(savedTemplate);
      }
    } catch (error) {
      console.error('加载提示词模板失败:', error);
    }
  }, [setPromptTemplate]);

  // 获取实际产品类别（自定义或预设）
  const getActualProductCategory = useCallback(() => {
    return productCategory === '其他' ? (customProductCategory ? customProductCategory : '') : productCategory;
  }, [productCategory, customProductCategory]);

  // 获取实际话术风格（自定义或预设）
  const getActualScriptStyle = useCallback(() => {
    return scriptStyle === '其他' ? (customScriptStyle ? customScriptStyle : '') : scriptStyle;
  }, [scriptStyle, customScriptStyle]);

  // 构建AI助手的提示词
  const getPromptFromProductInfo = useCallback(() => {
    if (!productName) return '';
    
    let prompt = `以下是我们的产品信息，请根据产品信息和要求生成对应的直播带货话术：\n\n`;
    prompt += `产品名称: ${productName}\n`;
    
    const actualCategory = getActualProductCategory();
    if (actualCategory) {
      prompt += `产品类别: ${actualCategory}\n`;
    }
    
    if (productFeatures) {
      prompt += `产品特点: ${productFeatures}\n`;
    }
    
    if (productPrice) {
      prompt += `产品价格: ${productPrice}\n`;
    }
    
    if (productPromotion) {
      prompt += `促销信息: ${productPromotion}\n`;
    }
    
    if (targetAudience) {
      prompt += `适用人群: ${targetAudience}\n`;
    }
    
    if (stockInfo) {
      prompt += `库存状况: ${stockInfo}\n`;
    }
    
    const actualStyle = getActualScriptStyle();
    if (actualStyle) {
      prompt += `话术风格: ${actualStyle}\n`;
    }
    
    prompt += `\n`;
    
    // 使用自定义提示词模板
    prompt += promptTemplate;
    
    return prompt;
  }, [productName, productCategory, customProductCategory, productFeatures, productPrice, productPromotion, targetAudience, stockInfo, scriptStyle, customScriptStyle, promptTemplate, getActualProductCategory, getActualScriptStyle]);

  // 当产品信息变化时，自动更新提示词
  useEffect(() => {
    const prompt = getPromptFromProductInfo();
    setAutoPrompt(prompt);
  }, [productName, productCategory, customProductCategory, productFeatures, productPrice, productPromotion, targetAudience, stockInfo, scriptStyle, customScriptStyle, promptTemplate, getPromptFromProductInfo, setAutoPrompt]);

  // 处理AI生成的结果
  const handleAIResult = useCallback((result: string) => {
    setGeneratedDescription(result);
  }, [setGeneratedDescription]);

  // 加载预设模板
  const loadTemplate = useCallback((templateKey: keyof typeof PRODUCT_TEMPLATES) => {
    const template = PRODUCT_TEMPLATES[templateKey];
    if (template) {
      setProductName(template.name);
      setProductCategory(template.category);
      setCustomProductCategory(''); // 清空自定义类别
      setProductFeatures(template.features);
      setProductPrice(template.price);
      setProductPromotion(template.promotion);
      setTargetAudience(template.audience);
      setStockInfo(template.stock);
      setScriptStyle(template.style);
      setCustomScriptStyle(''); // 清空自定义风格
    }
  }, [setProductName, setProductCategory, setCustomProductCategory, setProductFeatures, setProductPrice, setProductPromotion, setTargetAudience, setStockInfo, setScriptStyle, setCustomScriptStyle]);

  // 切换帮助面板的展开/折叠状态
  const toggleHelp = useCallback(() => {
    setHelpExpanded(!helpExpanded);
  }, [helpExpanded, setHelpExpanded]);

  // 复制生成的话术到剪贴板
  const handleCopyText = useCallback(() => {
    if (generatedDescription) {
      navigator.clipboard.writeText(generatedDescription);
    }
  }, [generatedDescription]);
  
  // 添加清空所有产品信息的功能
  const clearAllProductInfo = useCallback(() => {
    setProductName('');
    setProductCategory('');
    setCustomProductCategory('');
    setProductFeatures('');
    setProductPrice('');
    setProductPromotion('');
    setTargetAudience('');
    setStockInfo('');
    setScriptStyle('亲切自然');
    setCustomScriptStyle('');
  }, [setProductName, setProductCategory, setCustomProductCategory, setProductFeatures, setProductPrice, 
      setProductPromotion, setTargetAudience, setStockInfo, setScriptStyle, setCustomScriptStyle]);
      
  // 打开清空确认对话框
  const openClearConfirm = useCallback(() => {
    setClearConfirmOpen(true);
  }, [setClearConfirmOpen]);
  
  // 关闭清空确认对话框
  const closeClearConfirm = useCallback(() => {
    setClearConfirmOpen(false);
  }, [setClearConfirmOpen]);
  
  // 确认清空并关闭对话框
  const confirmAndClear = useCallback(() => {
    clearAllProductInfo();
    closeClearConfirm();
  }, [clearAllProductInfo, closeClearConfirm]);

  return (
    <Box 
      sx={{ 
        width: '100%',
        backgroundImage: `linear-gradient(135deg, ${alpha(theme.background.dark, 0.95)} 0%, ${alpha(theme.background.main, 0.95)} 100%)`,
        borderRadius: 3,
        overflow: 'hidden',
        boxShadow: '0 10px 40px rgba(0,0,0,0.15)',
        py: 4,
        px: 3
      }}
    >
      <Box 
        sx={{ 
          mb: 5,
          display: 'flex',
          flexDirection: 'column',
          position: 'relative',
          '&::after': {
            content: '""',
            position: 'absolute',
            bottom: -20,
            left: 0,
            right: 0,
            height: '1px',
            background: `linear-gradient(90deg, transparent 0%, ${alpha(theme.primary, 0.3)} 50%, transparent 100%)`,
          },
        }}
      >
        <Box sx={{ position: 'relative', display: 'inline-block', mb: 3 }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 700,
              color: '#4f7df9',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              background: 'linear-gradient(135deg, #4f7df9 0%, #7a6cf7 100%)',
              backgroundClip: 'text',
              paddingBottom: '10px',
            }}
          >
            产品话术生成
          </Typography>
          <Box 
            sx={{ 
              position: 'absolute',
              bottom: -8,
              left: 0,
              width: '60px',
              height: '3px',
              background: 'linear-gradient(135deg, #4f7df9 0%, #7a6cf7 100%)',
              borderRadius: '3px'
            }}
          />
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Typography 
            variant="body1" 
            sx={{ 
              mb: 2, 
              color: theme.text.secondary,
              width: '100%',
              lineHeight: 1.6
            }}
          >
            输入产品信息，一键生成专业的产品介绍话术，支持多种商品类型和话术风格。生成的话术将具有丰富的表现力和说服力，帮助你提升销售转化率。
          </Typography>
        </Box>
      </Box>

      <Box sx={{ mb: 3 }}>
        <Box 
          sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            mb: 1, 
            cursor: 'pointer',
            color: theme.primary,
            '&:hover': {
              color: theme.accent
            },
            transition: 'color 0.2s ease'
          }} 
          onClick={toggleHelp}
        >
          <HelpOutlineIcon sx={{ mr: 1 }} />
          <Typography variant="body2">
            使用指南
          </Typography>
          {helpExpanded ? (
            <ExpandLessIcon sx={{ ml: 1, fontSize: 18 }} />
          ) : (
            <ExpandMoreIcon sx={{ ml: 1, fontSize: 18 }} />
          )}
        </Box>
        
        <Collapse in={helpExpanded}>
          <Paper
            sx={{
              p: 2,
              mb: 2,
              bgcolor: alpha(theme.background.paper, 0.5),
              borderRadius: 2,
              backdropFilter: 'blur(10px)',
              border: `1px solid ${alpha(theme.primary, 0.2)}`,
              boxShadow: `0 4px 20px ${alpha(theme.background.dark, 0.3)}`
            }}
          >
            <Typography variant="subtitle2" gutterBottom sx={{ color: theme.text.primary, fontWeight: 600 }}>
              快速上手指南：
            </Typography>
            <Box component="ol" sx={{ m: 0, pl: 3, color: theme.text.secondary }}>
              <Box component="li" sx={{ mb: 0.5 }}>填写左侧表单中的产品信息（至少需要产品名称）</Box>
              <Box component="li" sx={{ mb: 0.5 }}>选择合适的话术风格，不同风格适合不同的产品和受众</Box>
              <Box component="li" sx={{ mb: 0.5 }}>系统会自动将产品信息转换为提示词并填入右侧AI助手</Box>
              <Box component="li" sx={{ mb: 0.5 }}>点击"生成内容"按钮，获取AI生成的产品话术</Box>
              <Box component="li" sx={{ mb: 0.5 }}>生成的话术将按照你的要求生成，符合直播带货需求</Box>
              <Box component="li" sx={{ mb: 0.5 }}>点击右上角彩色按钮，快速加载产品模板进行体验</Box>
              <Box component="li" sx={{ mb: 0.5 }}>点击设置图标可以自定义提示词模板，调整字数要求等内容</Box>
            </Box>
          </Paper>
        </Collapse>
      </Box>
      
      <Grid container spacing={4}>
        <Grid item xs={12} md={5}>
          <Card 
            sx={{ 
              bgcolor: alpha(theme.background.paper, 0.6),
              backdropFilter: 'blur(10px)',
              border: `1px solid ${alpha(theme.text.primary, 0.1)}`,
              boxShadow: `0 4px 30px ${alpha(theme.background.dark, 0.3)}`,
              borderRadius: 3,
              height: '100%',
              position: 'relative',
              overflow: 'visible'
            }}
          >
            <CardContent sx={{ position: 'relative', zIndex: 2, pt: 3, pb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6" sx={{ color: theme.text.primary, fontWeight: 600 }}>
                  产品信息
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Tooltip title="美妆产品示例" arrow>
                    <IconButton 
                      size="small"
                      onClick={() => loadTemplate("美妆")}
                      sx={{
                        bgcolor: alpha('#f06292', 0.1),
                        color: '#f06292',
                        '&:hover': {
                          bgcolor: alpha('#f06292', 0.2),
                        }
                      }}
                    >
                      <VolunteerActivismIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="数码产品示例" arrow>
                    <IconButton 
                      size="small"
                      onClick={() => loadTemplate("数码")}
                      sx={{
                        bgcolor: alpha('#64b5f6', 0.1),
                        color: '#64b5f6',
                        '&:hover': {
                          bgcolor: alpha('#64b5f6', 0.2),
                        }
                      }}
                    >
                      <ShoppingCartIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="厨房用品示例" arrow>
                    <IconButton 
                      size="small"
                      onClick={() => loadTemplate("厨房用品")}
                      sx={{
                        bgcolor: alpha('#81c784', 0.1),
                        color: '#81c784',
                        '&:hover': {
                          bgcolor: alpha('#81c784', 0.2),
                        }
                      }}
                    >
                      <LocalOfferIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="户外运动示例" arrow>
                    <IconButton 
                      size="small"
                      onClick={() => loadTemplate("户外运动")}
                      sx={{
                        bgcolor: alpha('#ffb74d', 0.1),
                        color: '#ffb74d',
                        '&:hover': {
                          bgcolor: alpha('#ffb74d', 0.2),
                        }
                      }}
                    >
                      <LightbulbOutlinedIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="清空所有信息" arrow>
                    <IconButton 
                      onClick={openClearConfirm} 
                      size="small" 
                      sx={{ 
                        color: theme.text.secondary,
                        bgcolor: alpha(theme.text.primary, 0.05),
                        '&:hover': {
                          color: theme.error,
                          bgcolor: alpha(theme.error, 0.1),
                        }
                      }}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="提示词设置" arrow>
                    <IconButton 
                      onClick={openPromptTemplateDialog} 
                      size="small" 
                sx={{
                        color: theme.primary,
                        bgcolor: alpha(theme.primary, 0.1),
                        '&:hover': {
                          bgcolor: alpha(theme.primary, 0.2),
                        }
                      }}
                    >
                      <SettingsIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Box>
              </Box>
              
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                    <ShoppingCartIcon sx={{ color: theme.primary, mr: 1, fontSize: 18, mt: 0.5 }} />
                    <Typography variant="body2" sx={{ color: theme.text.secondary, fontWeight: 500 }}>
                      产品名称
                    </Typography>
                  </Box>
                  <TextField
                    fullWidth
                    placeholder="输入产品名称"
                    value={productName}
                    onChange={handleTextInputChange(setProductName)}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        bgcolor: alpha(theme.background.light, 0.4),
                        '& fieldset': { borderColor: alpha(theme.text.primary, 0.1) },
                        '&:hover fieldset': { borderColor: alpha(theme.primary, 0.5) },
                        '&.Mui-focused fieldset': { borderColor: theme.primary },
                      },
                      '& .MuiInputBase-input': {
                        color: theme.text.primary,
                        fontWeight: 500,
                      }
                    }}
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                    <LocalOfferIcon sx={{ color: theme.primary, mr: 1, fontSize: 18, mt: 0.5 }} />
                    <Typography variant="body2" sx={{ color: theme.text.secondary, fontWeight: 500 }}>
                      产品类别
                    </Typography>
                  </Box>
                  <FormControl fullWidth>
                    <Select
                      value={productCategory}
                      displayEmpty
                      onChange={handleSelectChange(setProductCategory)}
                      sx={{
                        bgcolor: alpha(theme.background.light, 0.4),
                        color: theme.text.primary,
                        '& .MuiOutlinedInput-notchedOutline': {
                          borderColor: alpha(theme.text.primary, 0.1),
                        },
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: alpha(theme.primary, 0.5),
                        },
                        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                          borderColor: theme.primary,
                        },
                        '& .MuiSvgIcon-root': {
                          color: theme.text.secondary,
                        }
                      }}
                      renderValue={(selected) => {
                        if (!selected) {
                          return <Typography sx={{ color: alpha(theme.text.primary, 0.5) }}>选择类别</Typography>;
                        }
                        return selected === '其他' ? (customProductCategory || '自定义类别') : selected;
                      }}
                    >
                      <MenuItem value="">选择类别</MenuItem>
                      <MenuItem value="美妆">美妆</MenuItem>
                      <MenuItem value="服饰">服饰</MenuItem>
                      <MenuItem value="数码">数码</MenuItem>
                      <MenuItem value="家居">家居</MenuItem>
                      <MenuItem value="食品">食品</MenuItem>
                      <MenuItem value="母婴">母婴</MenuItem>
                      <MenuItem value="电器">电器</MenuItem>
                      <MenuItem value="厨房用品">厨房用品</MenuItem>
                      <MenuItem value="户外运动">户外运动</MenuItem>
                      <MenuItem value="健康保健">健康保健</MenuItem>
                      <MenuItem value="汽车用品">汽车用品</MenuItem>
                      <MenuItem value="宠物用品">宠物用品</MenuItem>
                      <MenuItem value="其他">其他</MenuItem>
                    </Select>
                  </FormControl>
                  {productCategory === '其他' && (
                    <TextField
                      fullWidth
                      placeholder="请输入自定义类别，不输入默认不发送"
                      value={customProductCategory}
                      onChange={handleTextInputChange(setCustomProductCategory)}
                      sx={{
                        mt: 2,
                        '& .MuiOutlinedInput-root': {
                          bgcolor: alpha(theme.background.light, 0.4),
                          '& fieldset': { borderColor: alpha(theme.text.primary, 0.1) },
                          '&:hover fieldset': { borderColor: alpha(theme.primary, 0.5) },
                          '&.Mui-focused fieldset': { borderColor: theme.primary },
                        },
                        '& .MuiInputBase-input': {
                          color: theme.text.primary,
                        }
                      }}
                    />
                  )}
                </Grid>
                
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                    <StyleIcon sx={{ color: theme.primary, mr: 1, fontSize: 18, mt: 0.5 }} />
                    <Typography variant="body2" sx={{ color: theme.text.secondary, fontWeight: 500 }}>
                      产品特点
                    </Typography>
                  </Box>
              <TextField
                fullWidth
                    placeholder="例如：防水、持久、轻薄、无添加等"
                    value={productFeatures}
                    onChange={handleTextInputChange(setProductFeatures)}
                    multiline
                    rows={3}
                sx={{
                      '& .MuiOutlinedInput-root': {
                        bgcolor: alpha(theme.background.light, 0.4),
                        '& fieldset': { borderColor: alpha(theme.text.primary, 0.1) },
                        '&:hover fieldset': { borderColor: alpha(theme.primary, 0.5) },
                        '&.Mui-focused fieldset': { borderColor: theme.primary },
                      },
                      '& .MuiInputBase-input': {
                        color: theme.text.primary,
                      }
                    }}
                  />
                </Grid>
                
                <Grid item xs={6}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                    <LocalOfferIcon sx={{ color: theme.accent, mr: 1, fontSize: 18, mt: 0.5 }} />
                    <Typography variant="body2" sx={{ color: theme.text.secondary, fontWeight: 500 }}>
                      产品价格
                    </Typography>
                  </Box>
                  <TextField
                    fullWidth
                    placeholder="例如：￥199.9"
                    value={productPrice}
                    onChange={handleTextInputChange(setProductPrice)}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        bgcolor: alpha(theme.background.light, 0.4),
                        '& fieldset': { borderColor: alpha(theme.text.primary, 0.1) },
                        '&:hover fieldset': { borderColor: alpha(theme.primary, 0.5) },
                        '&.Mui-focused fieldset': { borderColor: theme.primary },
                      },
                      '& .MuiInputBase-input': {
                        color: theme.text.primary,
                      }
                    }}
                  />
                </Grid>
                
                <Grid item xs={6}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                    <LocalOfferIcon sx={{ color: theme.accent, mr: 1, fontSize: 18, mt: 0.5 }} />
                    <Typography variant="body2" sx={{ color: theme.text.secondary, fontWeight: 500 }}>
                      促销信息
                    </Typography>
                  </Box>
                  <TextField
                    fullWidth
                    placeholder="例如：限时8折优惠，赠送配套礼品"
                    value={productPromotion}
                    onChange={handleTextInputChange(setProductPromotion)}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        bgcolor: alpha(theme.background.light, 0.4),
                        '& fieldset': { borderColor: alpha(theme.text.primary, 0.1) },
                        '&:hover fieldset': { borderColor: alpha(theme.primary, 0.5) },
                        '&.Mui-focused fieldset': { borderColor: theme.primary },
                      },
                      '& .MuiInputBase-input': {
                        color: theme.text.primary,
                      }
                    }}
                  />
                </Grid>
                
                <Grid item xs={6}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                    <PeopleIcon sx={{ color: theme.secondary, mr: 1, fontSize: 18, mt: 0.5 }} />
                    <Typography variant="body2" sx={{ color: theme.text.secondary, fontWeight: 500 }}>
                      适用人群
                    </Typography>
                  </Box>
                  <TextField
                    fullWidth
                    placeholder="例如：青少年、中老年人、孕妇等"
                    value={targetAudience}
                    onChange={handleTextInputChange(setTargetAudience)}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        bgcolor: alpha(theme.background.light, 0.4),
                        '& fieldset': { borderColor: alpha(theme.text.primary, 0.1) },
                        '&:hover fieldset': { borderColor: alpha(theme.primary, 0.5) },
                        '&.Mui-focused fieldset': { borderColor: theme.primary },
                      },
                      '& .MuiInputBase-input': {
                        color: theme.text.primary,
                      }
                    }}
                  />
                </Grid>
                
                <Grid item xs={6}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                    <InventoryIcon sx={{ color: theme.secondary, mr: 1, fontSize: 18, mt: 0.5 }} />
                    <Typography variant="body2" sx={{ color: theme.text.secondary, fontWeight: 500 }}>
                      库存状况
                    </Typography>
                  </Box>
                  <TextField
                    fullWidth
                    placeholder="例如：仅剩5件，今日限量发货"
                    value={stockInfo}
                    onChange={handleTextInputChange(setStockInfo)}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        bgcolor: alpha(theme.background.light, 0.4),
                        '& fieldset': { borderColor: alpha(theme.text.primary, 0.1) },
                        '&:hover fieldset': { borderColor: alpha(theme.primary, 0.5) },
                        '&.Mui-focused fieldset': { borderColor: theme.primary },
                      },
                      '& .MuiInputBase-input': {
                        color: theme.text.primary,
                  }
                }}
              />
                </Grid>
                
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                    <StyleIcon sx={{ color: theme.primary, mr: 1, fontSize: 18, mt: 0.5 }} />
                    <Typography variant="body2" sx={{ color: theme.text.secondary, fontWeight: 500 }}>
                      话术风格
                    </Typography>
                  </Box>
                  <FormControl fullWidth>
                    <Select
                      value={scriptStyle}
                      onChange={handleSelectChange(setScriptStyle)}
                      sx={{
                        bgcolor: alpha(theme.background.light, 0.4),
                        color: theme.text.primary,
                        '& .MuiOutlinedInput-notchedOutline': {
                          borderColor: alpha(theme.text.primary, 0.1),
                        },
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: alpha(theme.primary, 0.5),
                        },
                        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                          borderColor: theme.primary,
                        },
                        '& .MuiSvgIcon-root': {
                          color: theme.text.secondary,
                        }
                      }}
                      renderValue={(selected) => {
                        return selected === '其他' ? (customScriptStyle || '自定义风格') : selected;
                      }}
                    >
                      <MenuItem value="亲切自然">亲切自然</MenuItem>
                      <MenuItem value="幽默诙谐">幽默诙谐</MenuItem>
                      <MenuItem value="专业权威">专业权威</MenuItem>
                      <MenuItem value="情感共鸣">情感共鸣</MenuItem>
                      <MenuItem value="紧迫促销">紧迫促销</MenuItem>
                      <MenuItem value="其他">其他</MenuItem>
                    </Select>
                  </FormControl>
                  {scriptStyle === '其他' && (
                    <TextField
                      fullWidth
                      placeholder="请输入自定义话术风格，不输入默认不发送"
                      value={customScriptStyle}
                      onChange={handleTextInputChange(setCustomScriptStyle)}
                      sx={{
                        mt: 2,
                        '& .MuiOutlinedInput-root': {
                          bgcolor: alpha(theme.background.light, 0.4),
                          '& fieldset': { borderColor: alpha(theme.text.primary, 0.1) },
                          '&:hover fieldset': { borderColor: alpha(theme.primary, 0.5) },
                          '&.Mui-focused fieldset': { borderColor: theme.primary },
                        },
                        '& .MuiInputBase-input': {
                          color: theme.text.primary,
                        }
                      }}
                    />
                  )}
                </Grid>
                
                <Grid item xs={12}>
                  <Stack direction="row" spacing={1.5} mt={1}>
                    {productName && (
                      <Chip 
                        label={productName} 
                        size="small" 
                        sx={{ 
                          bgcolor: alpha(theme.primary, 0.1), 
                          color: theme.primary,
                          fontWeight: 500
                        }} 
                      />
                    )}
                    {getActualProductCategory() && (
                      <Chip 
                        label={getActualProductCategory()} 
                        size="small" 
                        sx={{ 
                          bgcolor: alpha(theme.accent, 0.1), 
                          color: theme.accent,
                          fontWeight: 500
                        }} 
                      />
                    )}
                    {getActualScriptStyle() && (
                      <Chip 
                        label={getActualScriptStyle()} 
                        size="small" 
                        sx={{ 
                          bgcolor: alpha(theme.secondary, 0.1), 
                          color: theme.secondary,
                          fontWeight: 500
                        }} 
                      />
                    )}
                  </Stack>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={7}>
          <Card 
            sx={{ 
              bgcolor: alpha(theme.background.paper, 0.6),
              backdropFilter: 'blur(10px)',
              border: `1px solid ${alpha(theme.text.primary, 0.1)}`,
              boxShadow: `0 4px 30px ${alpha(theme.background.dark, 0.3)}`,
              borderRadius: 3,
              height: '100%',
              display: 'flex',
              flexDirection: 'column'
            }}
          >
            <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', pt: 3, pb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" sx={{ color: theme.text.primary, fontWeight: 600 }}>
                  AI生成话术
                </Typography>
              </Box>
              
              <Typography variant="body2" sx={{ mb: 3, color: theme.text.secondary }}>
                左侧信息已自动转换为提示词，点击"生成内容"按钮获取AI话术（生成2000字以上内容可能需要较长时间）
              </Typography>
              
              {/* 集成AIHelper组件 */}
              <AIHelper
                systemPrompt={getSystemPrompt()}
                placeholder="输入产品信息后，点击生成内容获取AI生成的产品话术"
                onResultGenerated={handleAIResult}
                initialPrompt={autoPrompt}
                fileName={productName ? `${productName}_话术` : "产品话术"}
                useSession={false}
              />
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* 提示词模板设置对话框 */}
      <Dialog 
        open={promptTemplateDialogOpen} 
        onClose={closePromptTemplateDialog}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            bgcolor: alpha(theme.background.paper, 0.95),
            backdropFilter: 'blur(10px)',
            borderRadius: 3,
            boxShadow: '0 10px 40px rgba(0,0,0,0.3)',
            backgroundImage: `radial-gradient(at 30% 20%, ${alpha(theme.primary, 0.05)} 0%, transparent 50%), 
                              radial-gradient(at 70% 80%, ${alpha(theme.secondary, 0.05)} 0%, transparent 50%)`
          }
        }}
      >
        <DialogTitle sx={{ color: theme.text.primary, pt: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <SettingsIcon sx={{ mr: 1.5, color: theme.primary }} />
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              自定义提示词模板设置
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" sx={{ color: theme.text.secondary, mb: 2 }}>
            您可以在这里自定义提示词模板，调整字数要求，修改提示词内容。修改后点击保存，系统将使用您的自定义模板生成话术。
          </Typography>
          <TextField
            multiline
            fullWidth
            rows={20}
            value={promptTemplate}
            onChange={(e) => setPromptTemplate(e.target.value)}
            variant="outlined"
            sx={{
              '& .MuiOutlinedInput-root': {
                bgcolor: alpha(theme.background.light, 0.3),
                color: theme.text.primary,
                '& fieldset': { borderColor: alpha(theme.text.primary, 0.1) },
                '&:hover fieldset': { borderColor: alpha(theme.primary, 0.5) },
                '&.Mui-focused fieldset': { borderColor: theme.primary }
              }
            }}
          />
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button 
            onClick={closePromptTemplateDialog}
            sx={{
              color: theme.text.secondary,
              '&:hover': {
                color: theme.text.primary,
                bgcolor: alpha(theme.text.primary, 0.05)
              }
            }}
          >
            取消
          </Button>
          <Button 
            variant="contained"
            onClick={savePromptTemplate}
            sx={{
              bgcolor: theme.primary,
              '&:hover': { 
                bgcolor: theme.secondary,
                boxShadow: `0 4px 12px ${alpha(theme.primary, 0.3)}` 
              },
              transition: 'all 0.2s ease'
            }}
          >
            保存模板
          </Button>
        </DialogActions>
      </Dialog>

      {/* 清空确认对话框 */}
      <Dialog
        open={clearConfirmOpen}
        onClose={closeClearConfirm}
        maxWidth="xs"
        PaperProps={{
          sx: {
            bgcolor: theme.background.dark,
            borderRadius: 1.5,
            border: '1px solid #3c3f53',
            boxShadow: '0 8px 32px rgba(0,0,0,0.4)',
            width: '320px',
            m: 0
          }
        }}
      >
        <Box sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
            <Box sx={{ 
              bgcolor: '#3a2324',
              borderRadius: '50%',
              width: 36,
              height: 36,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: 1.5
            }}>
              <DeleteIcon sx={{ color: '#f44336', fontSize: 20 }} />
            </Box>
            <Typography 
              sx={{ 
                color: '#fff',
                fontWeight: 600,
                fontSize: '1.1rem'
              }}
            >
              确认清空
            </Typography>
          </Box>
          
          <Typography sx={{ 
            color: '#a0a0b2',
            mb: 2,
            fontSize: '0.9rem',
            lineHeight: 1.5
          }}>
            确定要清空所有产品信息吗？
            <Box component="span" sx={{ color: '#f55753', display: 'block', mt: 0.5 }}>
              此操作无法撤销。
            </Box>
          </Typography>
          
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'flex-end',
            mt: 2
          }}>
            <Button 
              onClick={closeClearConfirm}
              sx={{
                color: '#a0a0b2',
                fontSize: '0.9rem',
                px: 2,
                py: 0.7,
                textTransform: 'none',
                fontWeight: 500,
                '&:hover': {
                  color: '#fff',
                  bgcolor: 'transparent'
                }
              }}
            >
              取消
            </Button>
            <Button 
              variant="contained"
              onClick={confirmAndClear}
              startIcon={<DeleteIcon sx={{ fontSize: 18 }} />}
              sx={{
                bgcolor: '#f44336',
                color: '#fff',
                borderRadius: 1,
                fontSize: '0.9rem',
                px: 2,
                py: 0.7,
                ml: 1,
                textTransform: 'none',
                fontWeight: 500,
                '&:hover': { 
                  bgcolor: '#d32f2f'
                }
              }}
            >
              确认清空
            </Button>
          </Box>
        </Box>
      </Dialog>
    </Box>
  );
}; 