import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, Typography, Divider, Chip, Select, MenuItem, Button, Stack, InputLabel, FormControl, Tabs, Tab, Paper, TextField, Switch, FormControlLabel, List, ListItem, ListItemText, ListItemSecondaryAction, IconButton as MuiIconButton, Dialog, DialogTitle, DialogContent, DialogActions, Collapse, Avatar, Popover, ListItemAvatar } from '@mui/material';
import PublicIcon from '@mui/icons-material/Public';
import { Button as MuiButton, IconButton } from '@mui/material';
import SettingsIcon from '@mui/icons-material/Settings';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import SendIcon from '@mui/icons-material/Send';
import Checkbox from '@mui/material/Checkbox';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { ButtonGroup } from '@mui/material';
import PauseIcon from '@mui/icons-material/Pause';
import StopIcon from '@mui/icons-material/Stop';
import CloseIcon from '@mui/icons-material/Close';
import CheckIcon from '@mui/icons-material/Check';
import { alpha } from '@mui/material/styles';
import ArrowUpward from '@mui/icons-material/ArrowUpward';
import ArrowDownward from '@mui/icons-material/ArrowDownward';
import ExpandLess from '@mui/icons-material/ExpandLess';
import ExpandMore from '@mui/icons-material/ExpandMore';
import PersonIcon from '@mui/icons-material/Person';
import axios from 'axios';

import {
  startAutoMessage,
  stopAutoMessage,
  startAutoPopup,
  stopAutoPopup,
} from '../../services/automation';
import { useAutomationStore, AutoMessage } from '../../store/automation';

const theme = {
  primary: '#4f7df9',
  secondary: '#7a6cf7',
  accent: '#2ec0ff',
  gradient: {
    primary: 'linear-gradient(135deg, #4f7df9 0%, #7a6cf7 100%)',
    secondary: 'linear-gradient(135deg, #7a6cf7 0%, #9b6cf7 100%)',
    accent: 'linear-gradient(135deg, #2ec0ff 0%, #4f7df9 100%)'
  },
  background: {
    main: '#1a1f36',
    paper: '#232842',
    light: '#2a3153',
    dark: '#151b30',
    highlight: '#323c6b'
  },
  text: {
    primary: '#ffffff',
    secondary: '#c9d1f0',
    muted: '#8a94b8'
  },
  success: '#4caf50',
  warning: '#ff9800',
  error: '#f44336',
  border: '#373e59'
};
const advancedCardStyle = {
  backgroundColor: alpha(theme.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
  borderRadius: '12px',
  border: `1px solid ${alpha(theme.border, 0.2)}`,
  boxShadow: `0 8px 32px ${alpha(theme.background.dark, 0.5)}`,
  transition: 'all 0.3s ease',
  '&:hover': {
    boxShadow: `0 12px 40px ${alpha(theme.background.dark, 0.6)}`,
    transform: 'translateY(-2px)',
  }
};
const advancedCardContent = {
  p: 3,
  '&:last-child': {
    pb: 3
  }
};
const advancedButtonStyle = {
  background: theme.gradient.primary,
  color: theme.text.primary,
  fontWeight: 600,
  borderRadius: '10px',
  padding: '10px 24px',
  boxShadow: `0 4px 15px ${alpha(theme.primary, 0.3)}`,
  transition: 'all 0.3s ease',
  '&:hover': {
    background: theme.gradient.secondary,
    boxShadow: `0 6px 20px ${alpha(theme.secondary, 0.4)}`,
    transform: 'translateY(-2px)',
  },
  '&:active': {
    transform: 'translateY(0)',
  },
  '&.Mui-disabled': {
    background: alpha(theme.background.dark, 0.5),
    color: alpha(theme.text.primary, 0.3),
  }
};
const outlineBtnStyle = {
  background: 'none',
  color: theme.primary,
  border: `1px solid ${theme.primary}`,
  borderRadius: '8px',
  fontWeight: 600,
  boxShadow: 'none',
  transition: 'all 0.15s',
  '&:hover': {
    background: 'none',
    color: theme.primary,
    border: `1.5px solid ${theme.primary}`,
  },
  '&:active, &:focus': {
    background: 'none',
    color: theme.primary,
    border: `1px solid ${theme.primary}`,
    boxShadow: 'none',
  },
};
const outlineDangerBtnStyle = {
  background: 'none',
  color: theme.error,
  border: `1px solid ${theme.error}`,
  borderRadius: '8px',
  fontWeight: 600,
  boxShadow: 'none',
  transition: 'all 0.15s',
  '&:hover': {
    background: 'none',
    color: theme.error,
    border: `1.5px solid ${theme.error}`,
  },
  '&:active, &:focus': {
    background: 'none',
    color: theme.error,
    border: `1px solid ${theme.error}`,
    boxShadow: 'none',
  },
};
const iconBtnEditStyle = {
  color: theme.accent,
  '&:hover': {
    background: 'rgba(46,192,255,0.12)',
    color: theme.accent,
  },
};
const iconBtnDeleteStyle = {
  color: theme.error,
  '&:hover': {
    background: 'rgba(244,67,54,0.12)',
    color: theme.error,
  },
};

// 账号管理功能已删除 - 系统简化

const SmartControlWorkspace: React.FC = () => {
  const [connected, setConnected] = useState(false);
  const [tabValue, setTabValue] = useState(0);

  const [newMessage, setNewMessage] = useState('');
  const [editIdx, setEditIdx] = useState<number | null>(null);
  const [editValue, setEditValue] = useState('');
  const [msgInterval, setMsgInterval] = useState(30);
  const [msgRandom, setMsgRandom] = useState(false);
  const [msgAntiBlock, setMsgAntiBlock] = useState(false);
  const [oneKeyMsg, setOneKeyMsg] = useState('');

  const [popupInterval, setPopupInterval] = useState(60);
  const [popupRandom, setPopupRandom] = useState(false);

  const [comments] = useState<string[]>(['主播好厉害！', '怎么买？', '有优惠吗？']);
  const [replies] = useState<string[]>(['谢谢支持！', '点击下方链接购买', '有优惠券哦~']);
  const [settingsOpen, setSettingsOpen] = useState(false);

  const [msgPinned, setMsgPinned] = useState<Record<number, boolean>>({});
  const [msgIntervalMin, setMsgIntervalMin] = useState(1);
  const [msgIntervalMax, setMsgIntervalMax] = useState(30);
  const [oneKeyCount, setOneKeyCount] = useState(10);

  const [popupTabValue, setPopupTabValue] = useState(0);
  const [popupIntervalMin, setPopupIntervalMin] = useState(1);
  const [popupIntervalMax, setPopupIntervalMax] = useState(30);

  const [goodsListExpanded, setGoodsListExpanded] = useState(true);

  // 自动回复相关状态已删除

  // 自动回复相关状态已删除
  // 自动回复相关代码已删除

  const [autoMsgTaskRunning, setAutoMsgTaskRunning] = useState(false);
  const [popupTaskRunning, setPopupTaskRunning] = useState(false);
  // 自动回复状态已删除

  const [connectStatus, setConnectStatus] = useState<'disconnected'|'connecting'|'connected'>('disconnected');
  const [connectHover, setConnectHover] = useState(false);

  const [isConnected, setIsConnected] = useState(false);
  const [connecting, setConnecting] = useState(false);

  // 账号管理状态已删除

  // 商品弹窗 loading 状态
  const [popupLoading, setPopupLoading] = useState(false);
  // 一键刷屏 loading 状态
  const [loading, setLoading] = useState(false);

  // 浏览器设置状态已删除 - 系统自动管理

  const handleConnectClick = () => {
    if (connectStatus === 'disconnected') {
      setConnectStatus('connecting');
      setTimeout(() => {
        setConnectStatus('connected');
      }, 2000);
    } else if (connectStatus === 'connected' && connectHover) {
      setConnectStatus('disconnected');
    }
  };

  useEffect(() => {
    // 刷新评论列表逻辑
  }, []);

  const handleMoveUp = (idx: number) => {
    if (idx === 0) return;
    const arr = [...autoMessages];
    [arr[idx - 1], arr[idx]] = [arr[idx], arr[idx - 1]];
    setAutoMessages(arr);
  };

  const handleMoveDown = (idx: number) => {
    if (idx === autoMessages.length - 1) return;
    const arr = [...autoMessages];
    [arr[idx], arr[idx + 1]] = [arr[idx + 1], arr[idx]];
    setAutoMessages(arr);
  };

  const handleDelete = (idx: number) => {
    const arr = [...autoMessages];
    arr.splice(idx, 1);
    setAutoMessages(arr);
  };

  const handleDeleteProduct = (idx: number) => {
    const arr = [...goodsList];
    arr.splice(idx, 1);
    setGoodsList(arr);
  };

  const {
    platform, setPlatform,
    autoMessages, setAutoMessages,
    goodsList, setGoodsList,
  } = useAutomationStore();

  // 连接与任务状态
  useEffect(() => {
    const fetchStatus = async () => {
      try {
        const res = await axios.get('/api/automation/status');
        const wasConnected = isConnected;
        const newConnected = res.data.is_connected;

        setIsConnected(newConnected);
        setConnectStatus(newConnected ? 'connected' : 'disconnected');
        setAutoMsgTaskRunning(res.data.auto_message_running || false);
        setPopupTaskRunning(res.data.auto_popup_running || false);

        // 检测连接状态变化
        if (wasConnected && !newConnected) {
          console.log('检测到连接断开');
        } else if (!wasConnected && newConnected) {
          console.log('检测到连接建立');
        }
      } catch (e: any) {
        // 静默处理状态轮询错误，避免频繁弹窗
        console.warn('状态轮询失败:', e.message);
        // 如果是网络错误，可能后端服务不可用
        if (e.code === 'ECONNREFUSED' || e.response?.status >= 500) {
          setIsConnected(false);
          setConnectStatus('disconnected');
        }
      }
    };

    fetchStatus();
    const timer = setInterval(fetchStatus, 3000); // 增加轮询间隔，减少服务器压力
    return () => clearInterval(timer);
  }, [isConnected]);

  // 连接控制台
  const handleConnect = async () => {
    setConnecting(true);
    try {
      const response = await axios.post('/api/automation/connect', {
        platform,
        messages: [],
        interval_min: 5,
        interval_max: 10,
      });

      if (response.data.status === 'connected') {
        // 连接成功，显示成功消息
        console.log('连接成功:', response.data.message);
        if (response.data.account_name) {
          console.log('登录账号:', response.data.account_name);
        }
      } else if (response.data.status === 'failed') {
        // 连接失败，显示错误信息
        alert(`连接失败: ${response.data.message}`);
      } else if (response.data.status === 'already_connected') {
        // 已经连接
        console.log('已经连接到控制台');
      }

      // 不要直接 setIsConnected(true)，等 fetchStatus 轮询后端状态
    } catch (e: any) {
      console.error('连接异常:', e);
      const errorMsg = e.response?.data?.message || e.message || '连接失败，请检查后端服务和平台选择';
      alert(`连接失败: ${errorMsg}`);
    } finally {
      setConnecting(false);
    }
  };
  // 断开连接
  const handleDisconnect = async () => {
    setConnecting(true);
    try {
      await axios.post('/api/automation/disconnect');
      setIsConnected(false);
      setConnectStatus('disconnected');
      setAutoMsgTaskRunning(false);
      setPopupTaskRunning(false);
      console.log('已断开连接');
    } catch (e: any) {
      console.error('断开连接异常:', e);
      const errorMsg = e.response?.data?.message || e.message || '断开连接失败';
      alert(`断开失败: ${errorMsg}`);
    } finally {
      setConnecting(false);
    }
  };

  // 自动发言任务对接
  const handleStartAutoMessage = async () => {
    await startAutoMessage({
      platform,
      messages: autoMessages.map((msg) => ({ text: msg.text, pinned: msg.pinned })),
      interval_min: msgIntervalMin,
      interval_max: msgIntervalMax,
      random: msgRandom,
      anti_block: msgAntiBlock,
    });
    setAutoMsgTaskRunning(true);
  };
  const handleStopAutoMessage = async () => {
    await stopAutoMessage();
    setAutoMsgTaskRunning(false);
  };
  const handleOneKeySpam = async () => {
    setLoading(true);
    try {
      await axios.post('/api/automation/auto-message/batch', {
        platform,
        messages: autoMessages.map((msg) => msg.text),
        count: oneKeyCount,
        interval: 0.5, // 可根据需要调整
        random: msgRandom,
        anti_block: msgAntiBlock,
      });
    } finally {
      setLoading(false);
    }
  };

  // 浏览器相关函数已删除 - 系统自动管理

  // 自动弹窗任务对接（loading、禁用、状态同步）
  const handleStartAutoPopup = async () => {
    setPopupLoading(true);
    try {
      await startAutoPopup({
        platform,
        goods_list: goodsList,
        interval_min: popupIntervalMin,
        interval_max: popupIntervalMax,
        random_popup: popupRandom,
      });
      setPopupTaskRunning(true);
    } catch (e) {
      alert('启动弹窗任务失败');
    } finally {
      setPopupLoading(false);
    }
  };
  const handleStopAutoPopup = async () => {
    setPopupLoading(true);
    try {
      await stopAutoPopup();
      setPopupTaskRunning(false);
    } catch (e) {
      alert('停止弹窗任务失败');
    } finally {
      setPopupLoading(false);
    }
  };
  // 自动回复任务函数已删除

  // 状态圆点颜色
  const statusColor = connecting
    ? 'yellow'
    : isConnected
    ? 'green'
    : 'gray';

  // 账号管理函数已删除

  // 商品列表批量导入
  const handleBatchImportGoods = () => {
    const input = prompt('请输入商品序号，多个用逗号、空格或换行分隔');
    if (!input) return;
    const arr = input.split(/[,\s\n]+/).map(x => x.trim()).filter(Boolean);
    const unique = Array.from(new Set([...goodsList, ...arr]));
    setGoodsList(unique);
  };
  // 商品列表清空
  const handleClearGoods = () => {
    if (window.confirm('确定要清空商品列表吗？')) setGoodsList([]);
  };

  return (
    <Box sx={{ width: '100%', p: 0, m: 0, bgcolor: theme.background.main }}>
      <Box sx={{ mb: 0, display: 'flex', flexDirection: 'column', position: 'relative' }}>
        <Box sx={{ position: 'relative', display: 'inline-block', mb: 3 }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 700,
              background: theme.gradient.primary,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
              paddingBottom: '10px',
            }}
          >
            智能场控
          </Typography>
          <Box sx={{ position: 'absolute', left: 0, bottom: 0, width: 56, height: 4, bgcolor: theme.accent, borderRadius: 2 }} />
        </Box>
        <Typography sx={{ color: theme.text.secondary, fontSize: 18, mb: 2 }}>
          智能场控在线帮你管理你的直播间，支持商品弹窗讲解和自动发言功能。
        </Typography>
        <Divider sx={{ width: '100%', mb: 0, borderColor: alpha(theme.text.primary, 0.08) }} />
      </Box>

      {/* 双卡片并排布局 */}
      <Box sx={{ width: '100%', display: 'flex', gap: 3, flexWrap: { xs: 'wrap', md: 'nowrap' }, alignItems: 'stretch' }}>
        {/* 左侧：控制台连接卡片 */}
        <Box
          sx={{
            flex: 1,
            background: 'linear-gradient(135deg, #181f36 0%, #1a223d 100%)',
            borderRadius: 4,
            boxShadow: '0 8px 32px rgba(0,0,0,0.18)',
            p: 1.5,
            minWidth: 0,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
          }}
        >
          {/* 左侧：标题+描述+状态 */}
          <Box sx={{ flex: 1 }}>
            <Typography variant="h6" sx={{ fontWeight: 700, color: '#fff', mb: 1 }}>
              控制台状态
            </Typography>
            <Typography sx={{ color: '#bfc8e2', fontSize: 14, mb: 2 }}>
              查看并管理直播控制台的连接状态
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box
                sx={{
                  width: 10,
                  height: 10,
                  borderRadius: '50%',
                  bgcolor:
                    connectStatus === 'disconnected'
                      ? '#bfc8e2'
                      : connectStatus === 'connecting'
                      ? '#ffb300'
                      : '#4caf50',
                  mr: 1,
                  transition: 'background 0.2s'
                }}
              />
              <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                <Typography sx={{ color: '#bfc8e2', fontSize: 15 }}>
                  {connectStatus === 'disconnected'
                    ? '未连接'
                    : connectStatus === 'connecting'
                    ? '连接中...'
                    : '已连接'}
                </Typography>
                {connectStatus === 'connected' && (
                  <Typography sx={{ color: '#8a94b8', fontSize: 12 }}>
                    平台: {platform === 'douyin' ? '抖音小店' :
                           platform === 'buyin' ? '巨量百应' :
                           platform === 'douyin_group' ? '抖音团购' :
                           platform === 'xhs' ? '小红书' :
                           platform === 'wxvideo' ? '微信视频号' :
                           platform === 'kuaishou' ? '快手小店' :
                           platform === 'taobao' ? '淘宝直播' : platform}
                  </Typography>
                )}
              </Box>
            </Box>
          </Box>
          {/* 右侧：平台选择+按钮 */}
          <Stack direction="row" spacing={2} alignItems="center" sx={{ minWidth: 320, justifyContent: 'flex-end', mt: 2 }}>
            <FormControl sx={{ minWidth: 140 }} size="small">
              <Select
                value={platform}
                onChange={e => setPlatform(e.target.value)}
                sx={{
                  bgcolor: 'rgba(44,54,80,0.7)',
                  color: '#fff',
                  borderRadius: 2,
                  fontWeight: 600,
                  height: 36,
                }}
                disabled={connectStatus === 'connected'}
              >
                <MenuItem value="douyin">抖音小店 (推荐)</MenuItem>
                <MenuItem value="buyin">巨量百应 (推荐)</MenuItem>
                <MenuItem value="douyin_group">抖音团购</MenuItem>
                <MenuItem value="xhs">小红书</MenuItem>
                <MenuItem value="wxvideo">微信视频号</MenuItem>
                <MenuItem value="kuaishou">快手小店</MenuItem>
                <MenuItem value="taobao">淘宝直播</MenuItem>
              </Select>
            </FormControl>
            <Button
              variant="contained"
              startIcon={
                connectStatus === 'disconnected' ? <PublicIcon /> :
                connectStatus === 'connecting' ? <PublicIcon /> :
                connectStatus === 'connected' && connectHover ? <CloseIcon /> :
                connectStatus === 'connected' ? <CheckIcon /> : null
              }
              sx={{
                bgcolor:
                  connectStatus === 'disconnected' ? '#151c32' :
                  connectStatus === 'connecting' ? '#868d98' :
                  connectStatus === 'connected' && connectHover ? '#f44336' :
                  '#f5f6fa',
                color:
                  connectStatus === 'connected' && connectHover ? '#fff' :
                  connectStatus === 'connected' ? '#222' : '#fff',
                fontWeight: 700,
                px: 4,
                borderRadius: 2,
                height: 36,
                minWidth: 150,
                fontSize: 15,
                boxShadow: 'none',
                '&:hover': {
                  bgcolor:
                    connectStatus === 'disconnected' ? '#232842' :
                    connectStatus === 'connecting' ? '#868d98' :
                    connectStatus === 'connected' ? '#f44336' : undefined,
                  color: connectStatus === 'connected' ? '#fff' : undefined,
                },
                transition: 'all 0.2s',
              }}
              onClick={
                connectStatus === 'connected' && connectHover
                  ? handleDisconnect
                  : handleConnect
              }
              onMouseEnter={() => setConnectHover(true)}
              onMouseLeave={() => setConnectHover(false)}
              disabled={connectStatus === 'connecting'}
            >
              {connectStatus === 'disconnected'
                ? '连接控制台'
                : connectStatus === 'connecting'
                ? '连接中...'
                : connectStatus === 'connected' && connectHover
                ? '断开连接'
                : '已连接'}
            </Button>
          </Stack>
        </Box>
        {/* 右侧：使用说明卡片 */}
        <Box
          sx={{
            flex: 1,
            bgcolor: 'rgba(44,54,80,0.7)',
            borderRadius: 4,
            boxShadow: '0 8px 32px rgba(0,0,0,0.18)',
            p: 1.5,
            minWidth: 0,
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
          }}
        >
          <Typography variant="h6" sx={{ fontWeight: 700, color: '#fff', mb: 1 }}>
            {isConnected ? '连接信息' : '使用说明'}
          </Typography>
          <Typography sx={{ color: '#bfc8e2', fontSize: 14, mb: 2 }}>
            {isConnected ? '当前连接状态和平台信息' : '了解如何使用直播控制台'}
          </Typography>
          <Box>
            {isConnected ? (
              // 连接状态信息
              [
                `平台: ${platform === 'douyin' ? '抖音小店' :
                        platform === 'buyin' ? '巨量百应' :
                        platform === 'douyin_group' ? '抖音团购' :
                        platform === 'xhs' ? '小红书' :
                        platform === 'wxvideo' ? '微信视频号' :
                        platform === 'kuaishou' ? '快手小店' :
                        platform === 'taobao' ? '淘宝直播' : platform}`,
                `自动发言: ${autoMsgTaskRunning ? '运行中' : '已停止'}`,
                `自动弹窗: ${popupTaskRunning ? '运行中' : '已停止'}`
              ].map((text, idx) => (
                <Box key={idx} sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                  <Box sx={{
                    width: 8, height: 8, borderRadius: '50%',
                    bgcolor: idx === 0 ? '#4caf50' :
                             text.includes('运行中') ? '#4caf50' : '#bfc8e2',
                    mr: 1.5, mt: 0.8
                  }} />
                  <Typography sx={{ color: '#bfc8e2', fontSize: 14, lineHeight: 1.7 }}>
                    {text}
                  </Typography>
                </Box>
              ))
            ) : (
              // 使用说明
              [
                '选择对应的直播平台，推荐使用抖音小店或巨量百应',
                '点击"连接控制台"按钮，系统会自动打开浏览器',
                '在浏览器中完成登录，系统会自动跳转到直播控制台',
                '连接成功后，即可使用自动发言、弹窗等功能'
              ].map((text, idx) => (
                <Box key={idx} sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                  <Box sx={{
                    width: 22, height: 22, borderRadius: '50%',
                    bgcolor: '#232842', color: '#4f7df9',
                    fontWeight: 700, fontSize: 13, display: 'flex',
                    alignItems: 'center', justifyContent: 'center', mr: 1.5
                  }}>
                    {idx + 1}
                  </Box>
                  <Typography sx={{ color: '#bfc8e2', fontSize: 14, lineHeight: 1.7 }}>
                    {text}
                  </Typography>
                </Box>
              ))
            )}
          </Box>
        </Box>
      </Box>
      {/* 新增Tab分页 */}
      <Box sx={{ width: '100%', mt: 3 }}>
        <Tabs
          value={tabValue}
          onChange={(_, newValue) => setTabValue(newValue)}
          sx={{
            minHeight: 40,
            bgcolor: theme.background.paper,
            borderRadius: 2,
            '& .MuiTab-root': {
              fontSize: 16,
              fontWeight: 600,
              color: theme.text.secondary,
              minHeight: 40,
              px: 4,
              borderRadius: 2,
              mr: 2,
              textTransform: 'none',
              '&.Mui-selected': {
                color: theme.accent,
                bgcolor: alpha(theme.accent, 0.08),
              },
            },
            '& .MuiTabs-indicator': {
              height: 3,
              background: theme.gradient.accent,
              borderRadius: 2,
            },
          }}
        >
          <Tab label="自动发言" />
          <Tab label="自动弹窗" />
        </Tabs>
        {/* Tab内容区 */}
        <Box sx={{ mt: 3, minHeight: 220, color: theme.text.secondary, fontSize: 16 }}>
          {/* 自动发言Tab */}
          {tabValue === 0 && (
            <Box>
              {/* 头部：左标题右按钮 */}
              <Box display="flex" justifyContent="space-between" alignItems="flex-end" mb={2}>
                <Box>
                  <Typography sx={{ color: theme.text.primary, fontSize: 16 }}>配置自动发送消息的规则</Typography>
                </Box>
                <Button
                  variant="contained"
                  startIcon={autoMsgTaskRunning ? <StopIcon /> : <PlayArrowIcon />}
                  sx={{
                    ...advancedButtonStyle,
                    bgcolor: autoMsgTaskRunning ? theme.error : undefined,
                    background: autoMsgTaskRunning ? theme.error : theme.gradient.primary,
                    '&:hover': { bgcolor: autoMsgTaskRunning ? alpha(theme.error, 0.9) : theme.gradient.secondary },
                    minWidth: 0,
                  }}
                  onClick={autoMsgTaskRunning ? handleStopAutoMessage : handleStartAutoMessage}
                >
                  {autoMsgTaskRunning ? '停止任务' : '开始任务'}
                </Button>
              </Box>
              <Box display="flex" flexDirection="column" gap={3}>
                {/* 消息列表卡片 */}
                <Box sx={{ ...advancedCardStyle, ...advancedCardContent, bgcolor: alpha(theme.background.paper, 0.92) }}>
                  <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
                    <Box>
                      <Typography sx={{ fontWeight: 700, color: theme.text.primary, fontSize: 16 }}>消息列表</Typography>
                      <Typography sx={{ color: theme.text.secondary, fontSize: 14 }}>添加需要自动发送的消息内容</Typography>
                    </Box>
                    <Button
                      variant="outlined"
                      size="small"
                      sx={{
                        borderRadius: 2,
                        minWidth: 0,
                        px: 2,
                        fontWeight: 500,
                        bgcolor: alpha(theme.background.light, 0.7),
                        color: theme.primary,
                        border: `1px solid ${alpha(theme.primary, 0.15)}`,
                        '&:hover': { bgcolor: alpha(theme.primary, 0.08), borderColor: theme.primary },
                      }}
                      onClick={() => setAutoMessages([...autoMessages, { text: '', pinned: false }])}
                      startIcon={<AddIcon />}
                    >
                      添加消息
                    </Button>
                  </Box>
                  <Divider sx={{ mb: 2, borderColor: alpha(theme.text.primary, 0.08) }} />
                  {autoMessages.length === 0 && (
                    <Typography sx={{ color: '#bfc8e2', fontSize: 15, textAlign: 'center', py: 2 }}>暂无消息，请点击"添加消息"</Typography>
                  )}
                  {autoMessages.map((msg: AutoMessage, idx: number) => (
                    <Box
                      key={idx}
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        bgcolor: msg.pinned ? alpha(theme.accent, 0.13) : alpha(theme.background.light, 0.7),
                        borderRadius: 2,
                        boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
                        mb: 1.2,
                        px: 2,
                        py: 1.2,
                        transition: 'box-shadow 0.2s, background 0.2s',
                        '&:hover': {
                          boxShadow: '0 4px 16px rgba(46,192,255,0.10)',
                          bgcolor: msg.pinned ? alpha(theme.accent, 0.18) : alpha(theme.background.light, 0.85),
                        }
                      }}
                    >
                      <Typography sx={{ color: msg.pinned ? theme.accent : theme.text.secondary, fontWeight: 700, mr: 1 }}>{idx + 1}.</Typography>
                      <TextField
                        value={msg.text}
                        onChange={e => {
                          const arr = [...autoMessages];
                          arr[idx].text = e.target.value;
                          setAutoMessages(arr);
                        }}
                        fullWidth
                        size="small"
                        placeholder="输入消息内容"
                        inputProps={{ maxLength: 50 }}
                        sx={{
                          mr: 2,
                          '& .MuiOutlinedInput-root': {
                            bgcolor: 'transparent',
                            color: theme.text.primary,
                            fontSize: '1rem',
                            borderRadius: '8px',
                            '& fieldset': {
                              borderColor: alpha(theme.primary, 0.10),
                            },
                            '&:hover fieldset': {
                              borderColor: alpha(theme.primary, 0.22),
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: theme.primary,
                              borderWidth: 1.5,
                            },
                            '& input::placeholder': {
                              color: theme.text.muted,
                              opacity: 1,
                            },
                            '& input': {
                              color: theme.text.primary,
                            },
                          },
                        }}
                      />
                      <Box sx={{ display: 'flex', alignItems: 'center', ml: 2, minWidth: 0 }}>
                        <Checkbox
                          checked={msg.pinned}
                          onChange={e => {
                            const arr = [...autoMessages];
                            arr[idx].pinned = e.target.checked;
                            setAutoMessages(arr);
                          }}
                          sx={{ color: msg.pinned ? theme.accent : theme.text.secondary, p: 0 }}
                        />
                        <Typography sx={{ color: theme.text.secondary, fontSize: 14, ml: 0.5, lineHeight: 1, whiteSpace: 'nowrap' }}>
                          置顶此消息
                        </Typography>
                      </Box>
                      <IconButton onClick={() => handleDelete(idx)} sx={{ ...iconBtnDeleteStyle, ml: 2 }}><DeleteIcon /></IconButton>
                    </Box>
                  ))}
                </Box>
                {/* 发送设置卡片 */}
                <Box sx={{ ...advancedCardStyle, ...advancedCardContent, bgcolor: alpha(theme.background.paper, 0.92) }}>
                  <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
                    <Box>
                      <Typography sx={{ fontWeight: 700, color: theme.text.primary, fontSize: 16 }}>发送设置</Typography>
                      <Typography sx={{ color: theme.text.secondary, fontSize: 14 }}>配置消息发送的相关选项</Typography>
                    </Box>
                    <FormControlLabel
                      control={<Switch checked={msgRandom} onChange={e => setMsgRandom(e.target.checked)} />}
                      label={<Typography sx={{ color: theme.text.secondary, fontSize: 14 }}>随机发送</Typography>}
                      labelPlacement="start"
                      sx={{ ml: 0, mr: 0 }}
                    />
                  </Box>
                  <Divider sx={{ mb: 2, borderColor: alpha(theme.text.primary, 0.08) }} />
                  <Box display="flex" alignItems="center" gap={2} mb={1}>
                    <Typography sx={{ color: theme.text.secondary, fontSize: 14, minWidth: 80 }}>发送间隔（秒）</Typography>
                    <TextField
                      type="number"
                      value={msgIntervalMin}
                      onChange={e => setMsgIntervalMin(Number(e.target.value))}
                      size="small"
                      sx={{
                        width: 70,
                        mr: 1,
                        '& .MuiOutlinedInput-root': {
                          color: theme.text.primary,
                          fontSize: '1rem',
                          borderRadius: '8px',
                          bgcolor: 'transparent',
                          '& fieldset': {
                            borderColor: alpha(theme.primary, 0.10),
                          },
                          '&:hover fieldset': {
                            borderColor: alpha(theme.primary, 0.22),
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: theme.primary,
                            borderWidth: 1.5,
                          },
                          '& input': {
                            color: theme.text.primary,
                          },
                          '& input::placeholder': {
                            color: theme.text.muted,
                            opacity: 1,
                          },
                        },
                      }}
                    />
                    <Typography sx={{ color: theme.text.secondary, fontSize: 14 }}>至</Typography>
                    <TextField
                      type="number"
                      value={msgIntervalMax}
                      onChange={e => setMsgIntervalMax(Number(e.target.value))}
                      size="small"
                      sx={{
                        width: 70,
                        ml: 1,
                        '& .MuiOutlinedInput-root': {
                          color: theme.text.primary,
                          fontSize: '1rem',
                          borderRadius: '8px',
                          bgcolor: 'transparent',
                          '& fieldset': {
                            borderColor: alpha(theme.primary, 0.10),
                          },
                          '&:hover fieldset': {
                            borderColor: alpha(theme.primary, 0.22),
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: theme.primary,
                            borderWidth: 1.5,
                          },
                          '& input': {
                            color: theme.text.primary,
                          },
                          '& input::placeholder': {
                            color: theme.text.muted,
                            opacity: 1,
                          },
                        },
                      }}
                    />
                    <Typography sx={{ color: theme.text.secondary, fontSize: 14 }}>秒</Typography>
                  </Box>
                  <Typography sx={{ color: theme.text.secondary, fontSize: 13, mb: 2 }}>系统将在设定的时间区间内随机选择发送时机</Typography>
                  <FormControlLabel
                    control={<Switch checked={msgAntiBlock} onChange={e => setMsgAntiBlock(e.target.checked)} />}
                    label={<Typography sx={{ color: theme.text.secondary, fontSize: 14 }}>插入随机空格</Typography>}
                    sx={{ ml: 0 }}
                  />
                </Box>
                {/* 一键刷屏卡片 */}
                <Box sx={{ ...advancedCardStyle, ...advancedCardContent, bgcolor: alpha(theme.background.paper, 0.92) }}>
                  <Box display="flex" alignItems="center" gap={2}>
                    <Box flex={1}>
                      <Typography sx={{ fontWeight: 700, color: theme.text.primary, fontSize: 16 }}>一键刷屏</Typography>
                      <Typography sx={{ color: theme.text.secondary, fontSize: 14 }}>连续发送多条评论</Typography>
                    </Box>
                    <TextField size="small" type="number" value={oneKeyCount} onChange={e => setOneKeyCount(Number(e.target.value) || 1)} sx={{ width: 80, bgcolor: alpha(theme.background.light, 0.7), borderRadius: 1, mr: 2,
                      '& .MuiOutlinedInput-root': {
                        color: '#fff',
                        '& input': {
                          color: '#fff',
                        },
                        '& fieldset': {
                          borderColor: alpha(theme.primary, 0.10),
                        },
                        '&:hover fieldset': {
                          borderColor: alpha(theme.primary, 0.22),
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: theme.primary,
                          borderWidth: 1.5,
                        },
                        '& input::placeholder': {
                          color: theme.text.muted,
                          opacity: 1,
                        },
                      },
                    }} inputProps={{ min: 1 }} placeholder="刷屏条数" />
                    <Button variant="contained" sx={{ borderRadius: 2, bgcolor: theme.primary, color: '#fff', fontWeight: 600, px: 3, '&:hover': { bgcolor: theme.secondary } }} onClick={handleOneKeySpam} disabled={loading}>一键刷屏</Button>
                  </Box>
                </Box>
              </Box>
            </Box>
          )}
          {/* 自动弹窗Tab */}
          {tabValue === 1 && (
            <Box>
              {/* 头部：左标题右按钮 */}
              <Box display="flex" justifyContent="space-between" alignItems="flex-end" mb={2}>
                <Box>
                  <Typography sx={{ color: theme.text.primary, fontSize: 16 }}>配置自动弹出商品的规则</Typography>
                </Box>
                <Button
                  variant="contained"
                  startIcon={popupTaskRunning ? <StopIcon /> : <PlayArrowIcon />}
                  sx={{
                    ...advancedButtonStyle,
                    bgcolor: popupTaskRunning ? theme.error : theme.primary,
                    background: popupTaskRunning ? theme.error : theme.gradient.primary,
                    '&:hover': { bgcolor: popupTaskRunning ? alpha(theme.error, 0.9) : theme.gradient.secondary },
                    minWidth: 0,
                  }}
                  onClick={popupTaskRunning ? handleStopAutoPopup : handleStartAutoPopup}
                  disabled={popupLoading || (popupTaskRunning && popupLoading)}
                >
                  {popupLoading ? '处理中...' : popupTaskRunning ? '停止任务' : '开始任务'}
                </Button>
              </Box>
              {/* 卡片区 */}
              <Box display="flex" flexDirection="column" gap={3}>
                {/* 商品列表卡片（无拖拽，仅普通列表） */}
                <Box sx={{ ...advancedCardStyle, ...advancedCardContent, bgcolor: alpha(theme.background.paper, 0.92) }}>
                  <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
                    <Box>
                      <Typography sx={{ fontWeight: 700, color: '#fff', fontSize: 16 }}>商品列表</Typography>
                      <Typography sx={{ color: theme.text.secondary, fontSize: 14 }}>添加需要自动弹出的商品序号</Typography>
                    </Box>
                    <Box display="flex" gap={1}>
                      <Button variant="outlined" size="small" sx={{ borderRadius: 2, minWidth: 0, px: 2, fontWeight: 500 }} startIcon={<AddIcon />} onClick={() => {
                        let next = 1;
                        if (goodsList.length > 0) {
                          const nums = goodsList.map(x => parseInt(x, 10)).filter(x => !isNaN(x));
                          next = nums.length > 0 ? Math.max(...nums) + 1 : goodsList.length + 1;
                        }
                        setGoodsList([...goodsList, String(next)]);
                      }}>添加</Button>
                      <Button variant="outlined" size="small" sx={{ borderRadius: 2, minWidth: 0, px: 2, fontWeight: 500 }} onClick={handleBatchImportGoods}>批量导入</Button>
                      <Button variant="outlined" size="small" sx={{ borderRadius: 2, minWidth: 0, px: 2, fontWeight: 500, color: theme.error, borderColor: theme.error }} onClick={handleClearGoods}>清空</Button>
                    </Box>
                    <IconButton onClick={() => setGoodsListExpanded(v => !v)} sx={{ color: theme.text.secondary }}>
                      {goodsListExpanded ? <ExpandLess /> : <ExpandMore />}
                    </IconButton>
                  </Box>
                  <Collapse in={goodsListExpanded}>
                    <Box display="flex" flexDirection="column" gap={2} maxWidth={180}>
                      {goodsList.map((good: string, idx: number) => (
                        <Box
                          key={good + idx}
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            bgcolor: alpha(theme.background.light, 0.7),
                            borderRadius: 2,
                            boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
                            mb: 1.2,
                            px: 2,
                            py: 1.2,
                            transition: 'box-shadow 0.2s, background 0.2s',
                            '&:hover': {
                              boxShadow: '0 4px 16px rgba(46,192,255,0.10)',
                              bgcolor: alpha(theme.background.light, 0.85),
                            }
                          }}
                        >
                          <TextField
                            value={good}
                            onChange={e => {
                              const arr = [...goodsList];
                              arr[idx] = e.target.value.replace(/[^\d]/g, ''); // 只允许数字
                              setGoodsList(Array.from(new Set(arr)));
                            }}
                            fullWidth
                            size="small"
                            placeholder="输入商品序号"
                            sx={{
                              mr: 2,
                              '& .MuiOutlinedInput-root': {
                                bgcolor: 'transparent',
                                color: theme.text.primary,
                                fontSize: '1rem',
                                borderRadius: '8px',
                                '& fieldset': {
                                  borderColor: alpha(theme.primary, 0.10),
                                },
                                '&:hover fieldset': {
                                  borderColor: alpha(theme.primary, 0.22),
                                },
                                '&.Mui-focused fieldset': {
                                  borderColor: theme.primary,
                                  borderWidth: 1.5,
                                },
                                '& input::placeholder': {
                                  color: theme.text.muted,
                                  opacity: 1,
                                },
                                '& input': {
                                  color: theme.text.primary,
                                },
                              },
                            }}
                          />
                          <IconButton onClick={() => handleDeleteProduct(idx)} sx={{ ...iconBtnDeleteStyle, ml: 2 }}><DeleteIcon /></IconButton>
                        </Box>
                      ))}
                    </Box>
                  </Collapse>
                </Box>
                {/* 弹窗设置卡片 */}
                <Box sx={{ ...advancedCardStyle, ...advancedCardContent, bgcolor: alpha(theme.background.paper, 0.92) }}>
                  <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
                    <Box>
                      <Typography sx={{ fontWeight: 700, color: theme.text.primary, fontSize: 16 }}>弹窗设置</Typography>
                      <Typography sx={{ color: theme.text.secondary, fontSize: 14 }}>配置商品弹窗的相关选项</Typography>
                    </Box>
                    <FormControlLabel
                      control={<Switch checked={popupRandom} onChange={e => setPopupRandom(e.target.checked)} />}
                      label={<Typography sx={{ color: theme.text.secondary, fontSize: 14 }}>随机弹窗</Typography>}
                      labelPlacement="start"
                      sx={{ ml: 0, mr: 0 }}
                    />
                  </Box>
                  <Divider sx={{ mb: 2, borderColor: alpha(theme.text.primary, 0.08) }} />
                  <Box display="flex" alignItems="center" gap={2} mb={1}>
                    <Typography sx={{ color: theme.text.secondary, fontSize: 14, minWidth: 80 }}>弹窗间隔（秒）</Typography>
                    <TextField
                      type="number"
                      value={popupIntervalMin}
                      onChange={e => setPopupIntervalMin(Number(e.target.value))}
                      size="small"
                      sx={{
                        width: 70,
                        mr: 1,
                        '& .MuiOutlinedInput-root': {
                          color: theme.text.primary,
                          fontSize: '1rem',
                          borderRadius: '8px',
                          bgcolor: 'transparent',
                          '& fieldset': {
                            borderColor: alpha(theme.primary, 0.10),
                          },
                          '&:hover fieldset': {
                            borderColor: alpha(theme.primary, 0.22),
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: theme.primary,
                            borderWidth: 1.5,
                          },
                          '& input': {
                            color: theme.text.primary,
                          },
                          '& input::placeholder': {
                            color: theme.text.muted,
                            opacity: 1,
                          },
                        },
                      }}
                    />
                    <Typography sx={{ color: theme.text.secondary, fontSize: 14 }}>至</Typography>
                    <TextField
                      type="number"
                      value={popupIntervalMax}
                      onChange={e => setPopupIntervalMax(Number(e.target.value))}
                      size="small"
                      sx={{
                        width: 70,
                        ml: 1,
                        '& .MuiOutlinedInput-root': {
                          color: theme.text.primary,
                          fontSize: '1rem',
                          borderRadius: '8px',
                          bgcolor: 'transparent',
                          '& fieldset': {
                            borderColor: alpha(theme.primary, 0.10),
                          },
                          '&:hover fieldset': {
                            borderColor: alpha(theme.primary, 0.22),
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: theme.primary,
                            borderWidth: 1.5,
                          },
                          '& input': {
                            color: theme.text.primary,
                          },
                          '& input::placeholder': {
                            color: theme.text.muted,
                            opacity: 1,
                          },
                        },
                      }}
                    />
                    <Typography sx={{ color: theme.text.secondary, fontSize: 14 }}>秒</Typography>
                  </Box>
                  <Typography sx={{ color: theme.text.secondary, fontSize: 13, mb: 2 }}>系统将在设定的时间区间内随机选择弹窗时机</Typography>
                </Box>
              </Box>
            </Box>
          )}
          {/* 自动回复Tab已删除 */}




          {/* 浏览器设置Tab已删除 - 系统自动管理浏览器 */}
        </Box>
      </Box>
      {/* 自动回复设置弹窗已删除 */}






      {/* 账号管理UI已删除 - 系统简化 */}
    </Box>
  );
};

export { SmartControlWorkspace }; 