import React, { useState, useEffect, useRef, useCallback } from 'react';
import { 
  Box, 
  Card, 
  CardContent, 
  <PERSON><PERSON>graphy, 
  Stack,
  TextField,
  Button,
  Switch,
  FormControlLabel,
  Chip,
  Grid,
  Slider,
  alpha,
  IconButton,
  Tooltip,
  Alert,
  InputAdornment,
  Select,
  MenuItem,
  CircularProgress,
  Collapse,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Menu,
  ListItemIcon,
  ListItemText,
  Popover,
  Badge,
  Divider,
  Tabs,
  Tab,
} from '@mui/material';
import { 
  PlayArrow, 
  Stop, 
  Mic, 
  VideoCall,
  TextSnippet,
  Send,
  Info,
  Save,
  GraphicEq,
  VolumeUp,
  MoreVert,
  SmartToy,
  KeyboardArrowUp,
  KeyboardArrowDown,
  VolumeOff,
  Download,
  Upload,
  Settings,
  Search,
  CheckCircle,
  Close,
  PlayCircle,
  Add,
  Circle,
  LocalOffer,
  Label,
  FilterList,
} from '@mui/icons-material';
import { Model, TTSOptions } from '../../hooks/useTTS';
import { useLiveStream } from '../../hooks/useLiveStream';
import { useIndexTTS } from '../../hooks/useIndexTTS';
import { useWorkspaceState } from '../../hooks/useWorkspaceState';
import { useAIModel } from '../../contexts/AIModelContext';
import { LiveReplyContext } from '../../contexts/LiveReplyContext';
import { useLiveConnection } from '../../contexts/LiveConnectionContext';
import { TagStorage, Tag } from '../../utils/tagStorage';
import { message } from 'antd';

// 定义userGreeting的类型
interface UserGreetingType {
  welcome: boolean;
  reply: boolean;
  time: boolean;
  count: boolean;
}

interface LiveStreamWorkspaceProps {
  models: Model[];
  options: TTSOptions;
  setInsertLiveReply: (fn: (text: string, type?: 'welcome' | 'time' | 'count' | 'broadcast' | 'interactionReply') => void) => void;
}

// 定义主题颜色
const theme = {
  primary: '#4f7df9', // 主色调
  secondary: '#7a6cf7', // 辅助色
  accent: '#2ec0ff',   // 强调色
  gradient: {
    primary: 'linear-gradient(135deg, #4f7df9 0%, #7a6cf7 100%)',
    secondary: 'linear-gradient(135deg, #7a6cf7 0%, #9b6cf7 100%)',
    accent: 'linear-gradient(135deg, #2ec0ff 0%, #4f7df9 100%)'
  },
  background: {
    main: '#1a1f36', // 主背景色
    paper: '#232842', // 卡片背景色
    light: '#2a3153', // 浅背景色
    dark: '#151b30',  // 深背景色
    highlight: '#323c6b' // 高亮背景色
  },
  text: {
    primary: '#ffffff',  // 主文字色
    secondary: '#c9d1f0', // 次要文字色
    muted: '#8a94b8'      // 弱化文字色
  },
  success: '#4caf50',
  warning: '#ff9800',
  error: '#f44336',
  border: '#373e59' // 边框色
  };

  // 高级卡片样式
  const advancedCardStyle = {
  backgroundColor: alpha(theme.background.paper, 0.8),
  backdropFilter: 'blur(10px)',
    borderRadius: '12px',
  border: `1px solid ${alpha(theme.border, 0.2)}`,
  boxShadow: `0 8px 32px ${alpha(theme.background.dark, 0.5)}`,
  transition: 'all 0.3s ease',
    '&:hover': {
    boxShadow: `0 12px 40px ${alpha(theme.background.dark, 0.6)}`,
    transform: 'translateY(-2px)',
    }
  };

// 高级卡片内容样式
  const advancedCardContent = {
    p: 3,
    '&:last-child': {
      pb: 3
    }
  };

  // 高级按钮样式
  const advancedButtonStyle = {
  background: theme.gradient.primary,
  color: theme.text.primary,
  fontWeight: 600,
  borderRadius: '10px',
  padding: '10px 24px',
  boxShadow: `0 4px 15px ${alpha(theme.primary, 0.3)}`,
    transition: 'all 0.3s ease',
    '&:hover': {
    background: theme.gradient.secondary,
    boxShadow: `0 6px 20px ${alpha(theme.secondary, 0.4)}`,
    transform: 'translateY(-2px)',
    },
    '&:active': {
    transform: 'translateY(0)',
    },
    '&.Mui-disabled': {
    background: alpha(theme.background.dark, 0.5),
    color: alpha(theme.text.primary, 0.3),
  }
};

export const LiveStreamWorkspace: React.FC<LiveStreamWorkspaceProps> = ({
  models,
  options,
  setInsertLiveReply
}) => {
  // 预缓存条数设置，必须在组件体最顶部
  const [bufferSize, setBufferSize] = useWorkspaceState<number>('live-bufferSize', 3);
  // 使用useWorkspaceState替代useState
  const [currentModel, setCurrentModel] = useWorkspaceState<Model | null>('live-currentModel', null);
  // 添加助播音色状态
  const [assistantModel, setAssistantModel] = useWorkspaceState<Model | null>('live-assistantModel', null);
  // 获取直播连接上下文
  const { latestUsername, audienceCount } = useLiveConnection();

  const [speed, setSpeed] = useWorkspaceState<number>('live-speed', options.speed || 1);

  // 添加变量说明对话框状态
  const [variableDialogOpen, setVariableDialogOpen] = useState(false);

  // 添加模型选择对话框状态
  const [modelSelectDialogOpen, setModelSelectDialogOpen] = useState(false);
  const [selectingModelType, setSelectingModelType] = useState<'main' | 'assistant'>('main');
  const [modelSearchTerm, setModelSearchTerm] = useState('');
  
  // 标签过滤菜单
  const [tagFilterMenuAnchorEl, setTagFilterMenuAnchorEl] = useState<HTMLElement | null>(null);
  
  // 音色轮换设置对话框状态
  const [rotationSettingsDialogOpen, setRotationSettingsDialogOpen] = useState(false);
  
  // 添加标签系统相关状态
  const [tags, setTags] = useState<Tag[]>([]);
  const [modelTags, setModelTags] = useState<{[modelId: string]: string[]}>({});
  const [selectedTagIds, setSelectedTagIds] = useState<string[]>([]);
  const [filterMenuAnchorEl, setFilterMenuAnchorEl] = useState<HTMLElement | null>(null);
  const [activeTagFilters, setActiveTagFilters] = useState<string[]>([]);
  const [isCreateTagDialogOpen, setIsCreateTagDialogOpen] = useState(false);
  const [newTagName, setNewTagName] = useState('');
  const [newTagColor, setNewTagColor] = useState('#4f7df9');
  const [selectedModelForTag, setSelectedModelForTag] = useState<string | null>(null);
  const [tagMenuAnchorEl, setTagMenuAnchorEl] = useState<HTMLElement | null>(null);
  const [tagDialogOpen, setTagDialogOpen] = useState(false);

  // 标签数量限制
  const MAX_TAGS = 50;
  
  // 获取当前语速，考虑随机语速设置
  const getCurrentSpeed = useCallback(() => {
    // TODO: Add logic for random speed if enabled
    return speed;
  }, [speed]); // 依赖项包含 speed 状态

  // 添加打开模型选择对话框的函数
  const openModelSelectDialog = (type: 'main' | 'assistant') => {
    setSelectingModelType(type);
    setModelSearchTerm('');
    
    // 重新加载标签数据
    const savedTags = TagStorage.getTags();
    const savedModelTags = TagStorage.getModelTags();
    
    if(savedTags.length > 0) {
      setTags(savedTags);
    }
    
    if(Object.keys(savedModelTags).length > 0) {
      setModelTags(savedModelTags);
    }
    
    setModelSelectDialogOpen(true);
  };
  
  // 过滤模型列表
  const filteredModels = models.filter(model => {
    // 先按搜索词过滤
    const matchesSearch = !modelSearchTerm || 
      model.name.toLowerCase().includes(modelSearchTerm.toLowerCase()) ||
      model.id.toLowerCase().includes(modelSearchTerm.toLowerCase());
    
    // 如果有标签筛选，则检查模型是否包含所有选中的标签
    if (activeTagFilters.length > 0) {
      const modelTagIds = modelTags[model.id] || [];
      return matchesSearch && activeTagFilters.every(tagId => modelTagIds.includes(tagId));
    }
    
    return matchesSearch;
  });

  // 监听 models 变化并设置默认模型
  useEffect(() => {
    if (models.length > 0 && !currentModel) {
      const savedModelId = localStorage.getItem('selectedModelId');
      const defaultModel = models.find(model => model.id === savedModelId) || models[0];
      setCurrentModel(defaultModel);
      
      // 设置默认助播音色
      const savedAssistantModelId = localStorage.getItem('selectedAssistantModelId');
      const defaultAssistantModel = models.find(model => model.id === savedAssistantModelId) || models[1] || models[0];
      setAssistantModel(defaultAssistantModel);
    }
  }, [models, currentModel, setCurrentModel, setAssistantModel]);

  const {
    isLive,
    isPublicScreenConnected,
    scriptContent,
    setScriptContent,
    togglePublicScreen
  } = useLiveStream(options);

  // 添加公屏连接状态
  const [publicScreenConnected, setPublicScreenConnected] = useState(false);

  // 监听公屏连接状态变化
  useEffect(() => {
    // 监听公屏连接状态变化
    const handleLiveConnectionChange = (event: CustomEvent<boolean>) => {
      setPublicScreenConnected(event.detail);
    };

    // 添加事件监听
    window.addEventListener('live-connection-change', handleLiveConnectionChange as EventListener);

    // 组件卸载时移除事件监听
    return () => {
      window.removeEventListener('live-connection-change', handleLiveConnectionChange as EventListener);
    };
  }, []);
  


  // 监听模型变化并保存
  useEffect(() => {
    if (currentModel) {
      localStorage.setItem('selectedModelId', currentModel.id);
    }
    if (assistantModel) {
      localStorage.setItem('selectedAssistantModelId', assistantModel.id);
    }
  }, [currentModel, assistantModel]);

  // 监听语速变化并保存
  useEffect(() => {
    localStorage.setItem('selectedSpeed', (speed ?? 1).toString());
  }, [speed]);

  // 注意：scriptContent的保存现在由useLiveStream自动处理，无需重复保存

  // 修改语速设置函数
  const handleSpeedChange = (newSpeed: number) => {
    setSpeed(newSpeed);
    options.speed = newSpeed;
  };

  // 使用useWorkspaceState替代useState管理工作区内部状态
  const [isLiveStreaming, setIsLiveStreaming] = useWorkspaceState<boolean>('live-isLiveStreaming', false);
  const [isLoading, setIsLoading] = useWorkspaceState<boolean>('live-isLoading', false);
  const [aiMode, setAiMode] = useWorkspaceState<'full' | 'replyOnly' | 'off'>('live-aiMode', 'full');
  // 使用localStorage直接管理userGreeting状态，避免useWorkspaceState的sessionStorage冲突
  const [userGreeting, setUserGreeting] = useState<UserGreetingType>(() => {
    try {
      const saved = localStorage.getItem('live-userGreeting');
      if (saved) {
        return JSON.parse(saved);
      }
    } catch (error) {
      console.error('读取用户接待设置失败:', error);
    }
    return {
      welcome: false,
      reply: true, // 默认启用互动回复播报
      time: false,
      count: false // 新增人数开关
    };
  });

  // 添加模型轮换设置状态
  const [modelRotation, setModelRotation] = useWorkspaceState<{
    enabled: boolean;
    mode: 'timer' | 'random';
    interval: number;
    minInterval: number;
    maxInterval: number;
    tagIds: string[];
    lastSwitchTime: number;
    nextSwitchTime: number;
  }>('live-modelRotation', {
    enabled: false,         // 是否启用音色轮换
    mode: 'timer',          // 轮换模式: 'timer' (固定时间) 或 'random' (随机时间)
    interval: 60,           // 固定轮换时间间隔(秒)
    minInterval: 30,        // 随机轮换最小时间间隔(秒)
    maxInterval: 120,       // 随机轮换最大时间间隔(秒)
    tagIds: [],             // 限制在特定标签组内轮换
    lastSwitchTime: 0,      // 上次切换时间戳
    nextSwitchTime: 0,      // 下次切换时间戳
  });

  // 模型轮换实时数据，不需要持久化，只在运行时使用
  const [rotationState, setRotationState] = useState({
    timerId: null as NodeJS.Timeout | null,
    remainingSeconds: 0,    // 距离下次切换剩余时间
    availableModels: [] as Model[], // 当前标签过滤后可用的模型列表
  });

  // 添加useEffect确保初始化后设置正确的值
  useEffect(() => {
    // 确保reply设置为true
    if (!userGreeting.reply) {
      setUserGreeting((prev: UserGreetingType) => ({
        ...prev,
        reply: true
      }));
    }
  }, []);

  const [selectedTags, setSelectedTags] = useWorkspaceState('live-selectedTags', {
    welcome: true,
    reply: false,
    time: false,
    generalize: false
  });

  // 使用useWorkspaceState代替useState管理脚本展开状态
  const [isScriptExpanded, setIsScriptExpanded] = useWorkspaceState<boolean>('live-isScriptExpanded', true);

  const {
    audioState,
    isLoading: isAudioLoading,
    generateAudio,
    pauseAudio,
    resumeAudio,
    setProgress,
    setAudioState,
    setVolume,
    reset,
    appendAudioToQueue,
  } = useIndexTTS();

  const audioRef = useRef<HTMLAudioElement | null>(null);

  // 使用useWorkspaceState代替useState管理插话状态
  const [interruptText, setInterruptText] = useWorkspaceState<string>('live-interruptText', '');

  // 使用useWorkspaceState代替useState管理插话提示状态
  const [showInterruptSuccess, setShowInterruptSuccess] = useWorkspaceState<boolean>('live-showInterruptSuccess', false);

  // 新增：插话预设文本状态
  /**
   * 插话预设文本列表
   * @type {string[]}
   */
  // 共享预设localStorage key
  const INTERRUPT_PRESETS_KEY = 'interruptPresets';
  const getSharedPresets = () => {
    const saved = localStorage.getItem(INTERRUPT_PRESETS_KEY);
    return saved ? JSON.parse(saved) : [
    '感谢[昵称]送出的礼物！',
    '欢迎新来的[昵称]！',
    '点赞关注主播不迷路！',
    '今天有惊喜福利哦！',
    '还有[整点]时间，大家抓紧！'
    ];
  };
  const [interruptPresets, setInterruptPresets] = useState(getSharedPresets());
  const updatePresets = (newPresets: string[]) => {
    setInterruptPresets(newPresets);
    localStorage.setItem(INTERRUPT_PRESETS_KEY, JSON.stringify(newPresets));
    window.dispatchEvent(new Event('interruptPresetsChanged'));
  };
  /**
   * 预设菜单的锚点元素
   * @type {HTMLElement | null}
   */
  const [presetMenuAnchorEl, setPresetMenuAnchorEl] = useState<HTMLElement | null>(null);
  /**
   * 新增预设文本的输入框内容
   * @type {string}
   */
  const [newPresetText, setNewPresetText] = useState('');

  // 隐藏的系统提示词，包含变量说明，不在UI中显示
  const HIDDEN_SYSTEM_PROMPT = `注意：文本中可能包含以下特殊变量标记，请保持它们的原始格式不变：
 [昵称] - 用于显示观众昵称
 [人数] - 用于显示当前观众人数
 [时间] - 用于显示当前时间
 [整点] - 用于显示距离下一个整点的时间
 {选项1|选项2|选项3} - 随机选择一个选项的格式
 [助:内容] - 表示这段内容使用助播音色，你可以根据上下文修改"内容"部分

这些变量会在后续处理中被自动替换，请确保它们在你生成的文本中保持原样。
话术中不要出现和直播无关的内容，如：环节名称、备注的内容。
不要出现任何的特殊符号以及表情包符号，如~、#、@、%、……、-、——、*、|等符号。如果需要用-，请用其他的代替其意思，比如40-50转化为40到50
以及直播间中的违禁词（如：绝对、顶级、顶峰、绝无仅有、国家级、万能、首位、世界级、第一、唯一、最好、最佳、顶级、顶尖、无可比拟、无与伦比、极致、独一无二、完美、无敌、极好、极佳、超级、超强、最强、最棒、最牛、最先、最火、最热、最酷、最高、最低、最好、最佳、绝佳、最优、100%、首个、首选、首家、免检、一流、治疗、功效、疾病。）用成语的话也要避免出现极限的意思
规避功效、医疗、疗效方面的说法，特别是医疗和疗效类的内容，如：能让人**样，**疗效，**功效
不要出现诱导性的话术，如：只要**样就**样，你**样我就**样
如果话术中没有出现运费险或者包邮等没有的售后保障或者发货服务，则生成的时候不提相关的内容
不夸大宣传、无中生有、生成的话术要符合原话术内容`;

  // AI大模型相关状态
  const { generateText, isProcessing: isAIGenerating, error: aiError } = useAIModel();
  const [prompt, setPrompt] = useWorkspaceState<string>('live-prompt', '把【】里面的话术内容进行重写，要求意思一样文字完全不一样，不要出现抖音违禁词，只输出重写内容，要口语化不要书面语。');
  const [audioQueue, setAudioQueue] = useState<string[]>([]);
  const [currentQueueIndex, setCurrentQueueIndex] = useState(0);
  const [lastGeneratedText, setLastGeneratedText] = useState('');
  const isAutoGeneratingRef = useRef(false);
  const [promptDialogOpen, setPromptDialogOpen] = useState(false);

  // 添加试听相关状态
  const [previewAudio, setPreviewAudio] = useState<{
    url: string;
    modelId: string;
    isPlaying: boolean;
    audio: HTMLAudioElement | null;
  } | null>(null);

  // 添加试听加载状态
  const [previewLoading, setPreviewLoading] = useState<string | null>(null);

  // 修改处理模型试听的函数
  const handlePlayModelPreview = async (modelId: string, modelName: string, e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止冒泡，避免同时选中模型
    
    // 如果正在试听同一个模型，则暂停
    if (previewAudio && previewAudio.modelId === modelId && previewAudio.isPlaying) {
      if (previewAudio.audio) {
        previewAudio.audio.pause();
      }
      setPreviewAudio(prev => prev ? { ...prev, isPlaying: false } : null);
      return;
    }
    
    // 如果正在试听其他模型，先停止
    if (previewAudio && previewAudio.audio) {
      previewAudio.audio.pause();
      setPreviewAudio(null);
    }
    
    try {
      // 设置加载状态
      setPreviewLoading(modelId);
      
      // 显示加载状态
      setAudioState(prev => ({
        ...prev,
        error: null
      }));
      
      // 处理模型名称，去掉"使用参考音频"和".pt"后缀
      const cleanModelName = modelName
        .replace('使用参考音频', '')
        .replace(/\.pt$/, '')
        .trim();
      
      // 试听文案
      const previewText = `你好，我是${cleanModelName}，期待和你一起直播`;
      
      // 构建模型ID，确保有正确的后缀
      const modelWithSuffix = modelId.endsWith('.pt') ? modelId : `${modelId}.pt`;
      
      // 生成音频
      const audioUrl = await generateAudio({
        model: modelWithSuffix,
        text: previewText,
        speed: getCurrentSpeed(),
        isInterrupt: true // 使用isInterrupt参数
      });
      
      // 清除加载状态
      setPreviewLoading(null);
      
      if (audioUrl) {
        // 创建音频元素
        const audio = new Audio(audioUrl);
        
        // 设置音频事件
        audio.addEventListener('ended', () => {
          setPreviewAudio(prev => prev ? { ...prev, isPlaying: false } : null);
        });
        
        // 播放音频
        await audio.play();
        
        // 设置预览状态
        setPreviewAudio({
          url: audioUrl,
          modelId,
          isPlaying: true,
          audio
        });
      }
    } catch (error) {
      // 清除加载状态
      setPreviewLoading(null);
      
      console.error('试听失败:', error);
      setAudioState(prev => ({
        ...prev,
        error: '试听失败，请重试'
      }));
    }
  };

  // 在组件卸载时清理音频资源
  useEffect(() => {
    return () => {
      if (previewAudio && previewAudio.audio) {
        previewAudio.audio.pause();
        previewAudio.audio.src = '';
      }
    };
  }, [previewAudio]);

  // 修改Dialog关闭时的处理，确保停止试听
  const handleCloseModelDialog = () => {
    if (previewAudio && previewAudio.audio) {
      previewAudio.audio.pause();
      previewAudio.audio.src = '';
      setPreviewAudio(null);
    }
    setModelSelectDialogOpen(false);
  };

  // 添加符号过滤函数
  const filterTTSUnsupportedChars = (text: string) => {
    // 移除特定符号：— ~ # @ …… * | -
    // 使用一个正则表达式匹配所有需要移除的字符和序列
    // 注意：在字符集 [] 内，- 需要转义 (\-) 或放在开头/结尾。* 和 | 也需要转义 (\* 和 \|)
    // 省略号 …… 是一个序列，最好单独处理或使用更复杂的 regex
    // 为了简单起见，先移除省略号序列，再移除其他单个字符
    let filteredText = text.replace(/……/g, ''); // 移除省略号序列
    // 移除其他单个字符: — ~ # @ * | -
    filteredText = filteredText.replace(/[—~#@\*|\-]/g, '');

    return filteredText;
  };

  /**
   * 直播控制函数，根据aiMode决定是否调用大模型API
   * @returns {Promise<void>}
   */
  const handleLiveStreamToggle = async () => {
    try {
      // 移除调试日志
      if (!isLiveStreaming) {
        if (!currentModel) {
          throw new Error('请先选择一个音色');
        }
        if (!scriptContent.trim()) {
          throw new Error('请先输入直播话术');
        }
        setIsLoading(true);
        if (aiMode === 'full') {
          // 全自由：调用大模型API生成话术
          if (!prompt.trim()) {
            setIsLoading(false);
            throw new Error('请输入提示词以生成初始话术');
          }
                // 调用大模型API，传入原始脚本内容，将隐藏的系统提示词作为系统提示词参数
      const combinedPrompt = `${prompt}\n\n【${scriptContent}】`;
      const initialAIScript = await generateText(combinedPrompt, HIDDEN_SYSTEM_PROMPT, false);
          if (!initialAIScript) {
            setIsLoading(false);
            throw new Error('AI未能生成初始话术');
          }
          setLastGeneratedText(initialAIScript);
          
          // 导入templateUtils中的函数处理话术变量和随机词
          const { getRandomString, replacePlaceholders, getVariablesForTemplate } = await import('../../utils/templateUtils');
          
          // 1. 获取当前变量值
          const variables = getVariablesForTemplate('custom', {
            nickname: latestUsername || '家人们',
            audienceCount: audienceCount
          });
          
          // 2. 替换变量标记
          let processedText = replacePlaceholders(initialAIScript, variables);
          
          // 3. 处理随机词语
          processedText = getRandomString(processedText);
          
          // *** 添加符号过滤 ***
          processedText = filterTTSUnsupportedChars(processedText);
          // *** 过滤结束 ***
          
          const modelId = currentModel.id.endsWith('.pt') ? currentModel.id : `${currentModel.id}.pt`;
          // 添加助播模型参数
          const assistantModelId = assistantModel?.id.endsWith('.pt') ? 
            assistantModel.id : assistantModel?.id ? `${assistantModel.id}.pt` : null;
          
          const audioUrl = await generateAudio({
            model: modelId,
            speed: getCurrentSpeed(),
            text: processedText,
            assistantModel: assistantModelId || undefined,
            getCurrentSpeed,
            bufferSize // 新增
          } as any);
          if (!audioUrl) {
            setIsLoading(false);
            throw new Error('音频生成失败');
          }
          localStorage.setItem('liveMode', 'realtime');
          localStorage.setItem('realtimeScriptContent', scriptContent);
        } else if (aiMode === 'off') {
          // 关闭：只用脚本内容合成语音
          // 导入templateUtils中的函数处理话术变量和随机词
          const { getRandomString, replacePlaceholders, getVariablesForTemplate } = await import('../../utils/templateUtils');
          
          // 1. 获取当前变量值
          const variables = getVariablesForTemplate('custom', {
            nickname: latestUsername || '家人们',
            audienceCount: audienceCount
          });
          
          // 2. 替换变量标记
          let processedText = replacePlaceholders(scriptContent, variables);
          
          // 3. 处理随机词语
          processedText = getRandomString(processedText);
          
          // *** 添加符号过滤 ***
          processedText = filterTTSUnsupportedChars(processedText);
          // *** 过滤结束 ***
          
          setLastGeneratedText(processedText);
          const modelId = currentModel.id.endsWith('.pt') ? currentModel.id : `${currentModel.id}.pt`;
          // 添加助播模型参数
          const assistantModelId = assistantModel?.id.endsWith('.pt') ? 
            assistantModel.id : assistantModel?.id ? `${assistantModel.id}.pt` : null;
          
          const audioUrl = await generateAudio({
            model: modelId,
            speed: getCurrentSpeed(),
            text: processedText,
            assistantModel: assistantModelId || undefined,
            getCurrentSpeed,
            bufferSize // 新增
          } as any);
          if (!audioUrl) {
            setIsLoading(false);
            throw new Error('音频生成失败');
          }
          
          // 预先添加下一段话术到队列，避免播放中断
          setTimeout(() => {
            if (isLiveStreaming && aiMode === 'off') {
              autoEnqueueScriptContent();
            }
          }, 2000);
        } else {
          // 其他模式（如半自由，理论上已移除）
          setIsLoading(false);
          throw new Error('未知的智能发挥模式');
        }
        setIsLoading(false);
      } else {
        pauseAudio();
        reset();
        setIsLiveStreaming(false);
        setLastGeneratedText(scriptContent);
        isAutoGeneratingRef.current = false;
        
        // 重置音色轮换状态
        if (modelRotation.enabled) {
          setModelRotation(prev => ({
            ...prev,
            nextSwitchTime: 0,
            lastSwitchTime: 0
          }));
          
          if (rotationState.timerId) {
            clearInterval(rotationState.timerId);
            setRotationState(prev => ({
              ...prev,
              timerId: null,
              remainingSeconds: 0
            }));
          }
        }
        setAutoGenCount(0); // 停止直播时重置计数
      }
    } catch (err: unknown) {
      console.error('直播控制错误:', err);
      const errorMessage = err instanceof Error ? err.message : '启动直播时发生错误';
      setAudioState(prev => ({
        ...prev,
        error: errorMessage
      }));
      setIsLoading(false);
    }
  };

  // 自动生成并入队
  const autoGenerateAndEnqueue = async () => {
    if (aiMode !== 'full') return; // 只在全自由时允许自动生成
    if (!isLiveStreaming) return;
    if (!currentModel?.id) return;
    isAutoGeneratingRef.current = true;
    try {
      let promptToUse = prompt;
      // 每resetInterval次强制用脚本框内容
      if (autoGenCount > 0 && resetInterval > 0 && autoGenCount % resetInterval === 0) {
        promptToUse = scriptContent;
      } else {
        promptToUse = `${prompt}\n\n【${lastGeneratedText}】`;
      }
      const newScript = await generateText(promptToUse, HIDDEN_SYSTEM_PROMPT, false);
      if (newScript) {
        setLastGeneratedText(newScript);
        setAutoGenCount(c => c + 1);
        
        // 导入templateUtils中的函数处理话术变量和随机词
        const { getRandomString, replacePlaceholders, getVariablesForTemplate } = await import('../../utils/templateUtils');
        
        // 获取当前变量值，使用LiveConnectionContext中的真实数据
        const variables = getVariablesForTemplate('custom', {
          nickname: latestUsername || '家人们',
          audienceCount: audienceCount
        });
        
        // 替换变量标记
        let processedText = replacePlaceholders(newScript, variables);
        
        // 处理随机词语
        processedText = getRandomString(processedText);
        
        // *** 添加符号过滤 ***
        processedText = filterTTSUnsupportedChars(processedText);
        // *** 过滤结束 ***
        
        await appendAudioToQueue({
          model: currentModel.id.endsWith('.pt') ? currentModel.id : `${currentModel.id}.pt`,
          speed: getCurrentSpeed(),
          text: processedText,
          assistantModel: assistantModel?.id.endsWith('.pt') ? assistantModel.id : assistantModel?.id ? `${assistantModel.id}.pt` : undefined,
          getCurrentSpeed,
          bufferSize // 新增
        } as any);
      }
    } finally {
      isAutoGeneratingRef.current = false;
    }
  };

  // 新增：在关闭模式下直接使用脚本内容重复生成音频
  const autoEnqueueScriptContent = async () => {
    if (aiMode !== 'off') return; // 只在关闭模式时使用
    if (!isLiveStreaming) return;
    if (!currentModel?.id) return;
    if (!scriptContent.trim()) return;
    
    // 设置标志位，防止重复调用
    isAutoGeneratingRef.current = true;
    
    try {
      // 移除日志
      
      // 导入templateUtils中的函数处理话术变量和随机词
      const { getRandomString, replacePlaceholders, getVariablesForTemplate } = await import('../../utils/templateUtils');
      
      // 1. 获取当前变量值
      const variables = getVariablesForTemplate('custom', {
        nickname: latestUsername || '家人们',
        audienceCount: audienceCount
      });
      
      // 2. 替换变量标记
      let processedText = replacePlaceholders(scriptContent, variables);
      
      // 3. 处理随机词语
      processedText = getRandomString(processedText);
      
      // *** 添加符号过滤 ***
      processedText = filterTTSUnsupportedChars(processedText);
      // *** 过滤结束 ***
      
      // 使用脚本框的内容生成音频
      await appendAudioToQueue({
        model: currentModel.id.endsWith('.pt') ? currentModel.id : `${currentModel.id}.pt`,
        speed: getCurrentSpeed(),
        text: processedText,
        assistantModel: assistantModel?.id.endsWith('.pt') ? 
          assistantModel.id : assistantModel?.id ? `${assistantModel.id}.pt` : undefined,
        getCurrentSpeed,
        bufferSize // 新增
      } as any);
    } finally {
      isAutoGeneratingRef.current = false;
    }
  };

  // 监听音频队列长度，自动补充
  useEffect(() => {
    if (!isLiveStreaming) return;
    
    const remain = audioState.totalSentences - audioState.currentSentenceIndex;
    
    if (remain < 10 && !isAIGenerating && !isAutoGeneratingRef.current) {
      if (aiMode === 'full') {
        autoGenerateAndEnqueue();
      } else if (aiMode === 'off') {
        autoEnqueueScriptContent();
      }
    }
  }, [isLiveStreaming, audioState.currentSentenceIndex, audioState.totalSentences, isAIGenerating, aiMode]);

  // 在音频状态监听部分添加对播放状态的监听
  useEffect(() => {
    if (audioState.isPlaying) {
      setIsLiveStreaming(true);
      setIsLoading(false);
    }
  }, [audioState.isPlaying]);

  // 添加错误提示组件
  useEffect(() => {
    if (audioState.error) {
      // 5秒后自动清除错误
      const timer = setTimeout(() => {
        setAudioState(prev => ({ ...prev, error: null }));
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [audioState.error, setAudioState]);

  // 格式化时间
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // 修改插话处理函数
  const handleInterrupt = async () => {
    if (!currentModel) {
      setAudioState(prev => ({
        ...prev,
        error: '请先选择一个音色'
      }));
      return;
    }

    if (!interruptText.trim()) {
      setAudioState(prev => ({
        ...prev,
        error: '请输入插话内容'
      }));
      return;
    }

    try {
      // 立即显示发送成功提示
      setShowInterruptSuccess(true);
      setTimeout(() => setShowInterruptSuccess(false), 3000);
      
      setAudioState(prev => ({
        ...prev,
        error: null
      }));
      
      // 导入templateUtils中的函数处理话术变量和随机词
      const { getRandomString, replacePlaceholders, getVariablesForTemplate } = await import('../../utils/templateUtils');
      
      // 1. 获取当前变量值 
      const variables = getVariablesForTemplate('custom', {
        nickname: latestUsername || '家人们',
        audienceCount: audienceCount
      });
      
      // 2. 替换变量标记
      let processedText = replacePlaceholders(interruptText, variables);
      
      // 3. 处理随机词语
      processedText = getRandomString(processedText);
      
      // *** 添加符号过滤 ***
      processedText = filterTTSUnsupportedChars(processedText);
      // *** 过滤结束 ***
      
      const modelWithSuffix = currentModel.id.endsWith('.pt') ? currentModel.id : `${currentModel.id}.pt`;
      // 添加助播模型参数
      const assistantModelId = assistantModel?.id.endsWith('.pt') ? 
        assistantModel.id : assistantModel?.id ? `${assistantModel.id}.pt` : null;
      
      // 异步生成音频
      await generateAudio({
        model: modelWithSuffix,
        text: processedText,
        speed: getCurrentSpeed(),
        isInterrupt: true,
        assistantModel: assistantModelId || undefined,
        getCurrentSpeed,
        bufferSize // 新增
      } as any);
    } catch (error) {
      console.error('插话失败:', error);
      setAudioState(prev => ({
        ...prev,
        error: '插话失败，请重试'
      }));
    }
  };

  // 添加保存脚本函数
  const handleSaveScript = () => {
    try {
      // 创建 Blob 对象
      const blob = new Blob([scriptContent], { type: 'text/plain;charset=utf-8' });
      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `直播脚本_${new Date().toLocaleDateString()}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      // 显示保存成功提示
      setAudioState(prev => ({
        ...prev,
        error: '脚本保存成功'
      }));
    } catch (error) {
      console.error('保存脚本失败:', error);
      setAudioState(prev => ({
        ...prev,
        error: '保存脚本失败，请重试'
      }));
    }
  };

  // 添加导入脚本函数
  const handleImportScript = () => {
    try {
      // 创建文件输入元素
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.txt';
      
      // 处理文件选择
      input.onchange = (e) => {
        const file = (e.target as HTMLInputElement).files?.[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = (event) => {
            const content = event.target?.result as string;
            setScriptContent(content);
            // 显示导入成功提示
            setAudioState(prev => ({
              ...prev,
              error: '脚本导入成功'
            }));
          };
          reader.readAsText(file);
        }
      };
      
      // 触发文件选择
      input.click();
    } catch (error) {
      console.error('导入脚本失败:', error);
      setAudioState(prev => ({
        ...prev,
        error: '导入脚本失败，请重试'
      }));
    }
  };

  /**
   * 生成新话术并自动分句生成语音队列
   * @async
   * @returns {Promise<void>}
   */
  const handleGenerateNewScript = async () => {
    if (!scriptContent.trim() || !prompt.trim()) {
      setAudioState(prev => ({ ...prev, error: '请输入提示词和原始话术' }));
      return;
    }
    if (!currentModel?.id) {
      setAudioState(prev => ({ ...prev, error: '请选择音色' }));
      return;
    }
    const modelId = currentModel.id.endsWith('.pt') ? currentModel.id : `${currentModel.id}.pt`;
    // 添加助播模型参数
    const assistantModelId = assistantModel?.id.endsWith('.pt') ? 
      assistantModel.id : assistantModel?.id ? `${assistantModel.id}.pt` : null;
    
    try {
      // 1. 用大模型生成新话术，将隐藏的系统提示词作为系统提示词参数
      const combinedPrompt = `${prompt}\n\n【${scriptContent}】`;
      const newScript = await generateText(combinedPrompt, HIDDEN_SYSTEM_PROMPT, false);
      if (!newScript) {
        setAudioState(prev => ({ ...prev, error: 'AI未返回新话术' }));
        return;
      }
      setScriptContent(newScript); // 可选：同步到脚本框
      
      // 导入templateUtils中的函数处理话术变量和随机词
      const { getRandomString, replacePlaceholders, getVariablesForTemplate } = await import('../../utils/templateUtils');
      
      // 2. 获取当前变量值 
      const variables = getVariablesForTemplate('custom', {
        nickname: latestUsername || '家人们',
        audienceCount: audienceCount
      });
      
      // 3. 替换变量标记
      let processedText = replacePlaceholders(newScript, variables);
      
      // 4. 处理随机词语
      processedText = getRandomString(processedText);
      
      // 5. 直接用原有逻辑生成语音并自动播放
      await generateAudio({
        model: modelId,
        speed: getCurrentSpeed(),
        text: processedText,
        assistantModel: assistantModelId || undefined,
        getCurrentSpeed,
        bufferSize // 新增
      } as any);
    } catch (e) {
      setAudioState(prev => ({ ...prev, error: '生成新话术或语音失败' }));
    }
  };

  // 队列播放逻辑
  useEffect(() => {
    if (audioQueue.length > 0 && currentQueueIndex < audioQueue.length) {
      const url = audioQueue[currentQueueIndex];
      setAudioState(prev => ({ ...prev, audioUrl: url, isPlaying: true }));
    }
  }, [audioQueue, currentQueueIndex, setAudioState]);

  useEffect(() => {
    if (!audioState.isPlaying && audioQueue.length > 0 && currentQueueIndex < audioQueue.length - 1) {
      setCurrentQueueIndex(idx => idx + 1);
    }
  }, [audioState.isPlaying]);

  // 优化关闭模式下的脚本循环：提前补充队列，避免空白
  useEffect(() => {
    if (!isLiveStreaming) return;
    if (aiMode === 'off') {
      const remain = audioState.totalSentences - audioState.currentSentenceIndex;
      if (remain <= 10 && !isAudioLoading && !isAutoGeneratingRef.current) {
        autoEnqueueScriptContent();
      }
    }
  }, [isLiveStreaming, aiMode, audioState.currentSentenceIndex, audioState.totalSentences, isAudioLoading]);

  // 新增：插入公屏回复到音频队列
  const insertLiveReply = useCallback(async (text: string, type?: 'welcome' | 'time' | 'count' | 'interactionReply' | 'broadcast') => {
    // console.log('[insertLiveReply] 调用参数:', { text, type, isLiveStreaming, currentModel: currentModel?.id, userGreeting, aiMode });

    // 只处理明确的类型
    if (!type || !['welcome', 'count', 'time', 'interactionReply'].includes(type)) {
      // console.log('[insertLiveReply] 类型不支持，退出');
      return;
    }
    if (!isLiveStreaming) {
      // console.log('[insertLiveReply] 未在直播状态，退出');
      return;
    }
    if (!currentModel) {
      // console.log('[insertLiveReply] 未选择音色模型，退出');
      return;
    }
    if (!text.trim()) {
      // console.log('[insertLiveReply] 文本为空，退出');
      return;
    }

    let shouldGenerate = false;
    if (type === 'welcome') shouldGenerate = userGreeting.welcome;
    else if (type === 'count') shouldGenerate = userGreeting.count;
    else if (type === 'time') shouldGenerate = userGreeting.time;
    else if (type === 'interactionReply') shouldGenerate = userGreeting.reply;
    else return;

    // console.log('[insertLiveReply] 开关检查:', { type, shouldGenerate, userGreeting });
    if (!shouldGenerate) {
      // console.log('[insertLiveReply] 对应开关未启用，退出');
      return;
    }

    // 对于互动回复，直接使用传入的文本（已经在InteractionWorkspace中处理过AI和格式化）
    let finalText = text;
    // console.log('[insertLiveReply] 使用文本:', { originalText: text, type, aiMode });

    // 变量替换、随机词、符号过滤
    try {
      const { getRandomString, replacePlaceholders, getVariablesForTemplate } = await import('../../utils/templateUtils');
      const variables = getVariablesForTemplate('custom', {
        nickname: latestUsername || '家人们',
        audienceCount: audienceCount
      });
      let processedText = replacePlaceholders(finalText, variables);
      processedText = getRandomString(processedText);
      processedText = filterTTSUnsupportedChars(processedText);

      // 检查TTS服务可用性
      try {
        const response = await fetch('http://127.0.0.1:8088/status', { 
          method: 'GET',
          headers: { 'Content-Type': 'application/json' }
        });
        if (!response.ok) {
          setAudioState(prev => ({ ...prev, error: 'TTS服务不可用' }));
          return;
        }
      } catch (error) {
        setAudioState(prev => ({ ...prev, error: 'TTS服务连接失败' }));
        return;
      }

      const currentSpeed = getCurrentSpeed();
      // console.log('[insertLiveReply] 准备生成TTS:', {
      //   model: currentModel.id.endsWith('.pt') ? currentModel.id : `${currentModel.id}.pt`,
      //   text: processedText,
      //   speed: currentSpeed,
      //   finalText,
      //   originalText: text
      // });

      await generateAudio({
        model: currentModel.id.endsWith('.pt') ? currentModel.id : `${currentModel.id}.pt`,
        text: processedText,
        speed: currentSpeed,
        isInterrupt: true,
        assistantModel: assistantModel?.id?.endsWith('.pt') ? assistantModel.id : assistantModel?.id ? `${assistantModel.id}.pt` : undefined,
        getCurrentSpeed,
        bufferSize // 新增
      } as any);

      // console.log('[insertLiveReply] TTS生成完成');
    } catch (error) {
      console.error('[insertLiveReply] 处理失败:', error);
      setAudioState(prev => ({ ...prev, error: '自动回复处理失败' }));
    }
  }, [isLiveStreaming, userGreeting, currentModel, assistantModel, latestUsername, audienceCount, getCurrentSpeed, filterTTSUnsupportedChars, generateAudio, setAudioState, aiMode, bufferSize]);

  React.useEffect(() => {
    // 添加调试日志
    // console.log('设置insertLiveReply函数');
    setInsertLiveReply(insertLiveReply); // 确保传递最新的 insertLiveReply 函数
  }, [insertLiveReply, setInsertLiveReply]);

  // 添加：监听播报设置变更事件
  React.useEffect(() => {
    // 定义事件处理函数
    const handleBroadcastSettingsChange = (event: CustomEvent<{category: 'welcome' | 'time' | 'count', enabled: boolean}>) => {
      const { category, enabled } = event.detail;
      
      // 添加调试日志
      // console.log('接收到播报设置变更:', { category, enabled });
      
      // 根据类别更新相应的userGreeting设置
      if (category === 'welcome' || category === 'time' || category === 'count') {
        setUserGreeting((prev: UserGreetingType) => {
          const newState = {
            ...prev,
            [category]: enabled
          };
          // console.log('更新用户接待设置:', newState);
          return newState;
        });
      }
    };

    // 添加事件监听
    window.addEventListener('broadcast-settings-change', handleBroadcastSettingsChange as EventListener);
    
    // 组件卸载时移除事件监听
    return () => {
      window.removeEventListener('broadcast-settings-change', handleBroadcastSettingsChange as EventListener);
    };
  }, [userGreeting]);

  // 当userGreeting状态变化时，保存到localStorage并广播事件
  useEffect(() => {
    try {
      localStorage.setItem('live-userGreeting', JSON.stringify(userGreeting));
      // console.log('[userGreeting] 状态已保存:', userGreeting);

      // 广播事件给其他组件
      Object.entries(userGreeting).forEach(([category, enabled]) => {
        if (category === 'welcome' || category === 'time' || category === 'count') {
          const event = new CustomEvent('broadcast-settings-change', {
            detail: {
              category: category as 'welcome' | 'time' | 'count',
              enabled
            }
          });
          window.dispatchEvent(event);
        }
      });
    } catch (error) {
      console.error('保存用户接待设置失败:', error);
    }
  }, [userGreeting]);

  // 立即初始化开关状态并分发事件
  useEffect(() => {
    // 初始化时分发一次事件，让其他组件同步状态
    
    // 初始化时分发一次事件，让其他组件同步状态
    Object.entries(userGreeting).forEach(([category, enabled]) => {
      if (category === 'welcome' || category === 'time' || category === 'count') {
        const event = new CustomEvent('broadcast-settings-change', { 
          detail: { 
            category: category as 'welcome' | 'time' | 'count',
            enabled
          } 
        });
        window.dispatchEvent(event);
      }
    });
    
    // 将设置保存到localStorage，让其他组件可以获取
    localStorage.setItem('live-userGreeting', JSON.stringify(userGreeting));
  }, []);

  // 添加标签管理相关函数
  // 初始化加载标签数据
  useEffect(() => {
    const savedTags = TagStorage.getTags();
    const savedModelTags = TagStorage.getModelTags();
    
    if(savedTags.length > 0) {
      setTags(savedTags);
    } else {
      // 如果没有保存的标签，设置默认标签
      const defaultTags = [
        { id: 'female', name: '女声', color: '#FF61A6' },
        { id: 'male', name: '男声', color: '#3F84E5' },
        { id: 'special', name: '特色', color: '#8B5CF6' },
      ];
      setTags(defaultTags);
      TagStorage.saveTags(defaultTags);
    }
    
    if(Object.keys(savedModelTags).length > 0) {
      setModelTags(savedModelTags);
    }
  }, []);

  // 当标签列表变化时保存
  useEffect(() => {
    if(tags.length > 0) {
      TagStorage.saveTags(tags);
    }
  }, [tags]);

  // 当模型标签关联变化时保存
  useEffect(() => {
    if(Object.keys(modelTags).length > 0) {
      // TagStorage.saveModelTags(modelTags); // <--- 删除或注释这行
    }
  }, [modelTags]); // <--- 删除这个 useEffect 块

  // 为模型添加标签
  const addTagToModel = useCallback((modelId: string, tagId: string) => {
    if (!modelId || !tagId) {
      console.warn('尝试添加标签时缺少必要参数', { modelId, tagId });
      return;
    }

    // 检查标签是否存在
    const tagExists = tags.some(tag => tag.id === tagId);
    if (!tagExists) {
      console.warn(`尝试添加不存在的标签: ${tagId}`);
      return;
    }

    // 使用TagStorage添加标签
    TagStorage.addTagToModel(modelId, tagId);
    
    // 更新状态
    setModelTags(prev => {
      const modelTags = prev[modelId] || [];
      if (modelTags.includes(tagId)) {
        return prev;
      }
      return {
        ...prev,
        [modelId]: [...modelTags, tagId]
      };
    });
    
    // 触发标签系统更改事件
    window.dispatchEvent(new Event('tag-system-change'));
  }, [tags]);

  // 从模型中移除标签
  const removeTagFromModel = useCallback((modelId: string, tagId: string) => {
    if (!modelId || !tagId) {
      console.warn('尝试移除标签时缺少必要参数', { modelId, tagId });
      return;
    }

    // 使用TagStorage移除标签
    TagStorage.removeTagFromModel(modelId, tagId);
    
    // 更新状态
    setModelTags(prev => {
      if (!prev[modelId]) {
        return prev;
      }
      return {
        ...prev,
        [modelId]: prev[modelId].filter(id => id !== tagId)
      };
    });
    
    // 触发标签系统更改事件
    window.dispatchEvent(new Event('tag-system-change'));
  }, []);

  // 打开标签筛选菜单
  const handleOpenFilterMenu = (event: React.MouseEvent<HTMLElement>) => {
    console.log('打开标签筛选菜单', event.currentTarget);
    setFilterMenuAnchorEl(event.currentTarget);
  };

  // 关闭标签筛选菜单
  const handleCloseFilterMenu = () => {
    console.log('关闭标签筛选菜单');
    setFilterMenuAnchorEl(null);
  };

  // 切换标签筛选
  const handleTagFilterToggle = (tagId: string) => {
    console.log('切换标签筛选', tagId, '当前筛选:', activeTagFilters);
    
    setActiveTagFilters(prev => {
      let newFilters;
      if (prev.includes(tagId)) {
        newFilters = prev.filter(id => id !== tagId);
      } else {
        newFilters = [...prev, tagId];
      }
      console.log('更新后的筛选:', newFilters);
      return newFilters;
    });
  };

  // 删除标签
  const handleDeleteTag = (tagId: string) => {
    if (window.confirm(`确定要删除此标签吗？这将从所有模型中移除该标签。`)) {
      // 使用TagStorage删除标签
      TagStorage.removeTag(tagId);
      
      // 更新状态
      setTags(prev => prev.filter(tag => tag.id !== tagId));
      setModelTags(prev => {
        const updated = { ...prev };
        Object.keys(updated).forEach(modelId => {
          updated[modelId] = updated[modelId].filter(id => id !== tagId);
        });
        return updated;
      });
      
      // 从筛选器中移除
      setActiveTagFilters(prev => prev.filter(id => id !== tagId));
      
      // 触发标签系统更改事件
      window.dispatchEvent(new Event('tag-system-change'));
      
      message.success('标签已删除');
    }
  };

  // 打开创建标签对话框
  const handleCreateTagDialogOpen = () => {
    setIsCreateTagDialogOpen(true);
    handleCloseTagMenu(); // 关闭菜单
  };

  // 关闭创建标签对话框
  const handleCreateTagDialogClose = () => {
    setIsCreateTagDialogOpen(false);
    setNewTagName('');
    setNewTagColor('#4f7df9');
  };

  // 创建新标签
  const handleCreateTag = () => {
    if (!newTagName.trim()) {
      message.error('请输入标签名称');
      return;
    }

    if (tags.length >= MAX_TAGS) {
      message.error(`标签数量已达上限 (${MAX_TAGS})`);
      return;
    }

    // 生成新标签
    const newTag: Tag = {
      id: `tag_${Date.now()}_${Math.floor(Math.random() * 1000)}`,
      name: newTagName.trim(),
      color: newTagColor
    };
    
    // 使用TagStorage添加标签
    TagStorage.addTag(newTag);
    setTags(prev => [...prev, newTag]);
    
    // 如果有选中的模型，自动为其添加该标签
    if (selectedModelForTag) {
      TagStorage.addTagToModel(selectedModelForTag, newTag.id);
      setModelTags(prev => ({
        ...prev,
        [selectedModelForTag]: [...(prev[selectedModelForTag] || []), newTag.id]
      }));
    }
    
    // 触发标签系统更改事件
    window.dispatchEvent(new Event('tag-system-change'));
    
    // 关闭对话框并重置状态
    setIsCreateTagDialogOpen(false);
    setNewTagName('');
    setNewTagColor('#4f7df9');
    
    message.success(`标签 "${newTag.name}" 创建成功`);
  };

  // 打开标签菜单
  const handleOpenTagMenu = (event: React.MouseEvent<HTMLElement>, modelId: string) => {
    // 阻止事件冒泡，避免触发其他点击事件
    event.stopPropagation();
    // 保存点击位置，用于定位菜单
    setTagMenuAnchorEl(event.currentTarget);
    // 保存当前选中的模型ID
    setSelectedModelForTag(modelId);
    // 打开标签对话框
    setTagDialogOpen(true);
  };

  // 关闭标签菜单
  const handleCloseTagMenu = () => {
    setTagDialogOpen(false);
    setTagMenuAnchorEl(null);
  };

  // 渲染标签筛选菜单
  const renderTagFilterMenu = () => {
    return (
      <Popover
        anchorEl={filterMenuAnchorEl}
        open={Boolean(filterMenuAnchorEl)}
        onClose={handleCloseFilterMenu}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
        sx={{
          '& .MuiPaper-root': {
            bgcolor: alpha(theme.background.paper, 0.95),
            border: `1px solid ${alpha(theme.accent, 0.2)}`,
            borderRadius: '8px',
            boxShadow: `0 8px 20px ${alpha('#000', 0.2)}`,
            backdropFilter: 'blur(8px)',
            maxWidth: 280,
            overflow: 'visible',
            zIndex: 9999, // 提高z-index确保在最上层
          }
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography 
            sx={{ 
              fontSize: '0.9rem', 
              fontWeight: 600, 
              color: theme.text.primary,
              mb: 1
            }}
          >
            按标签筛选
          </Typography>
          
          {tags.length === 0 ? (
            <Typography 
              sx={{ 
                fontSize: '0.85rem', 
                color: theme.text.secondary,
                textAlign: 'center',
                py: 2
              }}
            >
              暂无标签，请先创建标签
            </Typography>
          ) : (
            <Box sx={{ 
              display: 'flex', 
              flexWrap: 'wrap', 
              gap: 1, 
              my: 1 
            }}>
              {tags.map(tag => {
                const isActive = activeTagFilters.includes(tag.id);
                
                return (
                  <Chip
                    key={tag.id}
                    label={tag.name}
                    size="small"
                    onClick={() => handleTagFilterToggle(tag.id)}
                    onDelete={() => handleDeleteTag(tag.id)}
                    sx={{
                      bgcolor: isActive ? alpha(tag.color, 0.2) : alpha(theme.background.light, 0.3),
                      color: isActive ? tag.color : theme.text.secondary,
                      borderRadius: '4px',
                      border: isActive ? `1px solid ${alpha(tag.color, 0.3)}` : '1px solid transparent',
                      '&:hover': {
                        bgcolor: isActive ? alpha(tag.color, 0.3) : alpha(theme.background.light, 0.5),
                      },
                      '& .MuiChip-deleteIcon': {
                        color: isActive ? alpha(tag.color, 0.7) : alpha(theme.text.secondary, 0.7),
                        '&:hover': {
                          color: isActive ? tag.color : theme.text.primary,
                        }
                      }
                    }}
                  />
                );
              })}
            </Box>
          )}
          
          {tags.length < MAX_TAGS && (
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Add />}
              onClick={() => {
                handleCloseFilterMenu();
                handleCreateTagDialogOpen();
              }}
              sx={{
                mt: 1,
                borderColor: alpha(theme.primary, 0.3),
                color: theme.primary,
                '&:hover': {
                  borderColor: theme.primary,
                  bgcolor: alpha(theme.primary, 0.05),
                }
              }}
            >
              创建新标签
            </Button>
          )}
        </Box>
      </Popover>
    );
  };

  // 监听标签系统更改事件
  useEffect(() => {
    // 定义一个处理标签系统更改的函数
    const handleTagSystemChange = () => {
      console.log('检测到标签系统更改，重新加载数据');
      const savedTags = TagStorage.getTags();
      const savedModelTags = TagStorage.getModelTags();
      
      if(savedTags.length > 0) {
        setTags(savedTags);
      }
      
      if(Object.keys(savedModelTags).length > 0) {
        setModelTags(savedModelTags);
      }
    };
    
    // 添加自定义事件监听器
    window.addEventListener('tag-system-change', handleTagSystemChange);
    
    // 组件卸载时移除监听器
    return () => {
      window.removeEventListener('tag-system-change', handleTagSystemChange);
    };
  }, []);

  // 添加音色轮换计时器
  useEffect(() => {
    // 只在直播状态下且启用了音色轮换时执行
    if (isLiveStreaming && modelRotation.enabled) {
      // 初始化模型池
      updateAvailableModelsForRotation();
      
      // 如果没有设置下次切换时间，立即设置
      if (modelRotation.nextSwitchTime === 0) {
        scheduleNextModelSwitch();
      }
      
      // 设置轮询计时器，每秒检查一次是否需要切换模型
      const timer = setInterval(() => {
        const now = Date.now();
        
        // 如果到达切换时间点
        if (now >= modelRotation.nextSwitchTime && modelRotation.nextSwitchTime > 0) {
          // 执行模型切换
          switchToRandomModel();
          // 安排下一次切换
          scheduleNextModelSwitch();
        }
        
        // 更新倒计时
        if (modelRotation.nextSwitchTime > 0) {
          const remaining = Math.max(0, Math.floor((modelRotation.nextSwitchTime - now) / 1000));
          setRotationState(prev => ({
            ...prev,
            remainingSeconds: remaining
          }));
        }
      }, 1000);
      
      // 保存计时器ID
      setRotationState(prev => ({
        ...prev,
        timerId: timer
      }));
      
      // 清理函数
      return () => {
        if (timer) {
          clearInterval(timer);
        }
      };
    } else {
      // 未启用或非直播状态，清除计时器
      if (rotationState.timerId) {
        clearInterval(rotationState.timerId);
        setRotationState(prev => ({
          ...prev,
          timerId: null,
          remainingSeconds: 0
        }));
      }
    }
  }, [isLiveStreaming, modelRotation.enabled, modelRotation.nextSwitchTime, modelRotation.mode, modelRotation.tagIds]);

  // 根据标签过滤更新可用于轮换的模型列表
  const updateAvailableModelsForRotation = () => {
    // 如果没有指定标签，则使用所有模型
    if (!modelRotation.tagIds || modelRotation.tagIds.length === 0) {
      setRotationState(prev => ({
        ...prev,
        availableModels: [...models]
      }));
      return;
    }
    
    // 根据所选标签过滤模型
    const filteredModels = models.filter(model => {
      const modelTagIds = modelTags[model.id] || [];
      // 模型必须包含所有指定的标签
      return modelRotation.tagIds.every(tagId => modelTagIds.includes(tagId));
    });
    
    setRotationState(prev => ({
      ...prev,
      availableModels: filteredModels
    }));
  };

  // 安排下一次模型切换
  const scheduleNextModelSwitch = () => {
    const now = Date.now();
    let nextSwitchTime = now;
    
    if (modelRotation.mode === 'timer') {
      // 固定时间间隔
      nextSwitchTime = now + (modelRotation.interval * 1000);
    } else {
      // 随机时间间隔
      const range = modelRotation.maxInterval - modelRotation.minInterval;
      const randomInterval = modelRotation.minInterval + Math.floor(Math.random() * range);
      nextSwitchTime = now + (randomInterval * 1000);
    }
    
    setModelRotation(prev => ({
      ...prev,
      lastSwitchTime: now,
      nextSwitchTime: nextSwitchTime
    }));
  };

  // 随机切换到新模型
  const switchToRandomModel = () => {
    // 确保有可用模型
    if (rotationState.availableModels.length === 0) {
      updateAvailableModelsForRotation();
      if (rotationState.availableModels.length === 0) {
        console.warn('没有符合条件的模型可供切换');
        return;
      }
    }
    
    // 从可用模型中随机选择一个，不同于当前模型
    let availableModels = [...rotationState.availableModels];
    
    // 如果当前只有一个模型，那就用它
    if (availableModels.length === 1) {
      setCurrentModel(availableModels[0]);
      return;
    }
    
    // 如果有多个模型，排除当前使用的模型
    if (currentModel) {
      availableModels = availableModels.filter(model => model.id !== currentModel.id);
    }
    
    // 如果过滤后没有模型，就使用所有模型
    if (availableModels.length === 0) {
      availableModels = [...rotationState.availableModels];
    }
    
    // 随机选择一个模型
    const randomIndex = Math.floor(Math.random() * availableModels.length);
    const newModel = availableModels[randomIndex];
    
    // 设置新模型
    setCurrentModel(newModel);
    
    // 可以添加切换通知
    message.success(`音色已切换至: ${newModel.name.replace('使用参考音频', '').trim()}`);
  };

  // 切换音色轮换启用状态
  const toggleModelRotation = (enabled: boolean) => {
    setModelRotation(prev => ({
      ...prev,
      enabled,
      nextSwitchTime: enabled ? Date.now() + (prev.interval * 1000) : 0,
      lastSwitchTime: enabled ? Date.now() : 0
    }));
    
    if (enabled) {
      updateAvailableModelsForRotation();
    }
  };

  // 更新音色轮换设置
  const updateModelRotationSettings = (settings: Partial<{
    enabled: boolean;
    mode: 'timer' | 'random';
    interval: number;
    minInterval: number;
    maxInterval: number;
    tagIds: string[];
    lastSwitchTime: number;
    nextSwitchTime: number;
  }>) => {
    setModelRotation(prev => ({
      ...prev,
      ...settings,
      // 如果修改了时间设置且已启用，重新计算下次切换时间
      nextSwitchTime: prev.enabled ? Date.now() + 
        (settings.mode === 'timer' 
          ? (settings.interval || prev.interval) * 1000
          : ((settings.minInterval || prev.minInterval) + 
            Math.floor(Math.random() * ((settings.maxInterval || prev.maxInterval) - (settings.minInterval || prev.minInterval)))) * 1000) 
        : prev.nextSwitchTime
    }));
    
    // 如果标签变了，更新可用模型列表
    if (settings.tagIds) {
      updateAvailableModelsForRotation();
    }
  };

  // 立即执行一次模型切换
  const triggerImmediateModelSwitch = () => {
    switchToRandomModel();
    scheduleNextModelSwitch();
  };

  // 添加在其他useState声明附近，例如在modelRotation声明之后
  // 添加临时状态变量用于输入框
  const [tempIntervalValue, setTempIntervalValue] = useState((modelRotation.interval ?? 60).toString());
  const [tempMinIntervalValue, setTempMinIntervalValue] = useState((modelRotation.minInterval ?? 30).toString());
  const [tempMaxIntervalValue, setTempMaxIntervalValue] = useState((modelRotation.maxInterval ?? 120).toString());

  // 更新临时状态变量，当modelRotation变化时
  useEffect(() => {
    setTempIntervalValue((modelRotation.interval ?? 60).toString());
    setTempMinIntervalValue((modelRotation.minInterval ?? 30).toString());
    setTempMaxIntervalValue((modelRotation.maxInterval ?? 120).toString());
  }, [modelRotation.interval, modelRotation.minInterval, modelRotation.maxInterval]);

  // 自动生成计数器和重置频率设置，默认50
  /**
   * 自动生成计数器
   * @type {number}
   */
  const [autoGenCount, setAutoGenCount] = useState(0);
  /**
   * 自动重置频率，每N次后用脚本框内容重置，默认50
   * @type {number}
   */
  const [resetInterval, setResetInterval] = useWorkspaceState<number>('live-resetInterval', 50);

  // 新增临时状态用于输入框体验
  const [tempResetIntervalValue, setTempResetIntervalValue] = useState((resetInterval ?? 50).toString());
  useEffect(() => {
    setTempResetIntervalValue((resetInterval ?? 50).toString());
  }, [resetInterval]);

  // 语速随机设置相关状态
  /**
   * 是否启用语速随机
   * @type {boolean}
   */
  const [speedRandomEnabled, setSpeedRandomEnabled] = useWorkspaceState<boolean>('live-speedRandomEnabled', false);
  /**
   * 语速随机最小值
   * @type {number}
   */
  const [speedRandomMin, setSpeedRandomMin] = useWorkspaceState<number>('live-speedRandomMin', 1);
  /**
   * 语速随机最大值
   * @type {number}
   */
  const [speedRandomMax, setSpeedRandomMax] = useWorkspaceState<number>('live-speedRandomMax', 1.3);
  // 临时输入框状态
  const [tempSpeedRandomMin, setTempSpeedRandomMin] = useState(speedRandomMin.toFixed(1));
  const [tempSpeedRandomMax, setTempSpeedRandomMax] = useState(speedRandomMax.toFixed(1));
  useEffect(() => {
    setTempSpeedRandomMin(speedRandomMin.toFixed(1));
    setTempSpeedRandomMax(speedRandomMax.toFixed(1));
  }, [speedRandomMin, speedRandomMax]);

  /**
   * 获取当前要用的语速
   * @returns {number}
   */
  
  // 语速随机设置UI部分改为：只显示开关和一个设置按钮，点击按钮弹出Popover显示最小/最大输入框
  const [speedRandomAnchorEl, setSpeedRandomAnchorEl] = useState<HTMLButtonElement | null>(null);

  // 新增：打开预设菜单
  const handleOpenPresetMenu = (event: React.MouseEvent<HTMLElement>) => {
    setPresetMenuAnchorEl(event.currentTarget);
  };

  // 新增：关闭预设菜单
  const handleClosePresetMenu = () => {
    setPresetMenuAnchorEl(null);
    setNewPresetText(''); // 关闭时清空输入框
  };

  // 新增：选择预设文本
  const handleSelectPreset = (text: string) => {
    setInterruptText(text);
    handleClosePresetMenu();
  };

  // 新增：添加新预设
  const handleAddPreset = () => {
    if (newPresetText.trim() && !interruptPresets.includes(newPresetText.trim())) {
      updatePresets([...interruptPresets, newPresetText.trim()]);
      setNewPresetText('');
      message.success('预设已添加');
    } else if (interruptPresets.includes(newPresetText.trim())) {
      message.warning('该预设已存在');
    }
  };

  // 新增：删除预设
  const handleDeletePreset = (textToDelete: string) => {
    updatePresets(interruptPresets.filter((text: string) => text !== textToDelete));
    message.success('预设已删除');
  };

  useEffect(() => {
    const syncPresets = () => {
      setInterruptPresets(getSharedPresets());
    };
    window.addEventListener('interruptPresetsChanged', syncPresets);
    window.addEventListener('storage', syncPresets);
    return () => {
      window.removeEventListener('interruptPresetsChanged', syncPresets);
      window.removeEventListener('storage', syncPresets);
    };
  }, []);

  const handleReplyOnlyToggle = async () => {
    try {
      if (!isLiveStreaming) {
        // 启动纯回复模式
        if (!currentModel) {
          throw new Error('请先选择一个音色');
        }

        setIsLoading(true);

        // 纯回复模式不需要生成主播话术，但需要初始化TTS服务
        try {
          const response = await fetch('http://127.0.0.1:8088/status', {
            method: 'GET',
            headers: { 'Content-Type': 'application/json' }
          });
          if (!response.ok) {
            setIsLoading(false);
            throw new Error('TTS服务不可用，请确保后端服务已启动');
          }
        } catch (error) {
          setIsLoading(false);
          throw new Error('TTS服务连接失败，请确保后端服务已启动');
        }

        // 设置直播状态
        setIsLiveStreaming(true);

        // 保存模式设置
        localStorage.setItem('liveMode', 'realtime');
        if (scriptContent.trim()) {
          localStorage.setItem('realtimeScriptContent', scriptContent);
        }

        setIsLoading(false);
      } else {
        // 停止纯回复模式
        pauseAudio();
        reset();
        setIsLiveStreaming(false);
        isAutoGeneratingRef.current = false;

        // 重置音色轮换状态
        if (modelRotation.enabled) {
          setModelRotation(prev => ({
            ...prev,
            nextSwitchTime: 0,
            lastSwitchTime: 0
          }));

          if (rotationState.timerId) {
            clearInterval(rotationState.timerId);
            setRotationState(prev => ({
              ...prev,
              timerId: null,
              remainingSeconds: 0
            }));
          }
        }
      }
    } catch (err: unknown) {
      console.error('纯回复模式控制错误:', err);
      const errorMessage = err instanceof Error ? err.message : '启动纯回复模式时发生错误';
      setAudioState(prev => ({
        ...prev,
        error: errorMessage
      }));
      setIsLoading(false);
    }
  };

  return (
    <LiveReplyContext.Provider value={{ insertLiveReply }}>
      <Box sx={{ 
        width: '100%', 
        minHeight: '100vh',
        background: `linear-gradient(135deg, ${theme.background.main} 0%, ${theme.background.dark} 100%)`,
        p: 3,
      }}>
        <Typography
          variant="h4"
          sx={{
            fontWeight: 600,
            mb: 3,
            background: theme.gradient.primary,
            backgroundClip: 'text',
            textFillColor: 'transparent',
            display: 'inline-block',
          }}
        >
          实时直播
        </Typography>

        <Stack spacing={2.5}>
          {/* 直播状态区域 */}
          <Card sx={advancedCardStyle}>
            <CardContent sx={advancedCardContent}>
              <Stack direction="row" alignItems="center" spacing={1.5} mb={3}>
                <VideoCall sx={{ 
                  color: theme.accent,
                  filter: 'drop-shadow(0 0 3px rgba(46,192,255,0.5))',
                }} />
                <Typography variant="h6" sx={{ 
                  color: theme.text.primary,
                  fontWeight: 600,
                  letterSpacing: '0.05em',
                  position: 'relative',
                  '&::after': {
                    content: '""',
                    position: 'absolute',
                    bottom: -5,
                    left: 0,
                    width: '40px',
                    height: '2px',
                    background: `linear-gradient(90deg, ${theme.accent}, transparent)`,
                    borderRadius: '2px',
                  },
                }}>
                  直播状态
                </Typography>
                <Box sx={{ flex: 1 }} />
                
                {/* 公屏连接状态指示器 */}
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  mr: 2,
                  px: 1.5,
                  py: 0.5,
                  borderRadius: '4px',
                  bgcolor: alpha(publicScreenConnected ? theme.success : theme.error, 0.1),
                  border: `1px solid ${alpha(publicScreenConnected ? theme.success : theme.error, 0.2)}`,
                }}>
                  <Box sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    bgcolor: publicScreenConnected ? theme.success : theme.error,
                    boxShadow: `0 0 10px ${alpha(publicScreenConnected ? theme.success : theme.error, 0.5)}`,
                  }} />
                  <Typography sx={{
                    color: publicScreenConnected ? theme.success : theme.error,
                    fontSize: '0.85rem',
                    fontWeight: 500,
                  }}>
                    公屏{publicScreenConnected ? '已连接' : '未连接'}
                  </Typography>
                </Box>
                
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                }}>
                  <Box sx={{
                    width: 6,
                    height: 6,
                    borderRadius: '50%',
                    bgcolor: isLiveStreaming ? theme.error : alpha(theme.text.primary, 0.3),
                    boxShadow: isLiveStreaming 
                      ? `0 0 10px ${alpha(theme.error, 0.5)}`
                      : 'none',
                    animation: isLiveStreaming ? 'pulse 2s infinite' : 'none',
                    '@keyframes pulse': {
                      '0%': { opacity: 1, transform: 'scale(1)' },
                      '50%': { opacity: 0.5, transform: 'scale(1.1)' },
                      '100%': { opacity: 1, transform: 'scale(1)' },
                    },
                  }} />
                  <Typography sx={{
                    color: isLiveStreaming ? theme.error : theme.text.secondary,
                    fontSize: '0.85rem',
                    fontWeight: 500,
                  }}>
                    {isLiveStreaming ? '直播中' : '未开播'}
                  </Typography>
                </Box>
              </Stack>
              
              <Box sx={{ 
                display: 'flex', 
                flexDirection: 'column',
                gap: 1.5
              }}>
                <Box sx={{ 
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2,
                  minWidth: 'fit-content'
                }}>
                  <Typography sx={{ 
                    color: theme.text.secondary,
                    fontSize: '0.85rem'
                  }}>
                    主播音色:
                  </Typography>
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={() => openModelSelectDialog('main')}
                    endIcon={<KeyboardArrowDown />}
                    sx={{
                      minWidth: 140,
                      height: 32,
                      fontSize: '0.85rem',
                      bgcolor: alpha(theme.background.light, 0.5),
                      color: theme.text.primary,
                      border: `1px solid ${alpha(theme.text.primary, 0.1)}`,
                      '&:hover': {
                        bgcolor: alpha(theme.background.light, 0.7),
                        borderColor: alpha(theme.text.primary, 0.2),
                      },
                      display: 'flex',
                      justifyContent: 'space-between',
                      px: 2
                    }}
                  >
                    {currentModel ? currentModel.name.replace('使用参考音频', '').trim() : '选择音色'}
                  </Button>

                  <Typography sx={{ 
                    color: theme.text.secondary,
                    fontSize: '0.85rem',
                    ml: 2
                  }}>
                    助播音色:
                  </Typography>
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={() => openModelSelectDialog('assistant')}
                    endIcon={<KeyboardArrowDown />}
                    sx={{
                      minWidth: 140,
                      height: 32,
                      fontSize: '0.85rem',
                      bgcolor: alpha(theme.background.light, 0.5),
                      color: theme.text.primary,
                      border: `1px solid ${alpha(theme.text.primary, 0.1)}`,
                      '&:hover': {
                        bgcolor: alpha(theme.background.light, 0.7),
                        borderColor: alpha(theme.text.primary, 0.2),
                      },
                      display: 'flex',
                      justifyContent: 'space-between',
                      px: 2
                    }}
                  >
                    {assistantModel ? assistantModel.name.replace('使用参考音频', '').trim() : '选择音色'}
                  </Button>

                  {/* 预缓存输入框 */}
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, ml: 2 }}>
                    <Typography sx={{ color: theme.text.secondary, fontSize: '0.85rem' }}>预缓存</Typography>
                    <TextField
                      type="number"
                      size="small"
                      value={bufferSize}
                      onChange={e => {
                        let v = Number(e.target.value);
                        if (isNaN(v)) v = 1;
                        if (v < 1) v = 1;
                        if (v > 20) v = 20;
                        setBufferSize(v);
                      }}
                      inputProps={{
                        min: 1,
                        max: 20,
                        step: 1,
                        style: { textAlign: 'center' }
                      }}
                      variant="outlined"
                      sx={{
                        width: 64,
                        minWidth: 64,
                        height: 32,
                        '& .MuiOutlinedInput-root': {
                          height: 32,
                          minHeight: 32,
                          borderRadius: '8px',
                          bgcolor: 'rgba(42,49,83,0.5)',
                          color: '#fff',
                          fontSize: '0.85rem',
                          fontWeight: 600,
                          px: 2,
                          '& fieldset': {
                            borderColor: 'rgba(255,255,255,0.18)',
                          },
                          '&:hover fieldset': {
                            borderColor: '#fff',
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: '#fff',
                          },
                        },
                        '& input': {
                          textAlign: 'center',
                          color: '#fff',
                          fontWeight: 600,
                          fontSize: '0.85rem',
                          background: 'transparent',
                          padding: 0,
                          height: 32,
                        },
                      }}
                    />
                  </Box>

                  {/* 语速输入框 */}
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, ml: 2 }}>
                    <Typography sx={{ color: theme.text.secondary, fontSize: '0.85rem', whiteSpace: 'nowrap' }}>语速:</Typography>
                    <TextField
                      type="number"
                      size="small"
                      value={speed}
                      onChange={e => {
                        let v = Number(e.target.value);
                        if (isNaN(v)) v = 0.5;
                        if (v < 0.5) v = 0.5;
                        if (v > 2) v = 2;
                        setSpeed(v);
                      }}
                      inputProps={{
                        min: 0.5,
                        max: 2,
                        step: 0.1,
                        style: { textAlign: 'center' }
                      }}
                      variant="outlined"
                      sx={{
                        width: 80,
                        minWidth: 80,
                        height: 32,
                        '& .MuiOutlinedInput-root': {
                          height: 32,
                          minHeight: 32,
                          borderRadius: '8px',
                          bgcolor: 'rgba(42,49,83,0.5)',
                          color: '#fff',
                          fontSize: '0.85rem',
                          fontWeight: 600,
                          px: 2,
                          '& fieldset': {
                            borderColor: 'rgba(255,255,255,0.18)',
                          },
                          '&:hover fieldset': {
                            borderColor: '#fff',
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: '#fff',
                          },
                        },
                        '& input': {
                          textAlign: 'center',
                          color: '#fff',
                          fontWeight: 600,
                          fontSize: '0.85rem',
                          background: 'transparent',
                          padding: 0,
                          height: 32,
                        },
                      }}
                    />
                  </Box>

                  {/* 语速设置按钮 */}
                  <Tooltip title="设置语速随机范围">
                    <IconButton
                      size="small"
                      onClick={e => setSpeedRandomAnchorEl(e.currentTarget)}
                      sx={{ color: theme.accent, ml: 0.5 }}
                    >
                      <Settings fontSize="small" />
                    </IconButton>
                  </Tooltip>
                  <Popover
                    open={Boolean(speedRandomAnchorEl)}
                    anchorEl={speedRandomAnchorEl}
                    onClose={() => setSpeedRandomAnchorEl(null)}
                    anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
                    transformOrigin={{ vertical: 'top', horizontal: 'left' }}
                    sx={{ zIndex: 2000 }}
                  >
                    <Box sx={{ p: 2, minWidth: 220 }}>
                      <FormControlLabel
                        control={<Switch checked={speedRandomEnabled} onChange={e => setSpeedRandomEnabled(e.target.checked)} size="small" sx={{ '& .MuiSwitch-switchBase.Mui-checked': { color: theme.accent, '& + .MuiSwitch-track': { backgroundColor: alpha(theme.accent, 0.5) } } }} />}
                        label={<Typography sx={{ color: theme.text.secondary, fontSize: '0.95rem' }}>语速随机</Typography>}
                        sx={{ mb: 2 }}
                      />
                      <Typography sx={{ color: theme.text.primary, fontWeight: 600, mb: 1 }}>语速随机范围</Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <TextField
                          label="最小语速"
                          size="small"
                          type="number"
                          inputProps={{ step: 0.1, min: 0.5, max: 2, pattern: '^([0-1](\\.[0-9])?|2(\\.0)?)$' }}
                          value={tempSpeedRandomMin}
                          onChange={e => {
                            const v = e.target.value;
                            setTempSpeedRandomMin(v);
                            const num = parseFloat(v);
                            if (!isNaN(num) && num >= 0.5 && num <= 2 && /^\d(\.\d)?$|^2(\.0)?$/.test(v)) {
                              setSpeedRandomMin(num);
                            }
                          }}
                          onBlur={() => {
                            if (isNaN(parseFloat(tempSpeedRandomMin)) || !/^\d(\.\d)?$|^2(\.0)?$/.test(tempSpeedRandomMin) || parseFloat(tempSpeedRandomMin) < 0.5 || parseFloat(tempSpeedRandomMin) > 2) {
                              setTempSpeedRandomMin(speedRandomMin.toFixed(1));
                            }
                          }}
                          disabled={!speedRandomEnabled}
                          sx={{ width: 90 }}
                        />
                        <Typography sx={{ color: theme.text.secondary }}>至</Typography>
                        <TextField
                          label="最大语速"
                          size="small"
                          type="number"
                          inputProps={{ step: 0.1, min: 0.5, max: 2, pattern: '^([0-1](\\.[0-9])?|2(\\.0)?)$' }}
                          value={tempSpeedRandomMax}
                          onChange={e => {
                            const v = e.target.value;
                            setTempSpeedRandomMax(v);
                            const num = parseFloat(v);
                            if (!isNaN(num) && num >= 0.5 && num <= 2 && /^\d(\.\d)?$|^2(\.0)?$/.test(v)) {
                              setSpeedRandomMax(num);
                            }
                          }}
                          onBlur={() => {
                            if (isNaN(parseFloat(tempSpeedRandomMax)) || !/^\d(\.\d)?$|^2(\.0)?$/.test(tempSpeedRandomMax) || parseFloat(tempSpeedRandomMax) < 0.5 || parseFloat(tempSpeedRandomMax) > 2) {
                              setTempSpeedRandomMax(speedRandomMax.toFixed(1));
                            }
                          }}
                          disabled={!speedRandomEnabled}
                          sx={{ width: 90 }}
                        />
                      </Box>
                    </Box>
                  </Popover>
                </Box>
                
                {/* 添加音色轮换控制 */}
                <Box sx={{ 
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2,
                  borderTop: `1px solid ${alpha(theme.border, 0.3)}`,
                  pt: 2,
                  mt: 1
                }}>
                  <Typography sx={{ 
                    color: theme.text.secondary,
                    fontSize: '0.85rem',
                    width: 70
                  }}>
                    音色轮换:
                  </Typography>
                  
                  <Switch
                    size="small"
                    checked={modelRotation.enabled}
                    onChange={(e) => toggleModelRotation(e.target.checked)}
                    sx={{
                      '& .MuiSwitch-switchBase.Mui-checked': {
                        color: theme.accent,
                        '& + .MuiSwitch-track': {
                          backgroundColor: alpha(theme.accent, 0.5),
                        },
                      },
                    }}
                  />
                  
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    opacity: modelRotation.enabled ? 1 : 0.6,
                    pointerEvents: modelRotation.enabled ? 'auto' : 'none',
                  }}>
                    <Button
                      size="small"
                      variant={modelRotation.mode === 'timer' ? 'contained' : 'outlined'}
                      onClick={() => updateModelRotationSettings({ mode: 'timer' })}
                      sx={{
                        minWidth: 'unset',
                        px: 1.5,
                        py: 0.5,
                        fontSize: '0.75rem',
                        bgcolor: modelRotation.mode === 'timer' ? theme.accent : 'transparent',
                        borderColor: alpha(theme.accent, 0.5),
                        color: modelRotation.mode === 'timer' ? theme.text.primary : theme.accent,
                        '&:hover': {
                          bgcolor: modelRotation.mode === 'timer' ? alpha(theme.accent, 0.8) : alpha(theme.accent, 0.1),
                        }
                      }}
                    >
                      固定时间
                    </Button>
                    
                    <Button
                      size="small"
                      variant={modelRotation.mode === 'random' ? 'contained' : 'outlined'}
                      onClick={() => updateModelRotationSettings({ mode: 'random' })}
                      sx={{
                        minWidth: 'unset',
                        px: 1.5,
                        py: 0.5,
                        fontSize: '0.75rem',
                        bgcolor: modelRotation.mode === 'random' ? theme.accent : 'transparent',
                        borderColor: alpha(theme.accent, 0.5),
                        color: modelRotation.mode === 'random' ? theme.text.primary : theme.accent,
                        '&:hover': {
                          bgcolor: modelRotation.mode === 'random' ? alpha(theme.accent, 0.8) : alpha(theme.accent, 0.1),
                        }
                      }}
                    >
                      随机时间
                    </Button>
                    
                    <Tooltip title="选择标签组限制轮换范围">
                      <Button
                        size="small"
                        endIcon={<KeyboardArrowDown sx={{ fontSize: '0.9rem' }} />}
                        onClick={(e) => setTagFilterMenuAnchorEl(e.currentTarget)}
                        sx={{
                          minWidth: 'unset',
                          px: 1.5,
                          py: 0.5,
                          fontSize: '0.75rem',
                          bgcolor: modelRotation.tagIds.length > 0 ? alpha(theme.primary, 0.1) : 'transparent',
                          border: `1px solid ${alpha(theme.primary, 0.5)}`,
                          color: theme.primary,
                          '&:hover': {
                            bgcolor: alpha(theme.primary, 0.1),
                          }
                        }}
                      >
                        {modelRotation.tagIds.length > 0 
                          ? `已选${modelRotation.tagIds.length}个标签` 
                          : '选择标签组'}
                      </Button>
                    </Tooltip>
                    
                    <Tooltip title="立即切换">
                      <IconButton
                        size="small"
                        onClick={triggerImmediateModelSwitch}
                        sx={{
                          color: theme.primary,
                          bgcolor: alpha(theme.primary, 0.1),
                          p: 0.8,
                          '&:hover': {
                            bgcolor: alpha(theme.primary, 0.2),
                          }
                        }}
                      >
                        <GraphicEq fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    
                    <Tooltip title="高级设置">
                      <IconButton
                        size="small"
                        onClick={() => setRotationSettingsDialogOpen(true)}
                        sx={{
                          color: theme.accent,
                          bgcolor: alpha(theme.accent, 0.1),
                          p: 0.8,
                          '&:hover': {
                            bgcolor: alpha(theme.accent, 0.2),
                          }
                        }}
                      >
                        <Settings fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    
                    {modelRotation.enabled && (
                      <Box sx={{ 
                        display: 'flex',
                        alignItems: 'center',
                        ml: 1,
                        px: 1.5,
                        py: 0.5,
                        borderRadius: '4px',
                        bgcolor: alpha(theme.background.light, 0.3),
                        border: `1px solid ${alpha(theme.border, 0.1)}`,
                      }}>
                        <Typography sx={{ 
                          fontSize: '0.75rem',
                          color: theme.text.secondary,
                        }}>
                          {rotationState.remainingSeconds > 0 
                            ? `${Math.floor(rotationState.remainingSeconds / 60)}:${(rotationState.remainingSeconds % 60).toString().padStart(2, '0')}后切换` 
                            : '准备切换...'}
                        </Typography>
                      </Box>
                    )}
                  </Box>
                </Box>
              </Box>
            </CardContent>
          </Card>

          {/* 话术脚本区域 */}
          <Card sx={advancedCardStyle}>
            <CardContent sx={advancedCardContent}>
              <Stack direction="row" alignItems="center" spacing={1.5} mb={3}>
                <TextSnippet sx={{ 
                  color: theme.primary,
                  filter: 'drop-shadow(0 0 3px rgba(79,125,249,0.5))',
                }} />
                <Typography variant="h6" sx={{ 
                  color: theme.text.primary,
                  fontWeight: 600,
                  letterSpacing: '0.05em',
                  position: 'relative',
                  '&::after': {
                    content: '""',
                    position: 'absolute',
                    bottom: -5,
                    left: 0,
                    width: '40px',
                    height: '2px',
                    background: `linear-gradient(90deg, ${theme.primary}, transparent)`,
                    borderRadius: '2px',
                  },
                }}>
                  话术脚本
                </Typography>
                <IconButton
                  size="small"
                  onClick={() => setPromptDialogOpen(true)}
                  sx={{ ml: 1, color: theme.primary }}
                  title="设置自定义提示词"
                >
                  <Settings />
                </IconButton>
                <IconButton
                  size="small"
                  onClick={() => setVariableDialogOpen(true)}
                  sx={{ color: theme.accent }}
                  title="变量和随机词使用说明"
                >
                  <Info />
                </IconButton>
              </Stack>

              <Box sx={{ 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'space-between',
                mb: 2
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <IconButton
                    onClick={() => setIsScriptExpanded(!isScriptExpanded)}
                        size="small"
                        sx={{
                      padding: 0.5,
                      color: theme.text.secondary,
                            '&:hover': {
                        color: theme.text.primary,
                        backgroundColor: alpha(theme.text.primary, 0.1),
                            },
                      transition: 'all 0.3s ease',
                    }}
                  >
                    {isScriptExpanded ? <KeyboardArrowUp fontSize="small" /> : <KeyboardArrowDown fontSize="small" />}
                  </IconButton>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Button
                    variant="outlined"
                        size="small"
                    startIcon={<Save sx={{ fontSize: 18 }} />}
                    onClick={handleSaveScript}
                        sx={{
                      borderColor: alpha(theme.text.primary, 0.2),
                      color: theme.text.primary,
                      fontSize: '0.85rem',
                      py: 0.5,
                            '&:hover': {
                        borderColor: theme.primary,
                        backgroundColor: alpha(theme.primary, 0.1),
                      }
                    }}
                  >
                    保存脚本
                  </Button>
                  <Button
                    variant="outlined"
                        size="small"
                    startIcon={<Upload sx={{ fontSize: 18 }} />}
                    onClick={handleImportScript}
                        sx={{
                      borderColor: alpha(theme.text.primary, 0.2),
                      color: theme.text.primary,
                      fontSize: '0.85rem',
                      py: 0.5,
                            '&:hover': {
                        borderColor: theme.primary,
                        backgroundColor: alpha(theme.primary, 0.1),
                      }
                    }}
                  >
                    导入脚本
                  </Button>
                </Box>
              </Box>

              <Collapse in={isScriptExpanded} timeout={300}>
              <TextField
                multiline
                rows={8}
                fullWidth
                value={scriptContent}
                onChange={(e) => setScriptContent(e.target.value)}
                placeholder="在此处输入直播话术脚本，直播过程中会自动按段落转为语音...
支持变量替换，如[昵称]、[人数]、[时间]、[整点]
支持随机词格式：{选项1|选项2|选项3}，实际生成语音时会随机选择一个
支持助播音色标记：[助:这段话由助播音色播报]，会自动使用助播音色"
                sx={{
                  mb: 2,
                  '& .MuiOutlinedInput-root': {
                      backgroundColor: alpha(theme.background.light, 0.5),
                      backdropFilter: 'blur(10px)',
                      color: theme.text.primary,
                    borderRadius: '10px',
                    '& fieldset': {
                        borderColor: alpha(theme.text.primary, 0.1),
                    },
                    '&:hover fieldset': {
                        borderColor: alpha(theme.text.primary, 0.2),
                    },
                    '&.Mui-focused fieldset': {
                        borderColor: theme.primary,
                    },
                      '& .MuiInputBase-input': {
                        fontSize: '0.85rem',
                        lineHeight: 1.6,
                        '&::placeholder': {
                          color: theme.text.muted,
                          opacity: 1,
                        }
                    }
                  }
                }}
              />
              
                {/* 智能控制选项 */}
                <Box sx={{ 
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 2,
                  mb: 2,
                }}>
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 2
                  }}>
                    <Typography sx={{ 
                      color: theme.text.secondary,
                      fontSize: '0.85rem',
                      minWidth: 70
                    }}>
                      智能发挥
                    </Typography>
                    <Box sx={{ 
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                      p: 0.5,
                      backgroundColor: alpha(theme.background.light, 0.3),
                      borderRadius: 2,
                    }}>
                      {[{ label: '全自由', value: 'full' }, { label: '纯回复', value: 'replyOnly' }, { label: '关闭', value: 'off' }].map((mode) => (
                        <Button
                          key={mode.value}
                          size="small"
                          variant={aiMode === mode.value ? 'contained' : 'text'}
                          onClick={() => setAiMode(mode.value as 'full' | 'replyOnly' | 'off')}
                          sx={{
                            minWidth: 60,
                            height: 28,
                            fontSize: '0.8rem',
                            bgcolor: aiMode === mode.value 
                              ? theme.primary 
                              : 'transparent',
                            color: aiMode === mode.value
                              ? theme.text.primary
                              : theme.text.secondary,
                            '&:hover': {
                              bgcolor: aiMode === mode.value
                                ? alpha(theme.primary, 0.9)
                                : alpha(theme.text.primary, 0.1),
                            }
                          }}
                        >
                          {mode.label}
                        </Button>
                      ))}
                    </Box>
                  </Box>

                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 2
                  }}>
                    <Typography sx={{ 
                      color: theme.text.secondary,
                      fontSize: '0.85rem',
                      minWidth: 70
                    }}>
                      用户接待
                    </Typography>
                    <Box sx={{ 
                      display: 'flex',
                      alignItems: 'center',
                      gap: 2,
                      p: 0.5,
                    }}>
                      {[
                        { key: 'welcome', label: '欢迎', tooltip: '是否播报新观众加入直播间的欢迎语' },
                        { key: 'reply', label: '回复', tooltip: '是否播报对用户互动消息的自动回复' },
                        { key: 'time', label: '报时', tooltip: '是否播报当前时间信息' },
                        { key: 'count', label: '人数', tooltip: '是否播报观众人数变化信息' }
                      ].map(({ key, label, tooltip }) => (
                        <Tooltip 
                          key={key} 
                          title={tooltip}
                          placement="top"
                          arrow
                        >
                          <FormControlLabel
                            control={
                              <Switch 
                                checked={userGreeting[key as keyof typeof userGreeting]}
                                onChange={(e) => {
                                  // console.log('[开关变化]', key, ':', e.target.checked);
                                  setUserGreeting((prev: UserGreetingType) => ({
                                    ...prev,
                                    [key]: e.target.checked
                                  }));
                                }}
                                size="small"
                                sx={{
                                  '& .MuiSwitch-switchBase': {
                                    padding: 0.5,
                                  },
                                  '& .MuiSwitch-thumb': {
                                    width: 14,
                                    height: 14,
                                  },
                                  '& .MuiSwitch-track': {
                                    borderRadius: 8,
                                  },
                                  '& .MuiSwitch-switchBase.Mui-checked': {
                                    color: theme.primary,
                                    '& + .MuiSwitch-track': {
                                      backgroundColor: alpha(theme.primary, 0.5),
                                    },
                                  },
                                }}
                              />
                            }
                            label={
                              <Typography sx={{ 
                                color: theme.text.secondary,
                                fontSize: '0.8rem'
                              }}>
                                {label}
                              </Typography>
                            }
                            sx={{
                              margin: 0,
                            }}
                          />
                        </Tooltip>
                      ))}
                    </Box>
                  </Box>
                </Box>

                {/* 启动按钮 */}
                <Box sx={{ 
                  display: 'flex', 
                  justifyContent: 'center',
                }}>
                <Button
                  variant="contained"
                  onClick={aiMode === 'replyOnly' ? handleReplyOnlyToggle : handleLiveStreamToggle}
                  disabled={isLoading || (!isLiveStreaming && aiMode !== 'replyOnly' && (!currentModel || !scriptContent.trim()))}
                  sx={{
                    minWidth: 160,
                    height: 36,
                    background: isLiveStreaming
                      ? theme.error
                      : theme.gradient.primary,
                    color: theme.text.primary,
                    fontSize: '0.9rem',
                    fontWeight: 500,
                    borderRadius: '10px',
                    boxShadow: `0 0 20px ${alpha(isLiveStreaming ? theme.error : theme.primary, 0.3)}`,
                    '&:hover': {
                      background: isLiveStreaming
                        ? alpha(theme.error, 0.9)
                        : theme.gradient.secondary,
                    },
                    '&.Mui-disabled': {
                      background: theme.gradient.primary,
                      opacity: 0.7,
                    }
                  }}
                >
                  {isLoading ? (
                    <Box sx={{ 
                      display: 'flex', 
                      alignItems: 'center', 
                      gap: 1,
                    }}>
                      <SmartToy sx={{ 
                        fontSize: 18,
                        animation: 'pulse 2s infinite',
                        '@keyframes pulse': {
                          '0%': {
                            opacity: 1,
                          },
                          '50%': {
                            opacity: 0.5,
                          },
                          '100%': {
                            opacity: 1,
                          },
                        },
                      }} />
                      <Typography sx={{ 
                        fontSize: '0.85rem',
                        fontWeight: 500,
                      }}>
                        组织语言中...
                      </Typography>
                    </Box>
                  ) : (
                    aiMode === 'replyOnly'
                      ? (isLiveStreaming ? '停止接待' : '启动接待')
                      : (isLiveStreaming ? '停止直播' : '启动AI主播')
                  )}
                </Button>
              </Box>
              </Collapse>
            </CardContent>
          </Card>

          {/* AI响应区域 */}
          <Card sx={advancedCardStyle}>
            <CardContent sx={advancedCardContent}>
              <Stack direction="row" alignItems="center" spacing={1.5} mb={3}>
                <Mic sx={{ 
                  color: theme.accent,
                  filter: 'drop-shadow(0 0 3px rgba(46,192,255,0.5))',
                }} />
                <Typography variant="h6" sx={{ 
                  color: theme.text.primary,
                  fontWeight: 600,
                  letterSpacing: '0.05em',
                  position: 'relative',
                  '&::after': {
                    content: '""',
                    position: 'absolute',
                    bottom: -5,
                    left: 0,
                    width: '40px',
                    height: '2px',
                    background: `linear-gradient(90deg, ${theme.accent}, transparent)`,
                    borderRadius: '2px',
                  },
                }}>
                  AI响应区域
                </Typography>
              </Stack>
              
              <Box sx={{ 
                width: '100%',
                backgroundColor: alpha(theme.background.light, 0.3),
                borderRadius: '10px',
                p: 1.5,
              }}>
                {/* 音频播放器 */}
                <Box sx={{ 
                display: 'flex',
                  alignItems: 'center',
                  gap: 2,
                  p: 2,
                  bgcolor: alpha(theme.background.light, 0.4),
                  borderRadius: '12px',
                  backdropFilter: 'blur(10px)',
                  border: `1px solid ${alpha(theme.text.primary, 0.1)}`,
                  boxShadow: `0 8px 32px ${alpha(theme.background.dark, 0.2)}`,
                }}>
                  <IconButton 
                    size="medium"
                    onClick={() => {
                      if (audioState.isPlaying) {
                        pauseAudio();
                      } else if (audioState.audioUrl) {
                        resumeAudio();
                      }
                    }}
                    disabled={!audioState.audioUrl}
                    sx={{
                      width: 45,
                      height: 45,
                      bgcolor: theme.primary,
                      color: theme.text.primary,
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        bgcolor: alpha(theme.primary, 0.8),
                        transform: 'scale(1.05)',
                      },
                      '&.Mui-disabled': {
                        bgcolor: alpha(theme.text.primary, 0.1),
                        color: alpha(theme.text.primary, 0.3),
                      },
                      boxShadow: `0 4px 12px ${alpha(theme.primary, 0.3)}`,
                    }}
                  >
                    {audioState.isPlaying ? 
                      <Stop sx={{ fontSize: 24 }} /> : 
                      <PlayArrow sx={{ fontSize: 24 }} />
                    }
                  </IconButton>
                  
                  <Box sx={{ 
                    flex: 1,
                    display: 'flex',
                    alignItems: 'center',
                    gap: 2,
                  }}>
                  <Typography sx={{ 
                      color: theme.text.primary,
                    fontSize: '0.85rem',
                      fontWeight: 500,
                      minWidth: 45,
                      textAlign: 'center',
                  }}>
                      {formatTime(audioState.currentTime)}
                  </Typography>

                    <Box 
                      sx={{
                        flex: 1,
                        height: 6,
                        bgcolor: alpha(theme.text.primary, 0.1),
                        borderRadius: '12px',
                        position: 'relative',
                        overflow: 'hidden',
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          height: 8,
                          '& .progress-bar': {
                            background: theme.gradient.secondary,
                            boxShadow: `0 0 20px ${alpha(theme.primary, 0.5)}`,
                          },
                          '& .progress-thumb': {
                            opacity: 1,
                            transform: 'scale(1)',
                          }
                        }
                      }}
                      onClick={(e) => {
                        const rect = e.currentTarget.getBoundingClientRect();
                        const ratio = (e.clientX - rect.left) / rect.width;
                        setProgress(ratio * audioState.duration);
                      }}
                    >
                      <Box 
                        className="progress-bar"
                        sx={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          width: `${(audioState.currentTime / audioState.duration) * 100}%`,
                          height: '100%',
                          background: theme.gradient.primary,
                          borderRadius: '12px',
                          transition: 'all 0.2s ease',
                        }}
                      />
                      <Box 
                        className="progress-thumb"
                        sx={{
                          position: 'absolute',
                          top: '50%',
                          left: `${(audioState.currentTime / audioState.duration) * 100}%`,
                          width: 12,
                          height: 12,
                          bgcolor: theme.text.primary,
                          borderRadius: '50%',
                          transform: 'translate(-50%, -50%) scale(0)',
                          opacity: 0,
                          transition: 'all 0.2s ease',
                          boxShadow: `0 0 10px ${alpha(theme.primary, 0.5)}`,
                      }}
                    />
                  </Box>

                    <Typography sx={{ 
                      color: theme.text.primary,
                      fontSize: '0.85rem',
                      fontWeight: 500,
                      minWidth: 45,
                      textAlign: 'center',
                    }}>
                      {formatTime(audioState.duration)}
                    </Typography>
                  </Box>

                  <Box sx={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    gap: 1,
                    position: 'relative',
                    '&:hover .volume-slider': {
                      width: 60,
                      opacity: 1,
                        }
                  }}>
                    <IconButton 
                      size="small"
                      onClick={() => {
                        setVolume(audioState.volume === 0 ? 100 : 0);
                      }}
                      sx={{
                        color: theme.text.primary,
                        '&:hover': {
                          color: theme.primary,
                          bgcolor: alpha(theme.primary, 0.1),
                        }
                      }}
                    >
                      {audioState.volume === 0 ? (
                        <VolumeOff sx={{ fontSize: 20 }} />
                      ) : (
                        <VolumeUp sx={{ fontSize: 20 }} />
                      )}
                    </IconButton>

                <Box 
                      className="volume-slider"
                  sx={{ 
                        width: 0,
                        opacity: 0,
                        transition: 'all 0.3s ease',
                        overflow: 'hidden',
                  }}
                >
                      <Slider
                        size="small"
                        value={audioState.volume * 100}
                        onChange={(_, value) => {
                          setVolume(value as number);
                        }}
                      sx={{ 
                          color: theme.primary,
                          '& .MuiSlider-rail': {
                            backgroundColor: alpha(theme.text.primary, 0.1),
                          },
                          '& .MuiSlider-track': {
                            background: theme.gradient.primary,
                          },
                          '& .MuiSlider-thumb': {
                            width: 10,
                            height: 10,
                            bgcolor: theme.text.primary,
                            boxShadow: `0 0 10px ${alpha(theme.primary, 0.3)}`,
                            '&:hover, &.Mui-focusVisible': {
                              boxShadow: `0 0 0 8px ${alpha(theme.primary, 0.2)}`,
                            },
                        },
                      }} 
                    />
                    </Box>
                  </Box>

                  <IconButton 
                    size="small"
                    onClick={() => {
                      if (audioState.audioUrl) {
                        const a = document.createElement('a');
                        a.href = audioState.audioUrl;
                        a.download = `audio_${Date.now()}.wav`;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                      }
                    }}
                    disabled={!audioState.audioUrl}
                      sx={{ 
                      color: theme.text.primary,
                      '&:hover': {
                        color: theme.primary,
                        bgcolor: alpha(theme.primary, 0.1),
                      },
                      '&.Mui-disabled': {
                        color: alpha(theme.text.primary, 0.3),
                      }
                    }}
                  >
                    <Download sx={{ fontSize: 20 }} />
                  </IconButton>
                </Box>

                {/* 当前播放文本 */}
                {audioState.isPlaying && (
                  <Box sx={{
                    p: 2,
                    color: theme.text.primary,
                    fontSize: '0.85rem',
                        lineHeight: 1.5,
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 1,
                  }}>
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                    }}>
                      <Box sx={{
                        width: 4,
                        height: 4,
                        borderRadius: '50%',
                        bgcolor: theme.accent,
                        animation: 'blink 1s infinite',
                        '@keyframes blink': {
                          '0%': { opacity: 1 },
                          '50%': { opacity: 0.3 },
                          '100%': { opacity: 1 },
                        }
                      }} />
                      <Typography sx={{
                        flex: 1,
                        color: theme.text.secondary,
                        fontSize: '0.75rem',
                      }}>
                        正在播放
                    </Typography>
                </Box>
                    <Typography sx={{
                      color: theme.text.primary,
                      fontSize: '0.85rem',
                      lineHeight: 1.6,
                      pl: 1,
                      borderLeft: `2px solid ${theme.accent}`,
                    }}>
                      {audioState.currentText || '等待播放...'}
                    </Typography>
                  </Box>
                )}
              </Box>
              
              {/* 插话输入框 */}
              <Box sx={{ 
                display: 'flex',
                flexDirection: 'column',
                gap: 1.5,
                mt: 2,
                position: 'relative',
              }}>
                {/* 插话成功提示 */}
                <Collapse in={showInterruptSuccess}>
                  <Alert 
                    severity="success"
                    sx={{
                      position: 'absolute',
                      bottom: '100%',
                      left: 0,
                      right: 0,
                      mb: 1,
                      backgroundColor: alpha(theme.success, 0.1),
                      color: theme.success,
                      border: `1px solid ${alpha(theme.success, 0.2)}`,
                      borderRadius: '8px',
                      '& .MuiAlert-icon': {
                        color: theme.success
                      }
                    }}
                  >
                    插话内容已添加到播放队列
                  </Alert>
                </Collapse>

                <Box sx={{
                  display: 'flex',
                  gap: 1.5,
                }}>
                  <TextField
                    size="small"
                    fullWidth
                    placeholder="输入插话内容，支持变量"
                    value={interruptText}
                    onChange={(e) => setInterruptText(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        if (currentModel && interruptText.trim()) {
                          handleInterrupt();
                        }
                      }
                    }}
                    InputProps={{
                      sx: {
                        bgcolor: alpha(theme.background.light, 0.3),
                        color: theme.text.primary,
                        borderRadius: '8px',
                        '& .MuiOutlinedInput-notchedOutline': {
                          borderColor: alpha(theme.text.primary, 0.1),
                        },
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: alpha(theme.primary, 0.3),
                        },
                        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                          borderColor: theme.primary,
                        },
                        '& input::placeholder': {
                          color: theme.text.muted,
                          opacity: 1,
                        },
                        '& input': {
                          color: theme.text.primary, // 确保输入文本颜色为白色
                        },
                      },
                    }}
                  />
                  <Button
                    variant="contained"
                    size="small"
                    endIcon={<Send sx={{ fontSize: 16 }} />}
                    onClick={handleInterrupt}
                    disabled={!currentModel || !interruptText.trim()}
                    sx={{
                      minWidth: 80,
                      height: 32,
                      background: theme.gradient.primary,
                      color: theme.text.primary,
                      fontSize: '0.85rem',
                      '&:hover': {
                          background: theme.gradient.secondary,
                      },
                      '&.Mui-disabled': {
                        opacity: 0.6,
                        background: alpha(theme.text.primary, 0.1),
                      }
                    }}
                  >
                    发送
                  </Button>
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={handleOpenPresetMenu}
                    sx={{
                      minWidth: 80,
                      height: 32,
                      borderColor: alpha(theme.text.primary, 0.2),
                      color: theme.text.primary,
                      fontSize: '0.85rem',
                      py: 0.5,
                      '&:hover': {
                        borderColor: theme.primary,
                        backgroundColor: alpha(theme.primary, 0.1),
                      }
                    }}
                  >
                    预设
                  </Button>
                </Box>
              </Box>
            </CardContent>
          </Card>

          {/* AI生成新话术区域 */}
          {/* 删除AI生成新话术的Card区域（包含输入框和按钮） */}
        </Stack>

        {/* 设置自定义提示词弹窗 */}
        <Dialog
          open={promptDialogOpen}
          onClose={() => setPromptDialogOpen(false)}
          maxWidth="sm"
          fullWidth
          PaperProps={{
            sx: {
              bgcolor: theme.background.paper,
              borderRadius: 2,
              boxShadow: '0 8px 32px rgba(0,0,0,0.3)',
              p: 2
            }
          }}
        >
          <DialogTitle sx={{ color: theme.text.primary, fontWeight: 600, pb: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Settings sx={{ mr: 1, color: theme.primary }} />
              设置自定义提示词
            </Box>
          </DialogTitle>
          <DialogContent>
            <TextField
              multiline
              minRows={4}
              fullWidth
              value={prompt}
              onChange={e => setPrompt(e.target.value)}
              placeholder="请输入自定义提示词..."
              sx={{
                mt: 1,
                '& .MuiOutlinedInput-root': {
                  bgcolor: theme.background.light,
                  color: theme.text.primary,
                  '& fieldset': { borderColor: theme.primary },
                  '&:hover fieldset': { borderColor: theme.accent },
                  '&.Mui-focused fieldset': { borderColor: theme.primary }
                },
                '& .MuiInputBase-input': {
                  color: theme.text.primary
                }
              }}
            />
            {/* 新增：重置频率设置项 */}
            <Box sx={{ mt: 2 }}>
              <TextField
                label="每N次自动生成后重置上下文（N）"
                type="text"
                size="small"
                value={tempResetIntervalValue}
                onChange={e => {
                  setTempResetIntervalValue(e.target.value);
                  const v = parseInt(e.target.value, 10);
                  if (!isNaN(v) && v > 0) {
                    setResetInterval(v);
                  }
                }}
                onBlur={() => {
                  if (tempResetIntervalValue === '' || isNaN(parseInt(tempResetIntervalValue)) || parseInt(tempResetIntervalValue) < 1) {
                    setTempResetIntervalValue(resetInterval.toString());
                  }
                }}
                inputProps={{ min: 1 }}
                helperText="防止AI跑偏，建议50~100，越小越容易回到原始脚本"
                sx={{ width: 260 }}
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setPromptDialogOpen(false)} sx={{ color: theme.text.secondary }}>取消</Button>
            <Button
              variant="contained"
              onClick={() => setPromptDialogOpen(false)}
              sx={{ bgcolor: theme.primary, color: theme.text.primary, '&:hover': { bgcolor: theme.secondary } }}
            >
              保存
            </Button>
          </DialogActions>
        </Dialog>

        {/* 变量使用说明弹窗 */}
        <Dialog
          open={variableDialogOpen}
          onClose={() => setVariableDialogOpen(false)}
          maxWidth="sm"
          fullWidth
          PaperProps={{
            sx: {
              bgcolor: theme.background.paper,
              borderRadius: 2,
              boxShadow: '0 8px 32px rgba(0,0,0,0.3)',
              p: 2
            }
          }}
        >
          <DialogTitle sx={{ color: theme.text.primary, fontWeight: 600, pb: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Info sx={{ mr: 1, color: theme.accent }} />
              支持的变量和随机词用法
            </Box>
          </DialogTitle>
          <DialogContent>
            <Typography variant="subtitle1" sx={{ 
              color: theme.text.primary, 
              fontWeight: 600,
              mb: 2,
              mt: 1
            }}>
              在话术中，您可以使用以下变量标记和随机词功能：
            </Typography>
            
            <Box sx={{ 
              mb: 3, 
              p: 2, 
              bgcolor: alpha(theme.background.light, 0.4),
              border: `1px solid ${alpha(theme.border, 0.2)}`,
              borderRadius: '8px'
            }}>
              <Typography variant="subtitle2" sx={{ 
                color: theme.text.primary,
                fontWeight: 600,
                mb: 1.5,
                display: 'flex',
                alignItems: 'center',
                gap: 1
              }}>
                <Box sx={{ width: 8, height: 8, borderRadius: '50%', bgcolor: theme.primary }} />
                变量标记
              </Typography>
              
              <Stack spacing={1.5}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Chip label="[昵称]" variant="outlined" size="small" sx={{ 
                    bgcolor: alpha(theme.primary, 0.1), 
                    borderColor: alpha(theme.primary, 0.3),
                    color: theme.text.primary
                  }}/>
                  <Typography variant="body2" sx={{ color: theme.text.secondary }}>
                    自动替换为观众昵称
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Chip label="[人数]" variant="outlined" size="small" sx={{ 
                    bgcolor: alpha(theme.primary, 0.1), 
                    borderColor: alpha(theme.primary, 0.3),
                    color: theme.text.primary
                  }}/>
                  <Typography variant="body2" sx={{ color: theme.text.secondary }}>
                    自动替换为当前观众人数
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Chip label="[时间]" variant="outlined" size="small" sx={{ 
                    bgcolor: alpha(theme.primary, 0.1), 
                    borderColor: alpha(theme.primary, 0.3),
                    color: theme.text.primary
                  }}/>
                  <Typography variant="body2" sx={{ color: theme.text.secondary }}>
                    自动替换为当前时间，如"18点30分"
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Chip label="[整点]" variant="outlined" size="small" sx={{ 
                    bgcolor: alpha(theme.primary, 0.1), 
                    borderColor: alpha(theme.primary, 0.3),
                    color: theme.text.primary
                  }}/>
                  <Typography variant="body2" sx={{ color: theme.text.secondary }}>
                    自动替换为距离下一个整点的时间，如"距离19点还有30分钟"
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Chip label="[助:内容]" variant="outlined" size="small" sx={{ 
                    bgcolor: alpha(theme.secondary, 0.1), 
                    borderColor: alpha(theme.secondary, 0.3),
                    color: theme.text.primary
                  }}/>
                  <Typography variant="body2" sx={{ color: theme.text.secondary }}>
                    内容将使用助播音色进行播放
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Chip label="{选项1|选项2|选项3}" variant="outlined" size="small" sx={{ 
                    bgcolor: alpha(theme.accent, 0.1), 
                    borderColor: alpha(theme.accent, 0.3),
                    color: theme.text.primary
                  }}/>
                  <Typography variant="body2" sx={{ color: theme.text.secondary }}>
                  实际生成语音时会随机选择一个
                  </Typography>
                </Box>
              </Stack>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button 
              variant="contained"
              onClick={() => setVariableDialogOpen(false)}
              sx={{ bgcolor: theme.accent, color: theme.text.primary, '&:hover': { bgcolor: alpha(theme.accent, 0.8) } }}
            >
              知道了
            </Button>
          </DialogActions>
        </Dialog>
        
        {/* 模型选择对话框 */}
        <Dialog
          open={modelSelectDialogOpen}
          onClose={handleCloseModelDialog}
          maxWidth="lg"
          fullWidth
          PaperProps={{
            sx: {
              bgcolor: theme.background.paper,
              borderRadius: 2,
              boxShadow: '0 8px 32px rgba(0,0,0,0.3)',
              p: '16px 12px 16px 16px', // 减少右侧内边距
              maxHeight: '85vh',
              minHeight: '70vh',
              width: '100%',
              height: 'auto',
              display: 'flex',
              flexDirection: 'column'
            }
          }}
        >
          <DialogTitle sx={{ color: theme.text.primary, fontWeight: 600, pb: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Typography variant="h6">
                选择{selectingModelType === 'main' ? '主播' : '助播'}音色
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <IconButton onClick={handleCloseModelDialog}>
                  <Close />
                </IconButton>
              </Box>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ 
            py: 2,
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
            pr: 2 // 增加右侧内边距，让滚动条远离卡片
          }}>
            {/* 搜索框 */}
            <TextField
              placeholder="搜索模型..."
              variant="outlined"
              size="small"
              fullWidth
              value={modelSearchTerm}
              onChange={(e) => setModelSearchTerm(e.target.value)}
              sx={{
                mb: 2,
                '& .MuiOutlinedInput-root': {
                  bgcolor: alpha(theme.background.light, 0.3),
                  borderRadius: '10px',
                  height: '40px',
                  '& fieldset': {
                    borderColor: alpha('#ffffff', 0.1),
                  },
                  '&:hover fieldset': {
                    borderColor: alpha('#2ec0ff', 0.3),
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#2ec0ff',
                  },
                  '& input': {
                    color: '#ffffff',
                    height: '40px',
                    boxSizing: 'border-box',
                    padding: '0 14px',
                  }
                }
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search sx={{ color: alpha('#ffffff', 0.7) }} />
                  </InputAdornment>
                ),
              }}
            />
            
            {/* 添加标签分类Tabs */}
            <Box sx={{ 
              display: 'flex',
              justifyContent: 'center', 
              alignItems: 'center',
              mb: 2,
              position: 'relative',
              width: '100%',
              height: '40px', // 固定高度
              minHeight: '40px'
            }}>
              {/* 添加标签筛选按钮 */}
              <Tooltip title="更多标签筛选" placement="top">
                <Badge
                  badgeContent={activeTagFilters.length > 1 ? activeTagFilters.length : 0}
                  color="primary"
                  sx={{
                    position: 'absolute',
                    left: 0,
                    '& .MuiBadge-badge': {
                      bgcolor: theme.accent,
                    }
                  }}
                >
                  <IconButton
                    onClick={handleOpenFilterMenu}
                    size="small"
                    sx={{
                      bgcolor: alpha(theme.background.light, 0.3),
                      color: activeTagFilters.length > 1 ? theme.accent : theme.text.secondary,
                      '&:hover': {
                        bgcolor: alpha(theme.background.light, 0.5),
                      },
                      width: '32px',
                      height: '32px'
                    }}
                  >
                    <FilterList fontSize="small" />
                  </IconButton>
                </Badge>
              </Tooltip>
              
              <Tabs 
                value={activeTagFilters.length === 0 ? "all" : activeTagFilters[0]}
                onChange={(_, value) => {
                  if (value === "all") {
                    setActiveTagFilters([]);
                  } else {
                    // 如果点击的是当前已选中的标签，则取消选择
                    if (activeTagFilters.length === 1 && activeTagFilters[0] === value) {
                      setActiveTagFilters([]);
                    } else {
                      // 只保留一个标签筛选
                      setActiveTagFilters([value]);
                    }
                  }
                }}
                variant="scrollable"
                scrollButtons="auto"
                sx={{
                  minHeight: '40px',
                  '& .MuiTab-root': {
                    minHeight: '40px',
                    height: '40px',
                    color: alpha('#ffffff', 0.7),
                    fontSize: '0.85rem',
                    textTransform: 'none',
                    py: 0.5,
                    px: 2,
                    borderRadius: '8px',
                    transition: 'all 0.2s ease',
                    mr: 1,
                    '&.Mui-selected': {
                      color: '#2ec0ff',
                      bgcolor: alpha('#2ec0ff', 0.1),
                    }
                  },
                  '& .MuiTabs-indicator': {
                    display: 'none',
                  }
                }}
              >
                <Tab 
                  label="全部" 
                  value="all" 
                  sx={{ 
                    borderBottom: activeTagFilters.length === 0 ? '2px solid #2ec0ff' : 'none' 
                  }} 
                />
                {tags.map(tag => (
                  <Tab 
                    key={tag.id}
                    label={tag.name} 
                    value={tag.id} 
                    icon={
                      <Box
                        sx={{
                          width: 8,
                          height: 8,
                          borderRadius: '50%',
                          bgcolor: tag.color,
                          mr: 1,
                          display: 'inline-block'
                        }}
                      />
                    }
                    iconPosition="start"
                    sx={{ 
                      borderBottom: activeTagFilters.includes(tag.id) ? `2px solid ${tag.color}` : 'none',
                      color: activeTagFilters.includes(tag.id) ? tag.color : alpha('#ffffff', 0.7),
                      '&.Mui-selected': {
                        color: tag.color,
                        bgcolor: alpha(tag.color, 0.1),
                      }
                    }} 
                  />
                ))}
              </Tabs>
            </Box>
            
            {/* 显示多个活跃的标签筛选器 */}
            <Box sx={{ 
              display: activeTagFilters.length > 1 ? 'flex' : 'none', 
              flexWrap: 'wrap', 
              gap: 0.5, 
              mb: 2,
              minHeight: '28px', // 固定最小高度
              height: 'auto'
            }}>
                <Typography 
                  variant="caption" 
                  sx={{ 
                    color: theme.text.secondary, 
                    mr: 1, 
                    display: 'flex', 
                    alignItems: 'center' 
                  }}
                >
                  多重筛选:
                </Typography>
                {activeTagFilters.map(tagId => {
                  const tag = tags.find(t => t.id === tagId);
                  if (!tag) return null;
                  
                  return (
                    <Chip
                      key={tag.id}
                      label={tag.name}
                      size="small"
                      onDelete={() => handleTagFilterToggle(tag.id)}
                      sx={{
                        bgcolor: alpha(tag.color, 0.15),
                        color: tag.color,
                        borderRadius: '4px',
                        fontSize: '0.75rem',
                        height: '24px',
                        '& .MuiChip-deleteIcon': {
                          color: alpha(tag.color, 0.7),
                          '&:hover': {
                            color: tag.color,
                          }
                        }
                      }}
                    />
                  );
                })}
              </Box>
            
            {/* 模型网格容器 */}
            <Box sx={{ 
              flex: 1, 
              overflow: 'auto',
              minHeight: '450px',
              maxHeight: 'calc(70vh - 180px)',
              '&::-webkit-scrollbar': {
                width: '8px',
                backgroundColor: 'transparent',
                marginLeft: '5px'
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: alpha(theme.text.primary, 0.2),
                borderRadius: '4px',
                '&:hover': {
                  backgroundColor: alpha(theme.text.primary, 0.3)
                }
              },
              '&::-webkit-scrollbar-track': {
                backgroundColor: 'transparent',
                marginRight: '10px'
              },
              paddingRight: '10px',
              marginRight: '5px'
            }}>
              {/* 模型网格 */}
              <Grid container spacing={3} sx={{ pr: 2 }}>
              {filteredModels
                .filter(model => !model.id.includes('使用参考音频'))
                .map(model => {
                  // 确定当前模型是否被选中
                  const isSelected = selectingModelType === 'main' 
                    ? currentModel?.id === model.id 
                    : assistantModel?.id === model.id;
                  
                  // 获取模型的标签
                  const modelTagIds = modelTags[model.id] || [];
                  const modelTagsList = modelTagIds.length > 0 
                    ? tags.filter(tag => modelTagIds.includes(tag.id))
                    : [];
                  
                  // 如果有标签筛选，检查模型是否符合筛选条件
                  if (activeTagFilters.length > 0 && !activeTagFilters.every(tagId => modelTagIds.includes(tagId))) {
                    return null; // 不显示不符合筛选条件的模型
                  }
                    
                  return (
                    <Grid item xs={12} sm={6} md={4} key={model.id}>
                      <Card
                        onClick={() => {
                          if(selectingModelType === 'main') {
                            setCurrentModel(model);
                          } else {
                            setAssistantModel(model);
                          }
                          setModelSelectDialogOpen(false);
                        }}
                        sx={{
                          position: 'relative',
                          cursor: 'pointer',
                          borderRadius: '12px',
                          background: isSelected 
                            ? `linear-gradient(135deg, ${alpha('#151c32', 0.7)}, ${alpha('#080e1c', 0.8)})`
                            : alpha('#151c32', 0.6),
                          border: isSelected 
                            ? `2px solid ${alpha('#2ec0ff', 0.8)}` 
                            : `1px solid ${alpha('#ffffff', 0.1)}`,
                          padding: '18px',
                          height: '100%',
                          minHeight: '200px',
                          display: 'flex',
                          flexDirection: 'column',
                          overflow: 'hidden',
                          boxShadow: isSelected 
                            ? `0 10px 20px rgba(0,0,0,0.2), 0 0 15px ${alpha('#2ec0ff', 0.3)}` 
                            : '0 8px 16px rgba(0,0,0,0.15)',
                          transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                          '&:hover': {
                            transform: 'translateY(-5px)',
                            boxShadow: isSelected 
                              ? `0 15px 30px rgba(0,0,0,0.25), 0 0 20px ${alpha('#2ec0ff', 0.4)}` 
                              : '0 12px 24px rgba(0,0,0,0.2)',
                            border: isSelected 
                              ? `2px solid ${alpha('#2ec0ff', 0.8)}` 
                              : `1px solid ${alpha('#2ec0ff', 0.5)}`,
                          },
                          '&::before': {
                            content: '""',
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            height: '2px',
                            background: isSelected 
                              ? `linear-gradient(90deg, transparent, ${alpha('#2ec0ff', 0.8)}, transparent)` 
                              : 'transparent',
                            opacity: 0.8,
                          },
                          '&::after': {
                            content: '""',
                            position: 'absolute',
                            bottom: 0,
                            left: isSelected ? '-100%' : '-200%',
                            width: '200%',
                            height: '1px',
                            background: `linear-gradient(90deg, transparent, ${alpha('#2ec0ff', 0.5)}, transparent)`,
                            transition: 'left 1.5s ease',
                            animation: isSelected ? 'shimmer 3s infinite linear' : 'none',
                          },
                          '@keyframes shimmer': {
                            '0%': { left: '-100%' },
                            '100%': { left: '100%' },
                          },
                        }}
                      >
                        {isSelected && (
                          <Box
                            sx={{
                              position: 'absolute',
                              top: 12,
                              right: 12,
                              color: '#2ec0ff',
                              animation: 'pulse 2s infinite ease-in-out',
                              '@keyframes pulse': {
                                '0%': { opacity: 0.7 },
                                '50%': { opacity: 1 },
                                '100%': { opacity: 0.7 },
                              },
                              zIndex: 2,
                            }}
                          >
                            <CheckCircle fontSize="small" />
                          </Box>
                        )}
                        
                        {/* 标签管理按钮 */}
                        <IconButton
                          size="small"
                          onClick={(e) => handleOpenTagMenu(e, model.id)}
                          sx={{
                            position: 'absolute',
                            top: 8,
                            right: isSelected ? 38 : 8,
                            zIndex: 3,
                            bgcolor: alpha('#ffffff', 0.1),
                            color: '#ffffff',
                            width: 28,
                            height: 28,
                            '&:hover': {
                              bgcolor: alpha('#2ec0ff', 0.2),
                            }
                          }}
                        >
                          <MoreVert fontSize="small" />
                        </IconButton>
                        
                        <Box sx={{ mb: 2 }}>
                          <Typography 
                            variant="subtitle1" 
                            component="div"
                            sx={{ 
                              fontWeight: 700,
                              color: isSelected ? '#2ec0ff' : '#ffffff',
                              mb: 1.5,
                              display: '-webkit-box',
                              WebkitLineClamp: 1,
                              WebkitBoxOrient: 'vertical',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              textShadow: isSelected ? '0 0 8px rgba(46,192,255,0.4)' : 'none',
                              fontSize: '1.2rem',
                              lineHeight: '1.4',
                              position: 'relative',
                              paddingLeft: isSelected ? '12px' : '0',
                              transition: 'all 0.3s ease',
                              '&::before': {
                                content: '""',
                                position: 'absolute',
                                left: 0,
                                top: '50%',
                                transform: 'translateY(-50%)',
                                width: isSelected ? '4px' : '0',
                                height: '80%',
                                backgroundColor: '#2ec0ff',
                                borderRadius: '2px',
                                transition: 'all 0.3s ease',
                              }
                            }}
                          >
                            {model.name.replace('使用参考音频', '').trim()}
                          </Typography>
                          <Typography 
                            sx={{ 
                              color: isSelected ? alpha('#2ec0ff', 0.8) : '#8695bb',
                              fontSize: '0.9rem',
                              display: 'block',
                              fontWeight: isSelected ? 500 : 400,
                              letterSpacing: '0.01em',
                            }}
                          >
                            ID: {model.id.substring(0, 8)}...
                          </Typography>
                        </Box>
                        
                        {/* 标签显示区域 */}
                        {modelTagsList.length > 0 && (
                          <Box sx={{ mb: 2, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {modelTagsList.map(tag => (
                              <Chip
                                key={tag.id}
                                label={tag.name}
                                size="small"
                                onDelete={(e: React.MouseEvent<any>) => {
                                  e.stopPropagation(); // 阻止卡片点击事件
                                  removeTagFromModel(model.id, tag.id);
                                }}
                                deleteIcon={<Close fontSize="small" />}
                                sx={{
                                  bgcolor: alpha(tag.color, 0.2),
                                  color: tag.color,
                                  borderRadius: '4px',
                                  fontSize: '0.75rem',
                                  height: '22px',
                                  '& .MuiChip-deleteIcon': {
                                    color: alpha(tag.color, 0.7),
                                    '&:hover': {
                                      color: tag.color,
                                    }
                                  }
                                }}
                                onClick={(e) => {
                                  e.stopPropagation(); // 阻止卡片点击事件
                                  handleTagFilterToggle(tag.id);
                                }}
                              />
                            ))}
                          </Box>
                        )}
                        
                        <Box 
                          sx={{ 
                            display: 'flex', 
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            mt: 'auto',
                            pt: 1.5,
                            borderTop: `1px solid ${alpha('#ffffff', 0.05)}`,
                          }}
                        >
                          <Box 
                            sx={{ 
                              height: '8px',
                              width: '60%', 
                              background: `linear-gradient(to right, ${alpha('#4f7df9', 0.3)}, ${alpha('#2ec0ff', 0.3)})`,
                              borderRadius: '4px',
                              overflow: 'hidden',
                              position: 'relative',
                              '&::after': {
                                content: '""',
                                position: 'absolute',
                                top: 0,
                                left: '-100%',
                                width: '100%',
                                height: '100%',
                                background: `linear-gradient(to right, transparent, ${alpha('#4f7df9', 0.8)}, ${alpha('#2ec0ff', 0.8)}, transparent)`,
                                animation: isSelected ? 'progressAnim 2s infinite' : 'none',
                                '@keyframes progressAnim': {
                                  '0%': { left: '-100%' },
                                  '100%': { left: '100%' },
                                },
                              }
                            }}
                          />
                          <IconButton
                            size="small"
                            onClick={(e) => handlePlayModelPreview(model.id, model.name, e)}
                            disabled={previewLoading !== null} // 任何模型正在加载时禁用所有按钮
                            sx={{
                              bgcolor: previewAudio && previewAudio.modelId === model.id && previewAudio.isPlaying 
                                ? alpha('#7a6cf7', 0.2) 
                                : alpha('#4f7df9', 0.1),
                              color: previewAudio && previewAudio.modelId === model.id && previewAudio.isPlaying 
                                ? '#7a6cf7' 
                                : '#4f7df9',
                              '&:hover': {
                                bgcolor: previewAudio && previewAudio.modelId === model.id && previewAudio.isPlaying
                                  ? alpha('#7a6cf7', 0.3)
                                  : alpha('#4f7df9', 0.2),
                                transform: 'scale(1.1)',
                              },
                              transition: 'all 0.2s ease',
                              width: '36px',
                              height: '36px',
                              position: 'relative',
                            }}
                          >
                            {previewLoading === model.id ? (
                              <CircularProgress size={16} sx={{ color: '#4f7df9' }} />
                            ) : previewAudio && previewAudio.modelId === model.id && previewAudio.isPlaying ? (
                              <Stop sx={{ fontSize: '1.3rem' }} />
                            ) : (
                              <PlayCircle sx={{ fontSize: '1.3rem' }} />
                            )}
                          </IconButton>
                        </Box>
                      </Card>
                    </Grid>
                  );
                })}
            </Grid>
            </Box>
            
            <Box sx={{ 
              py: 4, 
              textAlign: 'center',
              bgcolor: alpha(theme.background.light, 0.2),
              borderRadius: '10px',
              border: `1px dashed ${alpha('#ffffff', 0.1)}`,
              display: filteredModels.length === 0 ? 'block' : 'none',
              minHeight: '100px'
            }}>
              <Typography sx={{ color: alpha('#ffffff', 0.7) }}>
                没有找到匹配的模型
              </Typography>
            </Box>
          </DialogContent>
          
          {/* 在对话框中添加标签筛选菜单 */}
          {renderTagFilterMenu()}
        </Dialog>
        
        {/* 创建标签对话框 */}
        <Dialog
          open={isCreateTagDialogOpen}
          onClose={handleCreateTagDialogClose}
          PaperProps={{
            sx: {
              backgroundColor: theme.background.paper,
              backdropFilter: 'blur(10px)',
              border: `1px solid ${alpha(theme.text.primary, 0.1)}`,
              borderRadius: '12px'
            }
          }}
        >
          <DialogTitle sx={{ 
            px: 2, 
            py: 1.5, 
            borderBottom: `1px solid ${alpha('#ffffff', 0.05)}`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}>
            <Typography 
              sx={{ 
                fontSize: '1rem', 
                fontWeight: 600, 
                color: '#ffffff'
              }}
            >
              创建新标签
            </Typography>
            <IconButton 
              size="small" 
              onClick={handleCreateTagDialogClose}
              sx={{ color: '#ffffff' }}
            >
              <Close fontSize="small" />
            </IconButton>
          </DialogTitle>
          <DialogContent sx={{ p: 2 }}>
            <Stack spacing={2} sx={{ my: 1 }}>
              <TextField
                label="标签名称"
                fullWidth
                variant="outlined"
                value={newTagName}
                onChange={(e) => setNewTagName(e.target.value)}
                InputProps={{
                  sx: { 
                    borderRadius: '8px',
                    bgcolor: alpha('#ffffff', 0.05),
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: alpha('#ffffff', 0.1)
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: alpha('#2ec0ff', 0.3)
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#2ec0ff'
                    },
                    color: '#ffffff'
                  }
                }}
                InputLabelProps={{
                  sx: { color: alpha('#ffffff', 0.7) }
                }}
              />
              
              <Box>
                <Typography sx={{ mb: 1, color: alpha('#ffffff', 0.7), fontSize: '0.875rem' }}>
                  标签颜色
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {/* 预设颜色选择 */}
                  {['#4f7df9', '#f97d4f', '#7df94f', '#f94f7d', '#4ff97d', '#7d4ff9'].map(color => (
                    <Box
                      key={color}
                      onClick={() => setNewTagColor(color)}
                      sx={{
                        width: 32,
                        height: 32,
                        borderRadius: '8px',
                        bgcolor: color,
                        cursor: 'pointer',
                        border: newTagColor === color ? `2px solid #ffffff` : `2px solid transparent`,
                        transition: 'all 0.2s',
                        '&:hover': {
                          transform: 'scale(1.1)',
                        }
                      }}
                    />
                  ))}
                  
                  {/* 自定义颜色输入 */}
                  <TextField
                    type="color"
                    value={newTagColor}
                    onChange={(e) => setNewTagColor(e.target.value)}
                    sx={{ 
                      width: 32, 
                      height: 32,
                      '& .MuiInputBase-input': {
                        width: 32,
                        height: 32,
                        p: 0,
                        border: 'none'
                      }
                    }}
                  />
                </Box>
              </Box>
            </Stack>
          </DialogContent>
          <DialogActions sx={{ px: 2, pb: 2, pt: 1 }}>
            <Button 
              onClick={handleCreateTagDialogClose}
              sx={{ 
                color: alpha('#ffffff', 0.7),
                '&:hover': {
                  bgcolor: alpha('#ffffff', 0.05)
                }
              }}
            >
              取消
            </Button>
            <Button 
              onClick={handleCreateTag}
              variant="contained"
              disabled={!newTagName.trim()}
              sx={{ 
                bgcolor: '#2ec0ff',
                '&:hover': {
                  bgcolor: alpha('#2ec0ff', 0.8)
                },
                '&.Mui-disabled': {
                  bgcolor: alpha('#2ec0ff', 0.3),
                  color: alpha('#ffffff', 0.5)
                }
              }}
            >
              创建
            </Button>
          </DialogActions>
        </Dialog>
        
        {/* 标签管理菜单 */}
        <Menu
          anchorEl={tagMenuAnchorEl}
          open={tagDialogOpen}
          onClose={handleCloseTagMenu}
          PaperProps={{
            sx: {
              bgcolor: alpha('#151c32', 0.95),
              color: '#ffffff',
              borderRadius: '8px',
              boxShadow: '0 8px 16px rgba(0,0,0,0.3)',
              border: `1px solid ${alpha('#ffffff', 0.1)}`,
              minWidth: '220px',
              maxHeight: '350px',
              overflowY: 'auto'
            }
          }}
        >
          <Box sx={{ px: 2, py: 1.5, borderBottom: `1px solid ${alpha('#ffffff', 0.1)}` }}>
            <Typography sx={{ fontSize: '0.9rem', fontWeight: 600, mb: 0.5 }}>
              模型标签管理
            </Typography>
            <Typography sx={{ fontSize: '0.8rem', color: alpha('#ffffff', 0.7) }}>
              {selectedModelForTag ? models.find(m => m.id === selectedModelForTag)?.name || '未知模型' : ''}
            </Typography>
          </Box>
          
          {/* 标签列表 */}
          {tags && tags.length > 0 ? (
            tags.map((tag: Tag) => {
              // 检查这个标签是否已应用到模型上
              const modelTagIds = selectedModelForTag ? (modelTags[selectedModelForTag] || []) : [];
              const hasTag = modelTagIds.includes(tag.id);
              
              return (
                <MenuItem 
                  key={tag.id}
                  onClick={() => {
                    if (selectedModelForTag) {
                      if (hasTag) {
                        removeTagFromModel(selectedModelForTag, tag.id);
                      } else {
                        addTagToModel(selectedModelForTag, tag.id);
                      }
                    }
                  }}
                  sx={{
                    py: 1.2,
                    px: 2,
                    borderLeft: hasTag ? `3px solid ${tag.color}` : `3px solid transparent`,
                    '&:hover': {
                      bgcolor: alpha('#ffffff', 0.1),
                    },
                  }}
                >
                  <ListItemIcon sx={{ minWidth: '32px' }}>
                    {hasTag ? (
                      <CheckCircle sx={{ color: tag.color }} />
                    ) : (
                      <Circle sx={{ color: alpha(tag.color, 0.5) }} />
                    )}
                  </ListItemIcon>
                  <ListItemText 
                    primary={tag.name}
                    sx={{ 
                      '& .MuiTypography-root': { 
                        color: hasTag ? tag.color : '#ffffff',
                        fontWeight: hasTag ? 600 : 400,
                      }
                    }}
                  />
                </MenuItem>
              );
            })
          ) : (
            <MenuItem disabled>
              <ListItemText primary="暂无可用标签" />
            </MenuItem>
          )}
          
          <Divider sx={{ my: 1, borderColor: alpha('#ffffff', 0.1) }} />
          
          {/* 创建新标签选项 */}
          <MenuItem 
            onClick={handleCreateTagDialogOpen}
            sx={{
              py: 1.2,
              color: '#2ec0ff',
              '&:hover': {
                bgcolor: alpha('#2ec0ff', 0.1),
              },
            }}
          >
            <ListItemIcon sx={{ minWidth: '32px' }}>
              <Add sx={{ color: '#2ec0ff' }} />
            </ListItemIcon>
            <ListItemText primary="创建新标签" />
          </MenuItem>
        </Menu>
        
        {/* 渲染标签筛选菜单 */}
        {renderTagFilterMenu()}
        
        {/* 音色轮换标签选择菜单 */}
        <Popover
          anchorEl={tagFilterMenuAnchorEl}
          open={Boolean(tagFilterMenuAnchorEl)}
          onClose={() => setTagFilterMenuAnchorEl(null)}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'center',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'center',
          }}
          sx={{
            '& .MuiPaper-root': {
              bgcolor: alpha(theme.background.paper, 0.95),
              border: `1px solid ${alpha(theme.accent, 0.2)}`,
              borderRadius: '8px',
              boxShadow: `0 8px 20px ${alpha('#000', 0.2)}`,
              backdropFilter: 'blur(8px)',
              maxWidth: 280,
              overflow: 'visible',
              zIndex: 9999, // 提高z-index确保在最上层
            }
          }}
        >
          <Box sx={{ p: 2 }}>
            <Typography 
              sx={{ 
                fontSize: '0.9rem', 
                fontWeight: 600, 
                color: theme.text.primary,
                mb: 1
              }}
            >
              选择音色轮换范围
            </Typography>
            
            <Typography 
              sx={{ 
                fontSize: '0.8rem', 
                color: theme.text.secondary,
                mb: 2
              }}
            >
              只在选定标签的音色间切换
            </Typography>
            
            {tags.length === 0 ? (
              <Typography 
                sx={{ 
                  fontSize: '0.85rem', 
                  color: theme.text.secondary,
                  textAlign: 'center',
                  py: 2
                }}
              >
                暂无标签，请先创建标签
              </Typography>
            ) : (
              <Box sx={{ 
                display: 'flex', 
                flexWrap: 'wrap', 
                gap: 1, 
                my: 1 
              }}>
                {tags.map(tag => {
                  const isSelected = modelRotation.tagIds.includes(tag.id);
                  
                  return (
                    <Chip
                      key={tag.id}
                      label={tag.name}
                      size="small"
                      onClick={() => {
                        updateModelRotationSettings({
                          tagIds: isSelected
                            ? modelRotation.tagIds.filter(id => id !== tag.id)
                            : [...modelRotation.tagIds, tag.id]
                        });
                      }}
                      sx={{
                        bgcolor: isSelected ? alpha(tag.color, 0.2) : alpha(theme.background.light, 0.3),
                        color: isSelected ? tag.color : theme.text.secondary,
                        borderRadius: '4px',
                        border: isSelected ? `1px solid ${alpha(tag.color, 0.3)}` : '1px solid transparent',
                        '&:hover': {
                          bgcolor: isSelected ? alpha(tag.color, 0.3) : alpha(theme.background.light, 0.5),
                        }
                      }}
                    />
                  );
                })}
              </Box>
            )}
            
            <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
              <Button
                size="small"
                fullWidth
                variant="outlined"
                onClick={() => {
                  updateModelRotationSettings({ tagIds: [] });
                  setTagFilterMenuAnchorEl(null);
                }}
                sx={{
                  borderColor: alpha(theme.text.primary, 0.15),
                  color: theme.text.secondary,
                  fontWeight: 600,
                  borderRadius: '8px',
                  bgcolor: 'transparent',
                  height: 36,
                  fontSize: '0.95rem',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  whiteSpace: 'nowrap',
                  lineHeight: 1,
                  '&:hover': {
                    bgcolor: alpha(theme.text.primary, 0.07),
                    borderColor: theme.text.primary,
                  }
                }}
              >
                清除全部
              </Button>
              <Button
                size="small"
                fullWidth
                variant="contained"
                onClick={() => {
                  setTagFilterMenuAnchorEl(null);
                  setRotationSettingsDialogOpen(true);
                }}
                sx={{
                  bgcolor: theme.accent,
                  color: theme.text.primary,
                  fontWeight: 600,
                  borderRadius: '8px',
                  height: 36,
                  fontSize: '0.95rem',
                  boxShadow: 'none',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  whiteSpace: 'nowrap',
                  lineHeight: 1,
                  '&:hover': {
                    bgcolor: alpha(theme.accent, 0.85),
                    boxShadow: 'none',
                  }
                }}
              >
                高级设置
              </Button>
            </Box>
          </Box>
        </Popover>
        
        {/* 新增：插话预设菜单 */}
        <Menu
          anchorEl={presetMenuAnchorEl}
          open={Boolean(presetMenuAnchorEl)}
          onClose={handleClosePresetMenu}
          PaperProps={{
            sx: {
              bgcolor: alpha(theme.background.paper, 0.95),
              color: theme.text.primary,
              borderRadius: '8px',
              boxShadow: `0 8px 20px ${alpha('#000', 0.2)}`,
              backdropFilter: 'blur(8px)',
              minWidth: '250px',
              maxHeight: '350px',
              overflowY: 'auto'
            }
          }}
        >
          <Box sx={{ px: 2, py: 1.5, borderBottom: `1px solid ${alpha(theme.text.primary, 0.1)}` }}>
            <Typography sx={{ fontSize: '0.9rem', fontWeight: 600, mb: 0.5 }}>
              插话预设文本
            </Typography>
            <Typography sx={{ fontSize: '0.8rem', color: alpha(theme.text.secondary, 0.7) }}>
              点击选择或删除
            </Typography>
          </Box>
          
          {interruptPresets.length > 0 ? (
            interruptPresets.map(function(text: string, index: number) {
              return (
              <MenuItem
                key={index}
                onClick={() => handleSelectPreset(text)}
                sx={{
                  py: 1.2,
                  px: 2,
                  '&:hover': {
                    bgcolor: alpha(theme.text.primary, 0.05),
                  },
                }}
              >
                <ListItemText primary={text} sx={{ '& .MuiTypography-root': { fontSize: '0.85rem' } }} />
                <IconButton
                  size="small"
                    onClick={(e: React.MouseEvent<any>) => {
                    e.stopPropagation(); // 阻止MenuItem的点击事件
                    handleDeletePreset(text);
                  }}
                  sx={{ color: alpha(theme.text.secondary, 0.7), '&:hover': { color: theme.error } }}
                >
                  <Close fontSize="small" />
                </IconButton>
              </MenuItem>
              );
            })
          ) : (
            <MenuItem disabled>
              <ListItemText primary="暂无预设" sx={{ '& .MuiTypography-root': { fontSize: '0.85rem', color: theme.text.muted } }} />
            </MenuItem>
          )}

          <Divider sx={{ my: 1, borderColor: alpha(theme.text.primary, 0.1) }} />
          
          <Box sx={{ px: 2, pb: 1.5 }}>
            <TextField
              size="small"
              fullWidth
              placeholder="添加新预设"
              value={newPresetText}
              onChange={(e) => setNewPresetText(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleAddPreset();
                }
              }}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      size="small"
                      onClick={handleAddPreset}
                      disabled={!newPresetText.trim()}
                      sx={{ color: theme.primary }}
                    >
                      <Add fontSize="small" />
                    </IconButton>
                  </InputAdornment>
                ),
                sx: {
                  '& input': {
                    color: theme.text.primary, // 确保输入文本颜色为白色
                  },
                },
              }}
              sx={{
                // 这里可以放置TextField自身的其他样式，例如背景色等
              }}
            />
          </Box>
        </Menu>
        
        {/* 音色轮换设置对话框 */}
        <Dialog
          open={rotationSettingsDialogOpen}
          onClose={() => setRotationSettingsDialogOpen(false)}
          maxWidth="xs"
          fullWidth
          PaperProps={{
            sx: {
              bgcolor: theme.background.paper,
              borderRadius: 2,
              boxShadow: '0 8px 32px rgba(0,0,0,0.3)',
              p: 2
            }
          }}
        >
          <DialogTitle sx={{ color: theme.text.primary, fontWeight: 600, pb: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Settings sx={{ mr: 1, color: theme.accent }} />
              音色轮换设置
            </Box>
          </DialogTitle>
          <DialogContent>
            <Stack spacing={3} sx={{ mt: 1 }}>
              <FormControlLabel
                control={
                  <Switch 
                    checked={modelRotation.enabled}
                    onChange={(e) => toggleModelRotation(e.target.checked)}
                    color="primary"
                    sx={{
                      '& .MuiSwitch-switchBase.Mui-checked': {
                        color: theme.accent,
                        '& + .MuiSwitch-track': {
                          backgroundColor: alpha(theme.accent, 0.5),
                        },
                      },
                    }}
                  />
                }
                label={
                  <Typography sx={{ color: theme.text.primary, fontWeight: 500 }}>
                    启用音色轮换
                  </Typography>
                }
              />
              
              <Box>
                <Typography sx={{ color: theme.text.primary, mb: 1, fontWeight: 500 }}>
                  轮换模式
                </Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    fullWidth
                    variant={modelRotation.mode === 'timer' ? 'contained' : 'outlined'}
                    onClick={() => updateModelRotationSettings({ mode: 'timer' })}
                    sx={{
                      bgcolor: modelRotation.mode === 'timer' ? theme.accent : 'transparent',
                      borderColor: theme.accent,
                      color: modelRotation.mode === 'timer' ? theme.text.primary : theme.accent,
                      '&:hover': {
                        bgcolor: modelRotation.mode === 'timer' ? alpha(theme.accent, 0.8) : alpha(theme.accent, 0.1),
                      }
                    }}
                  >
                    固定时间间隔
                  </Button>
                  <Button
                    fullWidth
                    variant={modelRotation.mode === 'random' ? 'contained' : 'outlined'}
                    onClick={() => updateModelRotationSettings({ mode: 'random' })}
                    sx={{
                      bgcolor: modelRotation.mode === 'random' ? theme.accent : 'transparent',
                      borderColor: theme.accent,
                      color: modelRotation.mode === 'random' ? theme.text.primary : theme.accent,
                      '&:hover': {
                        bgcolor: modelRotation.mode === 'random' ? alpha(theme.accent, 0.8) : alpha(theme.accent, 0.1),
                      }
                    }}
                  >
                    随机时间间隔
                  </Button>
                </Box>
              </Box>
              
              {modelRotation.mode === 'timer' ? (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography sx={{ color: theme.text.primary, fontWeight: 500 }}>
                      固定切换间隔 (秒)
                    </Typography>
                  </Box>
                  <TextField
                    fullWidth
                    variant="outlined"
                    type="text" // 使用text类型
                    size="small"
                    value={tempIntervalValue} // 使用临时状态
                    onChange={(e) => {
                      // 更新临时状态
                      setTempIntervalValue(e.target.value);
                      // 如果是有效数字，则更新真实状态
                      const value = parseInt(e.target.value);
                      if (!isNaN(value) && value >= 1 && value <= 86400) {
                        updateModelRotationSettings({ interval: value });
                      }
                    }}
                    onBlur={() => {
                      // 当失去焦点时，如果不是有效值，恢复到上一个有效值
                      if (tempIntervalValue === '' || isNaN(parseInt(tempIntervalValue)) || 
                          parseInt(tempIntervalValue) < 1 || parseInt(tempIntervalValue) > 86400) {
                        setTempIntervalValue((modelRotation.interval ?? 60).toString());
                      }
                    }}
                    onFocus={(e) => e.target.select()} // 自动选中所有文本
                    InputProps={{
                      endAdornment: <InputAdornment position="end">秒</InputAdornment>,
                      inputProps: { min: 1, max: 86400 }
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '8px',
                        bgcolor: alpha(theme.background.light, 0.3),
                        '& fieldset': {
                          borderColor: alpha(theme.accent, 0.3),
                        },
                        '&:hover fieldset': {
                          borderColor: alpha(theme.accent, 0.5),
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: theme.accent,
                        },
                      },
                      '& input': {
                        color: theme.text.primary,
                      }
                    }}
                  />
                </Box>
              ) : (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography sx={{ color: theme.text.primary, fontWeight: 500 }}>
                      随机切换范围 (秒)
                    </Typography>
                  </Box>
                  <Stack direction="row" spacing={2} alignItems="center">
                    <TextField
                      fullWidth
                      variant="outlined"
                      type="text" // 使用text类型
                      size="small"
                      value={tempMinIntervalValue} // 使用临时状态
                      onChange={(e) => {
                        // 更新临时状态
                        setTempMinIntervalValue(e.target.value);
                        // 如果是有效数字，则更新真实状态
                        const value = parseInt(e.target.value);
                        if (!isNaN(value) && value >= 1 && value < modelRotation.maxInterval) {
                          updateModelRotationSettings({ minInterval: value });
                        }
                      }}
                      onBlur={() => {
                        // 当失去焦点时，如果不是有效值，恢复到上一个有效值
                        if (tempMinIntervalValue === '' || isNaN(parseInt(tempMinIntervalValue)) || 
                            parseInt(tempMinIntervalValue) < 1 || parseInt(tempMinIntervalValue) >= modelRotation.maxInterval ||
                            parseInt(tempMinIntervalValue) > 86400) {
                          setTempMinIntervalValue((modelRotation.minInterval ?? 30).toString());
                        }
                      }}
                      onFocus={(e) => e.target.select()} // 自动选中所有文本
                      InputProps={{
                        endAdornment: <InputAdornment position="end">秒</InputAdornment>,
                        inputProps: { min: 1, max: 86400 }
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: '8px',
                          bgcolor: alpha(theme.background.light, 0.3),
                          '& fieldset': {
                            borderColor: alpha(theme.accent, 0.3),
                          },
                          '&:hover fieldset': {
                            borderColor: alpha(theme.accent, 0.5),
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: theme.accent,
                          },
                        },
                        '& input': {
                          color: theme.text.primary,
                        }
                      }}
                    />
                    <Typography sx={{ color: theme.text.secondary }}>至</Typography>
                    <TextField
                      fullWidth
                      variant="outlined"
                      type="text" // 使用text类型
                      size="small"
                      value={tempMaxIntervalValue} // 使用临时状态
                      onChange={(e) => {
                        // 更新临时状态
                        setTempMaxIntervalValue(e.target.value);
                        // 如果是有效数字，则更新真实状态
                        const value = parseInt(e.target.value);
                        if (!isNaN(value) && value > modelRotation.minInterval && value <= 86400) {
                          updateModelRotationSettings({ maxInterval: value });
                        }
                      }}
                      onBlur={() => {
                        // 当失去焦点时，如果不是有效值，恢复到上一个有效值
                        if (tempMaxIntervalValue === '' || isNaN(parseInt(tempMaxIntervalValue)) || 
                            parseInt(tempMaxIntervalValue) <= modelRotation.minInterval || 
                            parseInt(tempMaxIntervalValue) > 86400) {
                          setTempMaxIntervalValue((modelRotation.maxInterval ?? 120).toString());
                        }
                      }}
                      onFocus={(e) => e.target.select()} // 自动选中所有文本
                      InputProps={{
                        endAdornment: <InputAdornment position="end">秒</InputAdornment>,
                        inputProps: { min: 1, max: 86400 }
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: '8px',
                          bgcolor: alpha(theme.background.light, 0.3),
                          '& fieldset': {
                            borderColor: alpha(theme.accent, 0.3),
                          },
                          '&:hover fieldset': {
                            borderColor: alpha(theme.accent, 0.5),
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: theme.accent,
                          },
                        },
                        '& input': {
                          color: theme.text.primary,
                        }
                      }}
                    />
                  </Stack>
                </Box>
              )}
              
              <Box>
                <Typography sx={{ color: theme.text.primary, mb: 1, fontWeight: 500 }}>
                  轮换音色标签 
                  <Typography component="span" sx={{ color: theme.text.secondary, ml: 1, fontSize: '0.85rem' }}>
                    (从选中标签的音色中随机切换)
                  </Typography>
                </Typography>
                <Box sx={{ 
                  display: 'flex', 
                  flexWrap: 'wrap', 
                  gap: 1,
                  maxHeight: '120px',
                  overflowY: 'auto',
                  p: 1,
                  border: `1px solid ${alpha(theme.text.primary, 0.1)}`,
                  borderRadius: '8px',
                  bgcolor: alpha(theme.background.light, 0.3)
                }}>
                  {tags.map(tag => {
                    const isSelected = modelRotation.tagIds.includes(tag.id);
                    
                    return (
                      <Chip
                        key={tag.id}
                        label={tag.name}
                        size="small"
                        onClick={() => {
                          updateModelRotationSettings({
                            tagIds: isSelected
                              ? modelRotation.tagIds.filter(id => id !== tag.id)
                              : [...modelRotation.tagIds, tag.id]
                          });
                        }}
                        sx={{
                          bgcolor: isSelected ? alpha(tag.color, 0.2) : alpha(theme.background.light, 0.3),
                          color: isSelected ? tag.color : theme.text.secondary,
                          borderRadius: '4px',
                          border: isSelected ? `1px solid ${alpha(tag.color, 0.3)}` : '1px solid transparent',
                          '&:hover': {
                            bgcolor: isSelected ? alpha(tag.color, 0.3) : alpha(theme.background.light, 0.5),
                          }
                        }}
                      />
                    );
                  })}
                  {tags.length === 0 && (
                    <Typography sx={{ color: theme.text.secondary, p: 1, textAlign: 'center', width: '100%' }}>
                      暂无可用标签
                    </Typography>
                  )}
                </Box>
                <Typography sx={{ 
                  color: theme.text.secondary, 
                  mt: 1, 
                  fontSize: '0.8rem',
                  fontStyle: 'italic'
                }}>
                  不选择标签则从所有音色中随机切换
                </Typography>
              </Box>
            </Stack>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setRotationSettingsDialogOpen(false)} sx={{ color: theme.text.secondary }}>
              取消
            </Button>
            <Button
              variant="contained"
              onClick={() => {
                // 应用设置
                setRotationSettingsDialogOpen(false);
                // 如果启用了轮换，立即更新可用模型
                if (modelRotation.enabled) {
                  updateAvailableModelsForRotation();
                }
              }}
              sx={{ 
                bgcolor: theme.accent, 
                color: theme.text.primary, 
                '&:hover': { 
                  bgcolor: alpha(theme.accent, 0.8) 
                } 
              }}
            >
              应用设置
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </LiveReplyContext.Provider>
  );
}; 