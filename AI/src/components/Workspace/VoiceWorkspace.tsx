import React, { useState, useRef, useEffect, useMemo, ChangeEvent, useCallback } from 'react';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Container,
  Typography,
  TextField,
  Grid,
  Slider,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Chip,
  Divider,
  useTheme,
  alpha,
  Stack,
  Tooltip,
  Tabs,
  Tab,
  List,
  ListItem,
  Paper,
  FormControlLabel,
  Checkbox,
  Alert,
  InputAdornment,
  Pagination,
  Popover,
  Badge
} from '@mui/material';
import {
  PlayArrow,
  Stop,
  VolumeUp,
  Speed,
  MusicNote,
  Download,
  Search,
  Label,
  MoreVert,
  CheckCircle,
  Circle,
  SettingsSuggest,
  Add,
  Close,
  PlayCircle,
  StopCircle,
  GraphicEq,
  Delete,
  GetApp,
  PauseOutlined,
  PlayCircleOutlined,
  RecordVoiceOver,
  AudioFile,
  FilterList,
  Settings,
  CheckBox,
  CheckBoxOutlineBlank,
  Error as <PERSON>rror<PERSON>con,
  LocalOffer,
  CloudUpload
} from '@mui/icons-material';
import { message } from 'antd';
import { AudioControls } from '../AudioPlayer/AudioControls';
import { 
  useTTS, 
  Model, 
  AudioQueueItem
} from '../../hooks/useTTS';
import { TagStorage, Tag } from '../../utils/tagStorage';
import { useWorkspaceState } from '../../hooks/useWorkspaceState';

// 添加ModelCardProps接口定义
interface ModelCardProps {
  model: {
    id: string;
    name: string;
  };
  isSelected: boolean;
  isPlaying: boolean;
  onSelect: () => void;
  onPlayPreview: () => void;
  tags?: Tag[]; // 修改为直接使用Tag类型数组
  allTags?: Tag[]; // 所有可用标签列表
  onTagClick?: (tagId: string) => void;
  onTagsManage?: (event: React.MouseEvent<HTMLElement>, modelId: string) => void;
  // 添加标签管理函数
  addTagToModel?: (modelId: string, tagId: string) => void;
  removeTagFromModel?: (modelId: string, tagId: string) => void;
  // 添加试听状态属性
  previewLoading?: string | null; // <--- 添加这一行
  previewAudio?: { // <--- 添加这一行
    url: string;
    modelId: string;
    isPlaying: boolean;
  } | null; // <--- 添加这一行
}

interface CustomTheme {
  border: string;
  // 其他主题属性...
}

// 修改theme类型，使border为字符串类型
interface CustomTheme {
  primary: string;
  secondary: string;
  accent: string;
  gradient: {
    primary: string;
    secondary: string;
    accent: string;
  };
  background: {
    main: string;
    paper: string;
    light: string;
    dark: string;
    highlight: string;
  };
  text: {
    primary: string;
    secondary: string;
    muted: string;
  };
  success: string;
  warning: string;
  error: string;
  border: string; // 修改为字符串
}

// 扩展Model类型，添加标签支持
interface ModelWithTags extends Model {
  tags?: string[]; // 标签ID列表
}

interface VoiceWorkspaceProps {
  models: Model[];
  currentModel: Model | null;
  loading: boolean;
  error: string | null;
  audioQueue: AudioQueueItem[];
  serviceStatus: {
    backend: boolean;
    indexTTS: boolean;
  };
  options: {
    speed: number;
    volume: number;
    pitch: number;
  };
  setModel: (model: Model | null) => void;
  /**
   * 生成语音
   * @param text 文本
   * @param isPreview 是否为试听（true时不入队）
   */
  generateSpeech: (text: string, isPreview?: boolean, modelId?: string) => Promise<string | undefined>;
  setOptions: (options: { speed?: number; volume?: number; pitch?: number }) => void;
  playAudio: (audioData: string, index: number) => Promise<void>;
  stopAudio: (index: number, forceReset?: boolean) => void;
  removeAudio: (index: number) => void;
  setAudioProgress: (index: number, progress: number) => void;
  downloadProcessedAudio: (index: number) => Promise<void>;
  onAddTag?: (modelId: string, tag: Tag) => void;
  onRemoveTag?: (modelId: string, tagId: string) => void;
  onCreateTag?: (tag: Omit<Tag, 'id'>) => void;
  onDeleteTag?: (tagId: string) => void;
  allTags?: Tag[];
}

export const VoiceWorkspace: React.FC<VoiceWorkspaceProps> = ({
    models,
    currentModel,
    loading,
    error,
    audioQueue,
    serviceStatus,
    options,
    setModel,
    generateSpeech,
    setOptions,
    playAudio,
    stopAudio,
    removeAudio,
    setAudioProgress,
    downloadProcessedAudio,
    onAddTag,
    onRemoveTag,
    onCreateTag,
    onDeleteTag,
    allTags
}) => {
  // 后续代码修改：theme.border需要是字符串而不是对象
  // 创建一个新的主题对象，确保border是字符串
  const theme: CustomTheme = {
    primary: '#4f7df9', // 主色调
    secondary: '#7a6cf7', // 辅助色
    accent: '#2ec0ff',   // 强调色
    gradient: {
      primary: 'linear-gradient(135deg, #4f7df9 0%, #7a6cf7 100%)',
      secondary: 'linear-gradient(135deg, #7a6cf7 0%, #9b6cf7 100%)',
      accent: 'linear-gradient(135deg, #2ec0ff 0%, #4f7df9 100%)'
    },
    background: {
      main: '#1a1f36', // 主背景色
      paper: '#232842', // 卡片背景色
      light: '#2a3153', // 浅背景色
      dark: '#151b30',  // 深背景色
      highlight: '#323c6b' // 高亮背景色
    },
    text: {
      primary: '#ffffff',  // 主文字色
      secondary: '#c9d1f0', // 次要文字色
      muted: '#8a94b8'      // 弱化文字色
    },
    success: '#4caf50',
    warning: '#ff9800',
    error: '#f44336',
    border: '#373e59' // 边框色 - 修改为字符串
  };

  // 使用useWorkspaceState替代useState管理文本输入
  const [text, setText] = useWorkspaceState<string>('voice-text', '');
  
  // 使用useWorkspaceState管理播放状态
  const [isPlaying, setIsPlaying] = useWorkspaceState<boolean>('voice-isPlaying', false);
  
  // 添加试听加载状态
  const [previewLoading, setPreviewLoading] = useState<string | null>(null); // <--- 添加这一行
  
  // 标签相关状态
  const [tags, setTags] = useWorkspaceState<Tag[]>('voice-tags', []);
  const [selectedTagIds, setSelectedTagIds] = useWorkspaceState<string[]>('voice-selectedTagIds', []);
  const [filterMenuAnchorEl, setFilterMenuAnchorEl] = useWorkspaceState<HTMLElement | null>('voice-filterMenuAnchorEl', null);
  
  // 创建标签对话框状态
  const [isCreateTagDialogOpen, setIsCreateTagDialogOpen] = useWorkspaceState<boolean>('voice-isCreateTagDialogOpen', false);
  const [newTagName, setNewTagName] = useWorkspaceState<string>('voice-newTagName', '');
  const [newTagColor, setNewTagColor] = useWorkspaceState<string>('voice-newTagColor', '#4f7df9');
  
  // 标签管理状态
  const [currentModelForTags, setCurrentModelForTags] = useWorkspaceState<string | null>('voice-currentModelForTags', null);
  const [tagMenuAnchorEl, setTagMenuAnchorEl] = useWorkspaceState<HTMLElement | null>('voice-tagMenuAnchorEl', null);
  
  // 分页和搜索状态
  const [currentPage, setCurrentPage] = useWorkspaceState<number>('voice-currentPage', 1);
  const [searchTerm, setSearchTerm] = useWorkspaceState<string>('voice-searchTerm', '');
  const [currentCategory, setCurrentCategory] = useWorkspaceState<string>('voice-currentCategory', 'all');
  
  // 其他状态
  const [anchorEl, setAnchorEl] = useWorkspaceState<HTMLElement | null>('voice-anchorEl', null);
  const [tagDialogOpen, setTagDialogOpen] = useWorkspaceState<boolean>('voice-tagDialogOpen', false);
  
  // 播放预览状态
  const [playingPreviewId, setPlayingPreviewId] = useWorkspaceState<string | null>('voice-playingPreviewId', null);
  
  // 修复生成状态
  const [generating, setGenerating] = useWorkspaceState<boolean>('voice-generating', false);
  
  // 修复模型过滤
  const [filteredModels, setFilteredModels] = useWorkspaceState<Model[]>('voice-filteredModels', []);
  
  // 修复模型标签
  const [modelTags, setModelTags] = useWorkspaceState<{[modelId: string]: string[]}>('voice-modelTags', {});
  
  // 修复选中模型
  const [selectedModelForTag, setSelectedModelForTag] = useWorkspaceState<string | null>('voice-selectedModelForTag', null);
  
  // 修复标签过滤
  const [activeTagFilters, setActiveTagFilters] = useWorkspaceState<string[]>('voice-activeTagFilters', []);
    
  // 每页显示的模型数量（常量，不需要状态）
  const itemsPerPage = 6;
  
  // 试听音频状态
  /**
   * 试听音频的本地状态，仅用于模型试听，不加入已生成语音队列
   */
  const [previewAudio, setPreviewAudio] = useState<{
    url: string;
    modelId: string;
    isPlaying: boolean;
  } | null>(null);
  
  // 修复renderAudioListItem函数
  const renderAudioListItem = (item: AudioQueueItem, index: number) => {
  return (
      <ListItem
        key={index}
        sx={{
          position: 'relative',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'flex-start',
          marginBottom: 2,
          padding: 2.5,
          borderRadius: '16px',
          background: `linear-gradient(135deg, ${alpha('#151c32', 0.7)}, ${alpha('#080e1c', 0.9)})`,
          border: `1px solid ${alpha('#2ec0ff', 0.2)}`,
          transition: 'all 0.3s ease',
          boxShadow: `0 8px 16px rgba(0,0,0,0.15)`,
          overflow: 'hidden',
          '&:hover': {
            boxShadow: `0 12px 24px rgba(0,0,0,0.2), 0 0 15px ${alpha('#2ec0ff', 0.1)}`,
            transform: 'translateY(-3px)',
            borderColor: alpha('#2ec0ff', 0.4),
          },
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '2px',
            background: `linear-gradient(90deg, transparent, ${alpha('#2ec0ff', 0.5)}, transparent)`,
            opacity: 0.8,
          },
        }}
      >
        {/* 添加序号指示 */}
        <Box
          sx={{
            position: 'absolute',
            top: 12,
            right: 12,
            bgcolor: alpha('#2ec0ff', 0.15),
            color: '#2ec0ff',
            borderRadius: '50%',
            width: 24,
            height: 24,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '0.75rem',
            fontWeight: 'bold',
            border: `1px solid ${alpha('#2ec0ff', 0.3)}`,
            zIndex: 2,
          }}
        >
          {index + 1}
        </Box>
        
        <Box sx={{ 
          width: '100%', 
          position: 'relative',
        }}>
          {/* 音频文本内容 */}
          <Typography sx={{
            color: '#ffffff',
            fontSize: '1rem',
            mb: 2.5,
            lineHeight: 1.6,
            fontWeight: 500,
            position: 'relative',
            pl: 1.5,
            '&::before': {
              content: '""',
              position: 'absolute',
              left: 0,
              top: 0,
              bottom: 0,
              width: '3px',
              borderRadius: '3px',
              background: `linear-gradient(to bottom, #4f7df9, #2ec0ff)`,
            }
          }}>
            {item.sentence}
        </Typography>
          
          {/* 音频波形动画 - 只在播放时显示 */}
          {item.isPlaying && (
            <Box 
              sx={{ 
                position: 'absolute',
                top: -8,
                right: 32,
                display: 'flex',
                alignItems: 'flex-end',
                height: 24,
                gap: 0.5,
              }}
            >
              {[...Array(5)].map((_, i) => (
                <Box 
                  key={i}
                  sx={{
                    width: 3,
                    height: `${Math.floor(Math.random() * 16) + 8}px`,
                    bgcolor: '#2ec0ff',
                    borderRadius: '2px',
                    animation: 'soundWave 0.8s infinite ease',
                    animationDelay: `${i * 0.1}s`,
                    '@keyframes soundWave': {
                      '0%, 100%': { height: `${Math.floor(Math.random() * 8) + 4}px` },
                      '50%': { height: `${Math.floor(Math.random() * 16) + 8}px` }
                    }
                  }}
                />
              ))}
            </Box>
          )}
          
          {/* 音频进度条区域 */}
          <Box sx={{ 
            display: 'flex',
            alignItems: 'center',
            width: '100%',
            mb: 2,
            mt: 1,
          }}>
            <Box sx={{ width: '100%' }}>
              {renderAudioProgress(item, index)}
            </Box>
            </Box>
            
          {/* 控制按钮组 */}
          <Box 
            sx={{ 
              display: 'flex', 
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            {/* 播放/暂停主按钮 */}
              <Button
                onClick={() => handlePlayPause(index)}
                disabled={!item.audioUrl}
              variant={item.isPlaying ? "contained" : "outlined"}
              startIcon={
                item.loading ? (
                  <CircularProgress size={18} sx={{ color: 'inherit' }} />
                ) : item.isPlaying ? (
                  <PauseOutlined />
                ) : (
                  <PlayCircleOutlined />
                )
              }
                sx={{ 
                minWidth: 110,
                height: 38,
                borderRadius: '10px',
                boxShadow: item.isPlaying ? `0 0 15px ${alpha('#2ec0ff', 0.5)}` : 'none',
                background: item.isPlaying 
                  ? `linear-gradient(90deg, ${alpha('#4f7df9', 0.8)}, ${alpha('#2ec0ff', 0.8)})` 
                  : 'transparent',
                borderColor: item.isPlaying ? 'transparent' : alpha('#2ec0ff', 0.5),
                color: item.isPlaying ? '#ffffff' : '#2ec0ff',
                fontWeight: 600,
                transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                  '&:hover': {
                  background: item.isPlaying 
                    ? `linear-gradient(90deg, ${alpha('#4f7df9', 0.9)}, ${alpha('#2ec0ff', 0.9)})` 
                    : alpha('#2ec0ff', 0.1),
                  borderColor: item.isPlaying ? 'transparent' : '#2ec0ff',
                  transform: 'translateY(-2px)',
                  boxShadow: item.isPlaying 
                    ? `0 0 20px ${alpha('#2ec0ff', 0.6)}` 
                    : `0 0 10px ${alpha('#2ec0ff', 0.2)}`,
                },
                '&:active': {
                  transform: 'translateY(0)',
                },
                '&.Mui-disabled': {
                  background: alpha('#151c32', 0.5),
                  borderColor: alpha('#ffffff', 0.1),
                  color: alpha('#ffffff', 0.3),
                }
              }}
            >
              {item.loading ? '加载中...' : item.isPlaying ? '暂停' : '播放'}
              </Button>

            {/* 右侧功能按钮组 */}
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Tooltip title="下载处理后的音频" placement="top" arrow>
              <IconButton 
                onClick={() => downloadProcessedAudio(index)}
                sx={{ 
                    color: '#2ec0ff',
                    bgcolor: alpha('#2ec0ff', 0.1),
                    width: 38,
                    height: 38,
                    borderRadius: '10px',
                    border: `1px solid ${alpha('#2ec0ff', 0.2)}`,
                  '&:hover': {
                      bgcolor: alpha('#2ec0ff', 0.2),
                      transform: 'translateY(-2px)',
                      boxShadow: `0 5px 10px ${alpha('#000000', 0.2)}`,
                    },
                    transition: 'all 0.2s ease',
                  }}
                >
                  <GraphicEq sx={{ fontSize: 20 }} />
              </IconButton>
            </Tooltip>
              
              <Tooltip title="下载原始音频" placement="top" arrow>
              <IconButton 
                onClick={() => {
                  const link = document.createElement('a');
                  link.href = item.audioUrl || '';
                  link.download = `audio_${index + 1}.wav`;
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                  message.success('原始音频下载成功');
                }}
                sx={{ 
                    color: '#4f7df9',
                    bgcolor: alpha('#4f7df9', 0.1),
                    width: 38,
                    height: 38,
                    borderRadius: '10px',
                    border: `1px solid ${alpha('#4f7df9', 0.2)}`,
                  '&:hover': {
                      bgcolor: alpha('#4f7df9', 0.2),
                      transform: 'translateY(-2px)',
                      boxShadow: `0 5px 10px ${alpha('#000000', 0.2)}`,
                    },
                    transition: 'all 0.2s ease',
                  }}
                >
                  <GetApp sx={{ fontSize: 20 }} />
              </IconButton>
            </Tooltip>
              
              <Tooltip title="删除此音频" placement="top" arrow>
              <IconButton 
                onClick={() => removeAudio(index)}
                sx={{ 
                    color: '#f44336',
                    bgcolor: alpha('#f44336', 0.1),
                    width: 38,
                    height: 38,
                    borderRadius: '10px',
                    border: `1px solid ${alpha('#f44336', 0.2)}`,
                  '&:hover': {
                      bgcolor: alpha('#f44336', 0.2),
                      transform: 'translateY(-2px)',
                      boxShadow: `0 5px 10px ${alpha('#000000', 0.2)}`,
                    },
                    transition: 'all 0.2s ease',
                  }}
                >
                  <Delete sx={{ fontSize: 20 }} />
              </IconButton>
            </Tooltip>
            </Box>
          </Box>
        </Box>
      </ListItem>
    );
  };

  // 处理播放/暂停切换
  const handlePlayPause = (index: number) => {
    const audioItem = audioQueue[index];
    if (!audioItem) return;
    
    if (audioItem.isPlaying) {
      // 如果正在播放，执行暂停操作 - 传递false表示不重置进度
      stopAudio(index, false);
    } else {
      // 先停止所有正在播放的音频
      audioQueue.forEach((item, i) => {
        if (item.isPlaying && i !== index) {
          // 停止其他音频时完全重置 - 传递true表示需要重置进度
          stopAudio(i, true);
        }
      });
      
      // 播放当前选中的音频
      playAudio(audioItem.audioUrl, index).catch((error: Error) => {
        console.error('播放失败:', error);
        message.error('播放失败，请重试');
      });
    }
  };
  
  // 添加一个直接用于渲染的播放进度组件，使用CSS动画实现平滑过渡
  const PlayingProgressBar = ({ item, index }: { item: AudioQueueItem, index: number }) => {
    const [localProgress, setLocalProgress] = useState<number>(item.progress || 0);
    
    // 当外部progress变化时更新本地状态
    useEffect(() => {
      setLocalProgress(item.progress || 0);
    }, [item.progress]);
    
    // 使用requestAnimationFrame实现平滑动画
    useEffect(() => {
      let animationId: number;
      
      if (item.isPlaying && item.duration) {
        const animate = () => {
          setLocalProgress((prev: number) => {
            // 每帧增加一点进度，但不超过100%
            const increment = (1 / item.duration!) * 0.05; // 每帧增加的百分比
            const newProgress = Math.min(100, prev + increment);
            // 如果接近实际进度，就直接设为实际进度
            if (Math.abs(newProgress - (item.progress || 0)) < 0.5) {
              return item.progress || 0;
            }
            return newProgress;
          });
          animationId = requestAnimationFrame(animate);
        };
        
        animationId = requestAnimationFrame(animate);
      }
      
      return () => {
        if (animationId) {
          cancelAnimationFrame(animationId);
        }
      };
    }, [item.isPlaying, item.duration, item.progress]);
    
    return (
        <Box sx={{ 
          width: '100%', 
          padding: '0 5px'
        }}>
          <Slider
          value={localProgress}
            onChange={(_, value) => {
              // 计算新的时间位置
            const newCurrentTime = ((value as number) / 100) * (item.duration || 0);
              // 更新进度
              setAudioProgress(index, value as number);
            // 强制更新本地状态，提供即时反馈
            setLocalProgress(value as number);
            }}
            aria-labelledby="audio-progress-slider"
            sx={{
            color: '#2ec0ff',
              height: 4,
              padding: '15px 0',
              '& .MuiSlider-thumb': {
              width: 14,
              height: 14,
                backgroundColor: '#ffffff',
              boxShadow: `0 0 0 2px #2ec0ff`,
              transition: 'all 0.2s',
                '&:hover, &.Mui-focusVisible': {
                boxShadow: `0 0 0 4px ${alpha('#2ec0ff', 0.3)}`,
              },
              '&::before': {
                boxShadow: '0 0 1px 0 rgba(0,0,0,0.32)',
                content: '""',
                position: 'absolute',
                width: 6,
                height: 6,
                borderRadius: '50%',
                background: '#2ec0ff',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                },
              },
              '& .MuiSlider-rail': {
              opacity: 0.3,
                backgroundColor: alpha('#ffffff', 0.2),
              borderRadius: 4,
              height: 4,
            },
            '& .MuiSlider-track': {
              border: 'none',
              height: 4,
              borderRadius: 4,
              background: `linear-gradient(to right, ${alpha('#4f7df9', 0.8)}, ${alpha('#2ec0ff', 0.8)})`,
              boxShadow: `0 0 8px ${alpha('#2ec0ff', 0.3)}`,
              },
            }}
          />
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
          color: alpha('#ffffff', 0.7),
          mt: 0.5,
          px: 0.5,
        }}>
          <Typography variant="caption" sx={{ 
            fontSize: '0.75rem',
            fontFamily: 'monospace',
            fontWeight: 500,
          }}>
            {formatTime(item.currentTime || 0)}
            </Typography>
          <Typography variant="caption" sx={{ 
            fontSize: '0.75rem',
            fontFamily: 'monospace',
            fontWeight: 500,
          }}>
            {formatTime(item.duration || 0)}
            </Typography>
          </Box>
        </Box>
    );
  };

  // 修改音频进度条渲染函数，使用新的PlayingProgressBar组件
  const renderAudioProgress = (audioItem: AudioQueueItem, index: number) => {
    return <PlayingProgressBar item={audioItem} index={index} />;
  };

  const handleModelChange = (modelId: string) => {
    if (!modelId) {
      return;
    }
    
    const selectedModel = models.find(m => m.id === modelId);
    if (!selectedModel) {
      return;
    }

    setModel(selectedModel);
  };

  const handleGenerateClick = async () => {
    try {
      if (!text.trim()) {
        message.error('请输入要转换的文本');
        return;
      }
      
      if (!currentModel) {
        message.error('请先选择一个模型');
        return;
      }
      
      setGenerating(true);
      // 正式生成时不传isPreview参数
      const audioData = await generateSpeech(text);
      if (audioData) {
        message.success('语音生成成功');
      }
    } catch (err: any) {
      console.error('生成失败:', err);
      
      // 设置错误信息
      let errorMsg = '生成失败';
      if (err && err.message) {
        errorMsg = err.message;
      }
      
      message.error(errorMsg);
    } finally {
      setGenerating(false);
    }
  };

  // 组件加载时，如果没有选择模型则自动选择第一个
  useEffect(() => {
    if (models.length > 0 && !currentModel) {
      const firstModel = models[0];
      setModel(firstModel);
    }
  }, [models, currentModel, setModel]);

  // 监听当前模型变化
  useEffect(() => {
    // 不需要输出日志
  }, [currentModel]);

  /**
   * 将 base64 音频数据转换为可播放的 URL
   * @param base64 base64 字符串
   * @returns 可用于 audio 标签的 object URL
   */
  function base64ToAudioUrl(base64: string): string {
    try {
      // 检查是否有 data: 前缀，没有则补全
      let realBase64 = base64;
      let mime = 'audio/wav';
      if (!base64.startsWith('data:')) {
        // 补全前缀
        realBase64 = `data:audio/wav;base64,${base64}`;
      }
      const arr = realBase64.split(',');
      mime = arr[0].match(/:(.*?);/)?.[1] || 'audio/wav';
      const bstr = atob(arr[1]);
      let n = bstr.length;
      const u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      const blob = new Blob([u8arr], { type: mime });
      return URL.createObjectURL(blob);
    } catch (e) {
      // 错误处理
      console.error('base64ToAudioUrl 转换失败，base64内容：', base64);
      throw e;
    }
  }

  /**
   * 去除模型名中的文件扩展名
   * @param name 模型文件名
   * @returns 去除扩展名后的模型名
   */
  function getModelDisplayName(name: string): string {
    return name.replace(/\.[^/.]+$/, '');
  }

  /**
   * 处理模型试听播放
   * @param modelId 当前模型ID
   * @param modelName 当前模型名称
   */
  const handlePlayModelPreview = async (modelId: string, modelName: string) => {
    // 如果正在试听同一个模型，则暂停
    if (previewAudio && previewAudio.modelId === modelId && previewAudio.isPlaying) {
      setPreviewAudio({ ...previewAudio, isPlaying: false });
      return;
    }
    
    // 如果正在试听其他模型，先停止
    if (previewAudio && previewAudio.isPlaying) {
        // 停止播放
        const audio = new Audio(previewAudio.url);
        audio.pause();
        // 清除旧的 audio url
        URL.revokeObjectURL(previewAudio.url);
        setPreviewAudio(null);
    }
    
    // 设置加载状态
    setPreviewLoading(modelId);

    try {
      // 试听文案（去除扩展名）
      const previewText = `你好，我是${getModelDisplayName(modelName)}，期待和你一起直播`;
      // 生成试听音频（不加入队列）
      const audioData = await generateSpeech(previewText, true, modelId);
      
      if (audioData) {
        // 转换为可播放的 URL
        const audioUrl = base64ToAudioUrl(audioData);
        setPreviewAudio({
          url: audioUrl,
          modelId,
          isPlaying: true
        });
        // 音频生成成功，清除加载状态
        setPreviewLoading(null);
      } else {
        // 音频生成失败，清除加载状态
        setPreviewLoading(null);
      }
    } catch (error) {
      console.error('生成试听音频失败:', error);
      message.error('生成试听音频失败');
      // 发生错误，清除加载状态
      setPreviewLoading(null);
    }
  };

  // 标签数量限制
  const MAX_TAGS = 50;

  // 打开标签筛选菜单
  const handleOpenFilterMenu = (event: React.MouseEvent<HTMLElement>) => {
    setFilterMenuAnchorEl(event.currentTarget);
  };

  // 关闭标签筛选菜单
  const handleCloseFilterMenu = () => {
    setFilterMenuAnchorEl(null);
  };

  // 删除标签
  const handleDeleteTag = (tagId: string) => {
    if (window.confirm(`确定要删除此标签吗？这将从所有模型中移除该标签。`)) {
      // 更新本地状态
      setTags(prev => prev.filter(tag => tag.id !== tagId));
      setModelTags(prev => {
        const updated = { ...prev };
        Object.keys(updated).forEach(modelId => {
          updated[modelId] = updated[modelId].filter(id => id !== tagId);
        });
        return updated;
      });
      
      // 从筛选器中移除
      setActiveTagFilters(prev => prev.filter(id => id !== tagId));
      
      // 使用TagStorage保存
      TagStorage.removeTag(tagId);
      
      // 触发标签系统更改事件
      window.dispatchEvent(new Event('tag-system-change'));
      
      message.success('标签已删除');
    }
  };

  // 添加标签筛选功能
  const handleTagFilterToggle = (tagId: string) => {
    setActiveTagFilters(prev => {
      if (prev.includes(tagId)) {
        return prev.filter(id => id !== tagId);
      } else {
        return [...prev, tagId];
      }
    });
  };

  // 打开标签菜单
  const handleOpenTagMenu = (event: React.MouseEvent<HTMLElement>, modelId: string) => {
    // 阻止事件冒泡，避免触发其他点击事件
    event.stopPropagation();
    // 保存点击位置，用于定位菜单
    setTagMenuAnchorEl(event.currentTarget);
    // 保存当前选中的模型ID
    setSelectedModelForTag(modelId);
    // 打开标签对话框
    setTagDialogOpen(true);
  };

  // 关闭标签菜单
  const handleCloseTagMenu = () => {
    setTagDialogOpen(false);
    setTagMenuAnchorEl(null);
  };

  // 打开创建标签对话框
  const handleCreateTagDialogOpen = () => {
    setIsCreateTagDialogOpen(true);
    handleCloseTagMenu(); // 关闭菜单
  };

  // 关闭创建标签对话框
  const handleCreateTagDialogClose = () => {
    setIsCreateTagDialogOpen(false);
    setNewTagName('');
    setNewTagColor('#4f7df9');
  };

  // 创建新标签
  const handleCreateTag = () => {
    if (!newTagName.trim()) {
      message.error('请输入标签名称');
      return;
    }
    
    if (tags.length >= MAX_TAGS) {
      message.error(`标签数量已达上限 (${MAX_TAGS})`);
      return;
    }
    
    const newTag: Tag = {
      id: `tag_${Date.now()}_${Math.floor(Math.random() * 1000)}`,
      name: newTagName.trim(),
      color: newTagColor
    };
    
    // 更新本地状态
    setTags(prev => [...prev, newTag]);
    
    // 使用TagStorage保存
    TagStorage.addTag(newTag);
    
    // 如果有选中的模型，自动为其添加该标签
    if (selectedModelForTag) {
      setModelTags(prev => ({
        ...prev,
        [selectedModelForTag]: [...(prev[selectedModelForTag] || []), newTag.id]
      }));
      TagStorage.addTagToModel(selectedModelForTag, newTag.id);
    }
    
    // 触发标签系统更改事件
    window.dispatchEvent(new Event('tag-system-change'));
    
    // 关闭对话框并重置状态
    setIsCreateTagDialogOpen(false);
    setNewTagName('');
    setNewTagColor('#4f7df9');
    
    message.success(`标签 "${newTag.name}" 创建成功`);
  };

  // 为模型添加标签
  const addTagToModel = useCallback((modelId: string, tagId: string) => {
    if (!modelId || !tagId) return;
    
    // 更新本地状态
    setModelTags(prev => {
      const modelTags = prev[modelId] || [];
      if (modelTags.includes(tagId)) {
        return prev;
      }
      return {
        ...prev,
        [modelId]: [...modelTags, tagId]
      };
    });
    
    // 使用TagStorage保存
    TagStorage.addTagToModel(modelId, tagId);
    
    // 触发标签系统更改事件
    window.dispatchEvent(new Event('tag-system-change'));
  }, []);

  // 从模型中移除标签
  const removeTagFromModel = useCallback((modelId: string, tagId: string) => {
    if (!modelId || !tagId) return;
    
    // 更新本地状态
    setModelTags(prev => {
      if (!prev[modelId]) {
        return prev;
      }
      return {
        ...prev,
        [modelId]: prev[modelId].filter(id => id !== tagId)
      };
    });
    
    // 使用TagStorage保存
    TagStorage.removeTagFromModel(modelId, tagId);
    
    // 触发标签系统更改事件
    window.dispatchEvent(new Event('tag-system-change'));
  }, []);

  // 切换标签
  const handleToggleTag = (tagId: string) => {
    if (!selectedModelForTag) return;
    
    const modelTagIds = modelTags[selectedModelForTag] || [];
    const hasTag = modelTagIds.includes(tagId);
    
    if (hasTag) {
      // 移除标签
      removeTagFromModel(selectedModelForTag, tagId);
      message.info(`标签已移除`);
    } else {
      // 添加标签
      addTagToModel(selectedModelForTag, tagId);
      message.success(`标签已添加`);
    }
  };

  // 搜索和过滤模型 - 更新以支持标签筛选
  useEffect(() => {
    let result = models;
    
    // 搜索过滤
    if (searchTerm.trim()) {
      const query = searchTerm.toLowerCase().trim();
      result = result.filter(model => 
        model.name.toLowerCase().includes(query) || 
        model.id.toLowerCase().includes(query)
      );
    }
    
    // 分类过滤
    if (currentCategory !== 'all') {
      // 基于标签过滤而不是模型名称
      result = result.filter(model => {
        const modelTagIds = modelTags[model.id] || [];
        return modelTagIds.includes(currentCategory);
      });
    }
    
    // 标签筛选
    if (activeTagFilters.length > 0) {
      result = result.filter(model => {
        const modelTagIds = modelTags[model.id] || [];
        return activeTagFilters.every(tagId => modelTagIds.includes(tagId));
      });
    }
    
    setFilteredModels(result);
    setCurrentPage(1); // 重置到第一页
  }, [searchTerm, models, currentCategory, modelTags, activeTagFilters]);

  // 计算分页
  const totalPages = Math.ceil(filteredModels.length / itemsPerPage);
  const displayedModels = filteredModels.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );
  
  // 处理页面变化
  const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
    setCurrentPage(page);
  };
  
  // 处理搜索
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };
  
  // 处理分类切换
  const handleCategoryChange = (_event: React.SyntheticEvent, newValue: string) => {
    setCurrentCategory(newValue);
  };

  // 高级卡片样式
  const advancedCardStyle = {
    position: 'relative',
    overflow: 'hidden',
    backgroundColor: theme.background.paper,
    borderRadius: '12px',
    boxShadow: `0 10px 25px -5px rgba(0,0,0,0.15), 0 0 1px 1px ${theme.border}`,
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      height: '1px',
      background: 'linear-gradient(90deg, transparent, rgba(79,125,249,0.5), transparent)',
      opacity: 0.8,
    },
    '&:hover': {
      boxShadow: `0 15px 35px -5px rgba(0,0,0,0.2), 0 0 1px 1px rgba(79,125,249,0.15)`,
      transform: 'translateY(-1px)',
    },
    transition: 'all 0.3s ease',
  };

  // 卡片内容样式
  const advancedCardContent = {
    padding: '20px',
  };

  // 高级按钮样式
  const advancedButtonStyle = {
    background: theme.gradient.primary,
    color: '#ffffff',
    fontWeight: 600,
    padding: '10px 24px',
    borderRadius: '10px',
    textTransform: 'none',
    transition: 'all 0.2s ease',
    position: 'relative',
    overflow: 'hidden',
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: -100,
      width: '70%',
      height: '100%',
      background: 'linear-gradient(120deg, transparent, rgba(255,255,255,0.2), transparent)',
      transition: 'left 0.5s ease',
    },
    '&:hover': {
      boxShadow: '0 5px 15px rgba(0,0,0,0.1), 0 0 0 1px rgba(79,125,249,0.2)',
      transform: 'translateY(-2px)',
      '&::before': {
        left: '200%',
      },
    },
    '&:active': {
      transform: 'translateY(0)',
    },
    '&.Mui-disabled': {
      background: alpha(theme.primary, 0.4),
      color: alpha('#ffffff', 0.7),
    },
  };

  // 添加ModelCard组件的定义
  const ModelCard: React.FC<ModelCardProps> = ({ 
    model, 
    isSelected, 
    isPlaying, 
    onSelect, 
    onPlayPreview,
    tags = [],
    allTags = [],
    onTagClick,
    onTagsManage,
    addTagToModel,
    removeTagFromModel,
    previewLoading,
    previewAudio
  }) => {
    // 添加对话框状态管理 - 使用React.useState而不是直接的useState
    const [dialogOpen, setDialogOpen] = React.useState(false);
    const [localTagMenuAnchorEl, setLocalTagMenuAnchorEl] = React.useState<null | HTMLElement>(null);
    
    // 先提取当前模型已应用的标签ID
    const modelTagIds = tags.map(t => t.id);
    
    // 处理标签菜单点击
    const handleTagMenuClick = (event: React.MouseEvent<HTMLElement>) => {
      event.stopPropagation(); // 阻止卡片点击事件传播
      setLocalTagMenuAnchorEl(event.currentTarget); // 设置菜单锚点
    };
    
    // 处理关闭标签菜单
    const handleCloseLocalTagMenu = () => {
      setLocalTagMenuAnchorEl(null);
    };
    
    // 标签管理逻辑
    const handleToggleModelTag = (tagId: string) => {
      const hasTag = modelTagIds.includes(tagId);
      
      if (hasTag) {
        // 如果已有该标签，则移除
        if (removeTagFromModel) {
          removeTagFromModel(model.id, tagId);
          message.info(`标签已从模型移除`);
        }
      } else {
        // 如果没有该标签，则添加
        if (addTagToModel) {
          addTagToModel(model.id, tagId);
          message.success(`标签已添加到模型`);
        }
      }
    };

    // 创建新标签
    const handleCreateNewTag = () => {
      // 触发自定义事件
      const event = new CustomEvent('createTag', { detail: { modelId: model.id } });
      window.dispatchEvent(event);
      // 关闭当前菜单
      handleCloseTagMenu();
    };

    // 处理标签点击
    const handleTagClick = (event: React.MouseEvent, tagId: string) => {
      event.stopPropagation();
      if (onTagClick) {
        onTagClick(tagId);
      }
    };

    // 处理标签删除
    const handleTagDelete = (event: React.MouseEvent, tagId: string) => {
      event.stopPropagation();
      if (removeTagFromModel) {
        removeTagFromModel(model.id, tagId);
        message.success(`标签已从模型移除`);
      }
    };
    
    // 检查当前模型是否正在加载或播放试听
    const isLoadingOrPlayingPreview = previewLoading === model.id || (previewAudio?.modelId === model.id && previewAudio?.isPlaying);
    const isOtherModelLoadingOrPlaying = (previewLoading !== null && previewLoading !== model.id) || (previewAudio?.isPlaying === true && previewAudio?.modelId !== model.id);
    
    return (
      <Card
        onClick={onSelect}
        sx={{
          position: 'relative',
          cursor: 'pointer',
          borderRadius: '12px',
          background: isSelected 
            ? `linear-gradient(135deg, ${alpha('#151c32', 0.7)}, ${alpha('#080e1c', 0.8)})`
            : alpha('#151c32', 0.6),
          border: isSelected 
            ? `2px solid ${alpha('#2ec0ff', 0.8)}` 
            : `1px solid ${alpha('#ffffff', 0.1)}`,
          padding: '18px',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          boxShadow: isSelected 
            ? `0 10px 20px rgba(0,0,0,0.2), 0 0 15px ${alpha('#2ec0ff', 0.3)}` 
            : '0 8px 16px rgba(0,0,0,0.15)',
          transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
          '&:hover': {
            transform: 'translateY(-5px)',
            boxShadow: isSelected 
              ? `0 15px 30px rgba(0,0,0,0.25), 0 0 20px ${alpha('#2ec0ff', 0.4)}` 
              : '0 12px 24px rgba(0,0,0,0.2)',
            border: isSelected 
              ? `2px solid ${alpha('#2ec0ff', 0.8)}` 
              : `1px solid ${alpha('#2ec0ff', 0.5)}`,
          },
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '2px',
            background: isSelected 
              ? `linear-gradient(90deg, transparent, ${alpha('#2ec0ff', 0.8)}, transparent)` 
              : 'transparent',
            opacity: 0.8,
          },
          '&::after': {
            content: '""',
            position: 'absolute',
            bottom: 0,
            left: isSelected ? '-100%' : '-200%',
            width: '200%',
            height: '1px',
            background: `linear-gradient(90deg, transparent, ${alpha('#2ec0ff', 0.5)}, transparent)`,
            transition: 'left 1.5s ease',
            animation: isSelected ? 'shimmer 3s infinite linear' : 'none',
          },
          '@keyframes shimmer': {
            '0%': { left: '-100%' },
            '100%': { left: '100%' },
          },
        }}
      >
        {isSelected && (
          <Box
            sx={{
              position: 'absolute',
              top: 12,
              right: 12,
              color: '#2ec0ff',
              animation: 'pulse 2s infinite ease-in-out',
              '@keyframes pulse': {
                '0%': { opacity: 0.7 },
                '50%': { opacity: 1 },
                '100%': { opacity: 0.7 },
              },
              zIndex: 2,
            }}
          >
            <CheckCircle fontSize="small" />
          </Box>
        )}
        
        {/* 标签管理按钮 - 修复触发处理函数 */}
        <IconButton
          size="small"
          onClick={handleTagMenuClick}
          sx={{
            position: 'absolute',
            top: 8,
            right: isSelected ? 38 : 8,
            zIndex: 3,
            bgcolor: alpha('#ffffff', 0.1),
            color: '#ffffff',
            width: 28,
            height: 28,
            '&:hover': {
              bgcolor: alpha('#2ec0ff', 0.2),
            }
          }}
        >
          <MoreVert fontSize="small" />
        </IconButton>
        
        {/* 标签菜单 - 使用Menu组件替代Dialog */}
        <Menu
          anchorEl={localTagMenuAnchorEl}
          open={Boolean(localTagMenuAnchorEl)}
          onClose={handleCloseLocalTagMenu}
          PaperProps={{
            sx: {
              bgcolor: alpha('#151c32', 0.95),
              color: '#ffffff',
              borderRadius: '8px',
              boxShadow: '0 8px 16px rgba(0,0,0,0.3)',
              border: `1px solid ${alpha('#ffffff', 0.1)}`,
              minWidth: '220px',
              maxHeight: '350px',
              overflowY: 'auto'
            }
          }}
        >
          <Box sx={{ px: 2, py: 1.5, borderBottom: `1px solid ${alpha('#ffffff', 0.1)}` }}>
            <Typography sx={{ fontSize: '0.9rem', fontWeight: 600, mb: 0.5 }}>
              模型标签管理
              </Typography>
            <Typography sx={{ fontSize: '0.8rem', color: alpha('#ffffff', 0.7) }}>
              {model.name}
              </Typography>
            </Box>
          
          {/* 标签列表 */}
              {allTags && allTags.length > 0 ? (
                allTags.map((tag: Tag) => {
                  // 检查这个标签是否已应用到模型上
                  const hasTag = modelTagIds.includes(tag.id);
                  
                  return (
                <MenuItem 
                      key={tag.id}
                      onClick={() => handleToggleModelTag(tag.id)}
                      sx={{
                        py: 1.2,
                        px: 2,
                        borderLeft: hasTag ? `3px solid ${tag.color}` : `3px solid transparent`,
                        '&:hover': {
                      bgcolor: alpha('#ffffff', 0.1),
                        },
                      }}
                    >
                  <ListItemIcon sx={{ minWidth: '32px' }}>
                        {hasTag ? (
                          <CheckCircle sx={{ color: tag.color }} />
                        ) : (
                          <Circle sx={{ color: alpha(tag.color, 0.5) }} />
                        )}
                  </ListItemIcon>
                  <ListItemText 
                    primary={tag.name}
                        sx={{ 
                      '& .MuiTypography-root': { 
                        color: hasTag ? tag.color : '#ffffff',
                          fontWeight: hasTag ? 600 : 400,
                      }
                    }}
                  />
                </MenuItem>
                  );
                })
              ) : (
            <MenuItem disabled>
              <ListItemText primary="暂无可用标签" />
            </MenuItem>
          )}
          
          <Divider sx={{ my: 1, borderColor: alpha('#ffffff', 0.1) }} />
          
          {/* 创建新标签选项 */}
          <MenuItem 
              onClick={handleCreateNewTag}
              sx={{
              py: 1.2,
                color: '#2ec0ff',
                '&:hover': {
                bgcolor: alpha('#2ec0ff', 0.1),
                },
              }}
            >
            <ListItemIcon sx={{ minWidth: '32px' }}>
              <Add sx={{ color: '#2ec0ff' }} />
            </ListItemIcon>
            <ListItemText primary="创建新标签" />
          </MenuItem>
        </Menu>

        {/* 恢复卡片内容 */}
        <Box sx={{ mb: 2 }}>
          <Typography 
            variant="subtitle1" 
            component="div"
            sx={{ 
              fontWeight: 700,
              color: isSelected ? '#2ec0ff' : '#ffffff',
              mb: 1.5,
              display: '-webkit-box',
              WebkitLineClamp: 1,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              textShadow: isSelected ? '0 0 8px rgba(46,192,255,0.4)' : 'none',
              fontSize: '1.2rem',
              lineHeight: '1.4',
              position: 'relative',
              paddingLeft: isSelected ? '12px' : '0',
              transition: 'all 0.3s ease',
              '&::before': {
                content: '""',
                position: 'absolute',
                left: 0,
                top: '50%',
                transform: 'translateY(-50%)',
                width: isSelected ? '4px' : '0',
                height: '80%',
                backgroundColor: '#2ec0ff',
                borderRadius: '2px',
                transition: 'all 0.3s ease',
              }
            }}
          >
            {model.name}
          </Typography>
          <Typography 
            sx={{ 
              color: isSelected ? alpha('#2ec0ff', 0.8) : '#8695bb',
              fontSize: '0.9rem',
              display: 'block',
              fontWeight: isSelected ? 500 : 400,
              letterSpacing: '0.01em',
            }}
          >
            ID: {model.id.substring(0, 8)}...
          </Typography>
        </Box>
        
        {/* 标签显示区域 */}
        {tags && tags.length > 0 && (
          <Box sx={{ mb: 2, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
            {tags.map(tag => (
              <Chip
                key={tag.id}
                label={tag.name}
                size="small"
                onDelete={(e) => {
                  e.stopPropagation(); // 阻止卡片点击事件
                  // 直接从模型中移除该标签
                  if (removeTagFromModel) {
                    removeTagFromModel(model.id, tag.id);
                    message.info(`标签"${tag.name}"已从模型移除`);
                  }
                }}
                deleteIcon={<Close fontSize="small" />}
                sx={{
                  bgcolor: alpha(tag.color, 0.2),
                  color: tag.color,
                  borderRadius: '4px',
                  fontSize: '0.75rem',
                  height: '22px',
                  '& .MuiChip-deleteIcon': {
                    color: alpha(tag.color, 0.7),
                    '&:hover': {
                      color: tag.color,
                    }
                  }
                }}
                onClick={(e) => {
                  e.stopPropagation(); // 阻止卡片点击事件
                  if (onTagClick) {
                    onTagClick(tag.id);
                  }
                }}
              />
            ))}
          </Box>
        )}

        <Typography 
          sx={{ 
            color: '#c5d0ee', 
            mb: 2,
            fontSize: '0.95rem',
            flex: 1,
            opacity: 0.9,
            lineHeight: '1.5',
          }}
        >
          模型ID: {model.id}
        </Typography>

        <Box 
          sx={{ 
            display: 'flex', 
            justifyContent: 'space-between',
            alignItems: 'center',
            mt: 'auto',
            pt: 1.5,
            borderTop: `1px solid ${alpha('#ffffff', 0.05)}`,
          }}
        >
          <Box 
            sx={{ 
              height: '8px',
              width: '60%', 
              background: `linear-gradient(to right, ${alpha('#4f7df9', 0.3)}, ${alpha('#2ec0ff', 0.3)})`,
              borderRadius: '4px',
              overflow: 'hidden',
              position: 'relative',
              '&::after': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: '-100%',
                width: '100%',
                height: '100%',
                background: `linear-gradient(to right, transparent, ${alpha('#4f7df9', 0.8)}, ${alpha('#2ec0ff', 0.8)}, transparent)`,
                animation: isSelected ? 'progressAnim 2s infinite' : 'none',
                '@keyframes progressAnim': {
                  '0%': { left: '-100%' },
                  '100%': { left: '100%' },
                },
              }
            }}
          />
          <IconButton
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              onPlayPreview();
            }}
            // 在其他模型加载或播放时禁用按钮
            disabled={isOtherModelLoadingOrPlaying} // <--- 添加此行
            sx={{
              bgcolor: isPlaying ? alpha('#7a6cf7', 0.2) : alpha('#4f7df9', 0.1),
              color: isPlaying ? '#7a6cf7' : '#4f7df9',
              '&:hover': {
                bgcolor: isPlaying ? alpha('#7a6cf7', 0.3) : alpha('#4f7df9', 0.2),
                transform: 'scale(1.1)',
              },
              transition: 'all 0.2s ease',
              width: '36px',
              height: '36px',
            }}
          >
            {isPlaying ? 
              <StopCircle sx={{ fontSize: '1.3rem' }} /> : 
              (isLoadingOrPlayingPreview ? 
                <CircularProgress size={20} sx={{ color: 'inherit' }} /> : // 显示加载指示器
                <PlayCircle sx={{ fontSize: '1.3rem' }} />
              )
            }
          </IconButton>
        </Box>
      </Card>
    );
  };

  // 修改搜索和过滤区块的代码 
  // 搜索和过滤区
  const renderSearchAndFilterArea = () => (
    <Box sx={{ mb: 3 }}>
      {/* 标签分类组件单独放置并移动到上面 */}
      <Box sx={{ 
        display: 'flex',
        justifyContent: 'center', 
        alignItems: 'center',
        mb: 2,
        mt: -1,
      }}>
        {/* 标签筛选按钮放在全部标签前面 */}
        <Tooltip title="标签筛选">
          <Badge
            badgeContent={activeTagFilters.length}
            color="primary"
            sx={{
              mr: 1,
              '& .MuiBadge-badge': {
                bgcolor: theme.accent,
              }
            }}
          >
            <IconButton
              onClick={handleOpenFilterMenu}
              size="small"
              sx={{
                bgcolor: alpha(theme.background.light, 0.3),
                color: activeTagFilters.length > 0 ? theme.accent : theme.text.secondary,
                '&:hover': {
                  bgcolor: alpha(theme.background.light, 0.5),
                },
                width: '40px',
                height: '40px'
              }}
            >
              <LocalOffer fontSize="small" />
            </IconButton>
          </Badge>
        </Tooltip>
        
        <Tabs 
          value={currentCategory} 
          onChange={handleCategoryChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{
            minHeight: '40px',
            '& .MuiTab-root': {
              minHeight: '40px',
              height: '40px',
              color: alpha('#ffffff', 0.7),
              fontSize: '0.85rem',
              textTransform: 'none',
              py: 0.5,
              px: 2,
              borderRadius: '8px',
              transition: 'all 0.2s ease',
              mr: 1,
              '&.Mui-selected': {
                color: '#2ec0ff',
                bgcolor: alpha('#2ec0ff', 0.1),
              }
            },
            '& .MuiTabs-indicator': {
              display: 'none',
            }
          }}
        >
          <Tab 
            label="全部" 
            value="all" 
            sx={{ 
              borderBottom: currentCategory === 'all' ? '2px solid #2ec0ff' : 'none' 
            }} 
          />
          {tags.map(tag => (
            <Tab 
              key={tag.id}
              label={tag.name} 
              value={tag.id} 
              icon={
                <Box
                  sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    bgcolor: tag.color,
                    mr: 1,
                    display: 'inline-block'
                  }}
                />
              }
              iconPosition="start"
              sx={{ 
                borderBottom: currentCategory === tag.id ? `2px solid ${tag.color}` : 'none',
                color: currentCategory === tag.id ? tag.color : alpha('#ffffff', 0.7),
                '&.Mui-selected': {
                  color: tag.color,
                  bgcolor: alpha(tag.color, 0.1),
                }
              }} 
            />
          ))}
        </Tabs>
      </Box>

      {/* 搜索和其他筛选组件 */}
      <Stack 
        direction={{ xs: 'column', sm: 'row' }} 
        spacing={2} 
        alignItems={{ xs: 'flex-start', sm: 'center' }}
        mb={2}
      >
        <Box 
          sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: 1,
            width: { xs: '100%', sm: activeTagFilters.length > 0 ? '60%' : '100%' },
            transition: 'width 0.3s ease'
          }}
        >
        <TextField
            placeholder="搜索模型..."
            value={searchTerm}
            onChange={handleSearchChange}
          variant="outlined"
            size="small"
            fullWidth
          InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search sx={{ color: alpha('#ffffff', 0.7) }} />
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                bgcolor: alpha(theme.background.light, 0.3),
                borderRadius: '10px',
                height: '40px',
                '& fieldset': {
                  borderColor: alpha('#ffffff', 0.1),
                },
                '&:hover fieldset': {
                  borderColor: alpha('#2ec0ff', 0.3),
                },
                '&.Mui-focused fieldset': {
                  borderColor: '#2ec0ff',
                },
                '& input': {
                  color: '#ffffff',
                  height: '40px',
                  boxSizing: 'border-box',
                  padding: '0 14px',
                }
              }
            }}
          />
        </Box>
        
        <Box sx={{ 
          display: 'flex', 
          alignItems: 'center',
          flexWrap: 'wrap', 
          gap: { xs: 1, sm: 2 } 
        }}>
          {/* 显示活跃的标签筛选器 */}
          {activeTagFilters.length > 0 && (
            <Box sx={{ 
              display: 'flex', 
              flexWrap: 'wrap', 
              gap: 0.5, 
              maxWidth: { xs: '100%', sm: '300px' }
            }}>
              {activeTagFilters.map(tagId => {
                const tag = tags.find(t => t.id === tagId);
                if (!tag) return null;
                
                return (
                  <Chip
                    key={tag.id}
                    label={tag.name}
                    size="small"
                    onDelete={() => handleTagFilterToggle(tag.id)}
                    sx={{
                      bgcolor: alpha(tag.color, 0.15),
                      color: tag.color,
                      borderRadius: '4px',
                      fontSize: '0.75rem',
                      height: '24px',
                      '& .MuiChip-deleteIcon': {
                        color: alpha(tag.color, 0.7),
                        '&:hover': {
                          color: tag.color,
                        }
                      }
                    }}
                  />
                );
              })}
            </Box>
          )}
        </Box>
      </Stack>
      
      {/* 渲染标签筛选菜单 */}
      {renderTagFilterMenu()}
      
      {filteredModels.length === 0 && (
        <Box sx={{ 
          py: 4, 
          textAlign: 'center',
          bgcolor: alpha(theme.background.light, 0.2),
          borderRadius: '10px',
          border: `1px dashed ${alpha('#ffffff', 0.1)}`,
        }}>
          <Typography sx={{ color: alpha('#ffffff', 0.7) }}>
            没有找到匹配的模型
        </Typography>
        </Box>
      )}
    </Box>
  );

  // 自动将模型分配到相应的标签类别
  useEffect(() => {
    // 初始化时，根据模型名称自动分配标签
    const updatedModelTags = { ...modelTags };
    
    models.forEach(model => {
      if (!updatedModelTags[model.id]) {
        updatedModelTags[model.id] = [];
        
        // 根据模型名称特征自动分配标签
        if (model.name.includes('女') || model.name.toLowerCase().includes('f')) {
          updatedModelTags[model.id].push('female');
        } else if (model.name.includes('男') || model.name.toLowerCase().includes('m')) {
          updatedModelTags[model.id].push('male');
        } else {
          updatedModelTags[model.id].push('special');
        }
      }
    });
    
    setModelTags(updatedModelTags);
  }, [models]);

  // 渲染模型列表
  const renderModelGrid = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const displayedModels = filteredModels.slice(startIndex, endIndex);
    
    return (
      <Grid container spacing={2.5}>
        {displayedModels.map(model => {
          // 确保布尔值类型
          const isModelSelected: boolean = (currentModel?.id === model.id) ? true : false;
          const isModelPlaying: boolean = (previewAudio && previewAudio.modelId === model.id && previewAudio.isPlaying) ? true : false;
          
          // 获取模型的标签 - 修复标签筛选逻辑
          const modelTagIds = modelTags[model.id] || [];
          const modelTagsList = modelTagIds.length > 0 
            ? tags.filter(tag => modelTagIds.includes(tag.id))
            : [];
          
          return (
            <Grid item xs={12} sm={6} md={4} key={model.id}>
              <Box sx={{ height: '100%' }}>
                <ModelCard
                  model={model}
                  isSelected={isModelSelected}
                  isPlaying={isModelPlaying}
                  onSelect={() => handleModelChange(model.id)}
                  onPlayPreview={() => handlePlayModelPreview(model.id, model.name)}
                  tags={modelTagsList}
                  allTags={tags} // 传递所有可用标签
                  onTagClick={handleTagFilterToggle}
                  // 传递标签管理函数
                  addTagToModel={addTagToModel}
                  removeTagFromModel={removeTagFromModel}
                  previewLoading={previewLoading} // <--- 传递 previewLoading
                  previewAudio={previewAudio} // <--- 传递 previewAudio
                />
              </Box>
            </Grid>
          );
        })}
      </Grid>
    );
  };

  // 渲染标签筛选菜单
  const renderTagFilterMenu = () => {
    return (
      <Popover
        anchorEl={filterMenuAnchorEl}
        open={Boolean(filterMenuAnchorEl)}
        onClose={handleCloseFilterMenu}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
        sx={{
          '& .MuiPaper-root': {
            bgcolor: alpha(theme.background.paper, 0.95),
            border: `1px solid ${alpha(theme.accent, 0.2)}`,
            borderRadius: '8px',
            boxShadow: `0 8px 20px ${alpha('#000', 0.2)}`,
            backdropFilter: 'blur(8px)',
            maxWidth: 280,
            overflow: 'visible',
          }
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography 
            sx={{ 
              fontSize: '0.9rem', 
              fontWeight: 600, 
              color: theme.text.primary,
              mb: 1
            }}
          >
            按标签筛选
          </Typography>
          
          {tags.length === 0 ? (
            <Typography 
              sx={{ 
                fontSize: '0.85rem', 
                color: theme.text.secondary,
                textAlign: 'center',
                py: 2
              }}
            >
              暂无标签，请先创建标签
            </Typography>
          ) : (
            <Box sx={{ 
              display: 'flex', 
              flexWrap: 'wrap', 
              gap: 1, 
              my: 1 
            }}>
              {tags.map(tag => {
                const isActive = activeTagFilters.includes(tag.id);
                
                return (
                  <Chip
                    key={tag.id}
                    label={tag.name}
                    size="small"
                    onClick={() => handleTagFilterToggle(tag.id)}
                    onDelete={() => handleDeleteTag(tag.id)}
                    sx={{
                      bgcolor: isActive ? alpha(tag.color, 0.2) : alpha(theme.background.light, 0.3),
                      color: isActive ? tag.color : theme.text.secondary,
                      borderRadius: '4px',
                      border: isActive ? `1px solid ${alpha(tag.color, 0.3)}` : '1px solid transparent',
                      '&:hover': {
                        bgcolor: isActive ? alpha(tag.color, 0.3) : alpha(theme.background.light, 0.5),
                      },
                      '& .MuiChip-deleteIcon': {
                        color: isActive ? alpha(tag.color, 0.7) : alpha(theme.text.secondary, 0.7),
                        '&:hover': {
                          color: isActive ? tag.color : theme.text.primary,
                        }
                      }
                    }}
                  />
                );
              })}
            </Box>
          )}
          
          {tags.length < MAX_TAGS && (
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Add />}
              onClick={() => {
                handleCloseFilterMenu();
                handleCreateTagDialogOpen();
              }}
              sx={{
                mt: 1,
                borderColor: alpha(theme.primary, 0.3),
                color: theme.primary,
                '&:hover': {
                  borderColor: theme.primary,
                  bgcolor: alpha(theme.primary, 0.05),
                }
              }}
            >
              创建新标签
            </Button>
          )}
        </Box>
      </Popover>
    );
  };

  // 初始化时加载标签数据
  useEffect(() => {
    const savedTags = TagStorage.getTags();
    const savedModelTags = TagStorage.getModelTags();
    
    if(savedTags.length > 0) {
      setTags(savedTags);
    } else {
      // 如果没有保存的标签，设置默认标签
      const defaultTags = [
        { id: 'female', name: '女声', color: '#FF61A6' },
        { id: 'male', name: '男声', color: '#3F84E5' },
        { id: 'special', name: '特色', color: '#8B5CF6' },
      ];
      setTags(defaultTags);
      TagStorage.saveTags(defaultTags);
    }
    
    if(Object.keys(savedModelTags).length > 0) {
      setModelTags(savedModelTags);
    }
  }, []);

  // 当标签列表变化时保存
  useEffect(() => {
    if(tags.length > 0) {
      TagStorage.saveTags(tags);
    }
  }, [tags]);

  // 当模型标签关联变化时保存
  useEffect(() => {
    if(Object.keys(modelTags).length > 0) {
      // TagStorage.saveModelTags(modelTags); // <--- 删除或注释这行
    }
  }, [modelTags]); // <--- 删除这个 useEffect 块

  // 添加文本处理函数
  const handleTextChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    setText(e.target.value);
  };

  // 格式化时间显示
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // 为确保进度条能正确更新，添加进度条自动更新逻辑
  useEffect(() => {
    const progressUpdateInterval = setInterval(() => {
      // 检查是否有正在播放的音频
      const playingAudioIndex = audioQueue.findIndex(item => item.isPlaying);
      if (playingAudioIndex !== -1) {
        // 获取正在播放的音频项
        const playingItem = audioQueue[playingAudioIndex];
        
        // 如果有当前时间和持续时间信息，但没有明确的进度信息，手动计算进度
        if (playingItem.currentTime !== undefined && playingItem.duration) {
          // 计算近似进度百分比
          const calculatedProgress = Math.min(100, (playingItem.currentTime / playingItem.duration) * 100);
          
          // 手动更新进度，模拟实时播放效果
          // 通过增加少量进度来模拟播放进度的前进
          if (playingItem.isPlaying) {
            // 每100毫秒更新一次，根据音频总长度计算应该增加的进度
            // 例如：一个60秒的音频，100毫秒应该前进约0.17%
            const progressIncrement = (0.1 / playingItem.duration) * 100;
            const newProgress = Math.min(100, (playingItem.progress || calculatedProgress) + progressIncrement);
            
            // 直接更新组件内的状态，而不是调用外部钩子的setAudioProgress
            // 这样避免了可能的异步操作导致的延迟
            const updatedQueue = [...audioQueue];
            updatedQueue[playingAudioIndex] = {
              ...playingItem,
              progress: newProgress,
              currentTime: playingItem.currentTime + 0.1 // 每100毫秒增加0.1秒
            };
            
            // 强制重新渲染
            setAudioProgress(playingAudioIndex, newProgress);
          }
        }
      }
    }, 100); // 100毫秒的更新频率
    
    // 清理函数
    return () => {
      clearInterval(progressUpdateInterval);
    };
  }, [audioQueue, setAudioProgress]);

  // 监听标签系统更改事件
  useEffect(() => {
    // 定义一个处理标签系统更改的函数
    const handleTagSystemChange = () => {
      console.log('VoiceWorkspace: 检测到标签系统更改，重新加载数据');
      const savedTags = TagStorage.getTags();
      const savedModelTags = TagStorage.getModelTags();
      
      if(savedTags.length > 0) {
        setTags(savedTags);
      }
      
      if(Object.keys(savedModelTags).length > 0) {
        setModelTags(savedModelTags);
      }
    };
    
    // 添加自定义事件监听器
    window.addEventListener('tag-system-change', handleTagSystemChange);
    
    // 组件卸载时移除监听器
    return () => {
      window.removeEventListener('tag-system-change', handleTagSystemChange);
    };
  }, []);

  return (
    <Box sx={{ width: '100%' }}>
      <Typography
        variant="h4"
        sx={{
          fontWeight: 600,
          mb: 3,
          background: theme.gradient.primary,
          backgroundClip: 'text',
          textFillColor: 'transparent',
          display: 'inline-block',
        }}
      >
        声音工作间
            </Typography>

      <Stack spacing={2.5}> {/* 增加整体Stack间距 */}
        {/* 移除原位置的系统状态卡片 */}
        
        {/* 模型选择卡片 */}
        <Card sx={advancedCardStyle}>
          <CardContent sx={advancedCardContent}>
            <Stack direction="row" alignItems="center" spacing={1.5} mb={3}>
              <RecordVoiceOver sx={{ 
                color: '#2ec0ff',
                filter: 'drop-shadow(0 0 3px rgba(46,192,255,0.5))',
              }} />
              <Typography variant="h6" sx={{ 
                color: theme.text.primary,
                fontWeight: 600,
                letterSpacing: '0.05em',
                position: 'relative',
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  bottom: -5,
                  left: 0,
                  width: '40px',
                  height: '2px',
                  background: 'linear-gradient(90deg, #2ec0ff, transparent)',
                  borderRadius: '2px',
                },
              }}>
                语音模型选择
              </Typography>
            </Stack>
            
            {/* 使用更新后的搜索和过滤区域 */}
            {renderSearchAndFilterArea()}
            
            <Box sx={{ mb: 3 }}>
              {renderModelGrid()}
            </Box>
            
            {/* 分页控件 */}
            {totalPages > 1 && (
              <Box sx={{ 
                display: 'flex', 
                justifyContent: 'center', 
                mt: 3,
                mb: 1 
              }}>
                <Pagination 
                  count={totalPages} 
                  page={currentPage} 
                  onChange={handlePageChange}
                  variant="outlined" 
                  shape="rounded"
                  sx={{
                    '& .MuiPaginationItem-root': {
                      color: '#ffffff',
                      border: `1px solid ${alpha('#ffffff', 0.15)}`,
                      '&.Mui-selected': {
                        bgcolor: alpha('#2ec0ff', 0.2),
                        borderColor: '#2ec0ff',
                        color: '#2ec0ff',
                        '&:hover': {
                          bgcolor: alpha('#2ec0ff', 0.3),
                        }
                      },
                      '&:hover': {
                        bgcolor: alpha('#ffffff', 0.05),
                      }
                    }
                  }}
                />
              </Box>
            )}
            
            {currentModel && (
              <Alert 
                severity="info" 
                icon={<CheckCircle sx={{ color: theme.accent }} />}
                sx={{ 
                  bgcolor: alpha(theme.accent, 0.1),
                  color: theme.text.primary,
                  border: `1px solid ${alpha(theme.accent, 0.3)}`,
                  borderRadius: '8px',
                  position: 'relative',
                  mt: 2,
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '4px',
                    height: '100%',
                    bgcolor: theme.accent,
                    borderTopLeftRadius: '8px',
                    borderBottomLeftRadius: '8px',
                  }
                }}
              >
                已选择模型: <b>{currentModel.name}</b>
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* 音频选项卡片 */}
        <Card sx={advancedCardStyle}>
          <CardContent sx={advancedCardContent}>
            <Stack spacing={2}>
              <Stack direction="row" alignItems="center" spacing={1.5}>
                <SettingsSuggest sx={{ color: theme.primary }} />
                <Typography variant="h6" sx={{ 
                  color: theme.text.primary,
                  fontWeight: 500,
                  letterSpacing: '0.05em',
                }}>
                  音频参数
                </Typography>
              </Stack>
            
            <Box>
              <Stack direction="row" alignItems="center" spacing={1} mb={1}>
                <Speed sx={{ color: theme.primary, fontSize: 20 }} />
                <Typography sx={{ color: theme.text.primary }}>
                  语速 ({options.speed.toFixed(1)}x)
                </Typography>
              </Stack>
            <Slider
              value={options.speed}
                onChange={(_, value) => {
                  setOptions({ ...options, speed: value as number });
                  // 提示用户如果有正在播放的音频，需要重新播放
                  const hasPlayingAudio = audioQueue.some((item) => item.isPlaying);
                  if (hasPlayingAudio) {
                    message.info('语速已更新，需要重新播放音频才能生效');
                  }
                }}
              min={0.5}
              max={2.0}
              step={0.1}
                marks={[
                  { value: 0.5, label: '慢' },
                  { value: 1.0, label: '中' },
                  { value: 2.0, label: '快' },
                ]}
                sx={{
                  color: theme.primary,
                  '& .MuiSlider-rail': {
                    bgcolor: alpha(theme.primary, 0.2),
                  },
                  '& .MuiSlider-track': {
                    bgcolor: theme.primary,
                  },
                  '& .MuiSlider-thumb': {
                    bgcolor: theme.primary,
                    '&:hover, &.Mui-focusVisible': {
                      boxShadow: `0 0 0 8px ${alpha(theme.primary, 0.2)}`,
                    },
                  },
                  '& .MuiSlider-markLabel': {
                    color: theme.text.secondary,
                  },
                }}
              />
            </Box>
              <Box>
                <Stack direction="row" alignItems="center" spacing={1} mb={1}>
                  <VolumeUp sx={{ color: theme.primary, fontSize: 20 }} />
                  <Typography sx={{ color: theme.text.primary }}>
              音量 ({options.volume.toFixed(1)})
            </Typography>
                </Stack>
            <Slider
              value={options.volume}
              onChange={(_, value) => {
                setOptions({ ...options, volume: value as number });
                // 提示用户如果有正在播放的音频，需要重新播放
                const hasPlayingAudio = audioQueue.some((item) => item.isPlaying);
                if (hasPlayingAudio) {
                  message.info('音量已更新，需要重新播放音频才能生效');
                }
              }}
              min={0.1}
              max={2.0}
              step={0.1}
              marks={[
                { value: 0.1, label: '小' },
                { value: 1.0, label: '中' },
                { value: 2.0, label: '大' },
              ]}
              sx={{
                color: theme.primary,
                '& .MuiSlider-rail': {
                  bgcolor: alpha(theme.primary, 0.2),
                },
                '& .MuiSlider-track': {
                  bgcolor: theme.primary,
                },
                '& .MuiSlider-thumb': {
                  bgcolor: theme.primary,
                  '&:hover, &.Mui-focusVisible': {
                    boxShadow: `0 0 0 8px ${alpha(theme.primary, 0.2)}`,
                  },
                },
                '& .MuiSlider-markLabel': {
                  color: theme.text.secondary,
                },
              }}
            />
              </Box>
              <Box>
                <Stack direction="row" alignItems="center" spacing={1} mb={1}>
                  <GraphicEq sx={{ color: theme.secondary, fontSize: 20 }} />
                  <Typography sx={{ color: theme.text.primary }}>
              音调 ({options.pitch.toFixed(1)})
            </Typography>
                </Stack>
            <Slider
              value={options.pitch}
              onChange={(_, value) => {
                setOptions({ ...options, pitch: value as number });
                // 提示用户如果有正在播放的音频，需要重新播放
                const hasPlayingAudio = audioQueue.some((item) => item.isPlaying);
                if (hasPlayingAudio) {
                  message.info('音调已更新，需要重新播放音频才能生效');
                }
              }}
              min={-12.0}
              max={12.0}
              step={1.0}
              marks={[
                { value: -12, label: '-12' },
                { value: 0, label: '0' },
                { value: 12, label: '+12' },
              ]}
              sx={{
                color: theme.secondary,
                '& .MuiSlider-rail': {
                  bgcolor: alpha(theme.secondary, 0.2),
                },
                '& .MuiSlider-track': {
                  bgcolor: theme.secondary,
                },
                '& .MuiSlider-thumb': {
                  bgcolor: theme.secondary,
                  '&:hover, &.Mui-focusVisible': {
                    boxShadow: `0 0 0 8px ${alpha(theme.secondary, 0.2)}`,
                  },
                },
                '& .MuiSlider-markLabel': {
                  color: theme.text.secondary,
                },
              }}
            />
              </Box>
            </Stack>
          </CardContent>
        </Card>

        {/* 文本输入卡片 */}
        <Card sx={advancedCardStyle}>
          <CardContent sx={advancedCardContent}>
            <Stack spacing={1.5}> {/* 减少间距 */}
              <Typography variant="h6" sx={{ 
                color: theme.text.primary,
                fontWeight: 500,
                letterSpacing: '0.1em',
              }}>
                文本转语音
        </Typography>
        <TextField
          fullWidth
          multiline
          rows={4}
                value={text}
                onChange={handleTextChange}
                placeholder="请输入要转换的文本..."
          variant="outlined"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    backgroundColor: alpha(theme.background.light, 0.5),
                    backdropFilter: 'blur(10px)',
                    color: theme.text.primary,
                    borderRadius: '10px',
                    '& fieldset': {
                      borderColor: theme.border,
                      borderWidth: '1px',
                      transition: 'all 0.2s ease',
                    },
                    '&:hover fieldset': {
                      borderColor: alpha(theme.primary, 0.3),
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: theme.primary,
                    },
                  },
                }}
              />
          <Button
            variant="contained"
                onClick={handleGenerateClick}
                disabled={generating || !currentModel}
                sx={advancedButtonStyle}
              >
                {generating ? '生成中...' : '生成语音'}
          </Button>
            </Stack>
          </CardContent>
        </Card>

        {/* 音频列表卡片 */}
        {audioQueue.length > 0 && (
          <Card sx={advancedCardStyle}>
            <CardContent sx={advancedCardContent}>
              <Typography variant="h6" gutterBottom sx={{ 
                color: theme.text.primary,
                mb: 1, // 减少间距
                letterSpacing: '0.05em',
                fontWeight: 'bold',
              }}>
                已生成的语音
          </Typography>
              <List sx={{ p: 0 }}>
                {audioQueue.map((item, index) => renderAudioListItem(item, index))}
              </List>
            </CardContent>
          </Card>
        )}
      </Stack>

      {/* 创建标签对话框 */}
      <Dialog
        open={isCreateTagDialogOpen}
        onClose={handleCreateTagDialogClose}
        PaperProps={{
          sx: {
            backgroundColor: theme.background.paper,
            backdropFilter: 'blur(10px)',
            border: `1px solid ${alpha(theme.text.primary, 0.1)}`,
            borderRadius: '12px'
          }
        }}
      >
        <DialogTitle sx={{ 
          px: 2, 
          py: 1.5, 
          borderBottom: `1px solid ${alpha('#ffffff', 0.05)}`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <Typography 
            sx={{ 
              fontSize: '1rem', 
              fontWeight: 600, 
              color: '#ffffff'
            }}
          >
            创建新标签
          </Typography>
          <IconButton 
            size="small" 
            onClick={handleCreateTagDialogClose}
            sx={{ color: '#ffffff' }}
          >
            <Close fontSize="small" />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ p: 2 }}>
          <Stack spacing={2} sx={{ my: 1 }}>
            <TextField
              label="标签名称"
              fullWidth
              variant="outlined"
              value={newTagName}
              onChange={(e) => setNewTagName(e.target.value)}
              InputProps={{
                sx: { 
                  borderRadius: '8px',
                  bgcolor: alpha('#ffffff', 0.05),
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: alpha('#ffffff', 0.1)
                  },
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: alpha('#2ec0ff', 0.3)
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#2ec0ff'
                  },
                  color: '#ffffff'
                }
              }}
              InputLabelProps={{
                sx: { color: alpha('#ffffff', 0.7) }
              }}
            />
            
            <Box>
              <Typography sx={{ mb: 1, color: alpha('#ffffff', 0.7), fontSize: '0.875rem' }}>
                标签颜色
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {/* 预设颜色选择 */}
                {['#4f7df9', '#f97d4f', '#7df94f', '#f94f7d', '#4ff97d', '#7d4ff9'].map(color => (
                  <Box
                    key={color}
                    onClick={() => setNewTagColor(color)}
                    sx={{
                      width: 32,
                      height: 32,
                      borderRadius: '8px',
                      bgcolor: color,
                      cursor: 'pointer',
                      border: newTagColor === color ? `2px solid #ffffff` : `2px solid transparent`,
                      transition: 'all 0.2s',
                      '&:hover': {
                        transform: 'scale(1.1)',
                      }
                    }}
                  />
                ))}
                
                {/* 自定义颜色输入 */}
                <TextField
                  type="color"
                  value={newTagColor}
                  onChange={(e) => setNewTagColor(e.target.value)}
                  sx={{ 
                    width: 32, 
                    height: 32,
                    '& .MuiInputBase-input': {
                      width: 32,
                      height: 32,
                      p: 0,
                      border: 'none'
                    }
                  }}
                />
              </Box>
            </Box>
          </Stack>
        </DialogContent>
        <DialogActions sx={{ px: 2, pb: 2, pt: 1 }}>
          <Button 
            onClick={handleCreateTagDialogClose}
            sx={{ 
              color: alpha('#ffffff', 0.7),
              '&:hover': {
                bgcolor: alpha('#ffffff', 0.05)
              }
            }}
          >
            取消
          </Button>
          <Button 
            onClick={handleCreateTag}
            variant="contained"
            disabled={!newTagName.trim()}
            sx={{ 
              bgcolor: '#2ec0ff',
              '&:hover': {
                bgcolor: alpha('#2ec0ff', 0.8)
              },
              '&.Mui-disabled': {
                bgcolor: alpha('#2ec0ff', 0.3),
                color: alpha('#ffffff', 0.5)
              }
            }}
          >
            创建
          </Button>
        </DialogActions>
      </Dialog>
      {/* 试听音频播放器，仅用于试听，不显示UI */}
      {previewAudio && previewAudio.isPlaying && (
        <audio
          src={previewAudio.url}
          autoPlay
          onEnded={() => setPreviewAudio(null)}
          style={{ display: 'none' }}
        />
      )}
    </Box>
  );
};