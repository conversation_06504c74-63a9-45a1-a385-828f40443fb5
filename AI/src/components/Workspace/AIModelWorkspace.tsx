import React, { useState, useEffect, useRef, useMemo } from 'react';
import { 
  Box, 
  Card, 
  CardContent, 
  <PERSON>pography, 
  Stack,
  TextField,
  Button,
  Switch,
  FormControlLabel,
  List,
  ListItem,
  ListItemText,
  Divider,
  Chip,
  Grid,
  Paper,
  IconButton,
  alpha,
  Tooltip,
  Tab,
  Tabs,
  MenuItem,
  Select,
  SelectChangeEvent,
  FormControl,
  InputLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  InputAdornment,
  CircularProgress,
  Alert,
  Slider,
  Menu
} from '@mui/material';
import { 
  Send, 
  SmartToy,
  Save,
  Add,
  Delete,
  Settings,
  AutoAwesome,
  AccessTime,
  Celebration,
  Psychology,
  CachedOutlined,
  Edit,
  ContentCopy,
  PlayArrow,
  ChatBubble,
  Visibility,
  VisibilityOff,
  Timer,
  Shuffle,
  Upload
} from '@mui/icons-material';
import { Model, TTSOptions } from '../../hooks/useTTS';
import { useAIModel, AIModelType } from '../../contexts/AIModelContext';
import { useWorkspaceState } from '../../hooks/useWorkspaceState';
import AutoAnnounceManager from '../../components/AutoAnnounce/AutoAnnounceManager';
import { useLiveConnection } from '../../contexts/LiveConnectionContext';

// 自定义主题接口
interface CustomTheme {
  primary: string;
  secondary: string;
  accent: string;
  gradient: {
    primary: string;
    secondary: string;
    accent: string;
  };
  background: {
    main: string;
    paper: string;
    light: string;
    dark: string;
    highlight: string;
  };
  text: {
    primary: string;
    secondary: string;
    muted: string;
  };
  success: string;
  warning: string;
  error: string;
  border: string;
}

interface AIModelWorkspaceProps {
  models: Model[];
  currentModel: Model | null;
  options: TTSOptions;
}

interface PromptTemplate {
  id: string;
  name: string;
  content: string;
  category: 'welcome' | 'time' | 'product' | 'custom' | 'count' | 'generalize';
}

interface BroadcastSettings {
  mode: 'fixed' | 'random'; // 播报模式：固定间隔或随机间隔
  fixedInterval: number;    // 固定间隔秒数
  randomMinInterval: number; // 随机间隔最小秒数
  randomMaxInterval: number; // 随机间隔最大秒数
  enabled: boolean;         // 是否启用自动播报
  useAI?: boolean;          // 是否使用AI泛化
  aiPrompt?: string;        // AI泛化提示词
}

export const AIModelWorkspace: React.FC<AIModelWorkspaceProps> = ({
  models,
  currentModel,
  options
}) => {
  // 创建自定义主题
  const theme: CustomTheme = {
    primary: '#4f7df9', // 主色调
    secondary: '#7a6cf7', // 辅助色
    accent: '#2ec0ff',   // 强调色
    gradient: {
      primary: 'linear-gradient(135deg, #4f7df9 0%, #7a6cf7 100%)',
      secondary: 'linear-gradient(135deg, #7a6cf7 0%, #9b6cf7 100%)',
      accent: 'linear-gradient(135deg, #2ec0ff 0%, #4f7df9 100%)'
    },
    background: {
      main: '#1a1f36', // 主背景色
      paper: '#232842', // 卡片背景色
      light: '#2a3153', // 浅背景色
      dark: '#151b30',  // 深背景色
      highlight: '#323c6b' // 高亮背景色
    },
    text: {
      primary: '#ffffff',  // 主文字色
      secondary: '#c9d1f0', // 次要文字色
      muted: '#8a94b8'      // 弱化文字色
    },
    success: '#4caf50',
    warning: '#ff9800',
    error: '#f44336',
    border: '#373e59' // 边框色
  };
  
  // 使用ref标记是否已初始化
  const initialized = useRef(false);
  
  // 获取AI模型上下文
  const { config, updateConfig, generateText, isProcessing, error: aiError } = useAIModel();
  
  // 状态管理
  const [currentTab, setCurrentTab] = useWorkspaceState<number>('ai-model-currentTab', 0);
  const [promptTemplates, setPromptTemplates] = useWorkspaceState<PromptTemplate[]>('ai-model-promptTemplates', [
    { 
      id: '1', 
      name: '标准欢迎语', 
      content: '欢迎[昵称]来到我的直播间！记得点好关注福利马上开炸！', 
      category: 'welcome' 
    },
    { 
      id: '2', 
      name: '个性化欢迎语', 
      content: '欢迎[昵称]来到直播间！今天福利优惠超多不要着急走开！', 
      category: 'welcome' 
    },
    { 
      id: '3', 
      name: '整点报时', 
      content: '现在是[时间]，还没下单的抓紧时间下单，马上就下播了，下播后就没用这价格了', 
      category: 'time' 
    },
    { 
      id: '4', 
      name: '半点报时', 
      content: '现在是[时间]，[整点]，没下单的抓紧时间去拍了，马上下播截单了！', 
      category: 'time' 
    },
    {
      id: '5',
      name: '人数播报',
      content: '现在直播间[人数]位朋友在线，没下单的抓紧时间下单没有多少库存了！',
      category: 'count'
    }
  ]);
  
  // 对话框状态
  const [openDialog, setOpenDialog] = useWorkspaceState<boolean>('ai-model-openDialog', false);
  const [currentTemplate, setCurrentTemplate] = useWorkspaceState<PromptTemplate | null>('ai-model-currentTemplate', null);
  const [editMode, setEditMode] = useWorkspaceState<boolean>('ai-model-editMode', false);
  
  // 将API密钥显示状态保存在组件内
  const [showApiKey, setShowApiKey] = useWorkspaceState<boolean>('ai-model-showApiKey', false);
  
  // 从上下文中提取值以便在组件中使用
  const { 
    selectedModel,
    aiModelEnabled,
    apiKeysMap,
    modelNicknamesMap
  } = config;
  
  // 当前模型的API密钥
  const apiKey = apiKeysMap[selectedModel];
  
  // 当前模型的昵称/版本
  const modelNickname = modelNicknamesMap[selectedModel];
  
  // 播报设置状态
  const [broadcastSettings, setBroadcastSettings] = useWorkspaceState<{
    welcome: BroadcastSettings;
    time: BroadcastSettings;
    count: BroadcastSettings;
    generalize: BroadcastSettings;
  }>('ai-model-broadcastSettings', {
    welcome: {
      mode: 'fixed',
      fixedInterval: 60, // 默认60秒
      randomMinInterval: 60, // 默认60-300秒
      randomMaxInterval: 300,
      enabled: false,
      useAI: false,
      aiPrompt: '请对以下直播欢迎语进行改写，保持原意但使表达更加多样化、生动有趣：[原内容]'
    },
    time: {
      mode: 'fixed',
      fixedInterval: 60, // 默认60秒
      randomMinInterval: 60, // 默认60-300秒
      randomMaxInterval: 300,
      enabled: false,
      useAI: false,
      aiPrompt: '请对以下直播报时语进行改写，保持原意但使表达更加多样化、生动有趣：[原内容]'
    },
    count: {
      mode: 'fixed',
      fixedInterval: 60, // 默认60秒
      randomMinInterval: 60, // 默认60-300秒
      randomMaxInterval: 300,
      enabled: false,
      useAI: false,
      aiPrompt: '请对以下直播人数播报语进行改写，保持原意但使表达更加多样化、生动有趣：[原内容]'
    },
    generalize: {
      mode: 'fixed',
      fixedInterval: 120, // 默认120秒
      randomMinInterval: 60, // 默认60-300秒
      randomMaxInterval: 300,
      enabled: false,
      useAI: true,
      aiPrompt: '请对以下直播话术进行改写，保持原意但使表达更加多样化、生动有趣：[原内容]'
    }
  });
  
  // 添加临时状态变量用于输入框
  const [tempFixedIntervalValue, setTempFixedIntervalValue] = useState('60');
  const [tempRandomMinIntervalValue, setTempRandomMinIntervalValue] = useState('60');
  const [tempRandomMaxIntervalValue, setTempRandomMaxIntervalValue] = useState('300');

  // 更新临时状态变量，当broadcastSettings变化时
  useEffect(() => {
    const category = currentTab === 0 ? 'welcome' : 
                    currentTab === 1 ? 'time' : 
                    currentTab === 2 ? 'count' : 'generalize';
    setTempFixedIntervalValue(broadcastSettings[category].fixedInterval.toString());
    setTempRandomMinIntervalValue(broadcastSettings[category].randomMinInterval.toString());
    setTempRandomMaxIntervalValue(broadcastSettings[category].randomMaxInterval.toString());
  }, [broadcastSettings, currentTab]);
  
  // 播报设置菜单状态
  const [broadcastMenuAnchor, setBroadcastMenuAnchor] = useState<null | HTMLElement>(null);
  const [broadcastSettingsOpen, setBroadcastSettingsOpen] = useState(false);
  
  // 处理Tab切换
  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };
  
  // 模型切换
  const handleModelChange = (event: SelectChangeEvent<string>) => {
    const newModel = event.target.value as AIModelType;
    updateConfig({ selectedModel: newModel });
  };
  
  // 更新API密钥时同时更新映射
  const handleApiKeyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newApiKey = e.target.value;
    
    // 更新当前模型的API密钥映射
    updateConfig({
      apiKeysMap: {
        ...apiKeysMap,
        [selectedModel]: newApiKey
      }
    });
  };
  
  // 更新模型昵称时同时更新映射
  const handleModelNicknameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newNickname = e.target.value;
    
    // 更新当前模型的昵称映射
    updateConfig({
      modelNicknamesMap: {
        ...modelNicknamesMap,
        [selectedModel]: newNickname
      }
    });
  };
  
  // 切换API密钥显示/隐藏
  const toggleApiKeyVisibility = () => {
    setShowApiKey(!showApiKey);
  };
  
  // 切换AI模型启用状态
  const toggleAIModelEnabled = () => {
    updateConfig({ aiModelEnabled: !aiModelEnabled });
  };
  
  // 打开提示词模板编辑对话框
  const handleOpenDialog = (mode: boolean, template?: PromptTemplate) => {
    setEditMode(mode);
    if (mode && template) {
      setCurrentTemplate(template);
    } else {
      // 创建新模板时，根据当前标签页自动设置默认名称
      const defaultName = currentTab === 0 ? "新建欢迎语" : 
                        currentTab === 1 ? "新建报时提示词" : 
                        currentTab === 2 ? "新建人数播报" : 
                        "新建提示词";
      setCurrentTemplate({
        id: Date.now().toString(),
        name: defaultName,
        content: '',
        category: currentTab === 0 ? 'welcome' : currentTab === 1 ? 'time' : currentTab === 2 ? 'count' : 'custom'
      });
    }
    setOpenDialog(true);
  };
  
  // 关闭模板对话框
  const handleCloseDialog = () => {
    setOpenDialog(false);
    setCurrentTemplate(null);
  };
  
  // 保存模板
  const handleSaveTemplate = () => {
    if (currentTemplate) {
      if (editMode) {
        setPromptTemplates(templates => 
          templates.map(template => 
            template.id === currentTemplate.id ? currentTemplate : template
          )
        );
      } else {
        setPromptTemplates(templates => [...templates, currentTemplate]);
      }
      handleCloseDialog();
    }
  };
  
  // 删除模板
  const handleDeleteTemplate = (id: string) => {
    setPromptTemplates(templates => templates.filter(template => template.id !== id));
  };
  
  // 复制模板内容
  const handleCopyTemplate = (content: string) => {
    navigator.clipboard.writeText(content);
  };
  
  // 更新模板字段
  const handleTemplateChange = (field: keyof PromptTemplate, value: any) => {
    if (currentTemplate) {
      setCurrentTemplate({
        ...currentTemplate,
        [field]: value
      });
    }
  };

  // 打开播报设置菜单
  const handleOpenBroadcastMenu = (event: React.MouseEvent<HTMLElement>) => {
    setBroadcastMenuAnchor(event.currentTarget);
    setBroadcastSettingsOpen(true);
  };
  
  // 关闭播报设置菜单
  const handleCloseBroadcastMenu = () => {
    setBroadcastMenuAnchor(null);
    setBroadcastSettingsOpen(false);
  };
  
  // 更新播报设置
  const handleAnnounceSettingsChange = (category: 'welcome' | 'time' | 'count' | 'generalize', updates: Partial<BroadcastSettings>) => {
    // 更新本地状态
    setBroadcastSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        ...updates
      }
    }));
    
    // 如果更新了启用状态，分发事件通知其他组件
    if (updates.enabled !== undefined) {
      const event = new CustomEvent('broadcast-settings-change', { 
        detail: { category, enabled: updates.enabled } 
      });
      window.dispatchEvent(event);
    }
  };
  
  // 获取当前标签页对应的播报类别
  const getCurrentBroadcastCategory = (): 'welcome' | 'time' | 'count' | 'generalize' => {
    if (currentTab === 0) return 'welcome';
    if (currentTab === 1) return 'time';
    if (currentTab === 2) return 'count';
    return 'generalize';
  };

  // 高级卡片样式
  const cardStyle = {
    position: 'relative',
    overflow: 'hidden',
    backgroundColor: alpha(theme.background.paper, 0.7),
    borderRadius: '16px',
    boxShadow: `0 10px 40px ${alpha('#000', 0.2)}`,
    border: `1px solid ${alpha(theme.border, 0.3)}`,
    transition: 'transform 0.3s ease, box-shadow 0.3s ease',
    '&:hover': {
      boxShadow: `0 15px 50px ${alpha('#000', 0.25)}`,
      transform: 'translateY(-4px)'
    },
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      height: '3px',
      background: theme.gradient.primary,
      opacity: 0.8,
    }
  };

  // 卡片内容样式
  const cardContentStyle = {
    p: 3,
    '&:last-child': {
      pb: 3
    }
  };

  // 高级按钮样式
  const primaryButtonStyle = {
    py: 1.2,
    px: 3,
    background: theme.gradient.primary,
    color: theme.text.primary,
    borderRadius: '10px',
    fontWeight: 'bold',
    textTransform: 'none',
    boxShadow: `0 4px 15px ${alpha(theme.primary, 0.3)}`,
    transition: 'all 0.3s ease',
    '&:hover': {
      background: theme.gradient.secondary,
      boxShadow: `0 6px 20px ${alpha(theme.primary, 0.4)}`,
      transform: 'translateY(-2px)'
    },
    '&:active': {
      transform: 'translateY(0px)'
    },
    '&.Mui-disabled': {
      background: `linear-gradient(90deg, ${alpha(theme.primary, 0.3)}, ${alpha(theme.secondary, 0.3)})`,
      color: alpha(theme.text.primary, 0.5)
    }
  };

  // 次要按钮样式
  const secondaryButtonStyle = {
    py: 1.2,
    px: 3,
    borderColor: alpha(theme.text.primary, 0.3),
    color: theme.text.primary,
    borderRadius: '10px',
    fontWeight: 'medium',
    textTransform: 'none',
    transition: 'all 0.3s ease',
    '&:hover': {
      borderColor: theme.accent,
      backgroundColor: alpha(theme.accent, 0.1),
      transform: 'translateY(-2px)'
    }
  };

  // 输入框样式
  const textFieldStyle = {
    '& .MuiOutlinedInput-root': {
      bgcolor: alpha(theme.background.light, 0.6),
      borderRadius: '12px',
      '& fieldset': {
        borderColor: alpha(theme.text.primary, 0.2),
      },
      '&:hover fieldset': {
        borderColor: alpha(theme.primary, 0.5),
      },
      '&.Mui-focused fieldset': {
        borderColor: theme.primary,
      },
      '& input, & textarea': {
        color: theme.text.primary,
      }
    },
    '& .MuiInputLabel-root': {
      color: alpha(theme.text.primary, 0.9),
    },
    '& .MuiInputLabel-root.Mui-focused': {
      color: theme.primary,
    }
  };

  // 模块标题样式
  const sectionTitleStyle = {
    fontWeight: 600,
    letterSpacing: '0.05em',
    color: theme.text.primary,
    display: 'flex',
    alignItems: 'center',
    gap: 1.5,
    mb: 2,
    position: 'relative',
    '&::after': {
      content: '""',
      position: 'absolute',
      bottom: -4,
      left: 0,
      width: '40px',
      height: '2px',
      background: theme.gradient.primary,
      borderRadius: '2px'
    }
  };

  // 添加关于变量的标签，仅在生成此组件时显示，不影响日常使用
  const getVariableTag = (category: string) => {
    switch (category) {
      case 'welcome':
        return null;
      case 'time':
        return null;
      case 'count':
        return null;
      default:
        return null;
    }
  };

  // 格式化秒数为可读时间
  const formatSeconds = (seconds: number): string => {
    if (seconds < 60) return `${seconds}秒`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}分钟`;
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return minutes > 0 ? `${hours}小时${minutes}分钟` : `${hours}小时`;
  };

  // 获取直播连接状态和数据
  const { isConnected, latestUsername, audienceCount } = useLiveConnection();
  
  // 导出模板
  const handleExportTemplates = () => {
    const dataStr = JSON.stringify(promptTemplates, null, 2);
    const blob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `AI提示词模板_${new Date().toLocaleDateString()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 导入模板
  const handleImportTemplates = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (event) => {
          try {
            const imported = JSON.parse(event.target?.result as string) as PromptTemplate[];
            if (Array.isArray(imported)) {
              // 合并并去重（id不同视为新模板）
              const merged = [...promptTemplates];
              imported.forEach(t => {
                if (!merged.find(item => item.id === t.id)) {
                  merged.push(t);
                }
              });
              setPromptTemplates(merged);
            }
          } catch {
            alert('导入失败，文件格式错误');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  // 固定间隔最大值
  const maxFixed = 88888; // 最高88888秒
  // 随机间隔最大值
  const maxRandom = 88888; // 最高88888秒

  return (
    <Box sx={{ width: '100%' }}>
      <Typography
        variant="h4"
        sx={{
          fontWeight: 700,
          mb: 3,
          background: theme.gradient.primary,
          backgroundClip: 'text',
          textFillColor: 'transparent',
          display: 'inline-block',
          position: 'relative',
          '&::after': {
            content: '""',
            position: 'absolute',
            bottom: -8,
            left: 0,
            width: '60px',
            height: '3px',
            background: theme.gradient.primary,
            borderRadius: '3px'
          }
        }}
      >
        大模型对接
      </Typography>

      <Stack spacing={3}>
        {/* 模型设置卡片 */}
        <Card sx={cardStyle}>
          <CardContent sx={cardContentStyle}>
            <Typography variant="h6" sx={sectionTitleStyle}>
              <SmartToy sx={{ color: theme.primary }} />
              AI模型设置
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Paper sx={{
                  p: 2.5,
                  borderRadius: '12px',
                  bgcolor: alpha(theme.background.dark, 0.6),
                  border: `1px solid ${alpha(theme.border, 0.2)}`,
                  mb: 2,
                  position: 'relative',
                  overflow: 'hidden',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    background: `radial-gradient(circle at 30% 20%, ${alpha(theme.primary, 0.05)} 0%, transparent 50%)`,
                    pointerEvents: 'none'
                  }
                }}>
                  <FormControlLabel
                    control={
                      <Switch 
                        checked={aiModelEnabled} 
                        onChange={toggleAIModelEnabled}
                        sx={{
                          '& .MuiSwitch-switchBase.Mui-checked': {
                            color: theme.primary,
                            '&:hover': {
                              backgroundColor: alpha(theme.primary, 0.1),
                            },
                          },
                          '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                            backgroundColor: alpha(theme.primary, 0.5),
                          },
                        }}
                      />
                    }
                    label={
                      <Typography sx={{ color: theme.text.primary, fontWeight: 500 }}>
                        启用AI大模型
                      </Typography>
                    }
                  />
                  <Typography sx={{ 
                    color: alpha(theme.text.secondary, 0.9), 
                    mt: 1, 
                    fontSize: '0.875rem',
                    lineHeight: 1.6
                  }}>
                    开启后系统将使用大模型来生成话术和回复内容
                  </Typography>
                </Paper>

                <Paper sx={{
                  p: 2.5,
                  borderRadius: '12px',
                  bgcolor: alpha(theme.background.highlight, 0.2),
                  border: `1px solid ${alpha(theme.border, 0.2)}`,
                  position: 'relative',
                  '&::after': {
                    content: '""',
                    position: 'absolute',
                    bottom: 0,
                    right: 0,
                    width: '120px',
                    height: '120px',
                    background: `radial-gradient(circle, ${alpha(theme.primary, 0.1)} 0%, transparent 70%)`,
                    borderRadius: '50%',
                    pointerEvents: 'none'
                  }
                }}>
                  <Typography sx={{ 
                    fontWeight: 600,
                    color: theme.text.primary,
                    mb: 2
                  }}>
                    <Settings sx={{ fontSize: 18, mr: 1, verticalAlign: 'text-bottom', color: theme.accent }} />
                    系统状态
                  </Typography>
                  
                  <Stack spacing={1}>
                    <Box sx={{ 
                      display: 'flex', 
                      alignItems: 'center', 
                      justifyContent: 'space-between',
                      py: 0.5
                    }}>
                      <Typography variant="body2" sx={{ color: theme.text.secondary }}>
                        AI模型状态
                      </Typography>
                      <Chip 
                        label={aiModelEnabled ? "已启用" : "已禁用"} 
                        size="small"
                        sx={{
                          bgcolor: aiModelEnabled ? alpha(theme.success, 0.1) : alpha(theme.error, 0.1),
                          color: aiModelEnabled ? theme.success : theme.error,
                          fontWeight: 500,
                          borderRadius: '6px',
                          border: `1px solid ${aiModelEnabled ? alpha(theme.success, 0.3) : alpha(theme.error, 0.3)}`
                        }}
                      />
                    </Box>
                    
                    <Box sx={{ 
                      display: 'flex', 
                      alignItems: 'center', 
                      justifyContent: 'space-between',
                      py: 0.5
                    }}>
                      <Typography variant="body2" sx={{ color: theme.text.secondary }}>
                        当前模型
                      </Typography>
                      <Chip 
                        label={selectedModel} 
                        size="small"
                        sx={{
                          bgcolor: alpha(theme.primary, 0.1),
                          color: theme.primary,
                          fontWeight: 500,
                          borderRadius: '6px',
                          border: `1px solid ${alpha(theme.primary, 0.3)}`
                        }}
                      />
                    </Box>
                    
                    <Box sx={{ 
                      display: 'flex', 
                      alignItems: 'center', 
                      justifyContent: 'space-between',
                      py: 0.5
                    }}>
                      <Typography variant="body2" sx={{ color: theme.text.secondary }}>
                        API密钥状态
                      </Typography>
                      <Chip 
                        label={apiKey ? "已配置" : "未配置"} 
                        size="small"
                        sx={{
                          bgcolor: apiKey ? alpha(theme.success, 0.1) : alpha(theme.warning, 0.1),
                          color: apiKey ? theme.success : theme.warning,
                          fontWeight: 500,
                          borderRadius: '6px',
                          border: `1px solid ${apiKey ? alpha(theme.success, 0.3) : alpha(theme.warning, 0.3)}`
                        }}
                      />
                    </Box>
                  </Stack>
                </Paper>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel id="ai-model-label" sx={{ color: alpha(theme.text.primary, 0.7) }}>
                    选择AI模型
                  </InputLabel>
                  <Select
                    labelId="ai-model-label"
                    value={selectedModel}
                    label="选择AI模型"
                    onChange={handleModelChange}
                    disabled={!aiModelEnabled}
                    sx={{
                      bgcolor: alpha(theme.background.light, 0.3),
                      color: theme.text.primary,
                      borderRadius: '12px',
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: alpha(theme.text.primary, 0.2),
                      },
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: alpha(theme.primary, 0.5),
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.primary,
                      },
                      '& .MuiSvgIcon-root': {
                        color: theme.text.primary,
                      }
                    }}
                  >
                    <MenuItem value="zhipu-free">智谱(免费)</MenuItem>
                    <MenuItem value="tongyi-qianwen">通义千问</MenuItem>
                    <MenuItem value="volcengine">火山引擎</MenuItem>
                    <MenuItem value="deepseek">DeepSeek</MenuItem>
                  </Select>
                </FormControl>
                
                <TextField
                  fullWidth
                  type={showApiKey ? "text" : "password"}
                  value={apiKey}
                  onChange={handleApiKeyChange}
                  disabled={!aiModelEnabled}
                  placeholder={
                    selectedModel === "tongyi-qianwen" ? "请输入阿里云API密钥(DASHSCOPE_API_KEY)" : 
                    selectedModel === "volcengine" ? "请输入火山引擎API密钥" :
                    selectedModel === "deepseek" ? "请输入DeepSeek API密钥" :
                    selectedModel === "zhipu-free" ? "请输入智谱API密钥" :
                    "请输入API密钥"
                  }
                  sx={{
                    ...textFieldStyle,
                    mb: 2
                  }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={toggleApiKeyVisibility}
                          edge="end"
                          sx={{ 
                            color: theme.text.primary,
                            '&:hover': { color: theme.primary } 
                          }}
                        >
                          {showApiKey ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />

                <TextField
                  fullWidth
                  value={modelNickname}
                  onChange={handleModelNicknameChange}
                  disabled={!aiModelEnabled}
                  placeholder={`请输入模型昵称，未输入默认使用${
                    selectedModel === "tongyi-qianwen" ? "qwen-max" : 
                    selectedModel === "volcengine" ? "chatglm-6b-model" :
                    selectedModel === "deepseek" ? "deepseek-chat" :
                    selectedModel
                  }`}
                  sx={{
                    ...textFieldStyle,
                    mb: 2
                  }}
                />

                {/* 添加API密钥获取指南 */}
                <Paper sx={{
                  p: 2.5, 
                  bgcolor: alpha(theme.background.highlight, 0.3), 
                  borderRadius: '12px',
                  border: `1px solid ${alpha(theme.border, 0.2)}`,
                  mt: 1
                }}>
                  <Typography sx={{ 
                    color: theme.text.primary, 
                    fontSize: '0.875rem', 
                    mb: 1,
                    fontWeight: 600,
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1
                  }}>
                    <Settings sx={{ fontSize: '1rem', color: theme.accent }} />
                    {selectedModel === "tongyi-qianwen" ? "获取阿里云API密钥" : 
                     selectedModel === "volcengine" ? "获取火山引擎API密钥" :
                     selectedModel === "deepseek" ? "获取DeepSeek API密钥" :
                     selectedModel === "zhipu-free" ? "获取智谱API密钥" :
                     "获取API密钥"}
                  </Typography>
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={() => {
                      const url = selectedModel === "tongyi-qianwen" 
                        ? "https://dashscope.console.aliyun.com/apiKey"
                        : selectedModel === "volcengine"
                        ? "https://console.volcengine.com/ark/region:ark+cn-beijing/apiKey?apikey=%7B%7D"
                        : selectedModel === "deepseek"
                        ? "https://platform.deepseek.com/api_keys"
                        : selectedModel === "zhipu-free"
                        ? "https://open.bigmodel.cn/usercenter/apikeys"
                        : "#";
                      window.open(url, '_blank');
                    }}
                    sx={{
                      borderColor: alpha(theme.primary, 0.5),
                      color: theme.primary,
                      textTransform: 'none',
                      mt: 1,
                      '&:hover': {
                        borderColor: theme.primary,
                        bgcolor: alpha(theme.primary, 0.1),
                      }
                    }}
                  >
                    点击前往{
                      selectedModel === "tongyi-qianwen" ? "阿里云控制台" : 
                      selectedModel === "volcengine" ? "火山引擎控制台" :
                      selectedModel === "deepseek" ? "DeepSeek平台" :
                      selectedModel === "zhipu-free" ? "智谱AI开放平台" :
                      "官方网站"
                    }获取
                  </Button>
                </Paper>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* 提示词模板管理卡片 */}
        <Card sx={cardStyle}>
          <CardContent sx={cardContentStyle}>
            <Box sx={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center', 
              mb: 3,
              flexWrap: { xs: 'wrap', md: 'nowrap' },
              gap: { xs: 2, md: 0 }
            }}>
              <Typography variant="h6" sx={sectionTitleStyle}>
                <AutoAwesome sx={{ color: theme.accent }} />
                提示词管理
              </Typography>
              
              <Tabs
                value={currentTab}
                onChange={handleTabChange}
                sx={{
                  minHeight: '42px',
                  '& .MuiTab-root': {
                    color: alpha(theme.text.primary, 0.7),
                    minHeight: '42px',
                    borderRadius: '21px',
                    px: 2,
                    py: 0,
                    minWidth: 'auto',
                    mx: 0.5,
                    transition: 'all 0.3s ease',
                    '&.Mui-selected': {
                      color: theme.text.primary,
                      bgcolor: alpha(theme.primary, 0.15),
                    },
                    '&:hover': {
                      bgcolor: alpha(theme.primary, 0.05),
                    }
                  },
                  '& .MuiTabs-indicator': {
                    display: 'none',
                  }
                }}
              >
                <Tab label="欢迎提示词" />
                <Tab label="报时提示词" />
                <Tab label="人数播报" />
              </Tabs>
            </Box>
            
            <Box sx={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              mb: 2.5,
              flexDirection: { xs: 'column', sm: 'row' },
              gap: { xs: 2, sm: 0 },
              alignItems: 'center'
            }}>
              <Box sx={{ maxWidth: { sm: '60%' } }}>
                {/* 不显示变量说明 */}
              </Box>
              
              <Box sx={{ display: 'flex', gap: 2 }}>
                <Button
                  variant="outlined"
                  startIcon={<Timer />}
                  onClick={handleOpenBroadcastMenu}
                  sx={{
                    ...secondaryButtonStyle,
                    borderColor: alpha(broadcastSettings[getCurrentBroadcastCategory()].enabled ? theme.success : theme.text.primary, 0.3),
                    color: broadcastSettings[getCurrentBroadcastCategory()].enabled ? theme.success : theme.text.primary,
                  }}
                >
                  {broadcastSettings[getCurrentBroadcastCategory()].enabled ? '播报已开启' : '播报设置'}
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Upload />}
                  onClick={handleImportTemplates}
                  sx={secondaryButtonStyle}
                >
                  导入模板
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Save />}
                  onClick={handleExportTemplates}
                  sx={secondaryButtonStyle}
                >
                  导出模板
                </Button>
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={() => handleOpenDialog(false)}
                  sx={primaryButtonStyle}
                >
                  添加模板
                </Button>
              </Box>
            </Box>
            
            {/* 播报设置菜单 */}
            <Menu
              anchorEl={broadcastMenuAnchor}
              open={broadcastSettingsOpen}
              onClose={handleCloseBroadcastMenu}
              PaperProps={{
                sx: {
                  backgroundColor: alpha(theme.background.paper, 0.95),
                  borderRadius: '16px',
                  border: `1px solid ${alpha(theme.border, 0.3)}`,
                  boxShadow: `0 10px 40px ${alpha('#000', 0.2)}`,
                  width: '320px',
                  p: 2,
                  mt: 1
                }
              }}
            >
              <Typography sx={{ 
                fontWeight: 600, 
                fontSize: '1rem',
                color: theme.text.primary,
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                mb: 2
              }}>
                <Timer sx={{ color: theme.primary, fontSize: '1.2rem' }} />
                播报频率设置
              </Typography>
              
              <FormControlLabel
                control={
                  <Switch 
                    checked={broadcastSettings[getCurrentBroadcastCategory()].enabled} 
                    onChange={(e) => handleAnnounceSettingsChange(
                      getCurrentBroadcastCategory(), 
                      { enabled: e.target.checked }
                    )}
                    sx={{
                      '& .MuiSwitch-switchBase.Mui-checked': {
                        color: theme.success,
                      },
                      '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                        backgroundColor: alpha(theme.success, 0.5),
                      },
                    }}
                  />
                }
                label={
                  <Typography sx={{ color: theme.text.primary, fontWeight: 500 }}>
                    {currentTab === 0 
                      ? '开启欢迎语自动播报' 
                      : currentTab === 1 
                        ? '开启时间自动播报' 
                        : '开启人数自动播报'
                    }
                  </Typography>
                }
                sx={{ mb: 2 }}
              />
              
              <Divider sx={{ my: 2, borderColor: alpha(theme.border, 0.3) }} />
              
              <Typography sx={{ 
                color: theme.text.secondary, 
                fontSize: '0.9rem',
                mb: 1.5 
              }}>
                播报模式
              </Typography>
              
              <Stack direction="row" spacing={1} sx={{ mb: 2 }}>
                <Button
                  variant={broadcastSettings[getCurrentBroadcastCategory()].mode === 'fixed' ? 'contained' : 'outlined'}
                  startIcon={<AccessTime />}
                  size="small"
                  onClick={() => handleAnnounceSettingsChange(
                    getCurrentBroadcastCategory(), 
                    { mode: 'fixed' }
                  )}
                  sx={{
                    flex: 1,
                    ...(broadcastSettings[getCurrentBroadcastCategory()].mode === 'fixed' ? primaryButtonStyle : secondaryButtonStyle),
                  }}
                >
                  固定间隔
                </Button>
                
                <Button
                  variant={broadcastSettings[getCurrentBroadcastCategory()].mode === 'random' ? 'contained' : 'outlined'}
                  startIcon={<Shuffle />}
                  size="small"
                  onClick={() => handleAnnounceSettingsChange(
                    getCurrentBroadcastCategory(), 
                    { mode: 'random' }
                  )}
                  sx={{
                    flex: 1,
                    ...(broadcastSettings[getCurrentBroadcastCategory()].mode === 'random' ? primaryButtonStyle : secondaryButtonStyle),
                  }}
                >
                  随机间隔
                </Button>
              </Stack>
              
              {broadcastSettings[getCurrentBroadcastCategory()].mode === 'fixed' ? (
                <>
                  <Typography sx={{ 
                    color: theme.text.secondary, 
                    fontSize: '0.9rem',
                    mb: 1.5,
                  }}>
                    固定间隔时间
                  </Typography>
                  
                  <TextField
                    fullWidth
                    type="text"
                    value={tempFixedIntervalValue}
                    onChange={(e) => {
                      // 更新临时状态
                      setTempFixedIntervalValue(e.target.value);
                      
                      // 如果是有效数字，则更新真实状态
                      const value = Number(e.target.value);
                      if (!isNaN(value) && value >= 1) {
                        // 限制最小值和最大值
                        const min = 1; // 最小1秒
                        const max = maxFixed; // 最高88888秒
                        
                        const validValue = Math.max(min, Math.min(max, value));
                        
                        handleAnnounceSettingsChange(
                          getCurrentBroadcastCategory(),
                          { fixedInterval: validValue }
                        );
                      }
                    }}
                    onBlur={() => {
                      // 当失去焦点时，如果不是有效值，恢复到上一个有效值
                      if (tempFixedIntervalValue === '' || isNaN(Number(tempFixedIntervalValue)) || 
                          Number(tempFixedIntervalValue) < 1) {
                        const category = getCurrentBroadcastCategory();
                        setTempFixedIntervalValue(broadcastSettings[category].fixedInterval.toString());
                      }
                    }}
                    onFocus={(e) => e.target.select()} // 自动选中所有文本
                    InputProps={{
                      endAdornment: <InputAdornment position="end">
                        <Typography sx={{ 
                          fontSize: '1rem', 
                          fontWeight: 500,
                          color: theme.text.primary,
                          ml: 0.5
                        }}>
                          秒
                        </Typography>
                      </InputAdornment>,
                    }}
                    size="small"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        bgcolor: alpha(theme.background.light, 0.3),
                        borderRadius: '8px',
                        '& fieldset': {
                          borderColor: alpha(theme.text.primary, 0.2),
                        },
                        '&:hover fieldset': {
                          borderColor: alpha(theme.primary, 0.5),
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: theme.primary,
                        }
                      },
                      '& input': {
                        color: theme.text.primary
                      }
                    }}
                  />
                  
                  <Typography sx={{ 
                    color: theme.text.primary, 
                    fontSize: '0.8rem',
                    mt: 1,
                    fontWeight: 500,
                    textAlign: 'right'
                  }}>
                    {formatSeconds(broadcastSettings[getCurrentBroadcastCategory()].fixedInterval)}
                  </Typography>
                </>
              ) : (
                <>
                  <Typography sx={{ 
                    color: theme.text.secondary, 
                    fontSize: '0.9rem',
                    mb: 1.5
                  }}>
                    随机间隔范围
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        type="text"
                        value={tempRandomMinIntervalValue}
                        onChange={(e) => {
                          // 更新临时状态
                          setTempRandomMinIntervalValue(e.target.value);
                          
                          // 如果是有效数字，则更新真实状态
                          const value = Number(e.target.value);
                          if (!isNaN(value) && value >= 1) {
                            // 限制最小值，并确保不超过最大值
                            const min = 1; // 最小1秒
                            const max = maxRandom; // 最高88888秒
                            
                            const validValue = Math.max(min, Math.min(max, value));
                            
                            handleAnnounceSettingsChange(
                              getCurrentBroadcastCategory(),
                              { randomMinInterval: validValue }
                            );
                          }
                        }}
                        onBlur={() => {
                          // 当失去焦点时，如果不是有效值，恢复到上一个有效值
                          if (tempRandomMinIntervalValue === '' || isNaN(Number(tempRandomMinIntervalValue)) || 
                              Number(tempRandomMinIntervalValue) < 1) {
                            const category = getCurrentBroadcastCategory();
                            setTempRandomMinIntervalValue(broadcastSettings[category].randomMinInterval.toString());
                          }
                        }}
                        onFocus={(e) => e.target.select()} // 自动选中所有文本
                        InputProps={{
                          endAdornment: <InputAdornment position="end">
                            <Typography sx={{ 
                              fontSize: '1rem', 
                              fontWeight: 500,
                              color: theme.text.primary,
                              ml: 0.5
                            }}>
                              秒
                            </Typography>
                          </InputAdornment>,
                        }}
                        size="small"
                        label="最小间隔"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            bgcolor: alpha(theme.background.light, 0.3),
                            borderRadius: '8px',
                            '& fieldset': {
                              borderColor: alpha(theme.text.primary, 0.2),
                            },
                            '&:hover fieldset': {
                              borderColor: alpha(theme.primary, 0.5),
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: theme.primary,
                            }
                          },
                          '& input': {
                            color: theme.text.primary
                          },
                          '& label': {
                            color: alpha(theme.text.primary, 0.7)
                          },
                          '& label.Mui-focused': {
                            color: theme.primary
                          }
                        }}
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        type="text"
                        value={tempRandomMaxIntervalValue}
                        onChange={(e) => {
                          // 更新临时状态
                          setTempRandomMaxIntervalValue(e.target.value);
                          
                          // 如果是有效数字，则更新真实状态
                          const value = Number(e.target.value);
                          if (!isNaN(value) && value >= 1) {
                            // 限制最大值，并确保不小于最小值
                            const min = 1; // 最小1秒
                            const max = maxRandom; // 最高88888秒
                            
                            const validValue = Math.max(min, Math.min(max, value));
                            
                            handleAnnounceSettingsChange(
                              getCurrentBroadcastCategory(),
                              { randomMaxInterval: validValue }
                            );
                          }
                        }}
                        onBlur={() => {
                          // 当失去焦点时，如果不是有效值，恢复到上一个有效值
                          if (tempRandomMaxIntervalValue === '' || isNaN(Number(tempRandomMaxIntervalValue)) || 
                              Number(tempRandomMaxIntervalValue) < 1) {
                            const category = getCurrentBroadcastCategory();
                            setTempRandomMaxIntervalValue(broadcastSettings[category].randomMaxInterval.toString());
                          }
                        }}
                        onFocus={(e) => e.target.select()} // 自动选中所有文本
                        InputProps={{
                          endAdornment: <InputAdornment position="end">
                            <Typography sx={{ 
                              fontSize: '1rem', 
                              fontWeight: 500,
                              color: theme.text.primary,
                              ml: 0.5
                            }}>
                              秒
                            </Typography>
                          </InputAdornment>,
                        }}
                        size="small"
                        label="最大间隔"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            bgcolor: alpha(theme.background.light, 0.3),
                            borderRadius: '8px',
                            '& fieldset': {
                              borderColor: alpha(theme.text.primary, 0.2),
                            },
                            '&:hover fieldset': {
                              borderColor: alpha(theme.primary, 0.5),
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: theme.primary,
                            }
                          },
                          '& input': {
                            color: theme.text.primary
                          },
                          '& label': {
                            color: alpha(theme.text.primary, 0.7)
                          },
                          '& label.Mui-focused': {
                            color: theme.primary
                          }
                        }}
                      />
                    </Grid>
                  </Grid>
                  
                  <Typography sx={{ 
                    color: theme.text.primary, 
                    fontSize: '0.8rem',
                    mt: 1,
                    fontWeight: 500,
                    textAlign: 'right'
                  }}>
                    {formatSeconds(broadcastSettings[getCurrentBroadcastCategory()].randomMinInterval)} - 
                    {formatSeconds(broadcastSettings[getCurrentBroadcastCategory()].randomMaxInterval)}
                  </Typography>
                </>
              )}
              
              <Divider sx={{ my: 2, borderColor: alpha(theme.border, 0.3) }} />
              
              {/* 添加AI泛化功能 */}
              <Typography sx={{ 
                color: theme.text.secondary, 
                fontSize: '0.9rem',
                mb: 1.5,
                display: 'flex',
                alignItems: 'center',
                gap: 1
              }}>
                <AutoAwesome sx={{ fontSize: '1rem', color: theme.accent }} />
                AI泛化功能
              </Typography>
              
              <FormControlLabel
                control={
                  <Switch 
                    checked={broadcastSettings[getCurrentBroadcastCategory()].useAI || false} 
                    onChange={(e) => handleAnnounceSettingsChange(
                      getCurrentBroadcastCategory(), 
                      { useAI: e.target.checked }
                    )}
                    sx={{
                      '& .MuiSwitch-switchBase.Mui-checked': {
                        color: theme.accent,
                      },
                      '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                        backgroundColor: alpha(theme.accent, 0.5),
                      },
                    }}
                  />
                }
                label={
                  <Typography sx={{ color: theme.text.primary, fontWeight: 500 }}>
                    启用AI泛化
                  </Typography>
                }
                sx={{ mb: 2 }}
              />
              
              {broadcastSettings[getCurrentBroadcastCategory()].useAI && (
                <>
                  <Typography sx={{ 
                    color: theme.text.secondary, 
                    fontSize: '0.9rem',
                    mb: 1.5 
                  }}>
                    AI泛化提示词
                  </Typography>
                  
                  <TextField
                    fullWidth
                    multiline
                    rows={3}
                    value={broadcastSettings[getCurrentBroadcastCategory()].aiPrompt || ''}
                    onChange={(e) => handleAnnounceSettingsChange(
                      getCurrentBroadcastCategory(),
                      { aiPrompt: e.target.value }
                    )}
                    placeholder="请输入AI泛化提示词，使用[原内容]表示原始话术"
                    size="small"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        bgcolor: alpha(theme.background.light, 0.3),
                        borderRadius: '8px',
                        '& fieldset': {
                          borderColor: alpha(theme.text.primary, 0.2),
                        },
                        '&:hover fieldset': {
                          borderColor: alpha(theme.primary, 0.5),
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: theme.primary,
                        }
                      },
                      '& textarea': {
                        color: theme.text.primary,
                        fontSize: '0.85rem',
                      }
                    }}
                  />
                  
                  <Typography sx={{ 
                    color: alpha(theme.text.secondary, 0.8), 
                    fontSize: '0.8rem',
                    mt: 1,
                    fontStyle: 'italic'
                  }}>
                    AI泛化会使用大模型对播报内容进行改写，使表达更加多样化。请确保API密钥已配置。
                  </Typography>
                </>
              )}
              
              <Typography sx={{ 
                color: alpha(theme.text.secondary, 0.8), 
                fontSize: '0.8rem',
                mt: 2,
                fontStyle: 'italic'
              }}>
                {currentTab === 0 
                  ? '系统将根据设置的时间间隔，定期从模板中随机选择一条欢迎语进行播报' 
                  : currentTab === 1 
                    ? '系统将根据设置的时间间隔，定期从模板中随机选择一条时间提示进行播报'
                    : '系统将根据设置的时间间隔，定期从模板中随机选择一条人数播报进行播报'
                }
              </Typography>
            </Menu>
            
            <Paper
              sx={{
                backgroundColor: alpha(theme.background.dark, 0.5),
                borderRadius: '16px',
                overflow: 'hidden',
                border: `1px solid ${alpha(theme.border, 0.3)}`,
                boxShadow: `0 8px 32px ${alpha('#000', 0.1)}`,
                position: 'relative',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '1px',
                  background: `linear-gradient(90deg, transparent, ${alpha(theme.primary, 0.3)}, transparent)`,
                  opacity: 0.8,
                },
              }}
            >
              <List sx={{ 
                maxHeight: '400px', 
                overflow: 'auto',
                '&::-webkit-scrollbar': {
                  width: '8px',
                },
                '&::-webkit-scrollbar-track': {
                  background: alpha(theme.background.dark, 0.3),
                  borderRadius: '4px',
                },
                '&::-webkit-scrollbar-thumb': {
                  background: alpha(theme.primary, 0.3),
                  borderRadius: '4px',
                  '&:hover': {
                    background: alpha(theme.primary, 0.5),
                  }
                }
              }}>
                {promptTemplates
                  .filter(template => {
                    if (currentTab === 0) return template.category === 'welcome';
                    if (currentTab === 1) return template.category === 'time';
                    if (currentTab === 2) return template.category === 'count';
                    return false;
                  })
                  .map((template, index, filteredArray) => (
                    <React.Fragment key={template.id}>
                      <ListItem
                        sx={{ 
                          py: 2.5, 
                          px: 3,
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            bgcolor: alpha(theme.background.highlight, 0.3),
                          }
                        }}
                        secondaryAction={
                          <Box>
                            <IconButton edge="end" sx={{ 
                              mr: 1,
                              color: theme.text.primary,
                              '&:hover': {
                                bgcolor: alpha(theme.text.primary, 0.1),
                              },
                            }} onClick={() => handleCopyTemplate(template.content)}>
                              <ContentCopy />
                            </IconButton>
                            <Tooltip title="编辑模板">
                              <IconButton edge="end" sx={{ 
                                mr: 1,
                                color: theme.text.primary,
                                '&:hover': {
                                  bgcolor: alpha(theme.primary, 0.1),
                                  color: theme.primary
                                },
                              }} onClick={() => handleOpenDialog(true, template)}>
                                <Edit />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="删除模板">
                              <IconButton edge="end" sx={{ 
                                color: theme.error,
                                '&:hover': {
                                  bgcolor: alpha(theme.error, 0.1),
                                },
                              }} onClick={() => handleDeleteTemplate(template.id)}>
                                <Delete />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        }
                      >
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              <Chip
                                size="small"
                                label={
                                  template.category === 'welcome' ? `欢迎语${index+1}` :
                                  template.category === 'time' ? `报时${index+1}` :
                                  template.category === 'count' ? `人数播报${index+1}` : ''
                                }
                                sx={{
                                  bgcolor: alpha(
                                    template.category === 'welcome' ? '#18cd94' :
                                    template.category === 'time' ? '#f59e0b' :
                                    template.category === 'count' ? theme.secondary : theme.primary
                                  , 0.15),
                                  color: 
                                    template.category === 'welcome' ? '#18cd94' :
                                    template.category === 'time' ? '#f59e0b' :
                                    template.category === 'count' ? theme.secondary : theme.primary,
                                  border: `1px solid ${alpha(
                                    template.category === 'welcome' ? '#18cd94' :
                                    template.category === 'time' ? '#f59e0b' :
                                    template.category === 'count' ? theme.secondary : theme.primary
                                  , 0.3)}`,
                                  borderRadius: '8px',
                                  fontWeight: 500,
                                }}
                              />
                            </Box>
                          }
                          secondary={
                            <Typography sx={{ 
                              color: alpha(theme.text.secondary, 0.9), 
                              fontSize: '0.875rem',
                              lineHeight: 1.6
                            }}>
                              {template.content}
                            </Typography>
                          }
                        />
                      </ListItem>
                      {index < filteredArray.length - 1 && (
                        <Divider sx={{ bgcolor: alpha(theme.border, 0.2) }} />
                      )}
                    </React.Fragment>
                  ))}
              </List>
              
              {promptTemplates.filter(template => {
                if (currentTab === 0) return template.category === 'welcome';
                if (currentTab === 1) return template.category === 'time';
                if (currentTab === 2) return template.category === 'count';
                return false;
              }).length === 0 && (
                <Box sx={{ 
                  p: 4, 
                  textAlign: 'center',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: 2 
                }}>
                  <AutoAwesome sx={{ 
                    fontSize: 40, 
                    color: alpha(theme.primary, 0.3),
                  }} />
                  <Typography sx={{ color: alpha(theme.text.secondary, 0.9) }}>
                    暂无模板，点击"添加模板"按钮创建新的模板
                  </Typography>
                </Box>
              )}
            </Paper>
          </CardContent>
        </Card>
      </Stack>
      
      {/* 提示词模板编辑对话框 */}
      <Dialog 
        open={openDialog} 
        onClose={handleCloseDialog}
        PaperProps={{
          sx: {
            backgroundColor: alpha(theme.background.paper, 0.95),
            backgroundImage: `radial-gradient(circle at 30% 20%, ${alpha(theme.primary, 0.05)} 0%, transparent 60%)`,
            borderRadius: '16px',
            border: `1px solid ${alpha(theme.border, 0.3)}`,
            minWidth: { xs: '90%', sm: '600px' },
            overflow: 'hidden',
            boxShadow: `0 20px 80px ${alpha('#000', 0.3)}`
          }
        }}
      >
        <DialogTitle sx={{ 
          color: theme.text.primary, 
          borderBottom: `1px solid ${alpha(theme.border, 0.3)}`,
          py: 2.5,
          px: 3,
          fontSize: '1.1rem',
          fontWeight: 600,
          display: 'flex',
          alignItems: 'center',
          gap: 1.5
        }}>
          <AutoAwesome sx={{ color: theme.primary }} />
          {editMode ? '编辑提示词模板' : '添加提示词模板'}
        </DialogTitle>
        <DialogContent sx={{ px: 3, py: 3 }}>
          {currentTemplate && (
            <Stack spacing={2.5}>
              <TextField
                fullWidth
                multiline
                rows={8}
                placeholder=""
                value={currentTemplate.content}
                onChange={(e) => handleTemplateChange('content', e.target.value)}
                  sx={{
                  ...textFieldStyle,
                  '& .MuiInputBase-root': {
                    ...textFieldStyle['& .MuiOutlinedInput-root'],
                    fontFamily: 'monospace',
                    minHeight: '200px'
                    },
                  '& .MuiInputBase-inputMultiline': {
                    overflowY: 'auto',
                    maxHeight: '200px'
                  }
                }}
              />
              
              <Paper sx={{
                p: 2.5,
                borderRadius: '12px',
                bgcolor: alpha(theme.background.highlight, 0.2),
                border: `1px solid ${alpha(theme.border, 0.2)}`,
                position: 'relative',
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  bottom: 0,
                  right: 0,
                  width: '120px',
                  height: '120px',
                  background: `radial-gradient(circle, ${alpha(theme.primary, 0.1)} 0%, transparent 70%)`,
                  borderRadius: '50%',
                  pointerEvents: 'none'
                }
              }}>
                <Typography sx={{ 
                  fontWeight: 600,
                  color: theme.text.primary,
                  mb: 1.5,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}>
                  <AutoAwesome sx={{ fontSize: 18, color: theme.accent }} />
                  变量使用说明
                </Typography>
                <Typography sx={{ 
                  color: alpha(theme.text.secondary, 0.9), 
                  fontSize: '0.875rem',
                  lineHeight: 1.7
                }}>
                  {currentTemplate.category === 'welcome' && "在欢迎语中，你可以使用 [昵称] 表示进来的用户昵称。"}
                  {currentTemplate.category === 'time' && `在报时提示词中，您可以使用 [时间] 表示当前时间（如"17点48分"），[整点]表示距离下一个整点的时间（如"距离18点还有12分钟"）。`}
                  {currentTemplate.category === 'count' && "在人数播报中，您可以使用 [人数]表示当前在线人数。"}
                </Typography>
              </Paper>
            </Stack>
          )}
        </DialogContent>
        <DialogActions sx={{ 
          p: 3, 
          borderTop: `1px solid ${alpha(theme.border, 0.2)}`,
          gap: 1
        }}>
          <Button 
            onClick={handleCloseDialog}
            variant="outlined"
            sx={secondaryButtonStyle}
          >
            取消
          </Button>
          <Button 
            onClick={handleSaveTemplate} 
            variant="contained"
            sx={primaryButtonStyle}
            startIcon={<Save />}
          >
            保存模板
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* 添加自动播报管理器组件 */}
      <AutoAnnounceManager 
        isConnected={isConnected}
        promptTemplates={promptTemplates}
        broadcastSettings={broadcastSettings}
        latestUsername={latestUsername}
        audienceCount={audienceCount}
      />
    </Box>
  );
}

export default AIModelWorkspace;