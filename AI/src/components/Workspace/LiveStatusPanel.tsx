import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Card, CardContent, Stack, Typography, Box, Button, IconButton, Switch, Slider, Popover, TextField, Tooltip, alpha, FormControlLabel, Chip, Dialog, DialogTitle, DialogContent, DialogActions, Menu, ListItemIcon, ListItemText, CircularProgress
} from '@mui/material';
import { VideoCall, KeyboardArrowDown, KeyboardArrowUp, Settings, PlayArrow, Stop, VolumeUp, GraphicEq, Add, Label, FilterList, Close, CheckCircle, Search } from '@mui/icons-material';
import { useWorkspaceState } from '../../hooks/useWorkspaceState';
import { useLiveConnection } from '../../contexts/LiveConnectionContext';
import { useIndexTTS } from '../../hooks/useIndexTTS';
import { useAIModel } from '../../contexts/AIModelContext';
import { TagStorage, Tag } from '../../utils/tagStorage';

// TODO: 引入所需 hooks、context、类型等

const theme = {
  primary: '#4f7df9',
  accent: '#2ec0ff',
  gradient: {
    primary: 'linear-gradient(135deg, #4f7df9 0%, #7a6cf7 100%)',
  },
  background: {
    paper: '#232842',
    dark: '#151b30',
    light: '#2a3153',
  },
  text: {
    primary: '#ffffff',
    secondary: '#c9d1f0',
    muted: '#8a94b8',
  },
  error: '#f44336',
  success: '#4caf50',
  border: '#373e59',
};

const advancedCardStyle = {
  backgroundColor: 'rgba(35,40,66,0.8)',
  backdropFilter: 'blur(10px)',
  borderRadius: '12px',
  border: '1px solid rgba(55,62,89,0.2)',
  boxShadow: '0 8px 32px rgba(21,27,48,0.5)',
  transition: 'all 0.3s ease',
};

const advancedCardContent = {
  p: 3,
  '&:last-child': { pb: 3 }
};

// TODO: 复制直播状态卡片的JSX和逻辑，所有状态通过 props/context/useWorkspaceState 传递

interface LiveStatusPanelProps {
  models: any[];
  currentModel: any;
  assistantModel: any;
  setCurrentModel: (model: any) => void;
  onOpenModelSelect: () => void;
  onOpenAssistantModelSelect: () => void;
  publicScreenConnected: boolean;
}

const LiveStatusPanel: React.FC<LiveStatusPanelProps> = ({
  models,
  currentModel,
  assistantModel,
  setCurrentModel,
  onOpenModelSelect,
  onOpenAssistantModelSelect,
  publicScreenConnected
}) => {
  // 直播状态相关状态
  const [speed, setSpeed] = useWorkspaceState<number>('live-speed', 1);
  const [isLiveStreaming, setIsLiveStreaming] = useWorkspaceState<boolean>('live-isLiveStreaming', false);
  const [isLoading, setIsLoading] = useWorkspaceState<boolean>('live-isLoading', false);
  const [selectingModelType, setSelectingModelType] = useState<'main' | 'assistant'>('main');
  const [modelSearchTerm, setModelSearchTerm] = useState('');
  const [tags, setTags] = useState<Tag[]>([]);
  const [modelTags, setModelTags] = useState<{[modelId: string]: string[]}>({});
  const [activeTagFilters, setActiveTagFilters] = useState<string[]>([]);
  const [tagFilterMenuAnchorEl, setTagFilterMenuAnchorEl] = useState<HTMLElement | null>(null);
  const [previewAudio, setPreviewAudio] = useState<any>(null);
  const [previewLoading, setPreviewLoading] = useState<string | null>(null);
  const { generateAudio, setAudioState } = useIndexTTS();
  const { latestUsername, audienceCount } = useLiveConnection();

  // 预缓存条数设置
  const [bufferSize, setBufferSize] = useWorkspaceState<number>('live-bufferSize', 3);

  // 音色轮换相关状态
  const [modelRotation, setModelRotation] = useWorkspaceState<{
    enabled: boolean;
    mode: 'timer' | 'random';
    interval: number;
    minInterval: number;
    maxInterval: number;
    tagIds: string[];
    lastSwitchTime: number;
    nextSwitchTime: number;
  }>('live-modelRotation', {
    enabled: false,
    mode: 'timer',
    interval: 60,
    minInterval: 30,
    maxInterval: 120,
    tagIds: [],
    lastSwitchTime: 0,
    nextSwitchTime: 0,
  });
  const [rotationState, setRotationState] = useState({
    timerId: null as NodeJS.Timeout | null,
    remainingSeconds: 0,
    availableModels: [] as any[],
  });
  const [rotationSettingsDialogOpen, setRotationSettingsDialogOpen] = useState(false);
  const [tempIntervalValue, setTempIntervalValue] = useState((modelRotation.interval ?? 60).toString());
  const [tempMinIntervalValue, setTempMinIntervalValue] = useState((modelRotation.minInterval ?? 30).toString());
  const [tempMaxIntervalValue, setTempMaxIntervalValue] = useState((modelRotation.maxInterval ?? 120).toString());

  // 用户接待相关
  const [userGreeting, setUserGreeting] = useWorkspaceState('live-userGreeting', {
    welcome: false,
    reply: true,
    time: false,
    count: false
  });

  // 语速随机相关状态
  const [speedRandomEnabled, setSpeedRandomEnabled] = useWorkspaceState<boolean>('live-speedRandomEnabled', false);
  const [speedRandomMin, setSpeedRandomMin] = useWorkspaceState<number>('live-speedRandomMin', 0.8);
  const [speedRandomMax, setSpeedRandomMax] = useWorkspaceState<number>('live-speedRandomMax', 1.2);
  const [speedRandomAnchorEl, setSpeedRandomAnchorEl] = useState<null | HTMLElement>(null);
  const [tempSpeedRandomMin, setTempSpeedRandomMin] = useState(speedRandomMin.toFixed(1));
  const [tempSpeedRandomMax, setTempSpeedRandomMax] = useState(speedRandomMax.toFixed(1));

  // 过滤模型列表（假设models从props或全局store获取，后续补充）
  const filteredModels = models.filter(model => {
    const matchesSearch = !modelSearchTerm || model.name.toLowerCase().includes(modelSearchTerm.toLowerCase()) || model.id.toLowerCase().includes(modelSearchTerm.toLowerCase());
    if (activeTagFilters.length > 0) {
      const modelTagIds = modelTags[model.id] || [];
      return matchesSearch && activeTagFilters.every(tagId => modelTagIds.includes(tagId));
    }
    return matchesSearch;
  });

  // 试听音色
  const handlePlayModelPreview = async (modelId: string, modelName: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (previewAudio && previewAudio.modelId === modelId && previewAudio.isPlaying) {
      if (previewAudio.audio) previewAudio.audio.pause();
      setPreviewAudio((prev: typeof previewAudio) => prev ? { ...prev, isPlaying: false } : null);
      return;
    }
    if (previewAudio && previewAudio.audio) {
      previewAudio.audio.pause();
      setPreviewAudio(null);
    }
    try {
      setPreviewLoading(modelId);
      setAudioState((prev: any) => ({ ...prev, error: null }));
      const cleanModelName = modelName.replace('使用参考音频', '').replace(/\.pt$/, '').trim();
      const previewText = `你好，我是${cleanModelName}，期待和你一起直播`;
      const modelWithSuffix = modelId.endsWith('.pt') ? modelId : `${modelId}.pt`;
      const audioUrl = await generateAudio({ model: modelWithSuffix, text: previewText, speed, isInterrupt: true });
      setPreviewLoading(null);
      if (audioUrl) {
        const audio = new Audio(audioUrl);
        audio.addEventListener('ended', () => setPreviewAudio((prev: typeof previewAudio) => prev ? { ...prev, isPlaying: false } : null));
        await audio.play();
        setPreviewAudio({ url: audioUrl, modelId, isPlaying: true, audio });
      }
    } catch (error) {
      setPreviewLoading(null);
      setAudioState((prev: any) => ({ ...prev, error: '试听失败，请重试' }));
    }
  };

  // 初始化标签
  useEffect(() => {
    const savedTags = TagStorage.getTags();
    const savedModelTags = TagStorage.getModelTags();
    if(savedTags.length > 0) setTags(savedTags);
    if(Object.keys(savedModelTags).length > 0) setModelTags(savedModelTags);
  }, []);

  // 轮换可用模型
  const updateAvailableModelsForRotation = () => {
    if (!modelRotation.tagIds || modelRotation.tagIds.length === 0) {
      setRotationState(prev => ({ ...prev, availableModels: [...models] }));
      return;
    }
    const filteredModels = models.filter(model => {
      const modelTagIds = modelTags[model.id] || [];
      return modelRotation.tagIds.every(tagId => modelTagIds.includes(tagId));
    });
    setRotationState(prev => ({ ...prev, availableModels: filteredModels }));
  };

  // 安排下次切换
  const scheduleNextModelSwitch = () => {
    const now = Date.now();
    let nextSwitchTime = now;
    if (modelRotation.mode === 'timer') {
      nextSwitchTime = now + (modelRotation.interval * 1000);
    } else {
      const range = modelRotation.maxInterval - modelRotation.minInterval;
      const randomInterval = modelRotation.minInterval + Math.floor(Math.random() * range);
      nextSwitchTime = now + (randomInterval * 1000);
    }
    setModelRotation(prev => ({ ...prev, lastSwitchTime: now, nextSwitchTime }));
  };

  // 随机切换模型
  const switchToRandomModel = () => {
    if (rotationState.availableModels.length === 0) {
      updateAvailableModelsForRotation();
      if (rotationState.availableModels.length === 0) return;
    }
    let availableModels = [...rotationState.availableModels];
    if (availableModels.length === 1) {
      setCurrentModel(availableModels[0]);
      return;
    }
    if (currentModel) {
      availableModels = availableModels.filter(model => model.id !== currentModel.id);
    }
    if (availableModels.length === 0) {
      availableModels = [...rotationState.availableModels];
    }
    const randomIndex = Math.floor(Math.random() * availableModels.length);
    const newModel = availableModels[randomIndex];
    setCurrentModel(newModel);
  };

  // 切换轮换开关
  const toggleModelRotation = (enabled: boolean) => {
    setModelRotation(prev => ({
      ...prev,
      enabled,
      nextSwitchTime: enabled ? Date.now() + (prev.interval * 1000) : 0,
      lastSwitchTime: enabled ? Date.now() : 0
    }));
    if (enabled) updateAvailableModelsForRotation();
  };

  // 更新轮换设置
  const updateModelRotationSettings = (settings: Partial<{
    enabled: boolean;
    mode: 'timer' | 'random';
    interval: number;
    minInterval: number;
    maxInterval: number;
    tagIds: string[];
    lastSwitchTime: number;
    nextSwitchTime: number;
  }>) => {
    setModelRotation(prev => ({
      ...prev,
      ...settings,
      nextSwitchTime: prev.enabled ? Date.now() + (settings.mode === 'timer' ? (settings.interval || prev.interval) * 1000 : ((settings.minInterval || prev.minInterval) + Math.floor(Math.random() * ((settings.maxInterval || prev.maxInterval) - (settings.minInterval || prev.minInterval)))) * 1000) : prev.nextSwitchTime
    }));
    if (settings.tagIds) updateAvailableModelsForRotation();
  };

  // 立即切换
  const triggerImmediateModelSwitch = () => {
    switchToRandomModel();
    scheduleNextModelSwitch();
  };

  // 计时器
  useEffect(() => {
    if (modelRotation.enabled) {
      updateAvailableModelsForRotation();
      if (modelRotation.nextSwitchTime === 0) scheduleNextModelSwitch();
      const timer = setInterval(() => {
        const now = Date.now();
        if (now >= modelRotation.nextSwitchTime && modelRotation.nextSwitchTime > 0) {
          switchToRandomModel();
          scheduleNextModelSwitch();
        }
        if (modelRotation.nextSwitchTime > 0) {
          const remaining = Math.max(0, Math.floor((modelRotation.nextSwitchTime - now) / 1000));
          setRotationState(prev => ({ ...prev, remainingSeconds: remaining }));
        }
      }, 1000);
      setRotationState(prev => ({ ...prev, timerId: timer }));
      return () => { if (timer) clearInterval(timer); };
    } else {
      if (rotationState.timerId) {
        clearInterval(rotationState.timerId);
        setRotationState(prev => ({ ...prev, timerId: null, remainingSeconds: 0 }));
      }
    }
  }, [modelRotation.enabled, modelRotation.nextSwitchTime, modelRotation.mode, modelRotation.tagIds]);

  // 临时输入框状态同步
  useEffect(() => {
    setTempIntervalValue((modelRotation.interval ?? 60).toString());
    setTempMinIntervalValue((modelRotation.minInterval ?? 30).toString());
    setTempMaxIntervalValue((modelRotation.maxInterval ?? 120).toString());
  }, [modelRotation.interval, modelRotation.minInterval, modelRotation.maxInterval]);

  return (
    <Card sx={advancedCardStyle}>
      <CardContent sx={advancedCardContent}>
        <Stack direction="row" alignItems="center" spacing={1.5} mb={3}>
          <VideoCall sx={{ color: theme.accent, filter: 'drop-shadow(0 0 3px rgba(46,192,255,0.5))' }} />
          <Typography variant="h6" sx={{ color: theme.text.primary, fontWeight: 600, letterSpacing: '0.05em', position: 'relative', '&::after': { content: '""', position: 'absolute', bottom: -5, left: 0, width: '40px', height: '2px', background: `linear-gradient(90deg, ${theme.accent}, transparent)`, borderRadius: '2px', }, }}>直播状态</Typography>
          <Box sx={{ flex: 1 }} />
          {/* 公屏连接状态指示器 */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mr: 2, px: 1.5, py: 0.5, borderRadius: '4px', bgcolor: alpha(publicScreenConnected ? theme.success : theme.error, 0.1), border: `1px solid ${alpha(publicScreenConnected ? theme.success : theme.error, 0.2)}` }}>
            <Box sx={{ width: 8, height: 8, borderRadius: '50%', bgcolor: publicScreenConnected ? theme.success : theme.error, boxShadow: `0 0 10px ${alpha(publicScreenConnected ? theme.success : theme.error, 0.5)}` }} />
            <Typography sx={{ color: publicScreenConnected ? theme.success : theme.error, fontSize: '0.85rem', fontWeight: 500 }}>公屏{publicScreenConnected ? '已连接' : '未连接'}</Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Box sx={{ width: 6, height: 6, borderRadius: '50%', bgcolor: isLiveStreaming ? theme.error : alpha(theme.text.primary, 0.3), boxShadow: isLiveStreaming ? `0 0 10px ${alpha(theme.error, 0.5)}` : 'none', animation: isLiveStreaming ? 'pulse 2s infinite' : 'none', '@keyframes pulse': { '0%': { opacity: 1, transform: 'scale(1)' }, '50%': { opacity: 0.5, transform: 'scale(1.1)' }, '100%': { opacity: 1, transform: 'scale(1)' }, }, }} />
            <Typography sx={{ color: isLiveStreaming ? theme.error : theme.text.secondary, fontSize: '0.85rem', fontWeight: 500 }}>{isLiveStreaming ? '直播中' : '未开播'}</Typography>
          </Box>
        </Stack>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, minWidth: 'fit-content' }}>
            <Typography sx={{ color: theme.text.secondary, fontSize: '0.85rem' }}>主播音色:</Typography>
            <Button
              variant="outlined"
              size="small"
              onClick={onOpenModelSelect}
              endIcon={<KeyboardArrowDown />}
              sx={{ minWidth: 140, height: 32, fontSize: '0.85rem', bgcolor: alpha(theme.background.light, 0.5), color: theme.text.primary, border: `1px solid ${alpha(theme.text.primary, 0.1)}`, '&:hover': { bgcolor: alpha(theme.background.light, 0.7), borderColor: alpha(theme.text.primary, 0.2) }, display: 'flex', justifyContent: 'space-between', px: 2 }}
            >
              {currentModel ? currentModel.name.replace('使用参考音频', '').trim() : '选择音色'}
            </Button>
            <Typography sx={{ color: theme.text.secondary, fontSize: '0.85rem', ml: 2 }}>助播音色:</Typography>
            <Button
              variant="outlined"
              size="small"
              onClick={onOpenAssistantModelSelect}
              endIcon={<KeyboardArrowDown />}
              sx={{ minWidth: 140, height: 32, fontSize: '0.85rem', bgcolor: alpha(theme.background.light, 0.5), color: theme.text.primary, border: `1px solid ${alpha(theme.text.primary, 0.1)}`, '&:hover': { bgcolor: alpha(theme.background.light, 0.7), borderColor: alpha(theme.text.primary, 0.2) }, display: 'flex', justifyContent: 'space-between', px: 2 }}
            >
              {assistantModel ? assistantModel.name.replace('使用参考音频', '').trim() : '选择音色'}
            </Button>
            {/* 预缓存数值输入框紧跟在助播音色右侧 */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, ml: 2, minWidth: 120 }}>
              <Tooltip title="每次开播时预先生成的音频条数，建议低配电脑设置大一些">
                <Typography sx={{ color: theme.text.secondary, fontSize: '0.85rem', whiteSpace: 'nowrap' }}>预缓存:</Typography>
              </Tooltip>
              <TextField
                type="number"
                size="small"
                value={bufferSize}
                onChange={e => {
                  let v = Number(e.target.value);
                  if (isNaN(v)) v = 1;
                  if (v < 1) v = 1;
                  if (v > 20) v = 20;
                  setBufferSize(v);
                }}
                inputProps={{
                  min: 1,
                  max: 20,
                  step: 1,
                  style: { textAlign: 'center' }
                }}
                variant="outlined"
                sx={{
                  width: 64,
                  minWidth: 64,
                  height: 32,
                  '& .MuiOutlinedInput-root': {
                    height: 32,
                    minHeight: 32,
                    borderRadius: '8px',
                    bgcolor: 'rgba(42,49,83,0.5)', // 跟Button一致
                    color: '#fff',
                    fontSize: '0.85rem',
                    fontWeight: 600,
                    px: 2,
                    '& fieldset': {
                      borderColor: 'rgba(255,255,255,0.18)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#fff',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#fff',
                    },
                  },
                  '& input': {
                    textAlign: 'center',
                    color: '#fff',
                    fontWeight: 600,
                    fontSize: '0.85rem',
                    background: 'transparent',
                    padding: 0,
                    height: 32,
                  },
                }}
              />
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Typography sx={{ color: theme.text.secondary, fontSize: '0.85rem', whiteSpace: 'nowrap' }}>语速:</Typography>
              <TextField
                type="number"
                size="small"
                value={speed}
                onChange={e => {
                  let v = Number(e.target.value);
                  if (isNaN(v)) v = 0.5;
                  if (v < 0.5) v = 0.5;
                  if (v > 2) v = 2;
                  setSpeed(v);
                }}
                inputProps={{
                  min: 0.5,
                  max: 2,
                  step: 0.1,
                  style: { textAlign: 'center' }
                }}
                variant="outlined"
                sx={{
                  width: 80,
                  minWidth: 80,
                  height: 32,
                  '& .MuiOutlinedInput-root': {
                    height: 32,
                    minHeight: 32,
                    borderRadius: '8px',
                    bgcolor: 'rgba(42,49,83,0.5)',
                    color: '#fff',
                    fontSize: '0.85rem',
                    fontWeight: 600,
                    px: 2,
                    '& fieldset': {
                      borderColor: 'rgba(255,255,255,0.18)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#fff',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#fff',
                    },
                  },
                  '& input': {
                    textAlign: 'center',
                    color: '#fff',
                    fontWeight: 600,
                    fontSize: '0.85rem',
                    background: 'transparent',
                    padding: 0,
                    height: 32,
                  },
                }}
              />
              <Tooltip title="设置语速随机范围">
                <IconButton size="small" onClick={e => setSpeedRandomAnchorEl(e.currentTarget)} sx={{ color: theme.accent, ml: 0.5 }}>
                  <Settings fontSize="small" />
                </IconButton>
              </Tooltip>
              <Popover open={Boolean(speedRandomAnchorEl)} anchorEl={speedRandomAnchorEl} onClose={() => setSpeedRandomAnchorEl(null)} anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }} transformOrigin={{ vertical: 'top', horizontal: 'left' }} sx={{ zIndex: 2000 }}>
                <Box sx={{ p: 2, minWidth: 220 }}>
                  <FormControlLabel
                    control={<Switch checked={speedRandomEnabled} onChange={e => setSpeedRandomEnabled(e.target.checked)} size="small" sx={{ '& .MuiSwitch-switchBase.Mui-checked': { color: theme.accent, '& + .MuiSwitch-track': { backgroundColor: alpha(theme.accent, 0.5) } } }} />}
                    label={<Typography sx={{ color: theme.text.secondary, fontSize: '0.95rem' }}>语速随机</Typography>}
                    sx={{ mb: 2 }}
                  />
                  <Typography sx={{ color: theme.text.primary, fontWeight: 600, mb: 1 }}>语速随机范围</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <TextField label="最小语速" size="small" type="number" inputProps={{ step: 0.1, min: 0.5, max: 2, pattern: '^([0-1](\\.[0-9])?|2(\\.0)?)$' }} value={tempSpeedRandomMin} onChange={e => { const v = e.target.value; setTempSpeedRandomMin(v); const num = parseFloat(v); if (!isNaN(num) && num >= 0.5 && num <= 2 && /^\d(\.\d)?$|^2(\.0)?$/.test(v)) { setSpeedRandomMin(num); } }} onBlur={() => { if (isNaN(parseFloat(tempSpeedRandomMin)) || !/^\d(\.\d)?$|^2(\.0)?$/.test(tempSpeedRandomMin) || parseFloat(tempSpeedRandomMin) < 0.5 || parseFloat(tempSpeedRandomMin) > 2) { setTempSpeedRandomMin(speedRandomMin.toFixed(1)); } }} disabled={!speedRandomEnabled} sx={{ width: 90 }} />
                    <Typography sx={{ color: theme.text.secondary }}>至</Typography>
                    <TextField label="最大语速" size="small" type="number" inputProps={{ step: 0.1, min: 0.5, max: 2, pattern: '^([0-1](\\.[0-9])?|2(\\.0)?)$' }} value={tempSpeedRandomMax} onChange={e => { const v = e.target.value; setTempSpeedRandomMax(v); const num = parseFloat(v); if (!isNaN(num) && num >= 0.5 && num <= 2 && /^\d(\.\d)?$|^2(\.0)?$/.test(v)) { setSpeedRandomMax(num); } }} onBlur={() => { if (isNaN(parseFloat(tempSpeedRandomMax)) || !/^\d(\.\d)?$|^2(\.0)?$/.test(tempSpeedRandomMax) || parseFloat(tempSpeedRandomMax) < 0.5 || parseFloat(tempSpeedRandomMax) > 2) { setTempSpeedRandomMax(speedRandomMax.toFixed(1)); } }} disabled={!speedRandomEnabled} sx={{ width: 90 }} />
                  </Box>
                </Box>
              </Popover>
            </Box>
          </Box>
          {/* 音色轮换控制 */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, borderTop: `1px solid ${alpha(theme.border, 0.3)}`, pt: 2, mt: 1 }}>
            <Typography sx={{ color: theme.text.secondary, fontSize: '0.85rem', width: 70 }}>音色轮换:</Typography>
            <Switch
              size="small"
              checked={modelRotation.enabled}
              onChange={(e) => toggleModelRotation(e.target.checked)}
              sx={{ '& .MuiSwitch-switchBase.Mui-checked': { color: theme.accent, '& + .MuiSwitch-track': { backgroundColor: alpha(theme.accent, 0.5) } } }}
            />
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, opacity: modelRotation.enabled ? 1 : 0.6, pointerEvents: modelRotation.enabled ? 'auto' : 'none' }}>
              <Button size="small" variant={modelRotation.mode === 'timer' ? 'contained' : 'outlined'} onClick={() => updateModelRotationSettings({ mode: 'timer' })} sx={{ minWidth: 'unset', px: 1.5, py: 0.5, fontSize: '0.75rem', bgcolor: modelRotation.mode === 'timer' ? theme.accent : 'transparent', borderColor: alpha(theme.accent, 0.5), color: modelRotation.mode === 'timer' ? theme.text.primary : theme.accent, '&:hover': { bgcolor: modelRotation.mode === 'timer' ? alpha(theme.accent, 0.8) : alpha(theme.accent, 0.1) } }}>固定时间</Button>
              <Button size="small" variant={modelRotation.mode === 'random' ? 'contained' : 'outlined'} onClick={() => updateModelRotationSettings({ mode: 'random' })} sx={{ minWidth: 'unset', px: 1.5, py: 0.5, fontSize: '0.75rem', bgcolor: modelRotation.mode === 'random' ? theme.accent : 'transparent', borderColor: alpha(theme.accent, 0.5), color: modelRotation.mode === 'random' ? theme.text.primary : theme.accent, '&:hover': { bgcolor: modelRotation.mode === 'random' ? alpha(theme.accent, 0.8) : alpha(theme.accent, 0.1) } }}>随机时间</Button>
              <Tooltip title="选择标签组限制轮换范围">
                <Button size="small" endIcon={<KeyboardArrowDown sx={{ fontSize: '0.9rem' }} />} onClick={(e) => setTagFilterMenuAnchorEl(e.currentTarget)} sx={{ minWidth: 'unset', px: 1.5, py: 0.5, fontSize: '0.75rem', bgcolor: modelRotation.tagIds.length > 0 ? alpha(theme.primary, 0.1) : 'transparent', border: `1px solid ${alpha(theme.primary, 0.5)}`, color: theme.primary, '&:hover': { bgcolor: alpha(theme.primary, 0.1) } }}>{modelRotation.tagIds.length > 0 ? `已选${modelRotation.tagIds.length}个标签` : '选择标签组'}</Button>
              </Tooltip>
              <Tooltip title="立即切换">
                <IconButton size="small" onClick={triggerImmediateModelSwitch} sx={{ color: theme.primary, bgcolor: alpha(theme.primary, 0.1), p: 0.8, '&:hover': { bgcolor: alpha(theme.primary, 0.2) } }}>
                  <GraphicEq fontSize="small" />
                </IconButton>
              </Tooltip>
              <Tooltip title="高级设置">
                <IconButton size="small" onClick={() => setRotationSettingsDialogOpen(true)} sx={{ color: theme.accent, bgcolor: alpha(theme.accent, 0.1), p: 0.8, '&:hover': { bgcolor: alpha(theme.accent, 0.2) } }}>
                  <Settings fontSize="small" />
                </IconButton>
              </Tooltip>
              {modelRotation.enabled && (
                <Box sx={{ display: 'flex', alignItems: 'center', ml: 1, px: 1.5, py: 0.5, borderRadius: '4px', bgcolor: alpha(theme.background.light, 0.3), border: `1px solid ${alpha(theme.border, 0.1)}` }}>
                  <Typography sx={{ fontSize: '0.75rem', color: theme.text.secondary }}>
                    {rotationState.remainingSeconds > 0 ? `${Math.floor(rotationState.remainingSeconds / 60)}:${(rotationState.remainingSeconds % 60).toString().padStart(2, '0')}后切换` : '准备切换...'}
                  </Typography>
                </Box>
              )}
            </Box>
          </Box>
        </Box>
        <Popover
          open={Boolean(tagFilterMenuAnchorEl)}
          anchorEl={tagFilterMenuAnchorEl}
          onClose={() => setTagFilterMenuAnchorEl(null)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
          transformOrigin={{ vertical: 'top', horizontal: 'center' }}
          sx={{
            '& .MuiPaper-root': {
              bgcolor: alpha(theme.background.paper, 0.95),
              border: `1px solid ${alpha(theme.accent, 0.2)}`,
              borderRadius: '8px',
              boxShadow: `0 8px 20px ${alpha('#000', 0.2)}`,
              backdropFilter: 'blur(8px)',
              maxWidth: 280,
              overflow: 'visible',
              zIndex: 9999,
            }
          }}
        >
          <Box sx={{ p: 2 }}>
            <Typography sx={{ fontSize: '0.9rem', fontWeight: 600, color: theme.text.primary, mb: 1 }}>
              选择音色轮换范围
            </Typography>
            <Typography sx={{ fontSize: '0.8rem', color: theme.text.secondary, mb: 2 }}>
              只在选定标签的音色间切换
            </Typography>
            {tags.length === 0 ? (
              <Typography sx={{ fontSize: '0.85rem', color: theme.text.secondary, textAlign: 'center', py: 2 }}>
                暂无标签，请先创建标签
              </Typography>
            ) : (
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, my: 1 }}>
                {tags.map(tag => {
                  const isSelected = modelRotation.tagIds.includes(tag.id);
                  return (
                    <Chip
                      key={tag.id}
                      label={tag.name}
                        size="small"
                      onClick={() => {
                        updateModelRotationSettings({
                          tagIds: isSelected
                            ? modelRotation.tagIds.filter(id => id !== tag.id)
                            : [...modelRotation.tagIds, tag.id]
                        });
                      }}
                      sx={{
                        bgcolor: isSelected ? alpha(tag.color, 0.2) : alpha(theme.background.light, 0.3),
                        color: isSelected ? tag.color : theme.text.secondary,
                        borderRadius: '4px',
                        border: isSelected ? `1px solid ${alpha(tag.color, 0.3)}` : '1px solid transparent',
                        '&:hover': {
                          bgcolor: isSelected ? alpha(tag.color, 0.3) : alpha(theme.background.light, 0.5),
                        }
                      }}
                    />
                  );
                })}
              </Box>
            )}
            <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
              <Button
                size="small"
                fullWidth
                variant="outlined"
                onClick={() => {
                  updateModelRotationSettings({ tagIds: [] });
                  setTagFilterMenuAnchorEl(null);
                }}
                sx={{
                  borderColor: alpha(theme.text.primary, 0.15),
                  color: theme.text.secondary,
                  fontWeight: 600,
                  borderRadius: '8px',
                  bgcolor: 'transparent',
                  height: 36,
                  fontSize: '0.95rem',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  whiteSpace: 'nowrap',
                  lineHeight: 1,
                  '&:hover': {
                    bgcolor: alpha(theme.text.primary, 0.07),
                    borderColor: theme.text.primary,
                  }
                }}
              >
                清除全部
              </Button>
              <Button
                size="small"
                fullWidth
                variant="contained"
                onClick={() => {
                  setTagFilterMenuAnchorEl(null);
                  setRotationSettingsDialogOpen(true);
                }}
                sx={{
                  bgcolor: theme.accent,
                  color: theme.text.primary,
                  fontWeight: 600,
                  borderRadius: '8px',
                  height: 36,
                  fontSize: '0.95rem',
                  boxShadow: 'none',
                  '&:hover': {
                    bgcolor: alpha(theme.accent, 0.85),
                    boxShadow: 'none',
                  }
                }}
              >
                高级设置
              </Button>
            </Box>
          </Box>
        </Popover>
        <Dialog
          open={rotationSettingsDialogOpen}
          onClose={() => setRotationSettingsDialogOpen(false)}
          maxWidth="xs"
          fullWidth
          PaperProps={{
            sx: {
              bgcolor: theme.background.paper,
              borderRadius: 2,
              boxShadow: '0 8px 32px rgba(0,0,0,0.3)',
              p: 2
            }
          }}
        >
          <DialogTitle sx={{ color: theme.text.primary, fontWeight: 600, pb: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Settings sx={{ mr: 1, color: theme.accent }} />
              音色轮换设置
        </Box>
          </DialogTitle>
          <DialogContent>
            <Stack spacing={3} sx={{ mt: 1 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={modelRotation.enabled}
                    onChange={(e) => toggleModelRotation(e.target.checked)}
                    color="primary"
                    sx={{ '& .MuiSwitch-switchBase.Mui-checked': { color: theme.accent, '& + .MuiSwitch-track': { backgroundColor: alpha(theme.accent, 0.5) } } }}
                  />
                }
                label={<Typography sx={{ color: theme.text.primary, fontWeight: 500 }}>启用音色轮换</Typography>}
              />
              <Box>
                <Typography sx={{ color: theme.text.primary, mb: 1, fontWeight: 500 }}>轮换模式</Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
              fullWidth
                    variant={modelRotation.mode === 'timer' ? 'contained' : 'outlined'}
                    onClick={() => updateModelRotationSettings({ mode: 'timer' })}
                    sx={{
                      bgcolor: modelRotation.mode === 'timer' ? theme.accent : 'transparent',
                      borderColor: theme.accent,
                      color: modelRotation.mode === 'timer' ? theme.text.primary : theme.accent,
                      '&:hover': {
                        bgcolor: modelRotation.mode === 'timer' ? alpha(theme.accent, 0.8) : alpha(theme.accent, 0.1),
                      }
                    }}
                  >
                    固定时间间隔
                  </Button>
                  <Button
                    fullWidth
                    variant={modelRotation.mode === 'random' ? 'contained' : 'outlined'}
                    onClick={() => updateModelRotationSettings({ mode: 'random' })}
                    sx={{
                      bgcolor: modelRotation.mode === 'random' ? theme.accent : 'transparent',
                      borderColor: theme.accent,
                      color: modelRotation.mode === 'random' ? theme.text.primary : theme.accent,
                      '&:hover': {
                        bgcolor: modelRotation.mode === 'random' ? alpha(theme.accent, 0.8) : alpha(theme.accent, 0.1),
                      }
                    }}
                  >
                    随机时间间隔
                  </Button>
                </Box>
              </Box>
              {modelRotation.mode === 'timer' ? (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography sx={{ color: theme.text.primary, fontWeight: 500 }}>固定切换间隔 (秒)</Typography>
                  </Box>
                  <TextField
                    fullWidth
                    variant="outlined"
                    type="text"
                    size="small"
                    value={tempIntervalValue}
                    onChange={(e) => {
                      setTempIntervalValue(e.target.value);
                      const value = parseInt(e.target.value);
                      if (!isNaN(value) && value >= 1 && value <= 86400) {
                        updateModelRotationSettings({ interval: value });
                      }
                    }}
                    onBlur={() => {
                      if (tempIntervalValue === '' || isNaN(parseInt(tempIntervalValue)) || parseInt(tempIntervalValue) < 1 || parseInt(tempIntervalValue) > 86400) {
                        setTempIntervalValue((modelRotation.interval ?? 60).toString());
                      }
                    }}
                    onFocus={(e) => e.target.select()}
                    InputProps={{
                      endAdornment: <span>秒</span>,
                      inputProps: { min: 1, max: 86400 }
                    }}
                  />
                </Box>
              ) : (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography sx={{ color: theme.text.primary, fontWeight: 500 }}>随机切换范围 (秒)</Typography>
                  </Box>
                  <Stack direction="row" spacing={2} alignItems="center">
                    <TextField
                      fullWidth
                      variant="outlined"
                      type="text"
                      size="small"
                      value={tempMinIntervalValue}
                      onChange={(e) => {
                        setTempMinIntervalValue(e.target.value);
                        const value = parseInt(e.target.value);
                        if (!isNaN(value) && value >= 1 && value < modelRotation.maxInterval) {
                          updateModelRotationSettings({ minInterval: value });
                        }
                      }}
                      onBlur={() => {
                        if (tempMinIntervalValue === '' || isNaN(parseInt(tempMinIntervalValue)) || parseInt(tempMinIntervalValue) < 1 || parseInt(tempMinIntervalValue) >= modelRotation.maxInterval || parseInt(tempMinIntervalValue) > 86400) {
                          setTempMinIntervalValue((modelRotation.minInterval ?? 30).toString());
                        }
                      }}
                      onFocus={(e) => e.target.select()}
                      InputProps={{
                        endAdornment: <span>秒</span>,
                        inputProps: { min: 1, max: 86400 }
                      }}
                    />
                    <TextField
                      fullWidth
                      variant="outlined"
                      type="text"
                      size="small"
                      value={tempMaxIntervalValue}
                      onChange={(e) => {
                        setTempMaxIntervalValue(e.target.value);
                        const value = parseInt(e.target.value);
                        if (!isNaN(value) && value > modelRotation.minInterval && value <= 86400) {
                          updateModelRotationSettings({ maxInterval: value });
                        }
                      }}
                      onBlur={() => {
                        if (tempMaxIntervalValue === '' || isNaN(parseInt(tempMaxIntervalValue)) || parseInt(tempMaxIntervalValue) <= modelRotation.minInterval || parseInt(tempMaxIntervalValue) > 86400) {
                          setTempMaxIntervalValue((modelRotation.maxInterval ?? 120).toString());
                        }
                      }}
                      onFocus={(e) => e.target.select()}
                      InputProps={{
                        endAdornment: <span>秒</span>,
                        inputProps: { min: 1, max: 86400 }
                      }}
                    />
                  </Stack>
                </Box>
              )}
              <Box>
                <Typography sx={{ color: theme.text.primary, mb: 1, fontWeight: 500 }}>标签筛选</Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, my: 1 }}>
                  {tags.map(tag => {
                    const isSelected = modelRotation.tagIds.includes(tag.id);
                    return (
                      <Chip
                        key={tag.id}
                        label={tag.name}
                        size="small"
                        onClick={() => {
                          updateModelRotationSettings({
                            tagIds: isSelected
                              ? modelRotation.tagIds.filter(id => id !== tag.id)
                              : [...modelRotation.tagIds, tag.id]
                          });
                        }}
                        sx={{
                          bgcolor: isSelected ? alpha(tag.color, 0.2) : alpha(theme.background.light, 0.3),
                          color: isSelected ? tag.color : theme.text.secondary,
                          borderRadius: '4px',
                          border: isSelected ? `1px solid ${alpha(tag.color, 0.3)}` : '1px solid transparent',
                          '&:hover': {
                            bgcolor: isSelected ? alpha(tag.color, 0.3) : alpha(theme.background.light, 0.5),
                          }
                        }}
                      />
                    );
                  })}
                  {tags.length === 0 && (
                    <Typography sx={{ color: theme.text.secondary, p: 1, textAlign: 'center', width: '100%' }}>
                      暂无可用标签
                    </Typography>
                  )}
                </Box>
                <Typography sx={{ color: theme.text.secondary, mt: 1, fontSize: '0.8rem', fontStyle: 'italic' }}>
                  不选择标签则从所有音色中随机切换
                </Typography>
              </Box>
            </Stack>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setRotationSettingsDialogOpen(false)} sx={{ color: theme.text.secondary }}>
              取消
            </Button>
            <Button
              variant="contained"
              onClick={() => {
                setRotationSettingsDialogOpen(false);
                if (modelRotation.enabled) {
                  updateAvailableModelsForRotation();
                }
              }}
              sx={{
                bgcolor: theme.accent,
                color: theme.text.primary,
                '&:hover': {
                  bgcolor: alpha(theme.accent, 0.8)
                }
              }}
            >
              应用设置
            </Button>
          </DialogActions>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default LiveStatusPanel; 
 
 