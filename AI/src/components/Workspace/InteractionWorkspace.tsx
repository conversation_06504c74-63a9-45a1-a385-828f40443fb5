import React, { useState, useEffect, useCallback, useRef } from 'react';
import { 
  Box, 
  Card, 
  CardContent, 
  Typography, 
  Stack,
  TextField,
  Button,
  Switch,
  FormControlLabel,
  List,
  ListItem,
  ListItemText,
  Divider,
  Chip,
  Grid,
  Paper,
  IconButton,
  alpha,
  Tooltip,
  Tab,
  Tabs,
  MenuItem,
  Menu,
  InputAdornment,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Snackbar,
  Radio,
  RadioGroup,
  ButtonGroup
} from '@mui/material';
import { 
  Send, 
  SmartToy,
  Save,
  Add,
  Delete,
  Settings,
  AutoAwesome,
  AccessTime,
  Psychology,
  CachedOutlined,
  PlayArrow,
  ChatBubble,
  Visibility,
  VisibilityOff,
  ContentCopy,
  Timer,
  Person,
  AddCircle,
  Comment as CommentIcon,
  Edit
} from '@mui/icons-material';
import { useAIModel } from '../../contexts/AIModelContext';
import { useLiveReply } from '../../contexts/LiveReplyContext';
import { useWorkspaceState } from '../../hooks/useWorkspaceState';
import { filterEmojis, truncateNickname } from '../../utils/templateUtils';

/**
 * 互动工作区组件属性接口
 * @interface InteractionWorkspaceProps
 */
interface InteractionWorkspaceProps {
  // 移除 TTS 相关属性
}

/**
 * 互动工作区组件
 * 提供直播间互动回复功能，包括关键词自动回复、AI模型回复和直播连接
 * 
 * @component
 * @param {InteractionWorkspaceProps} props - 组件属性
 * @returns {React.ReactElement} 互动工作区组件
 */
export const InteractionWorkspace: React.FC<InteractionWorkspaceProps> = ({
  // 移除 TTS 相关属性
}) => {
  // 获取AI模型上下文
  const { config, generateText, isProcessing, error: aiError, sessionConfig, updateSessionConfig, resetSession } = useAIModel();
  const { insertLiveReply } = useLiveReply();
  
  const [autoReplyEnabled, setAutoReplyEnabled] = useState(() => {
    // 尝试从localStorage加载之前保存的自动回复状态
    const savedAutoReplyEnabled = localStorage.getItem('autoReplyEnabled');
    return savedAutoReplyEnabled ? JSON.parse(savedAutoReplyEnabled) : false;
  });
  const [aiModelEnabled, setAiModelEnabled] = useState(false);
  const [keywordReplies, setKeywordReplies] = useState<{keyword: string, reply: string}[]>(() => {
    // 尝试从localStorage加载之前保存的关键词回复
    const savedKeywordReplies = localStorage.getItem('keywordReplies');
    return savedKeywordReplies ? JSON.parse(savedKeywordReplies) : [
      { keyword: "价格", reply: "这款产品的价格是299元，现在购买还有优惠哦！" },
      { keyword: "配送", reply: "我们全国包邮，一般2-3天就能送到您手中。" },
      { keyword: "材质", reply: "我们的产品采用优质材料制造，非常耐用，请您放心购买。" }
    ];
  });
  const [newKeyword, setNewKeyword] = useState("");
  const [newReply, setNewReply] = useState("");
  const [currentTab, setCurrentTab] = useState(0);
  
  // 添加回复格式设置状态
  const [replyFormat, setReplyFormat] = useState<string>(() => {
    const savedFormat = localStorage.getItem('replyFormat');
    return savedFormat || "{提问内容}，{用户名}宝子，呃， {回复内容}";
  });
  
  // 添加记录已回复消息的状态
  const [repliedMessages, setRepliedMessages] = useState<Set<string>>(new Set());
  
  // AI回复设置相关状态
  const [aiTestInput, setAiTestInput] = useState("");
  const [aiTestOutput, setAiTestOutput] = useState("");
  const [aiSystemPrompt, setAiSystemPrompt] = useState(() => {
    // 尝试从localStorage加载之前保存的提示词
    const savedPrompt = localStorage.getItem('aiSystemPrompt');
    return savedPrompt || "你是一个专业的带货主播，负责回答用户在直播间的问题。请保持友好、简洁的回复风格，回答限制在50字以内，注意不要提到抖音违禁词，不要回复特殊符号以及表情包符号，就返回正常的文本内容。";
  });
  const [prioritizeKeyword, setPrioritizeKeyword] = useState(() => {
    // 尝试从localStorage加载之前保存的优先使用关键词设置
    const savedSetting = localStorage.getItem('prioritizeKeyword');
    return savedSetting !== null ? JSON.parse(savedSetting) : true;
  });
  const [isTestingAI, setIsTestingAI] = useState(false);
  
  // 添加用户白名单状态
  const [userWhitelist, setUserWhitelist] = useState<string[]>(() => {
    // 尝试从localStorage加载之前保存的白名单
    const savedWhitelist = localStorage.getItem('userWhitelist');
    return savedWhitelist ? JSON.parse(savedWhitelist) : [];
  });
  const [newWhitelistUser, setNewWhitelistUser] = useState("");
  
  // 修改的直播互动状态
  const [liveUrl, setLiveUrl] = useState(() => {
    // 尝试从localStorage加载之前保存的直播链接
    const savedLiveUrl = localStorage.getItem('liveUrl');
    return savedLiveUrl || "";
  });
  const [isConnecting, setIsConnecting] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [systemAlert, setSystemAlert] = useState<{
    open: boolean;
    message: string;
    type: 'info' | 'error' | 'success' | 'warning';
  }>({
    open: false,
    message: '',
    type: 'info'
  });

  // 添加在线人数状态
  const [audienceCount, setAudienceCount] = useState<number>(0);

  // 从模型上下文获取提示词模板
  const [promptTemplates] = useWorkspaceState<Array<{
    id: string;
    name: string;
    content: string;
    category: 'welcome' | 'time' | 'count' | 'product' | 'custom';
  }>>('ai-model-promptTemplates', []);
  
  // 添加最近进入用户状态
  const [latestUsername, setLatestUsername] = useState<string | undefined>();
  // 添加上一个用户名的引用，用于比较
  const prevUsernameRef = useRef<string | undefined>();

  /**
   * 互动消息类型
   * @typedef {Object} MessageType
   * @property {'comment'|'enter'|'system'|'error'|'gift'|'follow'|'like'} type - 消息类型
   * @property {string} content - 消息内容
   * @property {string} [username] - 可选的用户名
   * @property {number} timestamp - 消息时间戳
   */
  type MessageType = {
    id: string;
    type: 'comment' | 'enter' | 'system' | 'error' | 'gift' | 'follow' | 'like';
    content: string;
    username?: string;
    timestamp: number;
  };

  const [interactionMessages, setInteractionMessages] = useState<MessageType[]>([]);

  // 添加WebSocket状态
  const [ws, setWs] = useState<WebSocket | null>(null);
  /**
   * 生成唯一的客户端标识符
   * @returns {string} 唯一的客户端ID
   */
  const [clientId] = useState<string>(() => {
    const timestamp = Date.now().toString(36);
    const randomStr = Math.random().toString(36).substring(2, 10);
    return `client_${timestamp}_${randomStr}`;
  });
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const maxReconnectAttempts = 3;
  const wsRef = useRef<WebSocket | null>(null);

  // 添加新的状态和引用
  const [isAutoScrollEnabled, setIsAutoScrollEnabled] = useState(true);
  const [unreadCount, setUnreadCount] = useState(0);
  const commentListRef = useRef<HTMLDivElement>(null);
  const enterListRef = useRef<HTMLDivElement>(null);

  // 添加会话状态提示
  const [showResetSessionConfirm, setShowResetSessionConfirm] = useState(false);

  // 添加上一次选择的模型状态
  const [lastSelectedModel, setLastSelectedModel] = useState(config.selectedModel);

  // 添加新的状态来跟踪正在处理的消息
  const [processingMessageId, setProcessingMessageId] = useState<string | null>(null);

  // 添加 useRef 来跟踪最后处理的消息
  const lastProcessedMessageRef = useRef<{
    id: string;
    timestamp: number;
  } | null>(null);

  // 添加 useRef 来跟踪处理中的消息
  const processingMessagesRef = useRef(new Set<string>());

  // 添加平台选择状态，默认为 douyin
  const [selectedPlatform, setSelectedPlatform] = useState<"douyin" | "wechat_channels">(() => {
    const savedPlatform = localStorage.getItem('selectedPlatform');
    return (savedPlatform === 'wechat_channels') ? 'wechat_channels' : 'douyin';
  });

  // 添加二维码数据状态
  const [qrCodeData, setQrCodeData] = useState<string | null>(null);
  // 添加二维码对话框显示状态
  const [showQrCodeDialog, setShowQrCodeDialog] = useState(false);

  // 添加默认视频号平台地址常量
  const WECHAT_CHANNELS_DEFAULT_URL = "https://channels.weixin.qq.com/platform";

  // 新增：关键词回复编辑功能
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editingKeyword, setEditingKeyword] = useState('');
  const [editingReply, setEditingReply] = useState('');

  const handleEditKeywordReply = (index: number) => {
    setEditingIndex(index);
    setEditingKeyword(keywordReplies[index].keyword);
    setEditingReply(keywordReplies[index].reply);
  };
  const handleSaveEditKeywordReply = (index: number) => {
    const updated = [...keywordReplies];
    updated[index] = { keyword: editingKeyword, reply: editingReply };
    setKeywordReplies(updated);
    localStorage.setItem('keywordReplies', JSON.stringify(updated));
    setEditingIndex(null);
  };
  const handleCancelEditKeywordReply = () => {
    setEditingIndex(null);
  };

  /**
   * 清理WebSocket连接
   */
  useEffect(() => {
    return () => {
      if (wsRef.current) {
        wsRef.current.close();
        wsRef.current = null;
      }
    };
  }, []);

  /**
   * 处理标签页切换
   * @param {React.SyntheticEvent} _ - 事件对象(未使用)
   * @param {number} newValue - 新的标签页索引值
   */
  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  /**
   * 添加关键词回复
   * 将新的关键词和回复内容添加到关键词回复列表中
   */
  const addKeywordReply = () => {
    if (newKeyword.trim() && newReply.trim()) {
      // 处理多个关键词，用逗号分隔
      const keywords = newKeyword.split(',').map(k => k.trim()).filter(k => k !== '');
      
      // 为每个关键词添加相同的回复
      const newReplies = keywords.map(keyword => ({ keyword, reply: newReply }));
      
      const updatedReplies = [...keywordReplies, ...newReplies];
      setKeywordReplies(updatedReplies);
      // 保存到localStorage
      localStorage.setItem('keywordReplies', JSON.stringify(updatedReplies));
      setNewKeyword("");
      setNewReply("");
    }
  };
  
  /**
   * 删除指定索引的关键词回复
   * @param {number} index - 要删除的关键词回复的索引
   */
  const deleteKeywordReply = (index: number) => {
    const newReplies = [...keywordReplies];
    newReplies.splice(index, 1);
    setKeywordReplies(newReplies);
    // 保存到localStorage
    localStorage.setItem('keywordReplies', JSON.stringify(newReplies));
  };

  /**
   * 使用文本转语音播放回复内容
   * @param {string} text - 要播放的文本内容
   */
  const speakReply = (text: string) => {
    // 直接使用参数中的文本进行TTS转换
    // 这里需要根据新的props结构来实现
  };

  /**
   * 触发直播连接状态变化的自定义事件
   * @param {boolean} isConnected - 是否已连接
   */
  const dispatchConnectionEvent = (isConnected: boolean) => {
    // console.log('触发直播连接状态事件:', isConnected);
    const event = new CustomEvent('live-connection-change', { detail: isConnected });
    window.dispatchEvent(event);
  };

  /**
   * 触发用户进入直播间的自定义事件
   * @param {string} username - 进入直播间的用户名
   */
  const dispatchUserEnterEvent = (username: string) => {
    // console.log('触发用户进入事件:', username);
    const event = new CustomEvent('live-user-enter', { detail: username });
    window.dispatchEvent(event);
  };

  /**
   * 触发观众人数变化的自定义事件
   * @param {number} count - 观众人数
   */
  const dispatchAudienceCountEvent = (count: number) => {
    // console.log('触发观众人数变化事件:', count);
    const event = new CustomEvent('live-audience-count', { detail: count });
    window.dispatchEvent(event);
  };

  /**
   * 处理WebSocket消息事件
   * 根据不同的消息类型进行处理：评论、进入、错误、观众人数等
   * @param {MessageEvent} event - WebSocket消息事件对象
   */
  const handleWebSocketMessage = (event: MessageEvent) => {
    try {
      const data = JSON.parse(event.data);
      
      // 根据消息类型处理不同的消息
      switch (data.type) {
        case 'comment':
          addInteractionMessage('comment', data.content, data.username);
          break;
        case 'enter':
          addInteractionMessage('enter', data.content);
          // 直接通过替换"来了"获取用户名
          const username = data.content.replace(/\s*来了$/, '').trim();
          // 过滤掉表情符号
          const filteredUsername = filterEmojis(username);
          // 如果过滤后为空，使用"家人"作为默认名称
          const normalizedUsername = filteredUsername || '家人';
          // 应用昵称截断逻辑
          const finalUsername = truncateNickname(normalizedUsername);
          // 更新最近进入的用户名
          setLatestUsername(finalUsername);
          
          // 触发用户进入事件
          dispatchUserEnterEvent(finalUsername);
          break;
        case 'error':
          addInteractionMessage('error', data.content);
          break;
        case 'audience':
          // 更新在线人数 - 修改处理逻辑
          const audienceMatch = data.content.match(/当前在线人数:\s*(\d+)/);
          if (audienceMatch && audienceMatch[1]) {
            const count = parseInt(audienceMatch[1]);
            setAudienceCount(count);
            
            // console.log('收到观众人数更新:', count);
            // 触发观众人数变化事件
            dispatchAudienceCountEvent(count);
          } else {
            // console.log('无法从消息中提取观众人数:', data.content);
          }
          break;
        case 'like':
          if (filterTypes.includes('like')) {
            addInteractionMessage('like', data.content, data.username);
          }
          break;
        case 'gift':
          if (filterTypes.includes('gift')) {
            addInteractionMessage('gift', data.content, data.username);
          }
          break;
        case 'follow':
          if (filterTypes.includes('follow')) {
            addInteractionMessage('follow', data.content, data.username);
          }
          break;
        case 'qrcode':
          console.log("收到二维码数据");
          setQrCodeData(data.data); // data.data 应该就是 base64 字符串
          setShowQrCodeDialog(true); // 收到二维码数据时打开对话框
          setSystemAlert({ open: true, message: '请扫描二维码登录', type: 'info' });
          // 二维码数据收到后，通常需要等待扫码成功，不需要关闭连接
          // setIsConnecting(false);
          // setIsConnected(false);
          break;
        case 'login_success':
          console.log("收到登录成功消息");
          setSystemAlert({ open: true, message: data.content, type: 'success' });
          setShowQrCodeDialog(false); // 收到登录成功消息时关闭对话框
          // 登录成功后，可能需要进行后续操作，例如导航到直播间页面等
          // TODO: Implement navigation to live stream page or other post-login logic
          break;
        default:
          console.log('未知消息类型:', data);
      }
    } catch (error) {
      console.error('解析消息失败:', error);
    }
  };

  /**
   * 处理直播间连接或断开连接
   * 如果未连接，建立WebSocket连接
   * 如果已连接，断开连接并重置相关状态
   */
  const handleConnect = async () => {
    if (!liveUrl) {
      addInteractionMessage('error', '请输入直播间地址');
      return;
    }

    if (isConnected) {
      // 如果已连接，则断开连接
      if (wsRef.current) {
        wsRef.current.close();
        wsRef.current = null;
      }
      setIsConnected(false);
      
      // 触发连接状态变化事件
      dispatchConnectionEvent(false);
      
      addInteractionMessage('system', '已断开直播间连接');
      setInteractionMessages([]); // 清空互动消息列表
      setAudienceCount(0); // 重置在线人数
      
      // 触发观众人数变化事件
      dispatchAudienceCountEvent(0);
      
      setRepliedMessages(new Set()); // 清空已回复消息集合
      return;
    }

    setIsConnecting(true);
    addInteractionMessage('system', '正在连接直播间...');
    
    // 无论连接是否成功，都保存当前的URL
    localStorage.setItem('liveUrl', liveUrl);
    
    try {
      // 使用已生成的唯一客户端ID
      
      // 连接WebSocket服务器
      const ws = new WebSocket(`ws://localhost:8000/ws/live/${clientId}`);
      
      // 新增：mode 映射函数
      const getBackendMode = () => {
        if (monitorMode === 'highperf') return 'high_performance';
        if (monitorMode === 'normal') return 'selenium';
        return 'selenium';
      };

      // 新增：按钮 key 到后端 key 的映射表
      const FILTER_TYPE_MAP = {
        comment: 'comments',
        enter: 'enters',
        audience: 'audience',
        gift: 'gifts',
        like: 'likes',
        follow: 'follows'
      } as const;

      ws.onopen = () => {
        // 发送连接请求，包含平台信息
        const filterTypesObj: Record<string, boolean> = {};
        (Object.keys(FILTER_TYPE_MAP) as Array<keyof typeof FILTER_TYPE_MAP>).forEach(key => {
          filterTypesObj[FILTER_TYPE_MAP[key]] = filterTypes.includes(key);
        });
        ws.send(JSON.stringify({
          type: 'connect',
          url: liveUrl,
          platform: selectedPlatform,
          mode: getBackendMode(),
          filterTypes: filterTypesObj // 发送对象格式
        }));
        wsRef.current = ws;
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          
          // 根据消息类型处理不同的消息
          switch (data.type) {
            case 'system':
              addInteractionMessage('system', data.content);
              // 判断连接成功消息，兼容抖音、视频号和高性能模式
              if (
                data.content === '成功连接到直播间' ||
                data.content === '微信视频号登录成功' ||
                (typeof data.content === 'string' && data.content.startsWith('高性能监控已连接直播间'))
              ) {
                setIsConnected(true);
                dispatchConnectionEvent(true);
              }
              break;
            case 'comment':
              addInteractionMessage('comment', data.content, data.username);
              break;
            case 'enter':
              addInteractionMessage('enter', data.content);
              // 直接通过替换"来了"获取用户名
              const username = data.content.replace(/\s*来了$/, '').trim();
              // 过滤掉表情符号
              const filteredUsername = filterEmojis(username);
              // 如果过滤后为空，使用"家人"作为默认名称
              const normalizedUsername = filteredUsername || '家人';
              // 应用昵称截断逻辑
              const finalUsername = truncateNickname(normalizedUsername);
              // 更新最近进入的用户名
              setLatestUsername(finalUsername);
              
              // 触发用户进入事件
              dispatchUserEnterEvent(finalUsername);
              break;
            case 'error':
              addInteractionMessage('error', data.content);
              setIsConnected(false);
              
              // 触发连接状态变化事件
              dispatchConnectionEvent(false);
              break;
            case 'audience':
              // 更新在线人数 - 修改处理逻辑
              const audienceMatch = data.content.match(/当前在线人数:\s*(\d+)/);
              if (audienceMatch && audienceMatch[1]) {
                const count = parseInt(audienceMatch[1]);
                setAudienceCount(count);
                
                // console.log('收到观众人数更新:', count);
                // 触发观众人数变化事件
                dispatchAudienceCountEvent(count);
              } else {
                // console.log('无法从消息中提取观众人数:', data.content);
              }
              break;
            case 'like':
              if (filterTypes.includes('like')) {
                addInteractionMessage('like', data.content, data.username);
              }
              break;
            case 'gift':
              if (filterTypes.includes('gift')) {
                addInteractionMessage('gift', data.content, data.username);
              }
              break;
            case 'follow':
              if (filterTypes.includes('follow')) {
                addInteractionMessage('follow', data.content, data.username);
              }
              break;
            case 'qrcode':
              console.log("收到二维码数据");
              setQrCodeData(data.data); // data.data 应该就是 base64 字符串
              setShowQrCodeDialog(true); // 收到二维码数据时打开对话框
              setSystemAlert({ open: true, message: '请扫描二维码登录', type: 'info' });
              // 二维码数据收到后，通常需要等待扫码成功，不需要关闭连接
              // setIsConnecting(false);
              // setIsConnected(false);
              break;
            case 'login_success':
              console.log("收到登录成功消息");
              setSystemAlert({ open: true, message: data.content, type: 'success' });
              setShowQrCodeDialog(false); // 收到登录成功消息时关闭对话框
              // 收到登录成功消息，更新连接状态
              setIsConnected(true); // 标记为已连接
              dispatchConnectionEvent(true); // 触发连接事件
              // 登录成功后，可能需要进行后续操作，例如导航到直播间页面等
              // TODO: Implement navigation to live stream page or other post-login logic
              break;
            default:
              // 未知消息类型
          }
        } catch (error) {
          console.error('解析消息失败:', error);
        }
      };

      // 断开连接时重置在线人数
      ws.onclose = () => {
        addInteractionMessage('system', '连接已关闭');
        setIsConnected(false);
        
        // 触发连接状态变化事件
        dispatchConnectionEvent(false);
        
        wsRef.current = null;
        setAudienceCount(0); // 重置在线人数
        
        // 触发观众人数变化事件
        dispatchAudienceCountEvent(0);
      };

      // 连接错误时重置在线人数
      ws.onerror = (error) => {
        console.error('WebSocket错误:', error);
        addInteractionMessage('error', '连接发生错误');
        setIsConnected(false);
        
        // 触发连接状态变化事件
        dispatchConnectionEvent(false);
        
        wsRef.current = null;
        setAudienceCount(0); // 重置在线人数
        
        // 触发观众人数变化事件
        dispatchAudienceCountEvent(0);
      };

    } catch (error: unknown) {
      const errorMessage = error instanceof Error 
        ? error.message 
        : '未知错误';
      addInteractionMessage('error', `连接失败: ${errorMessage}`);
    } finally {
      setIsConnecting(false);
    }
  };

  // 在组件卸载时清理WebSocket连接
  useEffect(() => {
    return () => {
      if (ws) {
        ws.close();
      }
    };
  }, [ws]);

  /**
   * 处理消息列表滚动事件
   * 检测是否滚动到底部，控制自动滚动功能和未读消息计数
   * @param {React.UIEvent<HTMLDivElement>} e - 滚动事件对象
   */
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const element = e.currentTarget;
    const isAtBottom = Math.abs(element.scrollHeight - element.clientHeight - element.scrollTop) < 500;
    setIsAutoScrollEnabled(isAtBottom);
    if (isAtBottom) {
      setUnreadCount(0);
    }
  };

  /**
   * 将指定类型的消息列表滚动到底部
   * 根据自动滚动设置决定是否滚动，如未启用则增加未读消息计数
   * @param {('comment'|'enter')} type - 消息类型，决定滚动哪个列表
   */
  const scrollToBottom = (type: 'comment' | 'enter') => {
    const ref = type === 'comment' ? commentListRef : enterListRef;
    if (ref.current && isAutoScrollEnabled) {
      ref.current.scrollTop = ref.current.scrollHeight;
    } else if (!isAutoScrollEnabled) {
      setUnreadCount((prev: number) => prev + 1);
    }
  };

  /**
   * 添加UUID生成函数
   * @returns {string} 唯一的UUID
   */
  const generateMessageId = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  };

  /**
   * 添加互动消息到消息列表
   * 根据消息类型滚动相应列表，系统和错误消息会触发提示弹窗
   * @param {MessageType['type']} type - 消息类型：评论/进入/系统/错误
   * @param {string} content - 消息内容
   * @param {string} [username] - 可选的用户名
   */
  const addInteractionMessage = (type: MessageType['type'], content: string, username?: string) => {
    // 生成唯一消息ID
    const messageId = generateMessageId();
    
    // 检查是否已存在相同的消息（在最近5秒内）
    const recentMessages = interactionMessages.filter(msg => 
      msg.type === type && 
      msg.content === content && 
      msg.username === username &&
      Date.now() - msg.timestamp < 5000
    );
    
    if (recentMessages.length > 0) {
      return;
    }
    
    setInteractionMessages(prev => [...prev, {
      id: messageId,
      type,
      content,
      username,
      timestamp: Date.now()
    }].slice(-100));
    
    // 根据消息类型决定滚动哪个列表
    if (type === 'comment') {
      setTimeout(() => scrollToBottom('comment'), 0);
    } else if (type === 'enter') {
      setTimeout(() => scrollToBottom('enter'), 0);
    }
    
    // 如果是系统消息，显示弹窗提示
    if (type === 'system' || type === 'error') {
      setSystemAlert({
        open: true,
        message: content,
        type: type === 'system' ? 'success' : 'error'
      });
    }
  };

  /**
   * 处理系统提示弹窗关闭
   */
  const handleAlertClose = () => {
    setSystemAlert(prev => ({ ...prev, open: false }));
  };

  // 高级卡片样式
  const advancedCardStyle = {
    position: 'relative',
    overflow: 'hidden',
    backgroundColor: alpha('#1a1f36', 0.7),
    borderRadius: '12px',
    boxShadow: `0 8px 32px ${alpha('#000', 0.2)}`,
    border: `1px solid ${alpha('#2a3153', 0.5)}`,
    transition: 'transform 0.3s ease, box-shadow 0.3s ease',
    '&:hover': {
      boxShadow: `0 10px 40px ${alpha('#000', 0.3)}`,
      transform: 'translateY(-2px)'
    }
  };

  // 卡片内容样式
  const advancedCardContent = {
    p: 3,
    '&:last-child': {
      pb: 3
    }
  };

  /**
   * 测试AI模型回复
   */
  const testAIReply = async () => {
    if (!aiTestInput || !config.aiModelEnabled) return;
    try {
      setIsTestingAI(true);
      // 测试AI回复
      // 获取实时直播话术脚本内容，优先从 localStorage 获取
      const getLiveScriptContent = () => {
        const liveMode = localStorage.getItem('liveMode') || 'realtime';
        if (liveMode === 'textlive') {
          return localStorage.getItem('textliveScriptContent') || '';
        }
        return localStorage.getItem('scriptContent') || '';
      };
      // 动态生成隐藏系统提示词
      const getHiddenSystemPrompt = () => `以下【】里面的是你的话术，【${getLiveScriptContent()}】`;
      const result = await generateText(aiTestInput, getHiddenSystemPrompt() + '\n' + aiSystemPrompt, sessionConfig.useSessionApi);
      if (result) {
        // 格式化AI测试结果
        const formattedResult = applyReplyFormat('测试用户', result, aiTestInput);
        setAiTestOutput(formattedResult);
      }
    } catch (error) {
      console.error("AI测试失败:", error);
      setAiTestOutput("AI回复生成失败，请检查AI模型配置。");
    } finally {
      setIsTestingAI(false);
    }
  };

  /**
   * 保存AI设置
   */
  const saveAISettings = () => {
    // 保存AI模型启用状态
    setAiModelEnabled(config.aiModelEnabled);
    
    // 保存系统提示词到localStorage
    localStorage.setItem('aiSystemPrompt', aiSystemPrompt);
    
    // 保存优先使用关键词设置
    localStorage.setItem('prioritizeKeyword', JSON.stringify(prioritizeKeyword));
    
    // 保存用户白名单到localStorage
    localStorage.setItem('userWhitelist', JSON.stringify(userWhitelist));
    
    // 保存回复格式到localStorage
    localStorage.setItem('replyFormat', replyFormat);
    
    // 保存关键词回复设置
    localStorage.setItem('keywordReplies', JSON.stringify(keywordReplies));
    localStorage.setItem('autoReplyEnabled', JSON.stringify(autoReplyEnabled));
    
    // 保存会话管理相关设置
    updateSessionConfig({
      ...sessionConfig,
      lastSystemPrompt: aiSystemPrompt  // 更新系统提示词
    });
    
    // 显示成功提示
    setSystemAlert({
      open: true,
      message: "设置已保存",
      type: "success"
    });
    
    // 设置已保存
  };

  /**
   * 应用回复格式
   * @param {string} username - 用户名
   * @param {string} reply - 回复内容
   * @param {string} question - 原始问题
   * @returns {string} 格式化后的回复内容
   */
  const applyReplyFormat = (username: string, reply: string, question: string): string => {
    // 处理用户名，使用truncateNickname函数处理用户名
    const processedUsername = truncateNickname(username || '用户');
    
    // 应用回复格式处理
    
    return replyFormat
      .replace("{用户名}", processedUsername)
      .replace("{回复内容}", reply)
      .replace("{提问内容}", question);
  };

  /**
   * 处理直播间评论的AI回复
   * @param {string} comment - 用户评论内容
   * @param {string} [username] - 用户昵称
   * @returns {Promise<string|null>} 返回AI回复或null
   */
  const handleAIReplyToComment = async (comment: string, username?: string) => {
    // 首先检查是否启用了AI回复
    if (!aiModelEnabled || !config.aiModelEnabled) return null;
    
    // 如果优先使用关键词回复，先检查是否匹配关键词
    if (prioritizeKeyword && autoReplyEnabled) {
      const keywordMatch = keywordReplies.find(item => 
        item.keyword.split('|').some(k => comment.includes(k.trim()))
      );
      
      if (keywordMatch) {
        const replies = keywordMatch.reply.split('|').map(s => s.trim()).filter(Boolean);
        return replies.length > 0 ? replies[Math.floor(Math.random() * replies.length)] : keywordMatch.reply;
      }
    }
    
    // 没有匹配关键词或不优先使用关键词，使用AI回复
    try {
      // 拼接带用户名的 prompt
      const prompt = `用户「${username || '匿名用户'}」在直播间说: ${comment}`;
      
      // 获取实时直播话术脚本内容，优先从 localStorage 获取
      const getLiveScriptContent = () => {
        const liveMode = localStorage.getItem('liveMode') || 'realtime';
        if (liveMode === 'textlive') {
          return localStorage.getItem('textliveScriptContent') || '';
        }
        // 实时直播模式使用 realtimeScriptContent
        return localStorage.getItem('realtimeScriptContent') || '';
      };
      // 动态生成隐藏系统提示词
      const getHiddenSystemPrompt = () => `以下【】里面的是你的话术，【${getLiveScriptContent()}】`;
      
      // 调用AI生成回复，明确指定使用会话ID
      const result = await generateText(prompt, getHiddenSystemPrompt() + '\n' + aiSystemPrompt, sessionConfig.useSessionApi);
      
      return result || null;
    } catch (error) {
      console.error("AI回复生成失败:", error);
      return null;
    }
  };

  // 处理接收到的评论消息，如果启用了自动回复或AI回复，生成回复
  useEffect(() => {
    const processLatestComment = async () => {
      // 获取最新的评论
      const comments = interactionMessages.filter(msg => msg.type === 'comment');
      if (comments.length === 0) return;
      
      const latestComment = comments[comments.length - 1];
      
      // 检查是否已经处理过这条消息
      if (lastProcessedMessageRef.current?.id === latestComment.id) {
        return;
      }
      
      // 避免处理太旧的消息，只处理30秒内的消息
      if (Date.now() - latestComment.timestamp > 30000) {
        return;
      }
      
      // 检查用户是否在白名单中
      if (latestComment.username && userWhitelist.includes(latestComment.username)) {
        return;
      }
      
      // 更新最后处理的消息
      lastProcessedMessageRef.current = {
        id: latestComment.id,
        timestamp: Date.now()
      };
      
      try {
        let replyContent: string | null = null;
        
        // 如果启用了关键词自动回复且不是优先使用AI
        if (autoReplyEnabled && (!aiModelEnabled || prioritizeKeyword)) {
          const keywordMatch = keywordReplies.find(item => 
            latestComment.content.includes(item.keyword)
          );
          
          if (keywordMatch) {
            replyContent = keywordMatch.reply;
          } 
          // 如果没有匹配关键词但启用了AI回复
          else if (aiModelEnabled && config.aiModelEnabled) {
            replyContent = await handleAIReplyToComment(latestComment.content, latestComment.username);
          }
        } 
        // 如果直接启用了AI回复而没有启用关键词或不优先使用关键词
        else if (aiModelEnabled && config.aiModelEnabled) {
          replyContent = await handleAIReplyToComment(latestComment.content, latestComment.username);
        }
        
        // 如果生成了回复内容，添加系统消息并播放语音
        if (replyContent) {
          // 应用回复格式
          const formattedReply = applyReplyFormat(
            latestComment.username || '用户', 
            replyContent, 
            latestComment.content
          );
          
          // 添加系统回复消息
          addInteractionMessage('system', formattedReply);
          // 新增：插入到直播音频队列
          insertLiveReply(formattedReply, 'interactionReply');
        }
      } catch (error) {
        console.error("处理消息失败:", error);
        // 处理失败时清除最后处理的消息记录
        lastProcessedMessageRef.current = null;
      }
    };
    
    processLatestComment();
  }, [interactionMessages, autoReplyEnabled, aiModelEnabled, prioritizeKeyword, keywordReplies, config.aiModelEnabled, userWhitelist, insertLiveReply]);

  /**
   * 从分享文本中提取直播链接
   * 支持抖音、快手等平台的分享格式
   * @param {string} text - 包含链接的分享文本
   * @returns {string|null} 提取的链接或null
   */
  const extractLiveUrlFromText = (text: string): string | null => {
    // 匹配常见的URL格式
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    const matches = text.match(urlRegex);
    
    if (matches && matches.length > 0) {
      return matches[0];
    }
    
    return null;
  };

  /**
   * 处理直播链接输入变化
   * 自动提取粘贴内容中的直播链接
   * @param {React.ChangeEvent<HTMLInputElement>} e - 输入框变化事件
   */
  const handleLiveUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputText = e.target.value;
    setLiveUrl(inputText);
    
    // 如果输入内容较长，可能是分享文本，尝试提取链接
    if (inputText.length > 30) {
      const extractedUrl = extractLiveUrlFromText(inputText);
      if (extractedUrl) {
        setLiveUrl(extractedUrl);
        // 保存到localStorage
        localStorage.setItem('liveUrl', extractedUrl);
        setSystemAlert({
          open: true,
          message: "已自动提取直播链接",
          type: "success"
        });
        return;
      }
    }
    
    // 普通输入也保存到localStorage
    localStorage.setItem('liveUrl', inputText);
  };

  /**
   * 处理会话设置更改
   */
  const handleSessionConfigChange = (key: keyof typeof sessionConfig, value: any) => {
    updateSessionConfig({ [key]: value });
  };

  /**
   * 重置会话
   */
  const handleResetSession = async () => {
    setShowResetSessionConfirm(true);
  };

  /**
   * 确认重置会话
   */
  const confirmResetSession = async () => {
    const success = await resetSession();
    if (success) {
      setSystemAlert({
        open: true,
        message: "会话已重置",
        type: "success"
      });
    } else {
      setSystemAlert({
        open: true,
        message: "重置会话失败",
        type: "error"
      });
    }
    setShowResetSessionConfirm(false);
  };

  // 监听模型变化
  useEffect(() => {
    const handleModelChange = async () => {
      // 如果模型发生变化
      if (config.selectedModel !== lastSelectedModel) {
        // 如果启用了会话管理，自动重置会话
        if (sessionConfig.useSessionApi && sessionConfig.sessionId) {
          const success = await resetSession();
          if (success) {
            setSystemAlert({
              open: true,
              message: "已切换AI模型，会话已重置",
              type: "info"
            });
          }
        }
        
        // 更新上一次选择的模型
        setLastSelectedModel(config.selectedModel);
      }
    };

    handleModelChange();
  }, [config.selectedModel, lastSelectedModel, resetSession, sessionConfig.sessionId, sessionConfig.useSessionApi]);

  // 组件挂载时，触发初始事件
  useEffect(() => {
    // 触发初始状态
    dispatchConnectionEvent(isConnected);
    dispatchAudienceCountEvent(audienceCount);
  }, []);

  /**
   * 切换平台
   * @param {'douyin' | 'wechat_channels'} platform - 要切换到的平台
   */
  const switchPlatform = (platform: 'douyin' | 'wechat_channels') => {
    if (isConnected) {
      setSystemAlert({
        open: true,
        message: "请先断开当前连接再切换平台",
        type: "warning"
      });
      return;
    }
    setSelectedPlatform(platform);
    localStorage.setItem('selectedPlatform', platform);

    // 如果切换到视频号，自动填充默认地址
    if (platform === 'wechat_channels') {
      setLiveUrl(WECHAT_CHANNELS_DEFAULT_URL);
      localStorage.setItem('liveUrl', WECHAT_CHANNELS_DEFAULT_URL);
    } else if (platform === 'douyin') { // 添加对抖音平台的处理
      setLiveUrl(""); // 清空直播间地址
      localStorage.setItem('liveUrl', ""); // 清空本地存储的地址
    }

    setSystemAlert({
      open: true,
      message: `已切换到${platform === 'douyin' ? '抖音' : '视频号'}平台`,
      type: "success"
    });
  };

  // 关键词导入导出功能（txt格式，每行"关键词,回复"）
  const handleExportKeywordReplies = () => {
    const txt = keywordReplies.map(item => `${item.keyword},${item.reply}`).join('\n');
    const blob = new Blob([txt], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `关键词回复_${new Date().toLocaleDateString()}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };
  const handleImportKeywordReplies = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.txt';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (event) => {
          try {
            const lines = (event.target?.result as string).split(/\r?\n/).filter(Boolean);
            const imported = lines.map(line => {
              const idx = line.indexOf(',');
              if (idx === -1) return null;
              return { keyword: line.slice(0, idx), reply: line.slice(idx + 1) };
            }).filter(Boolean) as { keyword: string; reply: string }[];
            if (imported.length > 0) {
              // 合并并去重
              const merged = [...keywordReplies, ...imported].reduce((acc, cur) => {
                if (!acc.find((item: { keyword: string; reply: string }) => item.keyword === cur.keyword && item.reply === cur.reply)) {
                  acc.push(cur);
                }
                return acc;
              }, [] as typeof keywordReplies);
              setKeywordReplies(merged);
              localStorage.setItem('keywordReplies', JSON.stringify(merged));
              setSystemAlert({ open: true, message: '导入成功', type: 'success' });
            } else {
              setSystemAlert({ open: true, message: '导入内容为空', type: 'error' });
            }
          } catch {
            setSystemAlert({ open: true, message: '导入失败，文件格式错误', type: 'error' });
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  const [liveScriptContent, setLiveScriptContent] = useState<string>(() => localStorage.getItem('scriptContent') || '');

  useEffect(() => {
    // 监听 window 事件
    function handler(e: any) {
      if (e.detail && e.detail.type === 'textlive') {
        setTextliveScriptContent(e.detail.content);
        setLiveMode('textlive');
        // 不再写入 scriptContent，避免覆盖实时直播内容
        // localStorage.setItem('scriptContent', e.detail.content); // 删除或注释
        localStorage.setItem('textliveScriptContent', e.detail.content);
        localStorage.setItem('liveMode', 'textlive');
      }
      // 可扩展：如果有实时直播事件类型，可在此处处理
    }
    window.addEventListener('live-script-content', handler);

    // 监听 localStorage 变化（支持跨标签页/刷新）
    function storageHandler(e: StorageEvent) {
      if (e.key === 'scriptContent' && e.newValue) {
        setLiveScriptContent(e.newValue);
      }
    }
    window.addEventListener('storage', storageHandler);

    return () => {
      window.removeEventListener('live-script-content', handler);
      window.removeEventListener('storage', storageHandler);
    };
  }, []);

  const [liveMode, setLiveMode] = useState(localStorage.getItem('liveMode') || 'realtime');
  const [textliveScriptContent, setTextliveScriptContent] = useState(localStorage.getItem('textliveScriptContent') || '');
  const [realtimeScriptContent, setRealtimeScriptContent] = useState(localStorage.getItem('realtimeScriptContent') || '');

  useEffect(() => {
    function storageHandler(e: StorageEvent) {
      if (e.key === 'liveMode' && e.newValue) setLiveMode(e.newValue);
      if (e.key === 'textliveScriptContent' && e.newValue !== null) setTextliveScriptContent(e.newValue);
      if (e.key === 'realtimeScriptContent' && e.newValue !== null) setRealtimeScriptContent(e.newValue);
    }
    window.addEventListener('storage', storageHandler);
    return () => window.removeEventListener('storage', storageHandler);
  }, []);

  // 统一：只用 state 版本
  const getLiveScriptContent = () => {
    if (liveMode === 'textlive') {
      return textliveScriptContent || '';
    }
    return realtimeScriptContent || '';
  };

  // 新增：监控方式和消息类型过滤状态
  const [monitorMode, setMonitorMode] = useState<'highperf' | 'normal'>(() => localStorage.getItem('monitorMode') as 'highperf' | 'normal' || 'normal');
  // 默认评论、进入、人数是开的
  const [filterTypes, setFilterTypes] = useState<string[]>(['comment', 'enter', 'audience']);
  const MESSAGE_TYPES = [
    { key: 'comment', label: '评论' },
    { key: 'enter', label: '进入' },
    { key: 'audience', label: '人数' },
    { key: 'gift', label: '礼物' },
    { key: 'like', label: '点赞' },
    { key: 'follow', label: '关注' },
  ];

  // 消息类型映射表，必须在 useEffect 之前
  const FILTER_TYPE_MAP = {
    comment: 'comments',
    enter: 'enters',
    audience: 'audience',
    gift: 'gifts',
    like: 'likes',
    follow: 'follows'
  } as const;

  // 实时过滤：filterTypes 变化时通知后端
  useEffect(() => {
    if (isConnected && wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      const filterTypesObj: Record<string, boolean> = {};
      (Object.keys(FILTER_TYPE_MAP) as Array<keyof typeof FILTER_TYPE_MAP>).forEach(key => {
        filterTypesObj[FILTER_TYPE_MAP[key]] = filterTypes.includes(key as string);
      });
      console.log('[WS] 发送 update_filter', filterTypesObj, wsRef.current);
      wsRef.current.send(JSON.stringify({
        type: 'update_filter',
        filterTypes: filterTypesObj
      }));
    } else {
      console.log('[WS] 未发送 update_filter，isConnected:', isConnected, 'wsRef:', wsRef.current);
    }
  }, [filterTypes, isConnected]);

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 删除自动播报管理器 */}
      
      <Typography
        variant="h4"
        sx={{
          fontWeight: 600,
          mb: 3,
          background: 'linear-gradient(90deg, #4f7df9, #835df6)',
          backgroundClip: 'text',
          textFillColor: 'transparent',
          display: 'inline-block',
        }}
      >
        互动回复
      </Typography>

      <Stack spacing={2.5}>
        {/* 回复设置卡片 */}
        <Card sx={advancedCardStyle}>
          <CardContent sx={advancedCardContent}>
            <Stack direction="row" alignItems="center" spacing={1.5} mb={2}>
              <Settings sx={{ color: '#4f7df9' }} />
              <Typography variant="h6" sx={{ 
                color: '#ffffff',
                fontWeight: 600,
                letterSpacing: '0.05em',
              }}>
                回复设置
              </Typography>
            </Stack>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <FormControlLabel
                  control={
                    <Switch 
                      checked={autoReplyEnabled} 
                      onChange={() => setAutoReplyEnabled(!autoReplyEnabled)}
                      sx={{
                        '& .MuiSwitch-switchBase.Mui-checked': {
                          color: '#4f7df9',
                          '&:hover': {
                            backgroundColor: alpha('#4f7df9', 0.1),
                          },
                        },
                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                          backgroundColor: alpha('#4f7df9', 0.5),
                        },
                      }}
                    />
                  }
                  label={
                    <Typography sx={{ color: '#ffffff' }}>
                      启用关键词自动回复
                    </Typography>
                  }
                />
                <Typography sx={{ color: alpha('#ffffff', 0.7), mt: 1, fontSize: '0.875rem' }}>
                  开启后系统会自动检测直播评论中的关键词，匹配时自动回复
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControlLabel
                  control={
                    <Switch 
                      checked={aiModelEnabled} 
                      onChange={() => setAiModelEnabled(!aiModelEnabled)}
                      disabled={!config.aiModelEnabled}
                      sx={{
                        '& .MuiSwitch-switchBase.Mui-checked': {
                          color: '#835df6',
                          '&:hover': {
                            backgroundColor: alpha('#835df6', 0.1),
                          },
                        },
                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                          backgroundColor: alpha('#835df6', 0.5),
                        },
                      }}
                    />
                  }
                  label={
                    <Typography sx={{ color: '#ffffff' }}>
                      启用AI模型回复
                    </Typography>
                  }
                />
                <Typography sx={{ color: alpha('#ffffff', 0.7), mt: 1, fontSize: '0.875rem' }}>
                  开启后无匹配关键词时，系统将使用AI模型智能回复问题
                  {!config.aiModelEnabled && <span style={{ color: '#ff5a7d' }}> (请先在AI设置中配置并启用AI模型)</span>}
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* 关键词和回复管理卡片 */}
        <Card sx={advancedCardStyle}>
          <CardContent sx={advancedCardContent}>
            <Stack direction="row" alignItems="center" justifyContent="space-between" mb={2}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <ChatBubble sx={{ color: '#4f7df9', mr: 1.5 }} />
                <Typography variant="h6" sx={{ 
                  color: '#ffffff',
                  fontWeight: 600,
                  letterSpacing: '0.05em',
                }}>
                  回复管理
                </Typography>
              </Box>
              
              <Tabs
                value={currentTab}
                onChange={handleTabChange}
                sx={{
                  '& .MuiTab-root': {
                    color: alpha('#ffffff', 0.7),
                    '&.Mui-selected': {
                      color: '#4f7df9',
                    }
                  },
                  '& .MuiTabs-indicator': {
                    backgroundColor: '#4f7df9',
                  }
                }}
              >
                <Tab label="公屏连接" />
                <Tab label="关键词回复" />
                <Tab label="AI回复设置" />
                <Tab label="高级设置" />
              </Tabs>
            </Stack>
            
            {currentTab === 0 ? (
              <Box>
                {selectedPlatform === 'douyin' && (
                  <Stack direction="row" alignItems="center" spacing={2} mb={2}>
                    <Typography sx={{ color: alpha('#ffffff', 0.7), fontSize: '0.95rem', minWidth: 72 }}>监控方式:</Typography>
                    <ButtonGroup sx={{ ml: 1, mr: 4, height: 36, boxShadow: 'none' }}>
                      <Button
                        variant={monitorMode === 'highperf' ? 'contained' : 'outlined'}
                        onClick={() => setMonitorMode('highperf')}
                        sx={{
                          bgcolor: monitorMode === 'highperf' ? '#3a5cff' : 'transparent',
                          color: '#fff',
                          borderColor: '#3a5cff',
                          fontWeight: 600,
                          px: 3,
                          borderRadius: '8px 0 0 8px',
                          boxShadow: 'none',
                          '&:hover': { bgcolor: '#3a5cff', color: '#fff' }
                        }}
                      >
                        高性能
                      </Button>
                      <Button
                        variant={monitorMode === 'normal' ? 'contained' : 'outlined'}
                        onClick={() => setMonitorMode('normal')}
                        sx={{
                          bgcolor: monitorMode === 'normal' ? '#3a5cff' : 'transparent',
                          color: '#fff',
                          borderColor: '#3a5cff',
                          fontWeight: 600,
                          px: 3,
                          borderRadius: '0 8px 8px 0',
                          boxShadow: 'none',
                          '&:hover': { bgcolor: '#3a5cff', color: '#fff' }
                        }}
                      >
                        兼容模式
                      </Button>
                    </ButtonGroup>
                    {monitorMode === 'highperf' && (
                      <>
                        <Typography sx={{ color: alpha('#ffffff', 0.7), fontSize: '0.95rem', minWidth: 96 }}>消息接受开关:</Typography>
                        <Stack direction="row" spacing={2} ml={1}>
                          {MESSAGE_TYPES.map(type => (
                            <Chip
                              key={type.key}
                              label={
                                <span style={{ display: 'flex', alignItems: 'center', fontWeight: 700, fontSize: 16 }}>
                                  {type.key === 'comment' ? '🗨️' : type.key === 'enter' ? '🟢' : type.key === 'audience' ? '👥' : type.key === 'gift' ? '🎁' : type.key === 'like' ? '👍' : type.key === 'follow' ? '❤️' : null}
                                  <span style={{ marginLeft: 4 }}>{type.label}</span>
                                </span>
                              }
                              color={filterTypes.includes(type.key) ? 'success' : 'default'}
                              variant={filterTypes.includes(type.key) ? 'filled' : 'outlined'}
                              onClick={() => setFilterTypes(
                                filterTypes.includes(type.key)
                                  ? filterTypes.filter(t => t !== type.key)
                                  : [...filterTypes, type.key]
                              )}
                              sx={{
                                cursor: 'pointer',
                                fontSize: 16,
                                fontWeight: 700,
                                borderRadius: '18px',
                                px: 2.5,
                                height: 36,
                                bgcolor: filterTypes.includes(type.key) ? '#43d16c' : '#232a3a',
                                color: filterTypes.includes(type.key) ? '#fff' : '#bfc8e2',
                                border: 'none',
                                boxShadow: 'none',
                              }}
                            />
                          ))}
                        </Stack>
                      </>
                    )}
                  </Stack>
                )}
                <Paper
                  sx={{
                    backgroundColor: alpha('#1f2747', 0.5),
                    borderRadius: '10px',
                    p: 2,
                    mb: 3,
                    border: `1px solid ${alpha('#ffffff', 0.1)}`
                  }}
                >
                  <Stack direction="row" alignItems="center" spacing={1.5} mb={2}>
                    <AutoAwesome sx={{ color: '#835df6' }} />
                    <Typography variant="h6" sx={{ 
                      color: '#ffffff',
                      fontWeight: 500 
                    }}>
                      公屏连接
                    </Typography>
                    {/* 平台选择 */}
                    <Stack direction="row" spacing={1} alignItems="center">
                      <Typography sx={{ color: alpha('#ffffff', 0.7), fontSize: '0.875rem' }}>
                        平台:
                      </Typography>
                      <Typography
                        sx={{
                          color: selectedPlatform === 'douyin' ? '#4f7df9' : alpha('#ffffff', 0.5),
                          fontSize: '0.875rem',
                          fontWeight: selectedPlatform === 'douyin' ? 600 : 400,
                          cursor: isConnected ? 'not-allowed' : 'pointer',
                          textDecoration: selectedPlatform === 'douyin' ? 'underline' : 'none',
                          '&:hover': {
                            color: isConnected ? alpha('#ffffff', 0.5) : '#4f7df9',
                            textDecoration: isConnected ? 'none' : 'underline',
                          }
                        }}
                        onClick={() => switchPlatform('douyin')}
                      >
                        抖音
                      </Typography>
                      <Typography sx={{ color: alpha('#ffffff', 0.3), fontSize: '0.875rem' }}>
                        |
                      </Typography>
                      <Typography
                        sx={{
                          color: selectedPlatform === 'wechat_channels' ? '#4f7df9' : alpha('#ffffff', 0.5),
                          fontSize: '0.875rem',
                          fontWeight: selectedPlatform === 'wechat_channels' ? 600 : 400,
                          cursor: isConnected ? 'not-allowed' : 'pointer',
                          textDecoration: selectedPlatform === 'wechat_channels' ? 'underline' : 'none',
                          '&:hover': {
                            color: isConnected ? alpha('#ffffff', 0.5) : '#4f7df9',
                            textDecoration: isConnected ? 'none' : 'underline',
                          }
                        }}
                        onClick={() => switchPlatform('wechat_channels')}
                      >
                        视频号
                      </Typography>
                    </Stack>
                  </Stack>
                  
                  <Stack direction="row" spacing={2} alignItems="center">
                    <TextField
                      fullWidth
                      value={liveUrl}
                      onChange={handleLiveUrlChange}
                      placeholder="输入直播间地址或粘贴分享文本"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          bgcolor: alpha('#1a1f36', 0.7),
                          borderRadius: '8px',
                          '& fieldset': {
                            borderColor: alpha('#ffffff', 0.1),
                          },
                          '&:hover fieldset': {
                            borderColor: alpha('#835df6', 0.5),
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: '#835df6',
                          },
                        },
                        '& .MuiOutlinedInput-input': {
                          color: '#ffffff',
                        },
                      }}
                    />
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Button
                        variant="contained"
                        onClick={handleConnect}
                        disabled={isConnecting}
                        sx={{
                          bgcolor: isConnected ? '#ff4d4f' : '#835df6',
                          '&:hover': {
                            bgcolor: isConnected ? alpha('#ff4d4f', 0.8) : alpha('#835df6', 0.8),
                          },
                          minWidth: '120px',
                        }}
                      >
                        {isConnected ? '断开' : '连接'}
                      </Button>
                      <Box
                        sx={{
                          width: 12,
                          height: 12,
                          borderRadius: '50%',
                          bgcolor: isConnected ? '#52c41a' : '#ff4d4f',
                          boxShadow: `0 0 8px ${isConnected ? '#52c41a' : '#ff4d4f'}`,
                          transition: 'all 0.3s'
                        }}
                      />
                    </Box>
                  </Stack>
                </Paper>

                <Paper
                  sx={{
                    backgroundColor: alpha('#1f2747', 0.5),
                    borderRadius: '10px',
                    p: 2,
                    border: `1px solid ${alpha('#ffffff', 0.1)}`
                  }}
                >
                  <Stack direction="row" alignItems="center" justifyContent="space-between" mb={2}>
                    <Stack direction="row" alignItems="center" spacing={1.5}>
                      <Psychology sx={{ color: '#835df6' }} />
                      <Typography variant="h6" sx={{ 
                        color: '#ffffff',
                        fontWeight: 500 
                      }}>
                        实时互动状态
                      </Typography>
                    </Stack>
                    <Stack direction="row" alignItems="center" spacing={2}>
                      {isConnected && (
                        <Box sx={{ 
                          display: 'flex', 
                          alignItems: 'center',
                          px: 2,
                          py: 0.5,
                          borderRadius: '16px',
                          bgcolor: alpha('#1a1f36', 0.7),
                          border: `1px solid ${alpha('#835df6', 0.3)}`,
                          minWidth: '100px',
                          justifyContent: 'center'
                        }}>
                          <Person sx={{ color: '#835df6', fontSize: '1rem', mr: 1 }} />
                          <Typography sx={{ 
                            color: '#ffffff',
                            fontSize: '0.875rem',
                            fontWeight: 500
                          }}>
                            {audienceCount.toLocaleString()} 人在线
                          </Typography>
                        </Box>
                      )}
                      {isConnected && (
                        <Chip 
                          label="监控中"
                          color="success"
                          size="small"
                          sx={{ bgcolor: alpha('#4caf50', 0.2) }}
                        />
                      )}
                    </Stack>
                  </Stack>
                  
                  <Grid container spacing={2}>
                    {/* 评论区域 */}
                    <Grid item xs={12} md={8}>
                      <Box
                        sx={{
                          height: '400px',
                          bgcolor: alpha('#151c32', 0.7),
                          borderRadius: '8px',
                          p: 2,
                          overflowY: 'auto',
                          display: 'flex',
                          flexDirection: 'column'
                        }}
                      >
                        <Stack direction="row" alignItems="center" spacing={1} mb={2}>
                          <CommentIcon sx={{ color: '#4f7df9' }} />
                          <Typography sx={{ color: '#ffffff', fontWeight: 500 }}>
                            实时评论
                          </Typography>
                        </Stack>
                        
                        <Stack 
                          spacing={1} 
                          sx={{ 
                            flex: 1,
                            display: 'flex',
                            flexDirection: 'column',
                            overflowY: 'auto',
                            '&::-webkit-scrollbar': {
                              width: '8px',
                            },
                            '&::-webkit-scrollbar-track': {
                              background: 'transparent',
                            },
                            '&::-webkit-scrollbar-thumb': {
                              background: alpha('#ffffff', 0.2),
                              borderRadius: '4px',
                            },
                            '&::-webkit-scrollbar-thumb:hover': {
                              background: alpha('#ffffff', 0.3),
                            }
                          }}
                          ref={commentListRef}
                          onScroll={handleScroll}
                        >
                          {interactionMessages
                            .filter(msg => ['comment', 'gift', 'follow'].includes(msg.type))
                            .map((msg, index) => (
                              <Box
                                key={msg.timestamp + index}
                                sx={{
                                  p: 1,
                                  borderRadius: '4px',
                                  bgcolor: msg.type === 'gift' ? alpha('#ff9800', 0.15) : msg.type === 'follow' ? alpha('#ff5a7d', 0.15) : alpha('#1a1f36', 0.5),
                                }}
                              >
                                <Stack direction="row" alignItems="center" spacing={1}>
                                  {msg.type === 'comment' && (
                                    <Typography sx={{ color: '#4f7df9', fontSize: '0.875rem', fontWeight: 500 }}>
                                      {msg.username || '用户'}:
                                    </Typography>
                                  )}
                                  {msg.type === 'comment' && (
                                    <Typography sx={{ color: '#ffffff', fontSize: '0.875rem' }}>
                                      {msg.content}
                                    </Typography>
                                  )}
                                  {msg.type === 'gift' && (
                                    <Typography sx={{ color: '#ff9800', fontSize: '0.875rem', fontWeight: 500 }}>
                                      🎁 {msg.content}
                                    </Typography>
                                  )}
                                  {msg.type === 'follow' && (
                                    <Typography sx={{ color: '#ff5a7d', fontSize: '0.875rem', fontWeight: 500 }}>
                                      ❤️ {msg.content}
                                    </Typography>
                                  )}
                                </Stack>
                              </Box>
                          ))}
                        </Stack>
                      </Box>
                    </Grid>

                    {/* 进入提醒区域 */}
                    <Grid item xs={12} md={4}>
                      <Box
                        sx={{
                          height: '400px',
                          bgcolor: alpha('#151c32', 0.7),
                          borderRadius: '8px',
                          p: 2,
                          overflowY: 'auto',
                          display: 'flex',
                          flexDirection: 'column'
                        }}
                      >
                        <Stack direction="row" alignItems="center" spacing={1} mb={2}>
                          <Person sx={{ color: '#835df6' }} />
                          <Typography sx={{ color: '#ffffff', fontWeight: 500 }}>
                            进入提醒
                          </Typography>
                        </Stack>

                        <Stack 
                          spacing={1}
                          sx={{ 
                            flex: 1,
                            display: 'flex',
                            flexDirection: 'column',
                            overflowY: 'auto',
                            '&::-webkit-scrollbar': {
                              width: '8px',
                            },
                            '&::-webkit-scrollbar-track': {
                              background: 'transparent',
                            },
                            '&::-webkit-scrollbar-thumb': {
                              background: alpha('#ffffff', 0.2),
                              borderRadius: '4px',
                            },
                            '&::-webkit-scrollbar-thumb:hover': {
                              background: alpha('#ffffff', 0.3),
                            }
                          }}
                          ref={enterListRef}
                          onScroll={handleScroll}
                        >
                          {interactionMessages
                            .filter(msg => ['enter', 'like'].includes(msg.type))
                            .map((msg, index) => (
                              <Box
                                key={msg.timestamp + index}
                                sx={{
                                  p: 1,
                                  borderRadius: '4px',
                                  bgcolor: msg.type === 'like' ? alpha('#ffe082', 0.15) : alpha('#835df6', 0.1),
                                }}
                              >
                                <Typography sx={{ color: msg.type === 'like' ? '#ffb300' : '#835df6', fontSize: '0.875rem' }}>
                                  {msg.type === 'like' ? '👍 ' : ''}{msg.content}
                                </Typography>
                              </Box>
                          ))}
                        </Stack>
                      </Box>
                    </Grid>
                  </Grid>
                </Paper>

                {/* 系统消息弹窗 */}
                <Snackbar
                  open={systemAlert.open}
                  autoHideDuration={3000}
                  onClose={handleAlertClose}
                  anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
                >
                  <Alert
                    onClose={handleAlertClose}
                    severity={systemAlert.type}
                    sx={{
                      bgcolor: systemAlert.type === 'success' ? alpha('#4caf50', 0.9) :
                              systemAlert.type === 'error' ? alpha('#ff5a7d', 0.9) :
                              systemAlert.type === 'warning' ? alpha('#ff9800', 0.9) :
                              alpha('#2196f3', 0.9),
                      color: '#ffffff',
                      '& .MuiAlert-icon': {
                        color: '#ffffff'
                      }
                    }}
                  >
                    {systemAlert.message}
                  </Alert>
                </Snackbar>
              </Box>
            ) : currentTab === 1 ? (
              <Box>
                {/* 在"添加新的关键词回复"输入区域上方添加导入导出按钮 */}
                <Stack direction="row" spacing={1} mb={2}>
                  <Button variant="outlined" size="small" onClick={handleExportKeywordReplies} sx={{ color: '#4f7df9', borderColor: '#4f7df9' }}>导出关键词</Button>
                  <Button variant="outlined" size="small" onClick={handleImportKeywordReplies} sx={{ color: '#835df6', borderColor: '#835df6' }}>导入关键词</Button>
                </Stack>
                
                <Paper
                  sx={{
                    backgroundColor: alpha('#1f2747', 0.5),
                    borderRadius: '10px',
                    p: 2,
                    mb: 3,
                    border: `1px solid ${alpha('#ffffff', 0.1)}`
                  }}
                >
                  <Typography sx={{ color: '#ffffff', mb: 2 }}>
                    添加新的关键词回复
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={5}>
                      <TextField
                        fullWidth
                        placeholder="关键词（多个用|分隔）"
                        value={newKeyword}
                        onChange={(e) => setNewKeyword(e.target.value)}
                        size="small"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            bgcolor: alpha('#1a1f36', 0.7),
                            borderRadius: '8px',
                            '& fieldset': {
                              borderColor: alpha('#ffffff', 0.1),
                            },
                            '&:hover fieldset': {
                              borderColor: alpha('#4f7df9', 0.3),
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: '#4f7df9',
                            },
                            '& input': {
                              color: '#ffffff',
                            }
                          }
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} md={5}>
                      <TextField
                        fullWidth
                        placeholder="回复内容（多个用|分隔）"
                        value={newReply}
                        onChange={(e) => setNewReply(e.target.value)}
                        size="small"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            bgcolor: alpha('#1a1f36', 0.7),
                            borderRadius: '8px',
                            '& fieldset': {
                              borderColor: alpha('#ffffff', 0.1),
                            },
                            '&:hover fieldset': {
                              borderColor: alpha('#4f7df9', 0.3),
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: '#4f7df9',
                            },
                            '& input': {
                              color: '#ffffff',
                            }
                          }
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} md={2}>
                      <Button
                        fullWidth
                        variant="contained"
                        startIcon={<AddCircle />}
                        onClick={addKeywordReply}
                        sx={{
                          bgcolor: '#4f7df9',
                          '&:hover': {
                            bgcolor: alpha('#4f7df9', 0.8),
                          },
                          height: '100%'
                        }}
                      >
                        添加
                      </Button>
                    </Grid>
                  </Grid>
                </Paper>
                
                <List
                  sx={{
                    bgcolor: alpha('#1f2747', 0.3),
                    borderRadius: '10px',
                    maxHeight: '350px',
                    overflow: 'auto'
                  }}
                >
                  {keywordReplies.map((item, index) => (
                    <React.Fragment key={index}>
                      {index === editingIndex ? (
                        <Box>
                          <TextField
                            fullWidth
                            placeholder="关键词"
                            value={editingKeyword}
                            onChange={(e) => setEditingKeyword(e.target.value)}
                            size="small"
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                bgcolor: alpha('#1a1f36', 0.7),
                                borderRadius: '8px',
                                '& fieldset': {
                                  borderColor: alpha('#ffffff', 0.1),
                                },
                                '&:hover fieldset': {
                                  borderColor: alpha('#4f7df9', 0.3),
                                },
                                '&.Mui-focused fieldset': {
                                  borderColor: '#4f7df9',
                                },
                                '& input': {
                                  color: '#ffffff',
                                }
                              }
                            }}
                          />
                          <TextField
                            fullWidth
                            placeholder="回复内容"
                            value={editingReply}
                            onChange={(e) => setEditingReply(e.target.value)}
                            size="small"
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                bgcolor: alpha('#1a1f36', 0.7),
                                borderRadius: '8px',
                                '& fieldset': {
                                  borderColor: alpha('#ffffff', 0.1),
                                },
                                '&:hover fieldset': {
                                  borderColor: alpha('#4f7df9', 0.3),
                                },
                                '&.Mui-focused fieldset': {
                                  borderColor: '#4f7df9',
                                },
                                '& input': {
                                  color: '#ffffff',
                                }
                              }
                            }}
                          />
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                            <Button
                              variant="contained"
                              color="success"
                              onClick={() => handleSaveEditKeywordReply(index)}
                              sx={{
                                bgcolor: '#4caf50',
                                '&:hover': {
                                  bgcolor: alpha('#4caf50', 0.8),
                                }
                              }}
                            >
                              保存
                            </Button>
                            <Button
                              variant="contained"
                              color="error"
                              onClick={handleCancelEditKeywordReply}
                              sx={{
                                bgcolor: '#ff5a7d',
                                '&:hover': {
                                  bgcolor: alpha('#ff5a7d', 0.8),
                                }
                              }}
                            >
                              取消
                            </Button>
                          </Box>
                        </Box>
                      ) : (
                        <ListItem
                          secondaryAction={
                            <Box>
                              <Tooltip title="编辑回复">
                                <IconButton edge="end" sx={{ color: '#ff5a7d' }} onClick={() => handleEditKeywordReply(index)}>
                                  <Edit />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="删除回复">
                                <IconButton edge="end" sx={{ color: '#ff5a7d' }} onClick={() => deleteKeywordReply(index)}>
                                  <Delete />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          }
                        >
                          <ListItemText
                            primary={
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <Chip
                                  label={item.keyword}
                                  size="small"
                                  sx={{
                                    mr: 2,
                                    bgcolor: alpha('#4f7df9', 0.2),
                                    color: '#4f7df9',
                                    border: `1px solid ${alpha('#4f7df9', 0.5)}`,
                                  }}
                                />
                                <Typography sx={{ color: '#ffffff' }}>
                                  {item.reply}
                                </Typography>
                              </Box>
                            }
                          />
                        </ListItem>
                      )}
                      {index < keywordReplies.length - 1 && (
                        <Divider sx={{ bgcolor: alpha('#ffffff', 0.1) }} />
                      )}
                    </React.Fragment>
                  ))}
                </List>
              </Box>
            ) : currentTab === 2 ? (
              <Box>
                <Paper
                  sx={{
                    backgroundColor: alpha('#1f2747', 0.5),
                    borderRadius: '10px',
                    p: 3,
                    mb: 2,
                    border: `1px solid ${alpha('#ffffff', 0.1)}`
                  }}
                >
                  <Stack direction="row" alignItems="center" spacing={2} mb={2}>
                    <AutoAwesome sx={{ color: '#835df6' }} />
                    <Typography sx={{ color: '#ffffff', fontWeight: 500 }}>
                      AI模型回复设置
                    </Typography>
                  </Stack>
                  
                  <Typography sx={{ color: alpha('#ffffff', 0.8), mb: 2 }}>
                    设置AI模型回复的行为和风格，让AI助手更符合您的品牌调性
                  </Typography>
                  
                  <Box sx={{ mb: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <FormControlLabel
                      control={
                        <Switch 
                          checked={prioritizeKeyword} 
                          onChange={() => setPrioritizeKeyword(!prioritizeKeyword)}
                          sx={{
                            '& .MuiSwitch-switchBase.Mui-checked': {
                              color: '#835df6',
                            },
                            '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                              backgroundColor: alpha('#835df6', 0.5),
                            },
                          }}
                        />
                      }
                      label={
                        <Typography sx={{ color: '#ffffff' }}>
                          优先使用关键词回复
                        </Typography>
                      }
                    />
                    
                    <Typography sx={{ color: alpha('#ffffff', 0.7) }}>
                      当前模型: {config.modelNicknamesMap[config.selectedModel] || config.selectedModel || "未设置"}
                      {config.aiModelEnabled ? 
                        <Chip 
                          label="已启用" 
                          size="small" 
                          sx={{ ml: 1, bgcolor: alpha('#4caf50', 0.2), color: '#4caf50' }} 
                        /> : 
                        <Chip 
                          label="已禁用" 
                          size="small" 
                          sx={{ ml: 1, bgcolor: alpha('#ff5a7d', 0.2), color: '#ff5a7d' }} 
                        />
                      }
                    </Typography>
                  </Box>
                  
                  <TextField
                    fullWidth
                    multiline
                    rows={4}
                    value={aiSystemPrompt}
                    onChange={(e) => setAiSystemPrompt(e.target.value)}
                    placeholder="设置AI回复的角色提示词，例如：你是一个专业的美妆顾问，擅长解答肌肤问题和化妆技巧..."
                    sx={{
                      mt: 2,
                      '& .MuiOutlinedInput-root': {
                        bgcolor: alpha('#1a1f36', 0.7),
                        borderRadius: '8px',
                        '& fieldset': {
                          borderColor: alpha('#ffffff', 0.1),
                        },
                        '&:hover fieldset': {
                          borderColor: alpha('#835df6', 0.3),
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#835df6',
                        },
                        '& textarea': {
                          color: '#ffffff',
                        }
                      }
                    }}
                  />
                  
                  {/* 添加会话管理选项 */}
                  <Box sx={{ mt: 3, p: 2, bgcolor: alpha('#1a1f36', 0.5), borderRadius: '8px' }}>
                    <Stack direction="row" alignItems="center" spacing={2} mb={2}>
                      <Psychology sx={{ color: '#835df6' }} />
                      <Typography sx={{ color: '#ffffff', fontWeight: 500 }}>
                        会话管理选项
                      </Typography>
                    </Stack>
                    
                    <FormControlLabel
                      control={
                        <Switch 
                          checked={sessionConfig.useSessionApi} 
                          onChange={() => handleSessionConfigChange('useSessionApi', !sessionConfig.useSessionApi)}
                          sx={{
                            '& .MuiSwitch-switchBase.Mui-checked': {
                              color: '#835df6',
                            },
                            '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                              backgroundColor: alpha('#835df6', 0.5),
                            },
                          }}
                        />
                      }
                      label={
                        <Typography sx={{ color: '#ffffff' }}>
                          启用会话管理（减少Token消耗，保持对话连贯性）
                        </Typography>
                      }
                    />
                    
                    {sessionConfig.useSessionApi && (
                      <>
                        <Stack direction="row" spacing={2} alignItems="center" mt={2}>
                          <Typography sx={{ color: alpha('#ffffff', 0.8), minWidth: 160 }}>
                            最大消息数量:
                          </Typography>
                          <TextField
                            type="number"
                            size="small"
                            value={sessionConfig.maxMessages}
                            onChange={(e) => handleSessionConfigChange('maxMessages', parseInt(e.target.value) || 20)}
                            inputProps={{ min: 5, max: 50 }}
                            sx={{
                              maxWidth: 120,
                              '& .MuiOutlinedInput-root': {
                                bgcolor: alpha('#1a1f36', 0.7),
                                '& fieldset': {
                                  borderColor: alpha('#ffffff', 0.1),
                                },
                                '& input': {
                                  color: '#ffffff',
                                }
                              }
                            }}
                          />
                          <Typography sx={{ color: alpha('#ffffff', 0.5), fontSize: '0.875rem' }}>
                            (达到上限后自动重置会话)
                          </Typography>
                        </Stack>
                        
                        <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <Stack direction="row" spacing={1} alignItems="center">
                            <Typography sx={{ color: alpha('#ffffff', 0.8) }}>
                              当前会话状态:
                            </Typography>
                            {sessionConfig.sessionId ? (
                              <Chip
                                label={`活跃 (${sessionConfig.messageCount}/${sessionConfig.maxMessages}条消息)`}
                                size="small"
                                color="success"
                                sx={{ bgcolor: alpha('#4caf50', 0.2) }}
                              />
                            ) : (
                              <Chip
                                label="未创建"
                                size="small"
                                color="default"
                                sx={{ bgcolor: alpha('#9e9e9e', 0.2) }}
                              />
                            )}
                          </Stack>
                          
                          <Button
                            size="small"
                            variant="outlined"
                            onClick={handleResetSession}
                            disabled={!sessionConfig.sessionId}
                            sx={{
                              borderColor: alpha('#ff5a7d', 0.5),
                              color: '#ff5a7d',
                              '&:hover': {
                                borderColor: '#ff5a7d',
                                bgcolor: alpha('#ff5a7d', 0.1),
                              },
                              '&.Mui-disabled': {
                                borderColor: alpha('#ff5a7d', 0.2),
                                color: alpha('#ff5a7d', 0.3),
                              }
                            }}
                          >
                            重置会话
                          </Button>
                        </Box>
                        
                        <Typography sx={{ color: alpha('#ffffff', 0.6), mt: 2, fontSize: '0.875rem' }}>
                          会话管理会自动记住对话上下文，减少重复发送系统提示词，提高AI回复的连贯性
                        </Typography>
                      </>
                    )}
                  </Box>
                  
                  <Button
                    variant="contained"
                    startIcon={<Save />}
                    onClick={saveAISettings}
                    sx={{
                      mt: 2,
                      bgcolor: '#835df6',
                      '&:hover': {
                        bgcolor: alpha('#835df6', 0.8),
                      }
                    }}
                  >
                    保存设置
                  </Button>
                  
                  {aiError && (
                    <Typography sx={{ color: '#ff5a7d', mt: 2, fontSize: '0.875rem' }}>
                      AI模型错误: {aiError}
                    </Typography>
                  )}
                </Paper>
                
                <Paper
                  sx={{
                    backgroundColor: alpha('#1f2747', 0.5),
                    borderRadius: '10px',
                    p: 3,
                    border: `1px solid ${alpha('#ffffff', 0.1)}`
                  }}
                >
                  <Stack direction="row" alignItems="center" spacing={2} mb={2}>
                    <Psychology sx={{ color: '#835df6' }} />
                    <Typography sx={{ color: '#ffffff', fontWeight: 500 }}>
                      AI回复测试
                    </Typography>
                  </Stack>
                  
                  <TextField
                    fullWidth
                    value={aiTestInput}
                    onChange={(e) => setAiTestInput(e.target.value)}
                    placeholder="输入测试问题，验证AI回复内容..."
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        bgcolor: alpha('#1a1f36', 0.7),
                        borderRadius: '8px',
                        '& fieldset': {
                          borderColor: alpha('#ffffff', 0.1),
                        },
                        '&:hover fieldset': {
                          borderColor: alpha('#835df6', 0.3),
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#835df6',
                        },
                        '& input': {
                          color: '#ffffff',
                        }
                      }
                    }}
                  />
                  
                  <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Button
                      variant="contained"
                      startIcon={isTestingAI ? <CircularProgress size={20} color="inherit" /> : <Send />}
                      onClick={testAIReply}
                      disabled={isTestingAI || !config.aiModelEnabled || !aiTestInput}
                      sx={{
                        bgcolor: '#835df6',
                        '&:hover': {
                          bgcolor: alpha('#835df6', 0.8),
                        }
                      }}
                    >
                      {isTestingAI ? '生成中...' : '测试回复'}
                    </Button>
                    
                    {!config.aiModelEnabled && (
                      <Typography sx={{ color: '#ff5a7d', fontSize: '0.875rem' }}>
                        请先在AI设置中启用AI模型
                      </Typography>
                    )}
                  </Box>
                  
                  {aiTestOutput && (
                    <Paper
                      sx={{
                        mt: 2,
                        p: 2,
                        bgcolor: alpha('#1a1f36', 0.7),
                        border: `1px solid ${alpha('#835df6', 0.3)}`,
                        borderRadius: '8px'
                      }}
                    >
                      <Stack direction="row" alignItems="flex-start" spacing={1}>
                        <AutoAwesome sx={{ color: '#835df6', mt: 0.5 }} />
                        <Typography sx={{ color: '#ffffff' }}>
                          {aiTestOutput}
                        </Typography>
                      </Stack>
                    </Paper>
                  )}
                </Paper>
              </Box>
            ) : currentTab === 3 ? (
              <Box>
                {/* 回复格式设置 */}
                <Paper
                  sx={{
                    backgroundColor: alpha('#1f2747', 0.5),
                    borderRadius: '10px',
                    p: 3,
                    border: `1px solid ${alpha('#ffffff', 0.1)}`
                  }}
                >
                  <Stack direction="row" alignItems="center" spacing={2} mb={2}>
                    <ChatBubble sx={{ color: '#835df6' }} />
                    <Typography sx={{ color: '#ffffff', fontWeight: 500 }}>
                      回复格式设置
                    </Typography>
                  </Stack>
                  
                  <Typography sx={{ color: alpha('#ffffff', 0.8), mb: 2 }}>
                    设置回复的输出格式，可以使用以下占位符：{'{用户名}'}、{'{回复内容}'}、{'{提问内容}'}
                  </Typography>
                  
                  <TextField
                    fullWidth
                    value={replyFormat}
                    onChange={(e) => setReplyFormat(e.target.value)}
                    placeholder="例如：@{用户名} {回复内容}"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        bgcolor: alpha('#1a1f36', 0.7),
                        borderRadius: '8px',
                        '& fieldset': {
                          borderColor: alpha('#ffffff', 0.1),
                        },
                        '&:hover fieldset': {
                          borderColor: alpha('#835df6', 0.3),
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#835df6',
                        },
                        '& input': {
                          color: '#ffffff',
                        }
                      }
                    }}
                  />
                  
                  <Box sx={{ mt: 2 }}>
                    <Typography sx={{ color: alpha('#ffffff', 0.7), mb: 1 }}>
                      预览：
                    </Typography>
                    <Paper
                      sx={{
                        p: 2,
                        bgcolor: alpha('#1a1f36', 0.7),
                        border: `1px solid ${alpha('#835df6', 0.3)}`,
                        borderRadius: '8px'
                      }}
                    >
                      <Typography sx={{ color: '#ffffff' }}>
                        {applyReplyFormat('示例用户', '这是AI回复的内容', '这是用户提问的内容')}
                      </Typography>
                    </Paper>
                  </Box>
                  
                  <Button
                    variant="contained"
                    startIcon={<Save />}
                    onClick={saveAISettings}
                    sx={{
                      mt: 3,
                      bgcolor: '#835df6',
                      '&:hover': {
                        bgcolor: alpha('#835df6', 0.8),
                      }
                    }}
                  >
                    保存设置
                  </Button>
                </Paper>
                
                <Paper
                  sx={{
                    backgroundColor: alpha('#1f2747', 0.5),
                    borderRadius: '10px',
                    p: 3,
                    mt: 2,
                    border: `1px solid ${alpha('#ffffff', 0.1)}`
                  }}
                >
                  <Stack direction="row" alignItems="center" spacing={2} mb={2}>
                    <Person sx={{ color: '#835df6' }} />
                    <Typography sx={{ color: '#ffffff', fontWeight: 500 }}>
                      用户白名单设置
                    </Typography>
                  </Stack>
                  
                  <Typography sx={{ color: alpha('#ffffff', 0.8), mb: 2 }}>
                    添加用户到白名单后，系统将不会回复这些用户的消息。这对于过滤主播自己、运营人员或机器人账号非常有用。
                  </Typography>
                  
                  <Stack direction="row" spacing={2} mb={3}>
                    <TextField
                      fullWidth
                      value={newWhitelistUser}
                      onChange={(e) => setNewWhitelistUser(e.target.value)}
                      placeholder="输入用户名..."
                      size="small"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          bgcolor: alpha('#1a1f36', 0.7),
                          borderRadius: '8px',
                          '& fieldset': {
                            borderColor: alpha('#ffffff', 0.1),
                          },
                          '&:hover fieldset': {
                            borderColor: alpha('#835df6', 0.3),
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: '#835df6',
                          },
                          '& input': {
                            color: '#ffffff',
                          }
                        }
                      }}
                    />
                    <Button
                      variant="contained"
                      startIcon={<AddCircle />}
                      onClick={() => {
                        if (newWhitelistUser.trim() && !userWhitelist.includes(newWhitelistUser.trim())) {
                          setUserWhitelist([...userWhitelist, newWhitelistUser.trim()]);
                          setNewWhitelistUser("");
                        }
                      }}
                      sx={{
                        bgcolor: '#835df6',
                        '&:hover': {
                          bgcolor: alpha('#835df6', 0.8),
                        }
                      }}
                    >
                      添加
                    </Button>
                  </Stack>
                  
                  <Typography sx={{ color: '#ffffff', mb: 1, fontWeight: 500 }}>
                    当前白名单
                  </Typography>
                  
                  <Paper
                    sx={{
                      bgcolor: alpha('#1a1f36', 0.7),
                      borderRadius: '8px',
                      p: 2,
                      maxHeight: '250px',
                      overflow: 'auto'
                    }}
                  >
                    {userWhitelist.length > 0 ? (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap' }}>
                        {userWhitelist.map((username, index) => (
                          <Chip 
                            key={index}
                            label={username}
                            onDelete={() => {
                              const newList = [...userWhitelist];
                              newList.splice(index, 1);
                              setUserWhitelist(newList);
                            }}
                            sx={{
                              m: 0.5,
                              bgcolor: alpha('#835df6', 0.2),
                              color: '#ffffff',
                              '& .MuiChip-deleteIcon': {
                                color: alpha('#ffffff', 0.7),
                                '&:hover': {
                                  color: '#ff5a7d',
                                }
                              }
                            }}
                          />
                        ))}
                      </Box>
                    ) : (
                      <Typography sx={{ color: alpha('#ffffff', 0.5), textAlign: 'center', py: 2 }}>
                        白名单为空，将回复所有用户的消息
                      </Typography>
                    )}
                  </Paper>
                  
                  <Button
                    variant="contained"
                    startIcon={<Save />}
                    onClick={saveAISettings}
                    sx={{
                      mt: 3,
                      bgcolor: '#835df6',
                      '&:hover': {
                        bgcolor: alpha('#835df6', 0.8),
                      }
                    }}
                  >
                    保存设置
                  </Button>
                </Paper>
              </Box>
            ) : null}
          </CardContent>
        </Card>
      </Stack>
      
      {/* 确认弹窗 - 重置会话 */}
      <Dialog
        open={showResetSessionConfirm}
        onClose={() => setShowResetSessionConfirm(false)}
        PaperProps={{
          sx: {
            bgcolor: alpha('#1a1f36', 0.95),
            color: '#ffffff',
            boxShadow: `0 8px 32px ${alpha('#000', 0.4)}`,
            borderRadius: '12px',
            border: `1px solid ${alpha('#4f7df9', 0.3)}`,
          }
        }}
      >
        <DialogTitle sx={{ color: '#ffffff' }}>
          确认重置会话?
        </DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ color: alpha('#ffffff', 0.8) }}>
            重置会话将清除当前对话历史，下次询问时将创建新的会话。
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button 
            onClick={() => setShowResetSessionConfirm(false)} 
            sx={{ color: alpha('#ffffff', 0.7) }}
          >
            取消
          </Button>
          <Button 
            onClick={confirmResetSession} 
            variant="contained"
            sx={{ bgcolor: '#ff5a7d', '&:hover': { bgcolor: alpha('#ff5a7d', 0.8) } }}
          >
            确认重置
          </Button>
        </DialogActions>
      </Dialog>

      {/* 二维码扫描对话框 */}
      <Dialog open={showQrCodeDialog} onClose={() => setShowQrCodeDialog(false)} aria-labelledby="qrcode-dialog-title">
        <DialogTitle id="qrcode-dialog-title">请使用微信扫码登录</DialogTitle>
        <DialogContent>
          <DialogContentText>
            请使用微信扫描下方的二维码进行登录。
          </DialogContentText>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              padding: 2,
            }}
          >
            {qrCodeData && (
              <img src={qrCodeData} alt="微信视频号扫码登录二维码" style={{ maxWidth: '250px', maxHeight: '250px', height: 'auto' }} />
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowQrCodeDialog(false)} sx={{ color: '#835df6' }}>关闭</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};