import React from 'react';
import { Model, TTSOptions } from '../../hooks/useTTS';
import { Tooltip } from '@mui/material';

type SidebarProps = {
  currentWorkspace: string;
  onWorkspaceChange: (workspace: string) => void;
  models: Model[];
  currentModel: Model | null;
  setModel: (model: Model) => void;
  options: TTSOptions;
  setOptions: (options: Partial<TTSOptions>) => void;
  serviceStatus?: {
    backend: boolean;
    indexTTS: boolean;
  };
};

const Sidebar: React.FC<SidebarProps> = ({
  currentWorkspace,
  onWorkspaceChange,
  models,
  currentModel,
  setModel,
  options,
  setOptions,
  serviceStatus = { backend: false, indexTTS: false }
}) => {
  const handleWorkspaceClick = (workspace: string) => {
    onWorkspaceChange(workspace);
  };

  // 工作区菜单配置
  const workspaces = [
    {
      id: 'live',
      name: '实时直播',
      description: '实时语音直播功能',
      icon: (
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M16 10L16 18" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
          <path d="M12 14V18" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
          <path d="M8 12V18" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
          <path d="M3 8L3 18C3 19.6569 4.34315 21 6 21H18C19.6569 21 21 19.6569 21 18V8M3 8L6.42857 3H17.5714L21 8M3 8H21" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      )
    },
    {
      id: 'text-live',
      name: '文本直播',
      description: '文本生成与分组管理',
      icon: (
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="3" y="5" width="18" height="14" rx="2" stroke="currentColor" strokeWidth="1.5"/>
          <path d="M7 9H17M7 13H13" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
        </svg>
      )
    },
    {
      id: 'interaction',
      name: '互动回复',
      description: '智能回复用户问题',
      icon: (
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M8 10H8.01M12 10H12.01M16 10H16.01M9 16H5C3.89543 16 3 15.1046 3 14V6C3 4.89543 3.89543 4 5 4H19C20.1046 4 21 4.89543 21 6V14C21 15.1046 20.1046 16 19 16H15L12 19L9 16Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      )
    },
    {
      id: 'training',
      name: '模型训练',
      description: '训练个性化声音模型',
      icon: (
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M14 5L21 12M21 12L14 19M21 12H3" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      )
    },
    {
      id: 'voice',
      name: '语音合成',
      description: '实时文本转语音',
      icon: (
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 15C13.6569 15 15 13.6569 15 12V6C15 4.34315 13.6569 3 12 3C10.3431 3 9 4.34315 9 6V12C9 13.6569 10.3431 15 12 15Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M5 10V12C5 16.4183 8.58172 20 13 20C17.4183 20 21 16.4183 21 12V10" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M13 20V23" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      )
    },
    {
      id: 'product',
      name: '生成话术',
      description: '生成专业的带货话术',
      icon: (
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M16 8V7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7V8M16 8H8M16 8H20.8C20.9105 8 21 8.08954 21 8.2V21.8C21 21.9105 20.9105 22 20.8 22H3.2C3.08954 22 3 21.9105 3 21.8V8.2C3 8.08954 3.08954 8 3.2 8H8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <circle cx="12" cy="15" r="2" stroke="currentColor" strokeWidth="1.5"/>
        </svg>
      )
    },
    {
      id: 'smart-control',
      name: '智能场控',
      description: '智能化直播场控功能',
      icon: (
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="1.5" fill="none" />
          <path d="M8 12h8M12 8v8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
        </svg>
      )
    },
    {
      id: 'ai-model',
      name: '大模型对接',
      description: '设置AI提示词和话术',
      icon: (
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 4.35418C12.7329 3.93669 13.8053 3.5 15 3.5C17.1689 3.5 19 5.09354 19 7C19 8.90646 17.1689 10.5 15 10.5C14.7367 10.5 14.4911 10.4789 14.2607 10.4397M12 4.35418C11.2671 3.93669 10.1947 3.5 9 3.5C6.83105 3.5 5 5.09354 5 7C5 8.90646 6.83105 10.5 9 10.5C9.26331 10.5 9.50886 10.4789 9.73927 10.4397M12 4.35418V13.5M12 13.5C12.7329 13.0825 13.8053 12.6458 15 12.6458C17.1689 12.6458 19 14.2394 19 16.1458C19 18.0523 17.1689 19.6458 15 19.6458C14.7367 19.6458 14.4911 19.6247 14.2607 19.5856M12 13.5C11.2671 13.0825 10.1947 12.6458 9 12.6458C6.83105 12.6458 5 14.2394 5 16.1458C5 18.0523 6.83105 19.6458 9 19.6458C9.26331 19.6458 9.50886 19.6247 9.73927 19.5856" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      )
    },
  ];

  return (
    <div className="w-72 bg-gradient-to-b from-[#0f1525] to-[#171e38] border-r border-[#202749]/40 flex flex-col h-full shadow-2xl shadow-black/30 relative z-20 backdrop-blur-md">
      {/* 顶部装饰线 */}
      <div className="absolute top-0 left-0 right-0 h-[2px] bg-gradient-to-r from-blue-500/0 via-blue-500/80 to-purple-500/0"></div>
      
      {/* 侧边栏纹理背景 */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgdmlld0JveD0iMCAwIDYwIDYwIj48cGF0aCBkPSJNMzAgMjlhMSAxIDAgMSAwLTItMCAxIDEgMCAwIDAgMiAweiIgZmlsbD0iIzIwMmIzYiIgZmlsbC1vcGFjaXR5PSIwLjE1Ii8+PC9zdmc+')] opacity-30 z-0"></div>
      
      {/* Logo区域 - 高级设计 */}
      <div className="p-8 border-b border-[#202749]/40 relative z-10">
        <div className="relative">
          <h1 className="text-2xl font-bold tracking-wider">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-500">
              AI语音直播
            </span>
          </h1>
          <div className="mt-2 h-0.5 w-20 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full"></div>
          <div className="absolute -top-1 -left-3 w-8 h-8 rounded-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 blur-md"></div>
        </div>
      </div>
      
      {/* 菜单区域 */}
      <div className="p-6 relative z-10">
        <h2 className="text-xs font-medium uppercase tracking-widest text-gray-300/70 mb-6 flex items-center">
          <span className="w-1 h-1 bg-blue-400 rounded-full mr-2"></span>
          工作区
          <span className="ml-auto text-[10px] px-2 py-0.5 rounded-full bg-blue-500/10 text-blue-400/80">控制台</span>
        </h2>
        
        <div className="space-y-3">
          {workspaces.map(workspace => (
            <button
              key={workspace.id}
              className={`group w-full px-4 py-3.5 flex items-start justify-between rounded-lg transition-all duration-300
              ${currentWorkspace === workspace.id
                ? 'bg-gradient-to-r from-[#2e3c80]/90 to-[#24306e]/90 text-white shadow-lg shadow-blue-900/30'
                : 'bg-[#1e2447]/50 hover:bg-[#252c52]/70 text-gray-300 hover:text-white'
              }`}
              onClick={() => handleWorkspaceClick(workspace.id)}
            >
              <div className="flex items-start">
                <div className={`flex items-center justify-center w-9 h-9 rounded-lg mr-3 transition-all flex-shrink-0
                  ${currentWorkspace === workspace.id
                    ? 'bg-white/10'
                    : 'bg-[#252c52]/40 group-hover:bg-[#252c52]/80'
                  }`}
                >
                  {workspace.icon}
                </div>
                <div className="text-left flex-1 min-w-0">
                  <div className="font-medium text-sm leading-tight truncate">{workspace.name}</div>
                  <div className="text-xs opacity-70 font-light leading-tight mt-0.5 truncate">{workspace.description}</div>
                </div>
              </div>
              
              {currentWorkspace === workspace.id && (
                <div className="flex items-center flex-shrink-0 mt-1">
                  <span className="w-1.5 h-1.5 rounded-full bg-blue-300 mr-1.5 animate-pulse"></span>
                  <span className="text-xs text-blue-300/80 whitespace-nowrap">活跃</span>
                </div>
              )}
            </button>
          ))}
        </div>
      </div>
      
      {/* 底部版本信息和系统状态 */}
      <div className="mt-auto p-6 border-t border-[#202749]/40 relative z-10">
        <div className="flex items-center">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-blue-400/70 mr-2">
            <path d="M17 18.4301H13L8.54999 21.39C7.88999 21.83 7 21.3601 7 20.5601V18.4301C4 18.4301 2 16.4301 2 13.4301V7.42999C2 4.42999 4 2.42999 7 2.42999H17C20 2.42999 22 4.42999 22 7.42999V13.4301C22 16.4301 20 18.4301 17 18.4301Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          <div className="flex flex-col">
            <span className="text-xs font-medium text-gray-300/90">AI语音直播助手</span>
            <span className="text-[10px] text-gray-400">版本 2.0.1</span>
          </div>
          <div className="ml-auto flex items-center gap-2">
            <Tooltip title={serviceStatus.backend ? "后端服务已连接" : "后端服务未连接"}>
              <div className="flex items-center">
                <span className="text-[10px] text-gray-400 mr-1">后端</span>
                <span className={`w-2 h-2 rounded-full ${serviceStatus.backend ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`}></span>
              </div>
            </Tooltip>
            <Tooltip title={serviceStatus.indexTTS ? "TTS服务已连接" : "TTS服务未连接"}>
              <div className="flex items-center">
                <span className="text-[10px] text-gray-400 mr-1">TTS</span>
                <span className={`w-2 h-2 rounded-full ${serviceStatus.indexTTS ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`}></span>
              </div>
            </Tooltip>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar; 