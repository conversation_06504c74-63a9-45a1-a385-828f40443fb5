import React from 'react';

interface StatusBarProps {
  isLoggedIn?: boolean;
  username?: string;
  isConnected?: boolean;
  onLogin?: () => void;
  onLogout?: () => void;
}

const StatusBar: React.FC<StatusBarProps> = ({
  isLoggedIn = false,
  username = '',
  isConnected = false,
  onLogin,
  onLogout
}) => {
  return (
    <header className="bg-gray-800 border-b border-gray-700 px-4 py-2 flex items-center">
      <h1 className="text-xl font-semibold flex-grow">
        IndexTTS
      </h1>

      {/* 连接状态 */}
      <span className={`
        px-2 py-1 rounded-full text-sm mr-4
        ${isConnected ? 'bg-green-600 text-white' : 'bg-red-600 text-white'}
      `}>
        {isConnected ? "已连接" : "未连接"}
      </span>

      {/* 用户信息 */}
      <div className="flex items-center">
        {isLoggedIn ? (
          <>
            <span className="text-sm mr-2">
              {username}
            </span>
            <button
              onClick={onLogout}
              className="px-3 py-1 text-sm hover:bg-gray-700 rounded transition-colors"
            >
              退出
            </button>
          </>
        ) : (
          <button
            onClick={onLogin}
            className="px-3 py-1 text-sm hover:bg-gray-700 rounded transition-colors"
          >
            👤 登录
          </button>
        )}
      </div>
    </header>
  );
};

export default StatusBar; 