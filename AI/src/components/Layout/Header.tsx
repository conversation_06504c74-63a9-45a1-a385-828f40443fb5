import React from 'react';
import { AppB<PERSON>, Toolbar, Typo<PERSON>, Button, Chip } from '@mui/material';

interface HeaderProps {
  isLoggedIn: boolean;
  username?: string;
  isConnected: boolean;
  onLogin: () => void;
  onLogout: () => void;
}

const Header: React.FC<HeaderProps> = ({
  isLoggedIn,
  username,
  isConnected,
  onLogin,
  onLogout
}) => {
  return (
    <AppBar position="static" className="bg-gray-800">
      <Toolbar className="justify-between">
        <Typography variant="h6" className="text-white">
          AI直播助手
        </Typography>

        <div className="flex items-center space-x-4">
          {/* 连接状态 */}
          <Chip
            icon={<span>{isConnected ? '🟢' : '🔴'}</span>}
            label={isConnected ? '已连接' : '未连接'}
            color={isConnected ? 'success' : 'error'}
            variant="outlined"
          />

          {/* 用户信息 */}
          {isLoggedIn ? (
            <div className="flex items-center space-x-2">
              <span>👤</span>
              <Typography variant="body1" className="text-white">
                {username}
              </Typography>
              <Button
                color="inherit"
                onClick={onLogout}
                size="small"
              >
                退出
              </Button>
            </div>
          ) : (
            <Button
              color="inherit"
              onClick={onLogin}
              startIcon={<span>👤</span>}
            >
              登录
            </Button>
          )}
        </div>
      </Toolbar>
    </AppBar>
  );
};

export default Header; 