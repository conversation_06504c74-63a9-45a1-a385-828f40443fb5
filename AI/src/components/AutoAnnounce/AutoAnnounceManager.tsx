import React, { useEffect, useCallback, useRef } from 'react';
import { useLiveReply } from '../../contexts/LiveReplyContext';
import { replacePlaceholders, getVariablesForTemplate } from '../../utils/templateUtils';
import { useAIModel } from '../../contexts/AIModelContext';

interface BroadcastSettings {
  mode: 'fixed' | 'random'; // 播报模式：固定间隔或随机间隔
  fixedInterval: number;    // 固定间隔秒数
  randomMinInterval: number; // 随机间隔最小秒数
  randomMaxInterval: number; // 随机间隔最大秒数
  enabled: boolean;         // 是否启用自动播报
  useAI?: boolean;          // 是否使用AI泛化
  aiPrompt?: string;        // AI泛化提示词
}

/**
 * 自动播报管理器组件属性接口
 * @interface AutoAnnounceManagerProps
 */
interface AutoAnnounceManagerProps {
  isConnected: boolean;
  promptTemplates: Array<{
    id: string;
    name: string;
    content: string;
    category: 'welcome' | 'time' | 'count' | 'product' | 'custom' | 'generalize';
  }>;
  broadcastSettings: {
    welcome: BroadcastSettings;
    time: BroadcastSettings;
    count: BroadcastSettings;
    generalize: BroadcastSettings;
  };
  latestUsername?: string; // 最近进入直播间的用户名
  audienceCount?: number;  // 当前在线人数
}

/**
 * 自动播报管理组件
 * 负责根据设置的时间间隔进行欢迎语、时间和人数的自动播报
 * @component
 */
const AutoAnnounceManager: React.FC<AutoAnnounceManagerProps> = ({
  isConnected,
  promptTemplates,
  broadcastSettings,
  latestUsername,
  audienceCount = 0
}) => {
  const { insertLiveReply } = useLiveReply();
  const { generateText, isProcessing, config } = useAIModel();
  
  // 存储下一次播报的时间
  const nextTimeBroadcastRef = useRef<number>(0);
  const nextWelcomeBroadcastRef = useRef<number>(0);
  const nextCountBroadcastRef = useRef<number>(0);
  const nextGeneralizeBroadcastRef = useRef<number>(0);
  
  // 存储已播报的用户名，避免重复播报欢迎语
  const broadcastedUsernamesRef = useRef<Set<string>>(new Set());
  
  // 存储最后一次播报的人数，避免重复播报相同人数
  const lastBroadcastedCountRef = useRef<number>(0);
  
  // 存储计时器ID
  const timeTimerRef = useRef<NodeJS.Timeout | null>(null);
  const welcomeTimerRef = useRef<NodeJS.Timeout | null>(null);
  const countTimerRef = useRef<NodeJS.Timeout | null>(null);
  const generalizeTimerRef = useRef<NodeJS.Timeout | null>(null);

  /**
   * 根据类别获取提示词模板
   * @param category 提示词类别
   * @returns 提示词模板对象，如果没有找到返回undefined
   */
  const getTemplateByCategory = useCallback((category: string) => {
    // 获取该类别的所有模板
    const templates = promptTemplates.filter(t => t.category === category);
    
    if (templates.length === 0) {
      return undefined;
    }
    
    // 如果只有一个模板，直接返回
    if (templates.length === 1) {
      return templates[0];
    }
    
    // 随机选择一个模板
    const randomIndex = Math.floor(Math.random() * templates.length);
    return templates[randomIndex];
  }, [promptTemplates]);
  
  /**
   * 计算下一次播报时间
   * @param settings 播报设置
   * @returns 下一次播报的时间戳（毫秒）
   */
  const calculateNextBroadcastTime = useCallback((settings: BroadcastSettings): number => {
    let nextInterval: number;
    if (settings.mode === 'fixed') {
      nextInterval = settings.fixedInterval * 1000;
    } else {
      // 随机模式下，在最小和最大间隔之间随机选择
      const min = settings.randomMinInterval;
      const max = settings.randomMaxInterval;
      nextInterval = Math.floor(Math.random() * (max - min + 1) + min) * 1000;
    }
    
    return Date.now() + nextInterval;
  }, []);
  
  /**
   * 自动播报欢迎语
   */
  const autoWelcomeBroadcast = useCallback(async () => {
    if (!broadcastSettings.welcome.enabled) {
      return;
    }
    
    if (!isConnected) {
      return;
    }
    
    if (!latestUsername) {
      return; // 没有用户名时不进行播报
    }
    
    const now = Date.now();
    
    if (now < nextWelcomeBroadcastRef.current) {
      return;
    }
    
    if (latestUsername !== '新朋友' && broadcastedUsernamesRef.current.has(latestUsername)) {
      return;
    }
    
    const welcomeTemplate = getTemplateByCategory('welcome');
    if (!welcomeTemplate) {
      return;
    }
    
    // 准备变量数据
    const variables = getVariablesForTemplate('welcome', { nickname: latestUsername });
    
    // 替换模板中的变量
    const baseAnnouncement = replacePlaceholders(welcomeTemplate.content, variables);
    
    // 确定是否使用AI泛化
    const useAI = broadcastSettings.welcome?.useAI || false;
    let announcement = baseAnnouncement;
    
    if (useAI && config.aiModelEnabled) {
      try {
        // 获取AI泛化提示词
        const aiPrompt = broadcastSettings.welcome?.aiPrompt || 
                        '请对以下直播欢迎语进行改写，保持原意但使表达更加多样化、生动有趣：[原内容]';
        
        // 替换提示词中的[原内容]占位符
        const prompt = aiPrompt.replace('[原内容]', baseAnnouncement);
        
        // 调用AI生成泛化内容，不使用系统提示词
        const aiResponse = await generateText(prompt, '', false);
        
        if (aiResponse) {
          announcement = aiResponse;
        }
      } catch (error) {
        console.error('AI泛化生成失败:', error);
      }
    }
    
    // 最终确认所有开关状态
    if (broadcastSettings.welcome.enabled) {
      // 插入到直播回复
      insertLiveReply(announcement, 'welcome');
      
      // 记录已播报的用户名
      if (latestUsername !== '新朋友') {
        broadcastedUsernamesRef.current.add(latestUsername);
      }
      
      // 计算下一次播报时间
      nextWelcomeBroadcastRef.current = calculateNextBroadcastTime(broadcastSettings.welcome);
    }
  }, [isConnected, broadcastSettings.welcome, latestUsername, getTemplateByCategory, insertLiveReply, calculateNextBroadcastTime, generateText, config.aiModelEnabled]);
  
  /**
   * 按照设置的间隔自动播报时间 
   */
  const autoTimeBroadcast = useCallback(async () => {
    if (!broadcastSettings.time.enabled) {
      return;
    }
    
    if (!isConnected) {
      return;
    }
    
    const now = Date.now();
    
    if (now < nextTimeBroadcastRef.current) return;
    
    const timeTemplate = getTemplateByCategory('time');
    if (!timeTemplate) {
      return;
    }
    
    // 准备变量数据
    const variables = getVariablesForTemplate('time');
    
    // 替换模板中的变量
    const baseAnnouncement = replacePlaceholders(timeTemplate.content, variables);
    
    // 确定是否使用AI泛化
    const useAI = broadcastSettings.time?.useAI || false;
    let announcement = baseAnnouncement;
    
    if (useAI && config.aiModelEnabled) {
      try {
        // 获取AI泛化提示词
        const aiPrompt = broadcastSettings.time?.aiPrompt || 
                        '请对以下直播报时语进行改写，保持原意但使表达更加多样化、生动有趣：[原内容]';
        
        // 替换提示词中的[原内容]占位符
        const prompt = aiPrompt.replace('[原内容]', baseAnnouncement);
        
        // 调用AI生成泛化内容，不使用系统提示词
        const aiResponse = await generateText(prompt, '', false);
        
        if (aiResponse) {
          announcement = aiResponse;
        }
      } catch (error) {
        console.error('AI泛化生成失败:', error);
      }
    }
    
    // 最终确认所有开关状态
    if (broadcastSettings.time.enabled) {
      // 插入到直播回复
      insertLiveReply(announcement, 'time');
      
      // 计算下一次播报时间
      nextTimeBroadcastRef.current = calculateNextBroadcastTime(broadcastSettings.time);
    }
  }, [isConnected, broadcastSettings.time, getTemplateByCategory, insertLiveReply, calculateNextBroadcastTime, generateText, config.aiModelEnabled]);
  
  /**
   * 按照设置的间隔自动播报人数
   */
  const autoCountBroadcast = useCallback(async () => {
    if (!broadcastSettings.count.enabled) {
      return;
    }
    
    if (!isConnected || audienceCount <= 0) {
      return;
    }
    
    const now = Date.now();
    
    if (now < nextCountBroadcastRef.current) {
      return;
    }
    
    if (lastBroadcastedCountRef.current === audienceCount && audienceCount !== 0) {
      return;
    }
    
    const countTemplate = getTemplateByCategory('count');
    if (!countTemplate) {
      return;
    }
    
    // 准备变量数据 - 确保人数正确传递
    const variables = getVariablesForTemplate('count', { audienceCount: audienceCount });
    
    // 替换模板中的变量
    const baseAnnouncement = replacePlaceholders(countTemplate.content, variables);
    
    // 确定是否使用AI泛化
    const useAI = broadcastSettings.count?.useAI || false;
    let announcement = baseAnnouncement;
    
    if (useAI && config.aiModelEnabled) {
      try {
        // 获取AI泛化提示词
        const aiPrompt = broadcastSettings.count?.aiPrompt || 
                        '请对以下直播人数播报语进行改写，保持原意但使表达更加多样化、生动有趣：[原内容]';
        
        // 替换提示词中的[原内容]占位符
        const prompt = aiPrompt.replace('[原内容]', baseAnnouncement);
        
        // 调用AI生成泛化内容，不使用系统提示词
        const aiResponse = await generateText(prompt, '', false);
        
        if (aiResponse) {
          announcement = aiResponse;
        }
      } catch (error) {
        console.error('AI泛化生成失败:', error);
      }
    }
    
    // 最终确认所有开关状态
    if (broadcastSettings.count.enabled) {
      // 插入到直播回复
      insertLiveReply(announcement, 'count');
      
      // 记录最后一次播报的人数
      lastBroadcastedCountRef.current = audienceCount;
      
      // 计算下一次播报时间
      nextCountBroadcastRef.current = calculateNextBroadcastTime(broadcastSettings.count);
    }
  }, [isConnected, broadcastSettings.count, audienceCount, getTemplateByCategory, insertLiveReply, calculateNextBroadcastTime, generateText, config.aiModelEnabled]);
  
  /**
   * 自动播报泛化提示词
   */
  const autoGeneralizeBroadcast = useCallback(async () => {
    if (!broadcastSettings.generalize?.enabled) {
      return;
    }
    
    if (!isConnected) {
      return;
    }
    
    const now = Date.now();
    
    if (now < nextGeneralizeBroadcastRef.current) {
      return;
    }
    
    // 随机选择一个欢迎语、报时或人数播报模板作为基础
    const categories = ['welcome', 'time', 'count'];
    const randomCategory = categories[Math.floor(Math.random() * categories.length)];
    const baseTemplate = getTemplateByCategory(randomCategory);
    
    if (!baseTemplate) {
      return;
    }
    
    // 准备变量数据
    let variables: Record<string, string> = {};
    if (randomCategory === 'welcome') {
      variables = getVariablesForTemplate('welcome', { nickname: latestUsername || '观众' });
    } else if (randomCategory === 'time') {
      variables = getVariablesForTemplate('time');
    } else if (randomCategory === 'count') {
      variables = getVariablesForTemplate('count', { audienceCount: audienceCount });
    }
    
    // 替换基础模板中的变量
    const baseAnnouncement = replacePlaceholders(baseTemplate.content, variables);
    
    // 确定是否使用AI泛化
    const useAI = broadcastSettings.generalize?.useAI || false;
    let announcement = '';
    
    if (useAI && config.aiModelEnabled) {
      try {
        // 获取AI泛化提示词
        const aiPrompt = broadcastSettings.generalize?.aiPrompt || 
                         '请对以下直播话术进行改写，保持原意但使表达更加多样化、生动有趣：[原内容]';
        
        // 替换提示词中的[原内容]占位符
        const prompt = aiPrompt.replace('[原内容]', baseAnnouncement);
        
        // 调用AI生成泛化内容，不使用系统提示词
        const aiResponse = await generateText(prompt, '', false);
        
        if (aiResponse) {
          announcement = aiResponse;
        } else {
          // 如果AI生成失败，使用原始内容
          announcement = baseAnnouncement;
        }
      } catch (error) {
        console.error('AI泛化生成失败:', error);
        // 出错时使用原始内容
        announcement = baseAnnouncement;
      }
    } else {
      // 不使用AI泛化时，直接使用原始内容
      announcement = baseAnnouncement;
    }
    
    // 最终确认所有开关状态
    if (broadcastSettings.generalize?.enabled) {
      // 插入到直播回复
      insertLiveReply(announcement, randomCategory as 'welcome' | 'time' | 'count');
      
      // 计算下一次播报时间
      nextGeneralizeBroadcastRef.current = calculateNextBroadcastTime(broadcastSettings.generalize);
    }
  }, [isConnected, broadcastSettings.generalize, latestUsername, audienceCount, getTemplateByCategory, insertLiveReply, calculateNextBroadcastTime, generateText, config.aiModelEnabled]);
  
  // 设置自动播报计时器
  useEffect(() => {
    // 每秒检查一次是否需要播报
    const checkInterval = 1000; // 1秒
    
    // 设置时间播报计时器
    timeTimerRef.current = setInterval(() => {
      autoTimeBroadcast();
    }, checkInterval);
    
    // 设置欢迎语播报计时器
    welcomeTimerRef.current = setInterval(() => {
      autoWelcomeBroadcast();
    }, checkInterval);
    
    // 设置人数播报计时器
    countTimerRef.current = setInterval(() => {
      autoCountBroadcast();
    }, checkInterval);
    
    // 设置泛化提示词播报计时器
    generalizeTimerRef.current = setInterval(() => {
      autoGeneralizeBroadcast();
    }, checkInterval);
    
    // 清理函数
    return () => {
      if (timeTimerRef.current) clearInterval(timeTimerRef.current);
      if (welcomeTimerRef.current) clearInterval(welcomeTimerRef.current);
      if (countTimerRef.current) clearInterval(countTimerRef.current);
      if (generalizeTimerRef.current) clearInterval(generalizeTimerRef.current);
    };
  }, [autoTimeBroadcast, autoWelcomeBroadcast, autoCountBroadcast, autoGeneralizeBroadcast]);

  useEffect(() => {
    if (!isConnected) {
      // 断开连接时清理资源
      nextTimeBroadcastRef.current = 0;
      nextWelcomeBroadcastRef.current = 0;
      nextCountBroadcastRef.current = 0;
      nextGeneralizeBroadcastRef.current = 0;
      broadcastedUsernamesRef.current.clear();
      lastBroadcastedCountRef.current = 0;
      
      if (timeTimerRef.current) {
        clearInterval(timeTimerRef.current);
        timeTimerRef.current = null;
      }
      
      if (welcomeTimerRef.current) {
        clearInterval(welcomeTimerRef.current);
        welcomeTimerRef.current = null;
      }
      
      if (countTimerRef.current) {
        clearInterval(countTimerRef.current);
        countTimerRef.current = null;
      }
      
      if (generalizeTimerRef.current) {
        clearInterval(generalizeTimerRef.current);
        generalizeTimerRef.current = null;
      }
    }
  }, [isConnected]);

  useEffect(() => {
    if (broadcastSettings.welcome.enabled) {
      nextWelcomeBroadcastRef.current = Date.now();
    }
    if (broadcastSettings.time.enabled) {
      nextTimeBroadcastRef.current = Date.now();
    }
    if (broadcastSettings.count.enabled) {
      nextCountBroadcastRef.current = Date.now();
    }
    if (broadcastSettings.generalize?.enabled) {
      nextGeneralizeBroadcastRef.current = Date.now();
    }
  }, [broadcastSettings.welcome.enabled, broadcastSettings.time.enabled, broadcastSettings.count.enabled, broadcastSettings.generalize?.enabled]);

  // 这是一个不可见的组件，只负责逻辑处理
  return null;
};

export default AutoAnnounceManager; 