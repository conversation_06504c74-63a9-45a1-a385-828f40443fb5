import React from 'react';

/**
 * 加载动画组件属性接口
 */
interface TTSLoadingProps {
  progress: number;
  message?: string;
}

/**
 * TTS加载动画组件
 * @param props - 组件属性
 * @returns 加载动画界面
 */
const TTSLoading: React.FC<TTSLoadingProps> = ({ progress, message = '生成中...' }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg shadow-xl max-w-sm w-full mx-4">
        <div className="flex flex-col items-center">
          {/* 加载动画 */}
          <div className="w-16 h-16 mb-4 relative">
            <div className="absolute inset-0 border-4 border-blue-200 rounded-full"></div>
            <div
              className="absolute inset-0 border-4 border-blue-500 rounded-full"
              style={{
                clipPath: `polygon(0 0, ${progress}% 0, ${progress}% 100%, 0 100%)`,
                transition: 'clip-path 0.3s ease-in-out',
              }}
            ></div>
            <span className="absolute inset-0 flex items-center justify-center text-blue-500 font-semibold">
              {Math.round(progress)}%
            </span>
          </div>

          {/* 加载信息 */}
          <p className="text-gray-600 text-center">{message}</p>
        </div>
      </div>
    </div>
  );
};

export default TTSLoading; 