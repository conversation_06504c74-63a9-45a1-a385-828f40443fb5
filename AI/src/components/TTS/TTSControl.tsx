import React, { useState, useCallback } from 'react';
import TTSSettings from './TTSSettings';
import TTSHistory from './TTSHistory';
import TTSLoading from './TTSLoading';
import TTSError from './TTSError';
import TTSBatchProcess from './TTSBatchProcess';

/**
 * TTS控制组件属性接口
 */
interface TTSControlProps {
  text?: string;
  onStatusChange?: (status: string) => void;
}

/**
 * TTS选项接口
 */
interface TTSOptions {
  speed: number;
  volume: number;
  pitch: number;
}

/**
 * TTS控制组件
 * @param props - 组件属性
 * @returns TTS控制界面
 */
const TTSControl: React.FC<TTSControlProps> = ({
  text: initialText = '',
  onStatusChange
}) => {
  // 状态管理
  const [text, setText] = useState(initialText);
  const [options, setOptions] = useState<TTSOptions>({
    speed: 1.0,
    volume: 1.0,
    pitch: 0,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [history, setHistory] = useState<any[]>([]);
  const [selectedModel, setSelectedModel] = useState('default');
  const [models, setModels] = useState<string[]>([]);

  /**
   * 处理生成请求
   */
  const handleGenerate = async () => {
    if (!text.trim()) return;

    setIsLoading(true);
    setProgress(0);
    setError(null);

    try {
      // 创建WebSocket连接
      const ws = new WebSocket('ws://localhost:8080/ws/tts');
      
      ws.onopen = () => {
        ws.send(JSON.stringify({
          text,
          model: selectedModel,
          ...options
        }));
      };

      ws.onmessage = (event) => {
        const response = JSON.parse(event.data);
        if (response.status === 'success') {
          setProgress(response.progress * 100);
          onStatusChange?.(response.message);
          
          if (response.audio_data) {
            setHistory(prev => [{
              id: Math.random().toString(36).substr(2, 9),
              text: response.sentence,
              audioUrl: response.audio_data,
              createdAt: new Date(),
            }, ...prev]);
          }
        } else if (response.status === 'error') {
          setError(response.message);
          onStatusChange?.(`错误: ${response.message}`);
        } else if (response.status === 'complete') {
          onStatusChange?.('生成完成');
          setIsLoading(false);
          ws.close();
        }
      };

      ws.onerror = (error) => {
        setError('WebSocket连接错误');
        onStatusChange?.('连接错误');
        setIsLoading(false);
      };

      ws.onclose = () => {
        setIsLoading(false);
      };
    } catch (err: any) {
      setError(err.message || '生成失败');
      onStatusChange?.('生成失败');
    }
  };

  /**
   * 处理批量生成请求
   */
  const handleBatchGenerate = async (texts: string[]) => {
    setIsLoading(true);
    setProgress(0);
    setError(null);

    try {
      for (let i = 0; i < texts.length; i++) {
        setText(texts[i]);
        await handleGenerate();
        setProgress((i + 1) / texts.length * 100);
      }
    } catch (err: any) {
      setError(err.message || '批量生成失败');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* 模型选择 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          选择模型
        </label>
        <select
          value={selectedModel}
          onChange={(e) => setSelectedModel(e.target.value)}
          className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          {models.map((model) => (
            <option key={model} value={model}>
              {model}
            </option>
          ))}
        </select>
      </div>

      {/* 文本输入 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          输入文本
        </label>
        <textarea
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder="请输入要转换的文本..."
          className="w-full h-32 p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* TTS设置 */}
      <TTSSettings
        speed={options.speed}
        volume={options.volume}
        pitch={options.pitch}
        onSpeedChange={(value) => setOptions((prev) => ({ ...prev, speed: value }))}
        onVolumeChange={(value) => setOptions((prev) => ({ ...prev, volume: value }))}
        onPitchChange={(value) => setOptions((prev) => ({ ...prev, pitch: value }))}
      />

      {/* 生成按钮 */}
      <button
        onClick={handleGenerate}
        disabled={isLoading || !text.trim()}
        className={`w-full py-2 px-4 rounded ${
          isLoading || !text.trim()
            ? 'bg-gray-300 cursor-not-allowed'
            : 'bg-blue-500 hover:bg-blue-600 text-white'
        }`}
      >
        {isLoading ? '生成中...' : '生成语音'}
      </button>

      {/* 批量处理 */}
      <TTSBatchProcess onProcess={handleBatchGenerate} />

      {/* 历史记录 */}
      <TTSHistory
        history={history}
        onPlay={() => {}}
        onDelete={(id) => setHistory(prev => prev.filter(item => item.id !== id))}
        onExport={() => {}}
      />

      {/* 加载状态 */}
      {isLoading && (
        <TTSLoading progress={progress} message="正在生成语音..." />
      )}

      {/* 错误提示 */}
      {error && (
        <TTSError
          message={error}
          onClose={() => setError(null)}
        />
      )}
    </div>
  );
};

export default TTSControl; 