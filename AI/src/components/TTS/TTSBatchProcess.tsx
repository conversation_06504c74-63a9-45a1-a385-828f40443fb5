import React, { useState } from 'react';

/**
 * 批量处理项接口
 */
interface BatchItem {
  id: string;
  text: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress: number;
  error?: string;
}

/**
 * 批量处理组件属性接口
 */
interface TTSBatchProcessProps {
  onProcess: (texts: string[]) => Promise<void>;
}

/**
 * TTS批量处理组件
 * @param props - 组件属性
 * @returns 批量处理界面
 */
const TTSBatchProcess: React.FC<TTSBatchProcessProps> = ({ onProcess }) => {
  const [items, setItems] = useState<BatchItem[]>([]);
  const [inputText, setInputText] = useState('');

  /**
   * 添加批量处理项
   */
  const handleAddItem = () => {
    if (!inputText.trim()) return;

    const texts = inputText
      .split('\n')
      .filter(text => text.trim())
      .map(text => ({
        id: Math.random().toString(36).substr(2, 9),
        text: text.trim(),
        status: 'pending' as const,
        progress: 0
      }));

    setItems(prev => [...prev, ...texts]);
    setInputText('');
  };

  /**
   * 开始批量处理
   */
  const handleStartProcess = async () => {
    const texts = items.filter(item => item.status === 'pending').map(item => item.text);
    if (texts.length === 0) return;

    try {
      await onProcess(texts);
      setItems(prev =>
        prev.map(item =>
          texts.includes(item.text)
            ? { ...item, status: 'completed', progress: 100 }
            : item
        )
      );
    } catch (err: any) {
      setItems(prev =>
        prev.map(item =>
          texts.includes(item.text)
            ? { ...item, status: 'error', error: err.message }
            : item
        )
      );
    }
  };

  /**
   * 删除处理项
   */
  const handleRemoveItem = (id: string) => {
    setItems(prev => prev.filter(item => item.id !== id));
  };

  /**
   * 清空所有项
   */
  const handleClearAll = () => {
    setItems([]);
    setInputText('');
  };

  return (
    <div className="p-4 bg-white rounded-lg shadow-md">
      <h3 className="text-lg font-semibold mb-4">批量处理</h3>

      <div className="space-y-4">
        <div>
          <textarea
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            placeholder="请输入要处理的文本，每行一条..."
            className="w-full h-32 p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <div className="flex space-x-2">
          <button
            onClick={handleAddItem}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            添加到队列
          </button>
          <button
            onClick={handleStartProcess}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            开始处理
          </button>
          <button
            onClick={handleClearAll}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            清空队列
          </button>
        </div>

        <div className="space-y-2">
          {items.map((item) => (
            <div
              key={item.id}
              className="p-3 border rounded flex items-center justify-between"
            >
              <div className="flex-1 mr-4">
                <p className="text-sm text-gray-600">{item.text}</p>
                {item.error && (
                  <p className="text-xs text-red-500 mt-1">{item.error}</p>
                )}
              </div>

              <div className="flex items-center space-x-4">
                <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div
                    className={`h-full rounded-full transition-all ${
                      item.status === 'error'
                        ? 'bg-red-500'
                        : item.status === 'completed'
                        ? 'bg-green-500'
                        : 'bg-blue-500'
                    }`}
                    style={{ width: `${item.progress}%` }}
                  />
                </div>
                <button
                  onClick={() => handleRemoveItem(item.id)}
                  className="text-red-500 hover:text-red-600"
                >
                  <svg
                    className="h-5 w-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TTSBatchProcess; 