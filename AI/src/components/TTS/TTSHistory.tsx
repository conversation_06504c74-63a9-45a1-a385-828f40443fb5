import React from 'react';

/**
 * 历史记录项接口
 */
interface HistoryItem {
  id: string;
  text: string;
  audioUrl: string;
  createdAt: Date;
}

/**
 * 历史记录组件属性接口
 */
interface TTSHistoryProps {
  history: HistoryItem[];
  onPlay: (item: HistoryItem) => void;
  onDelete: (id: string) => void;
  onExport: (item: HistoryItem) => void;
}

/**
 * TTS历史记录组件
 * @param props - 组件属性
 * @returns 历史记录界面
 */
const TTSHistory: React.FC<TTSHistoryProps> = ({
  history,
  onPlay,
  onDelete,
  onExport,
}) => {
  return (
    <div className="p-4 bg-white rounded-lg shadow-md">
      <h3 className="text-lg font-semibold mb-4">历史记录</h3>
      
      <div className="space-y-4">
        {history.length === 0 ? (
          <p className="text-gray-500 text-center">暂无历史记录</p>
        ) : (
          history.map((item) => (
            <div
              key={item.id}
              className="p-3 border rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="flex justify-between items-start mb-2">
                <p className="text-sm text-gray-600 line-clamp-2">{item.text}</p>
                <span className="text-xs text-gray-400">
                  {item.createdAt.toLocaleString()}
                </span>
              </div>
              
              <div className="flex space-x-2">
                <button
                  onClick={() => onPlay(item)}
                  className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
                >
                  播放
                </button>
                <button
                  onClick={() => onExport(item)}
                  className="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600"
                >
                  导出
                </button>
                <button
                  onClick={() => onDelete(item.id)}
                  className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600"
                >
                  删除
                </button>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default TTSHistory; 