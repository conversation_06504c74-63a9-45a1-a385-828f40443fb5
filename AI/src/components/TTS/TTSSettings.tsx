import React from 'react';
import { Slider } from '@mui/material';

/**
 * TTS设置组件的属性接口
 */
interface TTSSettingsProps {
  speed: number;
  volume: number;
  pitch: number;
  onSpeedChange: (value: number) => void;
  onVolumeChange: (value: number) => void;
  onPitchChange: (value: number) => void;
}

/**
 * TTS设置组件
 * @param props - 组件属性
 * @returns TTS设置界面
 */
const TTSSettings: React.FC<TTSSettingsProps> = ({
  speed,
  volume,
  pitch,
  onSpeedChange,
  onVolumeChange,
  onPitchChange,
}) => {
  return (
    <div className="p-4 bg-white rounded-lg shadow-md">
      <h3 className="text-lg font-semibold mb-4">语音设置</h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            语速 ({speed.toFixed(1)})
          </label>
          <Slider
            value={speed}
            onChange={(_, value) => onSpeedChange(value as number)}
            min={0.5}
            max={2.0}
            step={0.1}
            valueLabelDisplay="auto"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            音量 ({volume.toFixed(1)})
          </label>
          <Slider
            value={volume}
            onChange={(_, value) => onVolumeChange(value as number)}
            min={0}
            max={1}
            step={0.1}
            valueLabelDisplay="auto"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            音调 ({pitch.toFixed(1)})
          </label>
          <Slider
            value={pitch}
            onChange={(_, value) => onPitchChange(value as number)}
            min={-12}
            max={12}
            step={1}
            valueLabelDisplay="auto"
          />
        </div>
      </div>
    </div>
  );
};

export default TTSSettings; 