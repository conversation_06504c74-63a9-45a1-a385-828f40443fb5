import React, { useState, useEffect } from 'react';
import { 
  Box, 
  TextField, 
  Button, 
  Typography, 
  Paper, 
  CircularProgress,
  IconButton,
  Tooltip,
  Collapse,
  Alert,
  LinearProgress
} from '@mui/material';
import { 
  Send, 
  ContentCopy, 
  SmartToy, 
  ExpandMore, 
  ExpandLess,
  TextFields,
  Download
} from '@mui/icons-material';
import { useAIModel } from '../contexts/AIModelContext';

interface AIHelperProps {
  systemPrompt?: string; // 可选的系统提示词
  placeholder?: string;  // 输入框提示文本
  onResultGenerated?: (result: string) => void; // 生成结果的回调
  initialPrompt?: string; // 初始提示词，由外部组件提供
  fileName?: string; // 下载文件的名称
  useSession?: boolean; // 是否使用会话模式，默认true
}

/**
 * 通用AI助手组件，可在任何组件中使用
 * @param props AIHelper组件的属性
 * @returns AIHelper组件
 */
const AIHelper: React.FC<AIHelperProps> = ({
  systemPrompt = "你是一个专业的直播助手，擅长为主播生成多样化的话术表达",
  placeholder = "输入提示词，获取AI生成的内容...",
  onResultGenerated,
  initialPrompt = "",
  fileName = "AI生成内容",
  useSession = true
}) => {
  const [prompt, setPrompt] = useState<string>(initialPrompt);
  const [result, setResult] = useState<string>('');
  const [expanded, setExpanded] = useState<boolean>(true);
  const [wordCount, setWordCount] = useState<number>(0);
  
  // 使用AI上下文
  const { config, generateText, isProcessing, error } = useAIModel();
  
  // 当initialPrompt变化时更新prompt
  useEffect(() => {
    if (initialPrompt) {
      setPrompt(initialPrompt);
    }
  }, [initialPrompt]);
  
  // 当结果变化时，计算字数
  useEffect(() => {
    // 中文计算字数
    setWordCount(result.length);
  }, [result]);
  
  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPrompt(e.target.value);
  };
  
  // 生成内容
  const handleGenerate = async () => {
    if (!prompt.trim() || isProcessing) return;
    
    try {
      const generated = await generateText(prompt, systemPrompt, useSession);
      if (generated) {
        setResult(generated);
        
        // 调用回调函数
        if (onResultGenerated) {
          onResultGenerated(generated);
        }
      }
    } catch (error) {
      console.error('生成内容失败:', error);
    }
  };
  
  // 复制结果到剪贴板
  const handleCopy = () => {
    if (result) {
      navigator.clipboard.writeText(result);
    }
  };
  
  // 下载结果为文本文件
  const handleDownload = () => {
    if (!result) return;
    
    // 创建Blob对象
    const blob = new Blob([result], { type: 'text/plain;charset=utf-8' });
    
    // 创建下载链接
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    
    // 设置下载文件名
    const downloadFileName = `${fileName}_${new Date().toISOString().slice(0, 10)}.txt`;
    
    // 配置下载链接
    a.href = url;
    a.download = downloadFileName;
    a.click();
    
    // 释放URL对象
    URL.revokeObjectURL(url);
  };
  
  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleGenerate();
    }
  };
  
  // 切换展开/折叠状态
  const toggleExpanded = () => {
    setExpanded(!expanded);
  };

  // 获取字数达标状态
  const getWordCountStatus = () => {
    if (wordCount === 0) return { color: '#fff', text: '暂无内容' };
    return { color: '#4caf50', text: `${wordCount}字` };
  };
  
  const wordCountStatus = getWordCountStatus();

  return (
    <Paper sx={{
      p: 2,
      backgroundColor: 'rgba(30, 40, 80, 0.6)',
      backdropFilter: 'blur(10px)',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      borderRadius: 2,
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.2)',
      mb: 3
    }}>
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        mb: 1
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <SmartToy sx={{ mr: 1, color: '#4f7df9' }} />
          <Typography variant="h6" sx={{ color: '#ffffff', fontWeight: 500 }}>
            AI助手
          </Typography>
        </Box>
        <IconButton onClick={toggleExpanded} size="small" sx={{ color: '#ffffff' }}>
          {expanded ? <ExpandLess /> : <ExpandMore />}
        </IconButton>
      </Box>
      
      <Collapse in={expanded}>
        {!config.aiModelEnabled && (
          <Alert severity="warning" sx={{ mb: 2 }}>
            AI模型已禁用，请在"大模型对接"页面启用并配置API密钥
          </Alert>
        )}
        
        <TextField
          fullWidth
          placeholder={placeholder}
          value={prompt}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          disabled={isProcessing || !config.aiModelEnabled}
          multiline
          rows={2}
          variant="outlined"
          sx={{
            mb: 2,
            '& .MuiOutlinedInput-root': {
              bgcolor: 'rgba(255, 255, 255, 0.05)',
              color: '#ffffff',
              '& fieldset': { borderColor: 'rgba(255, 255, 255, 0.2)' },
              '&:hover fieldset': { borderColor: 'rgba(79, 125, 249, 0.5)' },
              '&.Mui-focused fieldset': { borderColor: '#4f7df9' }
            }
          }}
        />
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
          <Button
            variant="contained"
            onClick={handleGenerate}
            disabled={!prompt.trim() || isProcessing || !config.aiModelEnabled}
            startIcon={isProcessing ? <CircularProgress size={20} /> : <Send />}
            sx={{
              bgcolor: '#4f7df9',
              '&:hover': { bgcolor: '#3a67e0' },
              '&.Mui-disabled': { bgcolor: 'rgba(79, 125, 249, 0.3)' }
            }}
          >
            {isProcessing ? '生成中...' : '生成内容'}
          </Button>
          
          <Box>
            {result && (
              <>
                <Tooltip title="复制内容">
                  <IconButton onClick={handleCopy} sx={{ color: '#ffffff', mr: 1 }}>
                    <ContentCopy />
                  </IconButton>
                </Tooltip>
                <Tooltip title="下载为TXT文件">
                  <IconButton onClick={handleDownload} sx={{ color: '#ffffff' }}>
                    <Download />
                  </IconButton>
                </Tooltip>
              </>
            )}
          </Box>
        </Box>
        
        {isProcessing && (
          <Box sx={{ width: '100%', mb: 2 }}>
            <LinearProgress 
              sx={{ 
                height: 6, 
                borderRadius: 3,
                bgcolor: 'rgba(255,255,255,0.1)',
                '& .MuiLinearProgress-bar': {
                  bgcolor: '#4f7df9'
                }
              }} 
            />
            <Typography variant="caption" sx={{ color: 'rgba(255,255,255,0.7)', mt: 0.5, display: 'block' }}>
              正在生成直播话术，请稍候...
            </Typography>
          </Box>
        )}
        
        {result && (
          <>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <TextFields sx={{ color: wordCountStatus.color, mr: 1, fontSize: 18 }} />
              <Typography variant="caption" sx={{ color: wordCountStatus.color }}>
                {wordCountStatus.text}
              </Typography>
            </Box>
            <Paper sx={{
              p: 2,
              bgcolor: 'rgba(79, 125, 249, 0.1)',
              border: '1px solid rgba(79, 125, 249, 0.3)',
              borderRadius: 1
            }}>
              <Typography sx={{ 
                color: '#ffffff', 
                whiteSpace: 'pre-wrap',
                fontSize: '0.9rem',
                lineHeight: 1.5
              }}>
                {result}
              </Typography>
            </Paper>
          </>
        )}
        
        {error && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {error}
          </Alert>
        )}
      </Collapse>
    </Paper>
  );
};

export default AIHelper; 