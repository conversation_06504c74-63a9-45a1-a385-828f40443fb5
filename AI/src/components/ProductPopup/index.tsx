import React, { useState, useEffect } from 'react';
import { Card, Button, Input, Select, Switch, message, Space, Divider, Tag, Tooltip } from 'antd';
import { PlusOutlined, DeleteOutlined, PlayCircleOutlined, PauseCircleOutlined, SettingOutlined } from '@ant-design/icons';
import axios from 'axios';

const { Option } = Select;

interface GoodsItem {
  id: number;
  name: string;
  enabled: boolean;
}

interface PopupConfig {
  interval: [number, number];
  random: boolean;
  platform: string;
}

interface ShortcutMapping {
  id: string;
  key: string;
  ctrl?: boolean;
  alt?: boolean;
  shift?: boolean;
  goodsIds: number[];
}

const ProductPopup: React.FC = () => {
  const [goodsList, setGoodsList] = useState<GoodsItem[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [config, setConfig] = useState<PopupConfig>({
    interval: [30000, 45000],
    random: false,
    platform: 'douyin'
  });
  const [shortcuts, setShortcuts] = useState<ShortcutMapping[]>([]);
  const [newGoodsId, setNewGoodsId] = useState<number>(1);

  // 检查连接状态
  const checkConnectionStatus = async () => {
    try {
      const response = await axios.get('/api/automation/status');
      setIsConnected(response.data.is_connected);
      return response.data.is_connected;
    } catch (error) {
      console.error('检查连接状态失败:', error);
      setIsConnected(false);
      return false;
    }
  };

  // 组件加载时检查连接状态
  useEffect(() => {
    checkConnectionStatus();
    // 定期检查连接状态
    const interval = setInterval(checkConnectionStatus, 5000);
    return () => clearInterval(interval);
  }, []);

  // 添加商品
  const addGoods = () => {
    if (goodsList.find(item => item.id === newGoodsId)) {
      message.error('商品序号已存在！');
      return;
    }
    
    const newGoods: GoodsItem = {
      id: newGoodsId,
      name: `商品${newGoodsId}`,
      enabled: true
    };
    
    setGoodsList([...goodsList, newGoods]);
    setNewGoodsId(newGoodsId + 1);
  };

  // 删除商品
  const removeGoods = (id: number) => {
    setGoodsList(goodsList.filter(item => item.id !== id));
  };

  // 切换商品启用状态
  const toggleGoods = (id: number) => {
    setGoodsList(goodsList.map(item => 
      item.id === id ? { ...item, enabled: !item.enabled } : item
    ));
  };

  // 启动/停止自动弹窗
  const toggleAutoPopup = async () => {
    try {
      // 检查连接状态
      const connected = await checkConnectionStatus();
      if (!connected) {
        message.error('请先连接到直播控制台！');
        return;
      }

      if (isRunning) {
        // 停止弹窗
        const response = await axios.post('/api/automation/auto-popup/stop');
        if (response.data.status === 'stopped') {
          setIsRunning(false);
          message.success('自动弹窗已停止');
        } else {
          message.error('停止失败');
        }
      } else {
        // 启动弹窗
        const enabledGoods = goodsList.filter(item => item.enabled);
        if (enabledGoods.length === 0) {
          message.error('请至少启用一个商品！');
          return;
        }

        const params = {
          platform: config.platform,
          goods_list: enabledGoods.map(item => item.id.toString()),
          interval_min: config.interval[0] / 1000,
          interval_max: config.interval[1] / 1000,
          random_popup: config.random
        };

        const response = await axios.post('/api/automation/auto-popup/start', params);
        if (response.data.status === 'started') {
          setIsRunning(true);
          message.success('自动弹窗已启动');
        } else if (response.data.status === 'already_running') {
          message.warning('自动弹窗已在运行中');
          setIsRunning(true);
        } else {
          message.error('启动失败');
        }
      }
    } catch (error) {
      console.error('自动弹窗操作失败:', error);
      message.error('操作失败：' + (error as any).response?.data?.detail || (error as Error).message);
    }
  };

  // 手动弹窗
  const manualPopup = async (goodsId: number) => {
    try {
      // 检查连接状态
      const connected = await checkConnectionStatus();
      if (!connected) {
        message.error('请先连接到直播控制台！');
        return;
      }

      const params = {
        platform: config.platform,
        goods_id: goodsId.toString()
      };

      const response = await axios.post('/api/automation/auto-popup/manual', params);

      if (response.data.status === 'success') {
        message.success(`商品 ${goodsId} 弹窗成功`);
      } else if (response.data.status === 'not_connected') {
        message.error('未连接到控制台');
        setIsConnected(false);
      } else {
        message.error(response.data.message || '弹窗失败');
      }
    } catch (error) {
      console.error('手动弹窗失败:', error);
      message.error('弹窗失败：' + (error as any).response?.data?.detail || (error as Error).message);
    }
  };

  return (
    <div style={{ padding: '20px' }}>
      <Card title="商品弹窗控制" style={{ marginBottom: '20px' }}>
        <div style={{ marginBottom: '20px' }}>
          <Space>
            <Button
              type="primary"
              danger={isRunning}
              icon={isRunning ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={toggleAutoPopup}
              size="large"
              disabled={!isConnected}
            >
              {isRunning ? '停止自动弹窗' : '启动自动弹窗'}
            </Button>
            <Tag color={isRunning ? 'green' : 'default'}>
              {isRunning ? '运行中' : '已停止'}
            </Tag>
            <Tag color={isConnected ? 'blue' : 'red'}>
              {isConnected ? '已连接控制台' : '未连接控制台'}
            </Tag>
          </Space>
        </div>

        {!isConnected && (
          <div style={{ marginBottom: '20px', padding: '12px', backgroundColor: '#fff2e8', border: '1px solid #ffbb96', borderRadius: '6px' }}>
            <span style={{ color: '#d46b08' }}>
              ⚠️ 请先在智能场控页面连接到直播控制台，然后才能使用商品弹窗功能
            </span>
          </div>
        )}

        <Divider>商品列表</Divider>
        
        <div style={{ marginBottom: '20px' }}>
          <Space>
            <Input
              type="number"
              value={newGoodsId}
              onChange={(e) => setNewGoodsId(Number(e.target.value))}
              placeholder="商品序号"
              style={{ width: 120 }}
              min={1}
            />
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={addGoods}
            >
              添加商品
            </Button>
          </Space>
        </div>

        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))', gap: '16px' }}>
          {goodsList.map(item => (
            <Card 
              key={item.id}
              size="small"
              style={{ 
                border: item.enabled ? '1px solid #1890ff' : '1px solid #d9d9d9',
                backgroundColor: item.enabled ? '#f6ffed' : '#fafafa'
              }}
            >
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div>
                  <div style={{ fontWeight: 'bold' }}>商品 {item.id}</div>
                  <div style={{ fontSize: '12px', color: '#666' }}>{item.name}</div>
                </div>
                <Space>
                  <Switch 
                    checked={item.enabled}
                    onChange={() => toggleGoods(item.id)}
                    size="small"
                  />
                  <Tooltip title="手动弹窗">
                    <Button 
                      type="text" 
                      icon={<PlayCircleOutlined />}
                      onClick={() => manualPopup(item.id)}
                      disabled={!item.enabled}
                    />
                  </Tooltip>
                  <Tooltip title="删除商品">
                    <Button 
                      type="text" 
                      danger 
                      icon={<DeleteOutlined />}
                      onClick={() => removeGoods(item.id)}
                    />
                  </Tooltip>
                </Space>
              </div>
            </Card>
          ))}
        </div>
      </Card>

      <Card title="弹窗设置" style={{ marginBottom: '20px' }}>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px' }}>
          <div>
            <label style={{ display: 'block', marginBottom: '8px' }}>平台选择</label>
            <Select 
              value={config.platform}
              onChange={(value) => setConfig({...config, platform: value})}
              style={{ width: '100%' }}
            >
              <Option value="douyin">抖音小店</Option>
              <Option value="buyin">巨量百应</Option>
              <Option value="kuaishou">快手小店</Option>
              <Option value="taobao">淘宝直播</Option>
            </Select>
          </div>
          
          <div>
            <label style={{ display: 'block', marginBottom: '8px' }}>弹窗间隔 (秒)</label>
            <Space>
              <Input
                type="number"
                value={config.interval[0] / 1000}
                onChange={(e) => setConfig({
                  ...config, 
                  interval: [Number(e.target.value) * 1000, config.interval[1]]
                })}
                style={{ width: 80 }}
                min={10}
              />
              <span>-</span>
              <Input
                type="number"
                value={config.interval[1] / 1000}
                onChange={(e) => setConfig({
                  ...config, 
                  interval: [config.interval[0], Number(e.target.value) * 1000]
                })}
                style={{ width: 80 }}
                min={10}
              />
            </Space>
          </div>
          
          <div>
            <label style={{ display: 'block', marginBottom: '8px' }}>随机顺序</label>
            <Switch 
              checked={config.random}
              onChange={(checked) => setConfig({...config, random: checked})}
            />
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ProductPopup;
