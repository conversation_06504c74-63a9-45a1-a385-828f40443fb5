import React, { useState } from 'react';
import { Box, Button, TextField, Typography, Paper, Container, Alert, Link } from '@mui/material';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

/**
 * 注册页面组件
 * @returns 注册页面
 */
const RegisterPage: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const { register } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    if (password !== confirmPassword) {
      setError('两次输入的密码不一致');
      return;
    }
    try {
      await register(username, password);
      navigate('/'); // 注册成功自动跳转首页
    } catch (err: any) {
      // 优先展示后端返回的 detail 字段
      if (err.response && err.response.data && err.response.data.detail) {
        setError(err.response.data.detail);
      } else if (err.message) {
        setError(err.message);
      } else {
        setError('注册失败');
      }
    }
  };

  return (
    <div
      className="h-screen w-screen flex items-center justify-start relative overflow-hidden"
      style={{
        background: 'url("/beijing.jpg") center center / cover no-repeat, #0d1117',
        fontFamily: '微软雅黑, Arial, sans-serif',
      }}
    >
      {/* 左侧注册卡片 */}
      <div style={{ flex: '0 0 600px', display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh', zIndex: 1, marginLeft: '130px' }}>
        <div style={{ position: 'relative', width: 600, height: 780 }}>
          <img
            src="/border.png"
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              zIndex: 2,
              pointerEvents: 'none',
              objectFit: 'fill',
              display: 'block',
            }}
          />
          <div
            className="relative z-30 flex flex-col items-center justify-center"
            style={{
              width: '68%',
              height: '82%',
              margin: 'auto',
              padding: '120px 0 32px 40px',
              color: '#fff',
              justifyContent: 'center',
            }}
          >
            {/* 顶部logo和标题 */}
            <div className="flex flex-col items-center" style={{ marginBottom: 32, marginTop: 8 }}>
              <img src="/logo.png" alt="logo" style={{width:48,height:48,marginBottom:12,filter:'drop-shadow(0 0 12px #00cfff)'}} />
              <div style={{ fontSize: 24, fontWeight: 700, color: '#00cfff', letterSpacing: 2, textShadow: '0 0 8px #0ff8', marginBottom: 8 }}>注册</div>
              <div style={{ height: 3, width: 80, margin: '10px auto 0', background: 'linear-gradient(90deg,#00cfff,#1e90ff,#00cfff)', borderRadius: 2 }} />
            </div>
            {/* 注册表单 */}
            <Box component="form" onSubmit={handleSubmit} noValidate sx={{ mt: 1 }} className="w-full">
              <TextField
                margin="normal"
                required
                fullWidth
                id="username"
                label="用户名"
                name="username"
                autoComplete="username"
                autoFocus
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="mb-4"
                sx={{
                  '& .MuiInputBase-root': {
                    color: 'white',
                  },
                  '& .MuiInputLabel-root': {
                    color: 'rgba(255, 255, 255, 0.7)',
                  },
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      borderColor: 'rgba(255, 255, 255, 0.23)',
                    },
                    '&:hover fieldset': {
                      borderColor: 'rgba(255, 255, 255, 0.5)',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#90caf9',
                    },
                  },
                }}
              />
              <TextField
                margin="normal"
                required
                fullWidth
                name="password"
                label="密码"
                type="password"
                id="password"
                autoComplete="new-password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="mb-4"
                sx={{
                  '& .MuiInputBase-root': {
                    color: 'white',
                  },
                  '& .MuiInputLabel-root': {
                    color: 'rgba(255, 255, 255, 0.7)',
                  },
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      borderColor: 'rgba(255, 255, 255, 0.23)',
                    },
                    '&:hover fieldset': {
                      borderColor: 'rgba(255, 255, 255, 0.5)',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#90caf9',
                    },
                  },
                }}
              />
              <TextField
                margin="normal"
                required
                fullWidth
                name="confirmPassword"
                label="确认密码"
                type="password"
                id="confirmPassword"
                autoComplete="new-password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="mb-6"
                sx={{
                  '& .MuiInputBase-root': {
                    color: 'white',
                  },
                  '& .MuiInputLabel-root': {
                    color: 'rgba(255, 255, 255, 0.7)',
                  },
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      borderColor: 'rgba(255, 255, 255, 0.23)',
                    },
                    '&:hover fieldset': {
                      borderColor: 'rgba(255, 255, 255, 0.5)',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#90caf9',
                    },
                  },
                }}
              />
              <Button
                type="submit"
                fullWidth
                variant="contained"
                sx={{ mt: 3, mb: 2 }}
                className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white py-2 rounded-lg transition-all duration-300"
              >
                注册
              </Button>
              {error && <div style={{ color: 'red', marginTop: 8 }}>{error}</div>}
            </Box>
            {/* 底部彩色圆点上方添加返回登录按钮 */}
            <Button
              fullWidth
              variant="text"
              sx={{ color: '#2196f3', fontSize: 18, mb: 1 }}
              onClick={() => navigate('/login')}
            >
              返回登录
            </Button>
            {/* 底部彩色圆点 */}
            <div className="flex justify-center gap-3 mt-2 mb-2" style={{ marginBottom: 8 }}>
              <span className="inline-block w-3 h-3 rounded-full" style={{ background: '#00cfff' }}></span>
              <span className="inline-block w-3 h-3 rounded-full" style={{ background: '#1e90ff' }}></span>
              <span className="inline-block w-3 h-3 rounded-full" style={{ background: '#ffe600' }}></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegisterPage; 