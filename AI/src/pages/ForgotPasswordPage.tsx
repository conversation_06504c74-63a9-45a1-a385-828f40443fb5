import React, { useState } from 'react';
import { Box, Button, TextField, Typography, Paper, Container, Alert, Link } from '@mui/material';
import { Link as RouterLink, useNavigate } from 'react-router-dom';

/**
 * 忘记密码页面组件
 * @returns 忘记密码页面
 */
const ForgotPasswordPage: React.FC = () => {
  const [account, setAccount] = useState(''); // 可以是用户名、邮箱或手机号
  const [verificationCode, setVerificationCode] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmNewPassword, setConfirmNewPassword] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isCodeSent, setIsCodeSent] = useState(false);
  const navigate = useNavigate();

  const handleSendCode = async () => {
    setError('');
    setSuccess('');
    if (!account) {
      setError('请输入您的账号（用户名/邮箱/手机号）');
      return;
    }

    // TODO: 调用后端发送验证码API
    console.log('Sending verification code to:', account);

    // 模拟发送成功
    // try {
    //   // const response = await fetch('/api/send-verification-code', { /* ... */ });
    //   // if (response.ok) {
    //     setSuccess('验证码已发送，请查收。');
    //     setIsCodeSent(true);
    //   // } else {
    //   //   const data = await response.json();
    //   //   setError(data.message || '发送失败');
    //   // }
    // } catch (err) {
    //   setError('发送验证码过程中发生错误');
    // }

    // 模拟成功，实际需要调用API
    setSuccess('验证码发送请求已提交 (模拟成功)。请等待后端接口就绪。');
    setIsCodeSent(true);
  };

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    if (!account || !verificationCode || !newPassword || !confirmNewPassword) {
      setError('请填写所有字段');
      return;
    }

    if (newPassword !== confirmNewPassword) {
      setError('两次输入的新密码不一致');
      return;
    }

    // TODO: 调用后端重置密码API
    console.log('Resetting password for:', account);

    // 模拟重置成功或失败
    // try {
    //   // const response = await fetch('/api/reset-password', { /* ... */ });
    //   // if (response.ok) {
    //     setSuccess('密码已成功重置，请前往登录页面。');
    //     // navigate('/login'); // 重置成功后跳转到登录页
    //   // } else {
    //   //   const data = await response.json();
    //   //   setError(data.message || '重置失败');
    //   // }
    // } catch (err) {
    //   setError('重置密码过程中发生错误');
    // }

    // 模拟成功，实际需要调用API
    setSuccess('密码重置请求已提交 (模拟成功)。请等待后端接口就绪。');
    setAccount('');
    setVerificationCode('');
    setNewPassword('');
    setConfirmNewPassword('');
    setIsCodeSent(false);
  };

  return (
    <div
      className="h-screen w-screen flex items-center justify-start relative overflow-hidden"
      style={{
        background: 'url("/beijing.jpg") center center / cover no-repeat, #0d1117',
        fontFamily: '微软雅黑, Arial, sans-serif',
      }}
    >
      {/* 左侧忘记密码卡片 */}
      <div style={{ flex: '0 0 600px', display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh', zIndex: 1, marginLeft: '130px' }}>
        <div style={{ position: 'relative', width: 600, height: 780 }}>
          <img
            src="/border.png"
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              zIndex: 2,
              pointerEvents: 'none',
              objectFit: 'fill',
              display: 'block',
            }}
          />
          <div
            className="relative z-30 flex flex-col items-center justify-center"
            style={{
              width: '68%',
              height: '82%',
              margin: 'auto',
              padding: '120px 0 32px 40px',
              color: '#fff',
              justifyContent: 'center',
            }}
          >
            {/* 顶部logo和标题 */}
            <div className="flex flex-col items-center" style={{ marginBottom: 32, marginTop: 8 }}>
              <img src="/logo.png" alt="logo" style={{width:48,height:48,marginBottom:12,filter:'drop-shadow(0 0 12px #00cfff)'}} />
              <div style={{ fontSize: 24, fontWeight: 700, color: '#00cfff', letterSpacing: 2, textShadow: '0 0 8px #0ff8', marginBottom: 8 }}>忘记密码</div>
              <div style={{ height: 3, width: 80, margin: '10px auto 0', background: 'linear-gradient(90deg,#00cfff,#1e90ff,#00cfff)', borderRadius: 2 }} />
            </div>
            {/* 忘记密码表单 */}
            <Box component="form" onSubmit={handleResetPassword} noValidate sx={{ mt: 1 }} className="w-full">
              <TextField
                margin="normal"
                required
                fullWidth
                id="account"
                label="账号 (用户名/邮箱/手机号)"
                name="account"
                autoComplete="account"
                autoFocus
                value={account}
                onChange={(e) => setAccount(e.target.value)}
                className="mb-4"
                sx={{
                  '& .MuiInputBase-root': {
                    color: 'white',
                  },
                  '& .MuiInputLabel-root': {
                    color: 'rgba(255, 255, 255, 1)',
                  },
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      borderColor: 'rgba(255, 255, 255, 0.8)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#FFFFFF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#90caf9',
                    },
                  },
                }}
              />

              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 4 }}>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  id="verification-code"
                  label="验证码"
                  name="verification-code"
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value)}
                  disabled={!isCodeSent}
                  sx={{
                    '& .MuiInputBase-root': {
                      color: 'white',
                    },
                    '& .MuiInputLabel-root': {
                      color: 'rgba(255, 255, 255, 1)',
                    },
                    '& .MuiOutlinedInput-root': {
                      '& fieldset': {
                        borderColor: 'rgba(255, 255, 255, 0.8)',
                      },
                      '&:hover fieldset': {
                        borderColor: '#FFFFFF',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#90caf9',
                      },
                      '&.Mui-disabled fieldset': {
                         borderColor: 'rgba(255, 255, 255, 0.4)',
                      },
                       '&.Mui-disabled .MuiInputBase-input': {
                         color: 'rgba(255, 255, 255, 0.6)',
                       },
                        '&.Mui-disabled .MuiInputLabel-root': {
                         color: 'rgba(255, 255, 255, 0.5)',
                       },
                    },
                  }}
                />
                <Button
                  variant="outlined"
                  onClick={handleSendCode}
                  disabled={!account || isCodeSent}
                  sx={{
                    height: 56, flexShrink: 0,
                    borderColor: 'rgba(255, 255, 255, 0.8)',
                    color: 'rgba(255, 255, 255, 1)',
                    '&:hover': { 
                      borderColor: '#1e90ff', 
                      color: '#1e90ff' 
                    },
                    '&.Mui-disabled': {
                       borderColor: 'rgba(255, 255, 255, 0.4)',
                       color: 'rgba(255, 255, 255, 0.5)',
                    },
                  }}
                >
                  发送验证码
                </Button>
              </Box>

              <TextField
                margin="normal"
                required
                fullWidth
                name="newPassword"
                label="新密码"
                type="password"
                id="new-password"
                autoComplete="new-password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                className="mb-4"
                disabled={!isCodeSent}
                sx={{
                  '& .MuiInputBase-root': {
                    color: 'white',
                  },
                  '& .MuiInputLabel-root': {
                    color: 'rgba(255, 255, 255, 1)',
                  },
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      borderColor: 'rgba(255, 255, 255, 0.8)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#FFFFFF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#90caf9',
                    },
                    '&.Mui-disabled fieldset': {
                         borderColor: 'rgba(255, 255, 255, 0.4)',
                      },
                       '&.Mui-disabled .MuiInputBase-input': {
                         color: 'rgba(255, 255, 255, 0.6)',
                       },
                         '&.Mui-disabled .MuiInputLabel-root': {
                         color: 'rgba(255, 255, 255, 0.5)',
                       },
                  },
                }}
              />
              <TextField
                margin="normal"
                required
                fullWidth
                name="confirmNewPassword"
                label="确认新密码"
                type="password"
                id="confirm-new-password"
                autoComplete="new-password"
                value={confirmNewPassword}
                onChange={(e) => setConfirmNewPassword(e.target.value)}
                className="mb-6"
                disabled={!isCodeSent}
                sx={{
                  '& .MuiInputBase-root': {
                    color: 'white',
                  },
                  '& .MuiInputLabel-root': {
                    color: 'rgba(255, 255, 255, 1)',
                  },
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      borderColor: 'rgba(255, 255, 255, 0.8)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#FFFFFF',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#90caf9',
                    },
                    '&.Mui-disabled fieldset': {
                         borderColor: 'rgba(255, 255, 255, 0.4)',
                      },
                       '&.Mui-disabled .MuiInputBase-input': {
                         color: 'rgba(255, 255, 255, 0.6)',
                       },
                         '&.Mui-disabled .MuiInputLabel-root': {
                         color: 'rgba(255, 255, 255, 0.5)',
                       },
                  },
                }}
              />

              <Button
                type="submit"
                fullWidth
                variant="contained"
                sx={{ mt: 3, mb: 2 }}
                className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white py-2 rounded-lg transition-all duration-300"
                disabled={!isCodeSent}
              >
                重置密码
              </Button>
              <Box textAlign="center" mt={2}>
                <Link component={RouterLink} to="/login" variant="body2" className="text-blue-400 hover:text-blue-300">
                  返回登录
                </Link>
              </Box>
            </Box>
            {/* 底部彩色圆点 */}
            <div className="flex justify-center gap-3 mt-2 mb-2" style={{ marginBottom: 8 }}>
              <span className="inline-block w-3 h-3 rounded-full" style={{ background: '#00cfff' }}></span>
              <span className="inline-block w-3 h-3 rounded-full" style={{ background: '#1e90ff' }}></span>
              <span className="inline-block w-3 h-3 rounded-full" style={{ background: '#ffe600' }}></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPasswordPage;