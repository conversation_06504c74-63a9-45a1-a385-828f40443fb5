/**
 * 模板变量替换工具函数
 * 
 * @description 处理模板中的变量替换，支持[昵称]、[时间]、[整点]和[人数]等变量
 */

/**
 * 过滤表情符号和特殊字符
 * 
 * @param text 需要过滤的文本
 * @returns 过滤后的文本
 */
export function filterEmojis(text: string): string {
  if (!text) return '';
  
  // 过滤表情符号和特殊字符
  return text.replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]|[\u2600-\u27FF]|[\u2B50-\u2B55]|[\u231A-\u231B]|[\u23E9-\u23EC]|[\u23F0-\u23F3]|[\u23F8-\u23FA]|[\u2934-\u2935]|[\u2795-\u2797]|[\u2B05-\u2B07]|[\u2B1B-\u2B1C]|[\u2B50]|[\u3030]|[\u303D]|[\u3297]|[\u3299]|[\uFE0F]/g, '').trim();
}

/**
 * 从"{选项1|选项2|选项3}"格式的字符串中随机选择一个选项
 * 
 * @param text 带有选项的字符串
 * @returns 随机选择的选项或原始字符串
 */
export function getRandomString(text: string): string {
  // 匹配{option1|option2|option3}格式
  return text.replace(/\{([^{}]+)\}/g, (match, options) => {
    const choices = options.split('|');
    const randomIndex = Math.floor(Math.random() * choices.length);
    return choices[randomIndex];
  });
}

/**
 * 截取用户昵称
 * 如果昵称长度超过4个字符，则只取前2个字符
 * 对于纯英文单词，保持完整不截取
 * 
 * @param nickname 用户昵称
 * @returns 处理后的昵称
 */
export function truncateNickname(nickname: string): string {
  if (!nickname) return '家人';
  
  // 判断是否为纯英文单词（只包含英文字母、数字和常见英文标点）
  const isEnglishWord = /^[a-zA-Z0-9_\-\.]+$/.test(nickname);
  
  // 如果是纯英文单词，直接返回完整昵称
  if (isEnglishWord) {
    return nickname;
  }
  
  // 对于中文或混合内容，如果昵称长度超过4个字符，则只取前2个字符
  if (nickname.length > 4) {
    const truncated = nickname.substring(0, 2);
    return truncated;
  }
  
  return nickname;
}

/**
 * 替换模板中的变量
 * 
 * @param template 模板字符串
 * @param variables 变量对象，包含变量名和对应的值
 * @returns 替换变量后的字符串
 */
export function replacePlaceholders(template: string, variables: Record<string, any>): string {
  if (!template) return '';
  
  // 替换[昵称]变量
  if (variables.nickname) {
    template = template.replace(/\[昵称\]/g, variables.nickname);
  }
  
  // 替换[人数]变量 - 确保人数是数字类型且有效
  // 支持count和audienceCount两种属性名
  const audienceCount = variables.audienceCount !== undefined ? variables.audienceCount : variables.count;
  if (audienceCount !== undefined) {
    const count = Number(audienceCount);
    if (!isNaN(count)) {
      template = template.replace(/\[人数\]/g, count.toString());
    } else {
      template = template.replace(/\[人数\]/g, '0');
    }
  }
  
  // 替换[时间]变量
  if (variables.timeFormatted) {
    template = template.replace(/\[时间\]/g, variables.timeFormatted);
  } else {
    // 如果没有传入格式化的时间，自动生成
    const now = new Date();
    const hours = now.getHours();
    const minutes = now.getMinutes();
    
    // 特殊处理小时数2，读作"两"
    const hoursFormatted = hours === 2 ? '两' : hours.toString();
    
    const timeFormatted = `${hoursFormatted}点${minutes > 0 ? minutes + '分' : '整'}`;
    template = template.replace(/\[时间\]/g, timeFormatted);
  }
  
  // 替换[整点]变量
  if (variables.nextHourText !== undefined) {
    template = template.replace(/\[整点\]/g, variables.nextHourText);
  } else {
    // 如果没有传入整点文本，自动生成
    const nextHourText = getNextHourText();
    template = template.replace(/\[整点\]/g, nextHourText);
  }
  
  return template;
}

/**
 * 获取当前时间格式化字符串
 * 
 * @returns 格式化的时间字符串，如"15点30分"
 */
export function getCurrentTimeFormatted(): string {
  const now = new Date();
  const hours = now.getHours();
  const minutes = now.getMinutes();
  
  // 特殊处理小时数2，读作"两"
  const hoursFormatted = hours === 2 ? '两' : hours.toString();
  
  return `${hoursFormatted}点${minutes > 0 ? minutes + '分' : '整'}`;
}

/**
 * 获取距离下一个整点的文本描述
 * 
 * @returns 格式化的距离下一个整点的文本描述
 */
export function getNextHourText(): string {
  const now = new Date();
  const currentHour = now.getHours();
  const minutes = now.getMinutes();
  const nextHourMinutes = 60 - minutes;
  const nextHour = (currentHour + 1) % 24; // 处理跨天的情况
  
  return `距离${nextHour}点还有${nextHourMinutes}分钟`;
}

/**
 * 根据模板类型获取需要的变量
 * 
 * @param category 模板类别
 * @param userData 可选的用户数据
 * @returns 适用于该类型模板的变量对象
 */
export function getVariablesForTemplate(
  category: 'welcome' | 'time' | 'count' | 'product' | 'custom',
  userData?: {
    nickname?: string;
    audienceCount?: number;
  }
): Record<string, any> {
  const variables: Record<string, any> = {};
  
  // 基于模板类型返回不同的变量
  switch (category) {
    case 'welcome':
      // 如果传入了具体用户昵称，优先使用实际昵称，并过滤表情符号
      const originalNickname = userData?.nickname || '家人们';
      const filteredNickname = filterEmojis(originalNickname);
      // 如果过滤后为空，使用'家人'；否则应用昵称截断逻辑
      const finalNickname = filteredNickname ? truncateNickname(filteredNickname) : '家人们';
      variables.nickname = finalNickname;
      break;
      
    case 'time':
      variables.timeFormatted = getCurrentTimeFormatted();
      variables.nextHourText = getNextHourText();  // 使用新的文本格式
      break;
      
    case 'count':
      // 确保人数是数字类型且有效，如果无效则默认为0
      let count = 0;
      if (userData?.audienceCount !== undefined) {
        count = Number(userData.audienceCount);
        if (isNaN(count)) {
          count = 0;
        }
      }
      // 同时设置count和audienceCount两个属性名，确保不会遗漏
      variables.count = count;
      variables.audienceCount = count;
      break;
      
    case 'custom':
      // 自定义模板支持所有变量类型
      // 处理昵称
      if (userData?.nickname) {
        const filteredNickname = filterEmojis(userData.nickname);
        variables.nickname = filteredNickname ? truncateNickname(filteredNickname) : '家人们';
      } else {
        variables.nickname = '家人们';
      }
      
      // 处理人数
      let customCount = 0;
      if (userData?.audienceCount !== undefined) {
        customCount = Number(userData.audienceCount);
        if (isNaN(customCount)) {
          customCount = 0;
        }
      }
      // 同时设置count和audienceCount两个属性名，确保不会遗漏
      variables.count = customCount;
      variables.audienceCount = customCount;
      
      // 时间相关
      variables.timeFormatted = getCurrentTimeFormatted();
      variables.nextHourText = getNextHourText();
      break;
      
    default:
      break;
  }
  
  return variables;
}