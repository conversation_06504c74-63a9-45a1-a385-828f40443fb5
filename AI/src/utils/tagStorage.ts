/**
 * 标签接口定义
 */
export interface Tag {
  id: string;
  name: string;
  color: string;
}

/**
 * 标签存储工具类
 * 用于处理标签和模型标签关联的本地存储
 */
export class TagStorage {
  private static TAGS_KEY = 'voice_tags';
  private static MODEL_TAGS_KEY = 'model_tags';

  /**
   * 保存标签列表到本地存储
   * @param tags 标签列表
   */
  static saveTags(tags: Tag[]) {
    try {
      localStorage.setItem(this.TAGS_KEY, JSON.stringify(tags));
    } catch (error) {
      console.error('保存标签列表失败:', error);
    }
  }

  /**
   * 从本地存储获取标签列表
   * @returns 标签列表
   */
  static getTags(): Tag[] {
    try {
      const tags = localStorage.getItem(this.TAGS_KEY);
      if (!tags) {
        return [];
      }
      const parsedTags = JSON.parse(tags);
      return parsedTags;
    } catch (error) {
      console.error('获取标签列表失败:', error);
      return [];
    }
  }

  /**
   * 保存模型标签关联到本地存储
   * @param modelTags 模型标签关联对象
   */
  static saveModelTags(modelTags: {[modelId: string]: string[]}) {
    try {
      localStorage.setItem(this.MODEL_TAGS_KEY, JSON.stringify(modelTags));
    } catch (error) {
      console.error('保存模型标签关联失败:', error);
    }
  }

  /**
   * 从本地存储获取模型标签关联
   * @returns 模型标签关联对象
   */
  static getModelTags(): {[modelId: string]: string[]} {
    try {
      const modelTags = localStorage.getItem(this.MODEL_TAGS_KEY);
      if (!modelTags) {
        return {};
      }
      const parsedModelTags = JSON.parse(modelTags);
      return parsedModelTags;
    } catch (error) {
      console.error('获取模型标签关联失败:', error);
      return {};
    }
  }

  /**
   * 清除所有标签相关的本地存储
   */
  static clearAll() {
    try {
      localStorage.removeItem(this.TAGS_KEY);
      localStorage.removeItem(this.MODEL_TAGS_KEY);
    } catch (error) {
      console.error('清除标签存储失败:', error);
    }
  }

  /**
   * 添加单个标签
   * @param tag 要添加的标签
   */
  static addTag(tag: Tag) {
    try {
      const tags = this.getTags();
      if (!tags.some(t => t.id === tag.id)) {
        tags.push(tag);
        this.saveTags(tags);
      }
    } catch (error) {
      console.error('添加标签失败:', error);
    }
  }

  /**
   * 删除单个标签
   * @param tagId 要删除的标签ID
   */
  static removeTag(tagId: string) {
    try {
      const tags = this.getTags();
      const filteredTags = tags.filter(tag => tag.id !== tagId);
      this.saveTags(filteredTags);
      
      // 同时从所有模型中移除该标签
      const modelTags = this.getModelTags();
      Object.keys(modelTags).forEach(modelId => {
        modelTags[modelId] = modelTags[modelId].filter(id => id !== tagId);
      });
      this.saveModelTags(modelTags);
    } catch (error) {
      console.error('删除标签失败:', error);
    }
  }

  /**
   * 为模型添加标签
   * @param modelId 模型ID
   * @param tagId 标签ID
   */
  static addTagToModel(modelId: string, tagId: string) {
    try {
      const modelTags = this.getModelTags();
      if (!modelTags[modelId]) {
        modelTags[modelId] = [];
      }
      if (!modelTags[modelId].includes(tagId)) {
        modelTags[modelId].push(tagId);
        this.saveModelTags(modelTags);
      }
    } catch (error) {
      console.error('为模型添加标签失败:', error);
    }
  }

  /**
   * 从模型中移除标签
   * @param modelId 模型ID
   * @param tagId 标签ID
   */
  static removeTagFromModel(modelId: string, tagId: string) {
    try {
      const modelTags = this.getModelTags();
      if (modelTags[modelId]) {
        modelTags[modelId] = modelTags[modelId].filter(id => id !== tagId);
        this.saveModelTags(modelTags);
      }
    } catch (error) {
      console.error('从模型移除标签失败:', error);
    }
  }

  /**
   * 获取模型的所有标签
   * @param modelId 模型ID
   * @returns 标签列表
   */
  static getModelTagList(modelId: string): Tag[] {
    try {
      const modelTags = this.getModelTags();
      const allTags = this.getTags();
      const tagIds = modelTags[modelId] || [];
      return allTags.filter(tag => tagIds.includes(tag.id));
    } catch (error) {
      console.error('获取模型标签列表失败:', error);
      return [];
    }
  }

  /**
   * 更新标签信息
   * @param tagId 标签ID
   * @param updates 要更新的字段
   */
  static updateTag(tagId: string, updates: Partial<Tag>) {
    try {
      const tags = this.getTags();
      const tagIndex = tags.findIndex(tag => tag.id === tagId);
      if (tagIndex !== -1) {
        tags[tagIndex] = { ...tags[tagIndex], ...updates };
        this.saveTags(tags);
      }
    } catch (error) {
      console.error('更新标签失败:', error);
    }
  }
} 