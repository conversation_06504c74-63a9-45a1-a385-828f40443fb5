/**
 * 音频处理工具类
 */
export class AudioProcessor {
  private audioContext: AudioContext;
  private source: AudioBufferSourceNode | null = null;
  private gainNode: GainNode | null = null;
  private pitchNode: any | null = null; // 使用 PitchProcessor

  constructor() {
    this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
  }

  /**
   * 处理音频数据
   * @param audioData Base64格式的音频数据
   * @param volume 音量 (0.1-1.0)
   * @param pitch 音调 (0.5-2.0)
   * @returns 处理后的AudioBuffer
   */
  async processAudio(audioData: string, volume: number, pitch: number): Promise<AudioBuffer> {
    try {
      // 将Base64转换为ArrayBuffer
      const binaryString = window.atob(audioData);
      const len = binaryString.length;
      const bytes = new Uint8Array(len);
      for (let i = 0; i < len; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      const audioBuffer = await this.audioContext.decodeAudioData(bytes.buffer);

      // 创建处理节点
      const offlineContext = new OfflineAudioContext(
        audioBuffer.numberOfChannels,
        audioBuffer.length,
        audioBuffer.sampleRate
      );

      // 创建源节点
      const source = offlineContext.createBufferSource();
      source.buffer = audioBuffer;

      // 创建音量节点
      const gainNode = offlineContext.createGain();
      gainNode.gain.value = volume;

      // 音调处理
      if (pitch !== 1.0) {
        // 使用 PitchProcessor 进行音调调整
        source.playbackRate.value = pitch;
      }

      // 连接节点
      source.connect(gainNode);
      gainNode.connect(offlineContext.destination);

      // 开始处理
      source.start(0);
      const renderedBuffer = await offlineContext.startRendering();

      return renderedBuffer;
    } catch (error) {
      console.error('音频处理失败:', error);
      throw error;
    }
  }

  /**
   * 播放处理后的音频
   * @param audioBuffer 音频buffer
   * @returns 返回音频源节点，用于控制播放
   */
  playProcessedAudio(audioBuffer: AudioBuffer): AudioBufferSourceNode {
    const source = this.audioContext.createBufferSource();
    source.buffer = audioBuffer;
    
    const gainNode = this.audioContext.createGain();
    source.connect(gainNode);
    gainNode.connect(this.audioContext.destination);
    
    this.source = source;
    this.gainNode = gainNode;
    
    source.start(0);
    return source;
  }

  /**
   * 停止播放
   */
  stop() {
    if (this.source) {
      this.source.stop();
      this.source = null;
    }
  }

  /**
   * 关闭音频上下文，释放资源
   */
  close() {
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.stop();
      this.audioContext.close().catch(console.error);
    }
  }

  /**
   * 将处理后的AudioBuffer转换为Blob
   * @param audioBuffer 处理后的AudioBuffer
   * @returns Blob对象
   */
  async audioBufferToBlob(audioBuffer: AudioBuffer): Promise<Blob> {
    const offlineContext = new OfflineAudioContext(
      audioBuffer.numberOfChannels,
      audioBuffer.length,
      audioBuffer.sampleRate
    );

    const source = offlineContext.createBufferSource();
    source.buffer = audioBuffer;
    source.connect(offlineContext.destination);
    source.start(0);

    const renderedBuffer = await offlineContext.startRendering();
    const wavData = this.audioBufferToWav(renderedBuffer);
    return new Blob([wavData], { type: 'audio/wav' });
  }

  /**
   * 将AudioBuffer转换为WAV格式
   * @param audioBuffer AudioBuffer对象
   * @returns WAV格式的ArrayBuffer
   */
  private audioBufferToWav(audioBuffer: AudioBuffer): ArrayBuffer {
    const numChannels = audioBuffer.numberOfChannels;
    const sampleRate = audioBuffer.sampleRate;
    const format = 1; // PCM
    const bitDepth = 16;
    
    const bytesPerSample = bitDepth / 8;
    const blockAlign = numChannels * bytesPerSample;
    
    const buffer = audioBuffer.getChannelData(0);
    const samples = buffer.length;
    const dataSize = samples * blockAlign;
    const headerSize = 44;
    const totalSize = headerSize + dataSize;
    
    const arrayBuffer = new ArrayBuffer(totalSize);
    const view = new DataView(arrayBuffer);
    
    // WAV Header
    writeString(view, 0, 'RIFF');
    view.setUint32(4, totalSize - 8, true);
    writeString(view, 8, 'WAVE');
    writeString(view, 12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, format, true);
    view.setUint16(22, numChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * blockAlign, true);
    view.setUint16(32, blockAlign, true);
    view.setUint16(34, bitDepth, true);
    writeString(view, 36, 'data');
    view.setUint32(40, dataSize, true);
    
    // Write audio data
    const offset = 44;
    const volume = 1;
    for (let i = 0; i < samples; i++) {
      const sample = Math.max(-1, Math.min(1, buffer[i])) * volume;
      const value = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
      view.setInt16(offset + i * 2, value, true);
    }
    
    return arrayBuffer;
  }
}

function writeString(view: DataView, offset: number, string: string) {
  for (let i = 0; i < string.length; i++) {
    view.setUint8(offset + i, string.charCodeAt(i));
  }
}

// 导出工厂函数，替代单例实例
export function createAudioProcessor() {
  return new AudioProcessor();
} 