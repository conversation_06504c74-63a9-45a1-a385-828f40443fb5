// @ts-ignore
import lamejs from 'lamejs';

// 添加lamejs类型定义
declare module 'lamejs' {
  export default class Lame {
    static Mp3Encoder: new (channels: number, sampleRate: number, kbps: number) => {
      encodeBuffer: (left: Int16Array, right?: Int16Array) => Int8Array;
      flush: () => Int8Array;
    };
  }
}

/**
 * 将AudioBuffer转换为WAV格式
 */
const audioBufferToWav = (audioBuffer: AudioBuffer): Blob => {
  const numberOfChannels = audioBuffer.numberOfChannels;
  const sampleRate = audioBuffer.sampleRate;
  const format = 1; // PCM
  const bitDepth = 16;
  
  // 创建WAV文件头
  const headerLength = 44;
  const dataLength = audioBuffer.length * numberOfChannels * (bitDepth / 8);
  const header = new ArrayBuffer(headerLength);
  const view = new DataView(header);
  
  // RIFF chunk descriptor
  writeString(view, 0, 'RIFF');
  view.setUint32(4, 36 + dataLength, true);
  writeString(view, 8, 'WAVE');
  
  // fmt sub-chunk
  writeString(view, 12, 'fmt ');
  view.setUint32(16, 16, true); // fmt chunk length
  view.setUint16(20, format, true);
  view.setUint16(22, numberOfChannels, true);
  view.setUint32(24, sampleRate, true);
  view.setUint32(28, sampleRate * numberOfChannels * (bitDepth / 8), true);
  view.setUint16(32, numberOfChannels * (bitDepth / 8), true);
  view.setUint16(34, bitDepth, true);
  
  // data sub-chunk
  writeString(view, 36, 'data');
  view.setUint32(40, dataLength, true);
  
  // 写入音频数据
  const data = new Float32Array(audioBuffer.length * numberOfChannels);
  let offset = 0;
  
  // 交错通道数据
  for (let i = 0; i < audioBuffer.length; i++) {
    for (let channel = 0; channel < numberOfChannels; channel++) {
      data[offset++] = audioBuffer.getChannelData(channel)[i];
    }
  }
  
  // 转换为16位整数
  const intData = new Int16Array(data.length);
  for (let i = 0; i < data.length; i++) {
    const s = Math.max(-1, Math.min(1, data[i]));
    intData[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
  }
  
  return new Blob([header, intData], { type: 'audio/wav' });
};

/**
 * 将AudioBuffer转换为MP3 Blob
 * @param audioBuffer - 要转换的AudioBuffer
 * @returns Promise<Blob> - 转换后的MP3数据
 */
export const convertToMP3 = async (audioBuffer: AudioBuffer): Promise<Blob> => {
  try {
    console.log('开始转换MP3...');
    
    const channels = audioBuffer.numberOfChannels;
    const sampleRate = audioBuffer.sampleRate;
    
    // 创建MP3编码器
    const encoder = new lamejs.Mp3Encoder(channels, sampleRate, 128);
    
    // 获取音频数据
    const left = audioBuffer.getChannelData(0);
    const right = channels > 1 ? audioBuffer.getChannelData(1) : undefined;
    
    // 转换为16位整数
    const leftData = new Int16Array(left.length);
    const rightData = right ? new Int16Array(right.length) : undefined;
    
    for (let i = 0; i < left.length; i++) {
      leftData[i] = Math.max(-32768, Math.min(32767, Math.floor(left[i] * 32768)));
      if (right && rightData) {
        rightData[i] = Math.max(-32768, Math.min(32767, Math.floor(right[i] * 32768)));
      }
    }
    
    // 编码为MP3
    const mp3Data = [];
    const sampleBlockSize = 1152;
    
    for (let i = 0; i < leftData.length; i += sampleBlockSize) {
      const leftChunk = leftData.subarray(i, i + sampleBlockSize);
      const rightChunk = rightData?.subarray(i, i + sampleBlockSize);
      
      const mp3buf = rightChunk 
        ? encoder.encodeBuffer(leftChunk, rightChunk)
        : encoder.encodeBuffer(leftChunk);
        
      if (mp3buf.length > 0) {
        mp3Data.push(mp3buf);
      }
    }
    
    // 完成编码
    const mp3buf = encoder.flush();
    if (mp3buf.length > 0) {
      mp3Data.push(mp3buf);
    }
    
    // 合并所有MP3数据
    const mp3Array = new Uint8Array(
      mp3Data.reduce((total, buf) => total + buf.length, 0)
    );
    
    let offset = 0;
    for (const buf of mp3Data) {
      mp3Array.set(buf, offset);
      offset += buf.length;
    }
    
    console.log('MP3转换完成，大小:', mp3Array.length, '字节');
    return new Blob([mp3Array], { type: 'audio/mp3' });
    
  } catch (error) {
    console.error('MP3转换失败:', error);
    throw error;
  }
};

// 辅助函数：写入字符串到DataView
function writeString(view: DataView, offset: number, string: string): void {
  for (let i = 0; i < string.length; i++) {
    view.setUint8(offset + i, string.charCodeAt(i));
  }
} 