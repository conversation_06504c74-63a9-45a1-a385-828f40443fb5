import React, { createContext, useContext, useState, useEffect } from 'react';

// 工作区ID类型
export type WorkspaceId = 'live' | 'interaction' | 'product' | 'ai-model' | 'voice' | 'training' | 'text-live' | 'smart-control';

// 工作区上下文类型
interface WorkspaceContextType {
  currentWorkspace: WorkspaceId;
  setCurrentWorkspace: (workspaceId: WorkspaceId) => void;
  isWorkspaceActive: (workspaceId: WorkspaceId) => boolean;
  previousWorkspace: WorkspaceId | null;
}

// 创建工作区上下文
const WorkspaceContext = createContext<WorkspaceContextType | undefined>(undefined);

/**
 * 工作区上下文提供器组件
 * @param children 子组件
 */
export const WorkspaceProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // 从localStorage中获取保存的当前工作区
  const [currentWorkspace, setCurrentWorkspace] = useState<WorkspaceId>(() => {
    const saved = localStorage.getItem('currentWorkspace');
    return (saved as WorkspaceId) || 'live';
  });
  
  // 保存前一个工作区，用于追踪工作区切换
  const [previousWorkspace, setPreviousWorkspace] = useState<WorkspaceId | null>(null);

  // 当前工作区变更时更新localStorage和previousWorkspace
  useEffect(() => {
    try {
      localStorage.setItem('currentWorkspace', currentWorkspace);
      setPreviousWorkspace(prev => prev !== currentWorkspace ? prev : null);
    } catch (error) {
      console.error('Error saving current workspace:', error);
    }
  }, [currentWorkspace]);

  // 切换工作区
  const handleWorkspaceChange = (workspaceId: WorkspaceId) => {
    setPreviousWorkspace(currentWorkspace);
    setCurrentWorkspace(workspaceId);
  };

  // 检查指定工作区是否为当前活动工作区
  const isWorkspaceActive = (workspaceId: WorkspaceId) => {
    return currentWorkspace === workspaceId;
  };

  // 上下文值
  const contextValue: WorkspaceContextType = {
    currentWorkspace,
    setCurrentWorkspace: handleWorkspaceChange,
    isWorkspaceActive,
    previousWorkspace
  };

  return (
    <WorkspaceContext.Provider value={contextValue}>
      {children}
    </WorkspaceContext.Provider>
  );
};

/**
 * 使用工作区上下文的Hook
 * @returns 工作区上下文
 */
export const useWorkspace = (): WorkspaceContextType => {
  const context = useContext(WorkspaceContext);
  if (context === undefined) {
    throw new Error('useWorkspace must be used within a WorkspaceProvider');
  }
  return context;
}; 