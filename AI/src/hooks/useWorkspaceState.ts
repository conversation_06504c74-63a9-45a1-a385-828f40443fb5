import { useState, useEffect } from 'react';

/**
 * 工作区状态管理Hook，用于在工作区之间切换时保存和恢复状态
 * @param workspaceId 工作区ID
 * @param initialState 初始状态
 * @returns [当前状态, 设置状态的函数]
 */
export function useWorkspaceState<T>(workspaceId: string, initialState: T): [T, React.Dispatch<React.SetStateAction<T>>] {
  // 首先尝试从sessionStorage中获取已保存的状态
  const [state, setState] = useState<T>(() => {
    try {
      const savedState = sessionStorage.getItem(`workspace_${workspaceId}`);
      return savedState ? JSON.parse(savedState) : initialState;
    } catch (error) {
      console.error(`Error loading state for workspace ${workspaceId}:`, error);
      return initialState;
    }
  });

  // 跨页面/组件同步：监听自定义事件
  useEffect(() => {
    const handler = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (customEvent.detail && customEvent.detail.workspaceId === workspaceId) {
        setState(customEvent.detail.state);
      }
    };
    window.addEventListener('workspaceStateChanged', handler);
    return () => {
      window.removeEventListener('workspaceStateChanged', handler);
    };
  }, [workspaceId]);

  // 当状态更新时，将其保存到sessionStorage，并广播事件
  useEffect(() => {
    try {
      sessionStorage.setItem(`workspace_${workspaceId}`, JSON.stringify(state));
      // 广播自定义事件
      window.dispatchEvent(new CustomEvent('workspaceStateChanged', {
        detail: { workspaceId, state }
      }));
    } catch (error) {
      console.error(`Error saving state for workspace ${workspaceId}:`, error);
    }
  }, [state, workspaceId]);

  return [state, setState];
} 