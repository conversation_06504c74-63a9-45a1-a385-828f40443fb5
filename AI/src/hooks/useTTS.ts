import { useState, useEffect, useCallback, useRef } from 'react';
import { message } from 'antd';
import { createAudioProcessor } from '../utils/AudioProcessor';

// 添加 webkitAudioContext 类型声明
declare global {
  interface Window {
    webkitAudioContext: typeof AudioContext;
  }
}

/**
 * 音频队列项接口
 */
export interface AudioQueueItem {
  id: string;
  sentence: string;
  audioUrl: string;
  isPlaying: boolean;
  loading?: boolean;
  progress: number;
  duration?: number;
  currentTime?: number;
}

/**
 * 模型接口
 */
export interface Model {
  id: string;
  name: string;
}

/**
 * TTS选项接口
 */
export interface TTSOptions {
  speed: number;   // 语速范围：0.5-2.0
  volume: number;  // 音量范围：0.1-2.0
  pitch: number;   // 音调范围：-12 到 12，以半音为单位
}

/**
 * TTS状态接口
 */
export interface TTSState {
  loading: boolean;
  error: string | null;
  playing: boolean;
  models: Model[];
  currentModel: Model | null;
  options: TTSOptions;
  audioQueue: AudioQueueItem[];
  serviceStatus: {
    backend: boolean;
    indexTTS: boolean;
  };
}

// 默认选项
const defaultOptions: TTSOptions = {
  volume: 1.0,
  pitch: 0.0,  // 默认音调为0
  speed: 1.0
};

// API基础URL
const API_BASE_URL = 'http://127.0.0.1:8088';

/**
 * 检查浏览器是否支持 Web Audio API
 * @returns {boolean} 是否支持
 */
const checkAudioSupport = (): boolean => {
  try {
    // 检查 AudioContext 是否可用
    if (typeof window === 'undefined') {
      return false;
    }

    if (window.AudioContext || window.webkitAudioContext) {
      return true;
    } else {
      return false;
    }
  } catch (error) {
    console.error('检查 Web Audio API 支持时出错:', error);
    return false;
  }
};

/**
 * 创建音频上下文
 * @returns {AudioContext | null} 音频上下文或null
 */
const createAudioContext = (): AudioContext | null => {
  try {
    if (!checkAudioSupport()) {
      return null;
    }

    const AudioContextClass = window.AudioContext || window.webkitAudioContext;
    const context = new AudioContextClass();
    return context;
  } catch (error) {
    console.error('创建音频上下文失败:', error);
    return null;
  }
};

// 添加节流函数
const throttle = (func: Function, limit: number) => {
  let inThrottle: boolean;
  let lastResult: any;
  
  return function (this: any, ...args: any[]) {
    if (!inThrottle) {
      lastResult = func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
    return lastResult;
  };
};

// 修改 ExtendedAudioElement 接口
interface ExtendedAudioElement extends HTMLAudioElement {
  sourceNode?: AudioBufferSourceNode;
  gainNode?: GainNode;
  analyserNode?: AnalyserNode;
  compressorNode?: DynamicsCompressorNode;
  startTime?: number;
  customDuration?: number;
  pitchShifter?: PitchShifter;  // 保留这个属性，用于音调调整
}

// 添加 PitchShifter 类
class PitchShifter {
  public input: GainNode;  // 改为公共属性
  public output: GainNode;  // 改为公共属性
  private context: AudioContext;
  private shiftNode: AudioWorkletNode | null = null;
  private fallbackNodes: BiquadFilterNode[] = [];
  private initialized = false;
  private useFallback = false;

  constructor(context: AudioContext) {
    this.context = context;
    this.input = context.createGain();
    this.output = context.createGain();
  }

  async initialize() {
    if (this.initialized) return;
    
    try {
      // 尝试使用 AudioWorklet
      if (this.context.audioWorklet) {
        await this.initializeWorklet();
      } else {
        this.useFallback = true;
        this.initializeFallback();
      }
      this.initialized = true;
    } catch (error) {
      console.error('初始化 PitchShifter 失败，使用降级方案:', error);
      this.useFallback = true;
      this.initializeFallback();
      this.initialized = true;
    }
  }

  private async initializeWorklet() {
    try {
      const workletCode = `
        class PitchProcessor extends AudioWorkletProcessor {
          constructor() {
            super();
            this.phase = 0;
            this.bufferSize = 2048;
            this.buffer = new Float32Array(this.bufferSize);
          }

          static get parameterDescriptors() {
            return [{
              name: 'pitch',
              defaultValue: 1,
              minValue: 0.5,
              maxValue: 2
            }];
          }

          process(inputs, outputs, parameters) {
            const input = inputs[0];
            const output = outputs[0];
            const pitch = parameters.pitch[0];

            if (!input || !output || input.length === 0 || output.length === 0) {
              return true;
            }

            for (let channel = 0; channel < input.length; channel++) {
              const inputChannel = input[channel];
              const outputChannel = output[channel];

              // 使用环形缓冲区
              for (let i = 0; i < inputChannel.length; i++) {
                // 更新缓冲区
                this.buffer[i % this.bufferSize] = inputChannel[i];
                
                // 计算相位
                this.phase = (this.phase + pitch) % this.bufferSize;
                
                // 使用线性插值获取输出样本
                const position = this.phase;
                const index1 = Math.floor(position);
                const index2 = (index1 + 1) % this.bufferSize;
                const fraction = position - index1;
                
                outputChannel[i] = this.buffer[index1] * (1 - fraction) + 
                                 this.buffer[index2] * fraction;
              }
            }
            return true;
          }
        }
        registerProcessor('pitch-processor', PitchProcessor);
      `;

      const blob = new Blob([workletCode], { type: 'application/javascript' });
      const workletUrl = URL.createObjectURL(blob);

      await this.context.audioWorklet.addModule(workletUrl);
      console.log('✅ AudioWorklet 加载成功');

      this.shiftNode = new AudioWorkletNode(this.context, 'pitch-processor');
      
      // 设置初始音调
      this.shiftNode.parameters.get('pitch')?.setValueAtTime(1, this.context.currentTime);
      
      // 连接节点
      this.input.connect(this.shiftNode);
      this.shiftNode.connect(this.output);

      URL.revokeObjectURL(workletUrl);
    } catch (error) {
      console.error('AudioWorklet 初始化失败:', error);
      this.useFallback = true;
      this.initializeFallback();
    }
  }

  private initializeFallback() {
    // 创建多个 BiquadFilter 节点进行基本的音调处理
    const frequencies = [100, 200, 400, 800, 1600, 3200, 6400];
    this.fallbackNodes = frequencies.map(freq => {
      const filter = this.context.createBiquadFilter();
      filter.type = 'peaking';
      filter.frequency.value = freq;
      filter.Q.value = 1;
      return filter;
    });

    // 连接所有节点
    this.input.connect(this.fallbackNodes[0]);
    for (let i = 0; i < this.fallbackNodes.length - 1; i++) {
      this.fallbackNodes[i].connect(this.fallbackNodes[i + 1]);
    }
    this.fallbackNodes[this.fallbackNodes.length - 1].connect(this.output);
  }

  setPitch(pitch: number) {
    if (this.useFallback) {
      // 在降级模式下使用多个滤波器调整音调
      const baseGain = Math.pow(2, pitch / 12);  // 将半音转换为频率比
      this.fallbackNodes.forEach((filter, index) => {
        const frequency = filter.frequency.value;
        filter.frequency.setValueAtTime(frequency * baseGain, this.context.currentTime);
      });
    } else if (this.shiftNode) {
      // 使用 AudioWorklet 进行音调处理
      const ratio = Math.pow(2, pitch / 12);
      this.shiftNode.parameters.get('pitch')?.setValueAtTime(ratio, this.context.currentTime);
    }
  }

  connect(destination: AudioNode) {
    this.output.connect(destination);
  }

  disconnect() {
    this.output.disconnect();
  }
}

/**
 * TTS Hook
 */
export const useTTS = () => {
  // 使用 useState 管理状态
  const [state, setState] = useState<TTSState>({
    loading: false,
    error: null,
    playing: false,
    models: [],
    currentModel: null,
    options: defaultOptions,
    audioQueue: [],
    serviceStatus: {
      backend: false,
      indexTTS: false
    }
  });

  // 使用 useRef 存储音频元素
  const audioElements = useRef<{ [key: number]: ExtendedAudioElement }>({});
  const audioContextRef = useRef<AudioContext | null>(null);
  // 为每个组件创建独立的音频处理器实例
  const audioProcessorRef = useRef<ReturnType<typeof createAudioProcessor> | null>(null);

  // 获取音频上下文
  const getAudioContext = () => {
    if (!audioContextRef.current) {
      audioContextRef.current = createAudioContext();
    }
    return audioContextRef.current;
  };

  // 获取音频处理器
  const getAudioProcessor = () => {
    if (!audioProcessorRef.current) {
      audioProcessorRef.current = createAudioProcessor();
    }
    return audioProcessorRef.current;
  };

  // 更新音频参数的函数
  const updateAudioParams = useCallback((audio: ExtendedAudioElement, options: TTSOptions) => {
    if (!audio) return;

    try {
      // 语速由后端IndexTTS服务处理，前端不需要控制

      // 更新音量
      if (audio.gainNode) {
        console.log(`设置音量: ${options.volume}`);
        audio.gainNode.gain.setValueAtTime(
          options.volume,
          audio.gainNode.context.currentTime
        );
      }

      // 正确设置音调参数
      if (audio.sourceNode) {
        // 这里使用 detune 控制音调 (单位: cents, 100 cents = 1 半音)
        console.log(`设置音调: ${options.pitch} (${options.pitch * 100} cents)`);
        audio.sourceNode.detune.setValueAtTime(
          options.pitch * 100,
          audio.sourceNode.context.currentTime
        );
      }
    } catch (error) {
      console.error('更新音频参数失败:', error);
    }
  }, []);

  // 修改 setOptions 函数
  const setOptions = useCallback((newOptions: Partial<TTSOptions>) => {
    // 检查是否有正在播放的音频
    const hasPlayingAudio = state.audioQueue.some(item => item.isPlaying);
    
    // 如果有正在播放的音频且调整的不是语速，提示用户需要重新播放
    if (hasPlayingAudio && (newOptions.volume !== undefined || newOptions.pitch !== undefined)) {
      message.info('音频参数已更新，需要重新播放音频才能生效');
    }
    
    // 只更新状态，不再实时调整正在播放的音频
    setState(prev => ({
      ...prev,
      options: {
        ...prev.options,
        ...newOptions
      }
    }));
  }, [state.audioQueue]);

  /**
   * 设置当前模型
   */
  const setModel = useCallback((model: Model | null) => {
    setState(prev => ({
      ...prev,
      currentModel: model,
      error: null
    }));
  }, []);

  /**
   * 获取可用模型列表
   */
  const fetchModels = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/models`);

      if (!response.ok) {
        throw new Error(`获取模型列表失败: ${response.status}`);
      }

      const data = await response.json();
      
      if (!Array.isArray(data)) {
        throw new Error('模型数据格式错误');
      }

      const models = data.map(name => ({
        id: name,
        name: name
      }));

      return models;
    } catch (error) {
      console.error('获取模型失败:', error);
      throw error;
    }
  };

  /**
   * 检查后端服务状态
   */
  const checkBackendService = async (): Promise<boolean> => {
    try {
      // 简单的GET请求检查服务是否在线
      const response = await fetch(`${API_BASE_URL}/status`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      return response.ok;
    } catch (error) {
      console.error('后端服务检查失败:', error);
      return false;
    }
  };

  /**
   * 检查IndexTTS服务状态
   */
  const checkIndexTTSService = async (): Promise<boolean> => {
    try {
      // 简单的GET请求检查服务是否在线
      const response = await fetch(`${API_BASE_URL}/status`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      return response.ok;
    } catch (error) {
      console.error('IndexTTS服务检查失败:', error);
      return false;
    }
  };

  /**
   * 检查服务状态
   */
  const checkServices = async () => {
    try {
      // 检查后端服务
      const backendStatus = await checkBackendService();
      const indexTTSStatus = await checkIndexTTSService();
      
      // 不输出日志，只在调试时输出
      if (process.env.NODE_ENV === 'development') {
        console.debug('服务状态:', { backend: backendStatus, indexTTS: indexTTSStatus });
      }
      
      const status = {
        backend: backendStatus,
        indexTTS: indexTTSStatus
      };
      
      // 更新状态
      setState(prev => ({
        ...prev,
        serviceStatus: status
      }));
      
      return status;
      } catch (error) {
      const failedStatus = { backend: false, indexTTS: false };
      setState(prev => ({
        ...prev,
        serviceStatus: failedStatus
      }));
      return failedStatus;
    }
  };

  /**
   * 生成语音
   * @param text 需要转换的文本
   * @param isPreview 是否为试听（true时不加入audioQueue）
   * @returns 处理后的base64音频数据
   */
  const generateSpeech = async (
    text: string,
    isPreview?: boolean,
    modelIdForSpeech?: string
  ) => {
    try {
      if (!text.trim()) {
        throw new Error('请输入要转换的文本');
      }

      // 修改逻辑：如果isPreview为true且提供了modelIdForSpeech，则不强制要求currentModel存在
      if (!isPreview && !state.currentModel) {
        throw new Error('请先选择一个模型');
      }

      setState(prev => ({ ...prev, loading: true, error: null }));

      // 第一步：通过IndexTTS服务生成基础音频（应用语速）
      const response = await fetch(`${API_BASE_URL}/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          text: text.trim(),
          model: modelIdForSpeech || state.currentModel?.id, // 优先使用传入的modelIdForSpeech，安全访问currentModel的id
          speed: state.options.speed  // 语速由IndexTTS服务处理
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || '生成语音失败');
      }

      const data = await response.json();
      
      if (!data.audio) {
        throw new Error('返回的音频数据为空');
      }
      
      // 第二步：在前端应用音量和音调处理
      const audioContext = getAudioContext();
      if (!audioContext) {
        throw new Error('无法创建音频上下文');
      }
      
      // 解码获取到的音频数据
      const audioBuffer = await base64ToAudioBuffer(audioContext, data.audio);
      
      // 创建离线音频上下文用于处理
      const offlineCtx = new OfflineAudioContext(
        audioBuffer.numberOfChannels,
        audioBuffer.length,
        audioBuffer.sampleRate
      );
      
      // 创建源节点
      const sourceNode = offlineCtx.createBufferSource();
      sourceNode.buffer = audioBuffer;
      
      // 创建增益节点
      const gainNode = offlineCtx.createGain();
      
      // 设置参数
      
      // 设置音量
      gainNode.gain.value = state.options.volume;
      
      // 设置音调
      sourceNode.detune.value = state.options.pitch * 100;
      
      // 连接节点
      sourceNode.connect(gainNode);
      gainNode.connect(offlineCtx.destination);
      
      // 启动源节点
      sourceNode.start(0);
      
      // 渲染音频
      const processedBuffer = await offlineCtx.startRendering();
      
      // 将处理后的音频转换回base64编码
      const processedAudioData = await audioBufferToBase64(processedBuffer);
      
      // 只有正式生成才加入audioQueue
      if (!isPreview) {
        setState(prev => ({
          ...prev,
          audioQueue: [...prev.audioQueue, { 
            id: Date.now().toString(), 
            sentence: text, 
            audioUrl: processedAudioData, 
            isPlaying: false,
            loading: false,
            progress: 0
          }] 
        }));
      }

      return processedAudioData;
    } catch (error) {
      console.error('生成语音失败:', error);
      setState(prev => ({
        ...prev,
        loading: false, 
        error: error instanceof Error ? error.message : '生成语音失败'
      }));
      throw error;
    } finally {
      setState(prev => ({ ...prev, loading: false }));
    }
  };

  /**
   * 将 base64 音频数据转换为 AudioBuffer
   */
  const base64ToAudioBuffer = async (audioContext: AudioContext, base64Data: string): Promise<AudioBuffer> => {
    try {
      // 检查数据格式
      if (!base64Data) {
        throw new Error('音频数据为空');
      }

      // 如果数据已经包含前缀，直接使用
      const dataUrl = base64Data.startsWith('data:') ? base64Data : `data:audio/wav;base64,${base64Data}`;

      // 获取原始音频数据
      const response = await fetch(dataUrl);
      if (!response.ok) {
        throw new Error(`获取音频数据失败: ${response.status} ${response.statusText}`);
      }

      const arrayBuffer = await response.arrayBuffer();

      // 解码音频数据
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

      return audioBuffer;
    } catch (error) {
      console.error('转换音频数据失败:', error);
      throw error;
    }
  };

  /**
   * 将AudioBuffer转换为base64编码
   */
  const audioBufferToBase64 = async (audioBuffer: AudioBuffer): Promise<string> => {
    // 将AudioBuffer转换为WAV格式的Blob
    const wavBlob = audioBufferToWav(audioBuffer);
    
    // 将Blob转换为base64编码
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        if (reader.result) {
          // 提取base64数据部分
          const base64Data = reader.result.toString().split(',')[1];
          resolve(base64Data);
        } else {
          reject(new Error('转换音频数据失败'));
        }
      };
      reader.onerror = reject;
      reader.readAsDataURL(wavBlob);
    });
  };

  /**
   * 播放音频 - 简化版，不再执行实时处理
   */
  const playAudio = async (audioData: string, index: number) => {
    try {
      // 获取队列项，这是最重要的判断依据
      const queueItem = state.audioQueue[index];
      if (!queueItem) {
        throw new Error('找不到音频队列项');
      }
      
      const audioContext = getAudioContext();
      if (!audioContext) {
        throw new Error('无法创建音频上下文');
      }

      // 如果音频上下文被挂起，尝试恢复
      if (audioContext.state === 'suspended') {
        await audioContext.resume();
      }

      // 检查当前是否正在播放
      const existingAudio = audioElements.current[index];
      
      // 修改判断逻辑：只检查isPlaying状态和sourceNode存在性
      const isPlaying = queueItem.isPlaying === true;
      const hasActiveSource = existingAudio?.sourceNode !== undefined;
      const isCurrentlyPlaying = isPlaying && hasActiveSource;
      
      if (isCurrentlyPlaying) {
        stopAudio(index, false); // 传递false，表示这是暂停操作，需要保存当前位置
        return; // 直接返回，不执行后续播放逻辑
      }
      
      // 判断是否需要解码新的音频数据
      let audioBuffer: AudioBuffer;
      
      if (existingAudio?.sourceNode?.buffer) {
        // 重用现有的缓冲区
        audioBuffer = existingAudio.sourceNode.buffer;
      } else {
        // 解码新的音频数据
        audioBuffer = await base64ToAudioBuffer(audioContext, audioData);
      }
      
      // ★修改暂停后恢复逻辑 - 获取恢复位置（使用队列项中保存的当前时间）
      let resumeTime = 0;
      
      // 明确检查队列项中是否有已保存的时间
      if (queueItem.currentTime !== undefined && queueItem.currentTime > 0) {
        resumeTime = queueItem.currentTime;
      } else if (existingAudio?.currentTime !== undefined && existingAudio.currentTime > 0) {
        resumeTime = existingAudio.currentTime;
      }
      
      // 确保resumeTime在有效范围内
      if (resumeTime >= audioBuffer.duration) {
        resumeTime = 0;
      }
      
      // 创建音频源节点 - 直接使用完全处理好的音频
      const sourceNode = audioContext.createBufferSource();
      sourceNode.buffer = audioBuffer;
      
      // 直接连接到目标
      sourceNode.connect(audioContext.destination);

      // 保存节点引用
      const audio = existingAudio || new Audio();
      audio.sourceNode = sourceNode;
      audio.customDuration = audioBuffer.duration;
      
      // 更新开始时间和当前时间 (关键点)
      const actualStartTime = audioContext.currentTime;
      audio.startTime = actualStartTime;
      audio.currentTime = resumeTime; // 确保当前时间正确设置
      
      // 保存到引用
      audioElements.current[index] = audio;

      // 添加结束事件监听
      sourceNode.onended = () => {
        // 播放结束调用stopAudio并传递true，表示这是完全停止，需要重置位置
        stopAudio(index, true);
      };

      // 开始播放，从恢复位置开始
      sourceNode.start(0, resumeTime);
      
      // 更新状态 - 确保状态中的currentTime和progress与实际一致
      setState(prev => {
        const newQueue = [...prev.audioQueue];
        if (newQueue[index]) {
          newQueue[index] = { 
            ...newQueue[index], 
            isPlaying: true,
            duration: audioBuffer.duration,
            currentTime: resumeTime,
            progress: Math.min(100, (resumeTime / audioBuffer.duration) * 100)
          };
        }
        return { ...prev, audioQueue: newQueue };
      });

      // 启动一个新的进度更新循环
      let frameId: number | null = null;
      let lastProgressUpdate = audioContext.currentTime;

      const updateProgress = () => {
        // 检查音频节点是否还存在
        const currentAudio = audioElements.current[index];
        if (!currentAudio?.sourceNode) {
          if (frameId) cancelAnimationFrame(frameId);
          return;
        }

        const now = audioContext.currentTime;
        const startTime = currentAudio.startTime || actualStartTime;
        const elapsed = (now - startTime) + resumeTime;
        const duration = currentAudio.customDuration || 0;
        
        // 确保数值有效
        if (isNaN(elapsed) || isNaN(duration) || duration <= 0) {
          frameId = requestAnimationFrame(updateProgress);
          return;
        }

        // 提高更新频率以获取更平滑的进度条体验 (每25ms更新一次)
        if (now - lastProgressUpdate >= 0.025) {
          lastProgressUpdate = now;
          
          const progress = Math.min(100, (elapsed / duration) * 100);
          
          // 保存当前时间到音频对象，用于暂停后恢复
          currentAudio.currentTime = elapsed;
          
          setState(prev => {
            // 只有当该音频项还存在于队列中时才更新
            if (!prev.audioQueue[index]) {
              return prev;
            }
            
            const newQueue = [...prev.audioQueue];
            newQueue[index] = {
              ...newQueue[index],
              currentTime: elapsed,
              duration: duration,
              progress: progress
            };
            
            return { ...prev, audioQueue: newQueue };
          });
        }
        
        // 如果播放结束，停止更新
        if (elapsed >= duration) {
          if (frameId) cancelAnimationFrame(frameId);
          
          // 播放完成时调用stopAudio并传递true，表示这是完全停止，需要重置位置
          stopAudio(index, true);
          return;
        }
        
        // 继续循环
        frameId = requestAnimationFrame(updateProgress);
      };
      
      // 启动进度更新
      frameId = requestAnimationFrame(updateProgress);

    } catch (error) {
      console.error('播放音频失败:', error);
      message.error('播放音频失败');
    }
  };

  /**
   * 停止播放音频
   * @param index 音频索引
   * @param forceReset 强制重置（当播放完成时使用）
   */
  const stopAudio = (index: number, forceReset: boolean = false) => {
    try {
      const audio = audioElements.current[index];
      if (!audio) {
        return;
      }
      
      // 获取当前队列项
      const queueItem = state.audioQueue[index];
      if (!queueItem) {
        return;
      }
      
      // 计算当前时间
      let currentTime = 0;
      if (audio.sourceNode && audio.startTime !== undefined) {
        const context = audio.sourceNode.context;
        const elapsedSinceStart = context.currentTime - audio.startTime;
        currentTime = (audio.currentTime || 0) + elapsedSinceStart;
      } else if (audio.currentTime !== undefined) {
        currentTime = audio.currentTime;
      }
      
      // 停止音频源
      if (audio.sourceNode) {
        try {
          audio.sourceNode.stop();
          audio.sourceNode.disconnect();
        } catch (e) {
          console.warn('停止音频源失败:', e);
        }
        audio.sourceNode = undefined;
      }
      
      // 判断是否是暂停操作还是完全停止
      const isPauseOperation = !forceReset;
      
      if (isPauseOperation) {
        // 暂停操作：保存当前进度位置
        audio.currentTime = currentTime;
      } else {
        // 完全停止：重置所有状态
        audio.currentTime = 0;
        
        // 清除引用
        audioElements.current[index] = undefined as any;
      }
      
      // 更新状态
      setState(prev => {
        const newQueue = [...prev.audioQueue];
        if (newQueue[index]) {
          newQueue[index] = {
            ...newQueue[index],
            isPlaying: false,
            currentTime: isPauseOperation ? currentTime : 0,
            progress: isPauseOperation ? Math.min(100, (currentTime / (audio.customDuration || 1)) * 100) : 0
          };
        }
        return { ...prev, audioQueue: newQueue };
      });
      
    } catch (error) {
      console.error('停止音频失败:', error);
      message.error('停止音频失败');
    }
  };

  /**
   * 设置音频进度
   */
  const setAudioProgress = (index: number, progress: number) => {
    try {
      // 验证参数
      if (isNaN(progress) || progress < 0 || progress > 100) {
        console.error(`进度值无效: ${progress}`);
        return;
      }
      
      const audio = audioElements.current[index];
      const queueItem = state.audioQueue[index];
      
      if (!queueItem) {
        console.warn('找不到音频队列项');
        return;
      }
      
      const duration = queueItem.duration || (audio?.customDuration || 0);
      if (duration <= 0) {
        console.warn('音频持续时间无效');
        return;
      }
      
      // 计算新的播放位置 (秒)
      const newTime = (progress / 100) * duration;
      
      // 检查是否正在播放
      const isPlaying = queueItem.isPlaying;
      
      // 如果正在播放，先停止（但保留进度位置）
      if (isPlaying && audio?.sourceNode) {
        stopAudio(index, false); // 传递false，表示暂停但不重置进度
      }
      
      // 更新内存中的播放位置
      if (audio) {
        audio.currentTime = newTime;
      }
      
      // 先更新状态中的进度和时间
      setState(prev => {
        // 确保队列项存在
        if (!prev.audioQueue[index]) {
          return prev;
        }
        
        const newQueue = [...prev.audioQueue];
        newQueue[index] = {
          ...newQueue[index],
          currentTime: newTime,
          progress: progress
        };
        
        return { ...prev, audioQueue: newQueue };
      });
      
      // 如果原来在播放，则立即从新位置继续播放
      if (isPlaying && queueItem.audioUrl) {
        // 给系统一点时间来更新状态，以确保播放时使用新位置
        setTimeout(() => {
          try {
            // 重新获取队列项，确保使用最新状态
            const updatedQueueItem = state.audioQueue[index];
            if (updatedQueueItem && updatedQueueItem.audioUrl) {
              playAudio(updatedQueueItem.audioUrl, index);
            } else {
              console.error(`找不到要播放的音频队列项 ${index}`);
            }
          } catch (error) {
            console.error('从新位置播放失败:', error);
          }
        }, 150);  // 增加延迟，确保状态更新完成
      }
      
    } catch (error) {
      console.error('设置音频进度失败:', error);
      message.error('设置音频进度失败');
    }
  };

  /**
   * 移除指定索引的音频
   */
  const removeAudio = useCallback((index: number) => {
    // 如果正在播放，先停止
    if (audioElements.current[index]) {
      audioElements.current[index].pause();
      delete audioElements.current[index];
    }

    setState(prev => ({ 
      ...prev, 
      audioQueue: prev.audioQueue.filter((_, i) => i !== index),
    }));
  }, []);

  // 初始化时检查服务状态
  useEffect(() => {
    const initializeServices = async () => {
      try {
        // 检查服务状态
        const status = await checkServices();
      setState(prev => ({
        ...prev,
          serviceStatus: status
        }));

        // 如果后端服务正常，获取模型列表
        if (status.backend) {
          const modelList = await fetchModels();
          setState(prev => ({
            ...prev,
            models: modelList
          }));
        }
    } catch (error) {
        console.error('初始化服务失败:', error);
        setState(prev => ({
          ...prev,
          error: '初始化服务失败'
        }));
      }
    };

    initializeServices();

    // 定期检查服务状态
    const interval = setInterval(async () => {
      // 只在开发环境下输出日志
      if (process.env.NODE_ENV === 'development') {
        console.debug('检查服务状态...');
      }
      const status = await checkServices();
      setState(prev => ({
        ...prev,
        serviceStatus: status
      }));
    }, 30000); // 增加到30秒检查一次

    return () => clearInterval(interval);
  }, []);

  // 在组件初始化时检查并创建音频上下文
  useEffect(() => {
    if (!audioContextRef.current) {
      audioContextRef.current = createAudioContext();
    }

    const handleClick = async () => {
      if (audioContextRef.current?.state === 'suspended') {
        try {
          await audioContextRef.current.resume();
        } catch (error) {
          console.error('恢复音频上下文失败:', error);
        }
      }
    };

    document.addEventListener('click', handleClick);

    return () => {
      document.removeEventListener('click', handleClick);
      
      // 清理所有音频元素
      Object.values(audioElements.current).forEach(audio => {
        if (audio.sourceNode) {
          try {
            audio.sourceNode.stop();
            audio.sourceNode.disconnect();
          } catch (e) {
            // 忽略已停止的音频报错
          }
        }
      });
      
      // 关闭音频处理器
      if (audioProcessorRef.current) {
        audioProcessorRef.current.close();
        audioProcessorRef.current = null;
      }
      
      // 关闭音频上下文
      if (audioContextRef.current) {
        audioContextRef.current.close().catch(console.error);
        audioContextRef.current = null;
      }
    };
  }, []);

  /**
   * 将AudioBuffer转换为WAV格式
   * @param audioBuffer 要转换的音频缓冲区
   * @returns WAV格式的Blob对象
   */
  const audioBufferToWav = (audioBuffer: AudioBuffer): Blob => {
    const numOfChan = audioBuffer.numberOfChannels;
    const length = audioBuffer.length * numOfChan * 2;
    const buffer = new ArrayBuffer(44 + length);
    const view = new DataView(buffer);
    
    // RIFF chunk descriptor
    writeUTFBytes(view, 0, 'RIFF');
    view.setUint32(4, 36 + length, true);
    writeUTFBytes(view, 8, 'WAVE');
    
    // FMT sub-chunk
    writeUTFBytes(view, 12, 'fmt ');
    view.setUint32(16, 16, true); // 子块大小, PCM=16
    view.setUint16(20, 1, true); // 音频格式 (1 为 PCM)
    view.setUint16(22, numOfChan, true); // 通道数
    view.setUint32(24, audioBuffer.sampleRate, true); // 采样率
    view.setUint32(28, audioBuffer.sampleRate * 2 * numOfChan, true); // 字节率
    view.setUint16(32, numOfChan * 2, true); // 块对齐
    view.setUint16(34, 16, true); // 每个样本位数
    
    // Data sub-chunk
    writeUTFBytes(view, 36, 'data');
    view.setUint32(40, length, true);
    
    // 写入实际采样数据
    const dataOffset = 44;
    let offset = 0;
    
    for (let i = 0; i < audioBuffer.numberOfChannels; i++) {
      const channelData = audioBuffer.getChannelData(i);
      for (let j = 0; j < channelData.length; j++) {
        const sample = Math.max(-1, Math.min(1, channelData[j]));
        const sampleValue = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
        view.setInt16(dataOffset + offset, sampleValue, true);
        offset += 2;
      }
    }
    
    return new Blob([buffer], { type: 'audio/wav' });
  };

  /**
   * 辅助函数：写入UTF-8字符串
   */
  const writeUTFBytes = (view: DataView, offset: number, string: string) => {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i));
    }
  };

  /**
   * 下载处理后的音频
   * @param index 音频索引
   */
  const downloadProcessedAudio = async (index: number) => {
    try {
      console.log(`准备下载处理后的音频 ${index}`);
      const queueItem = state.audioQueue[index];
      if (!queueItem || !queueItem.audioUrl) {
        throw new Error('找不到音频数据');
      }
      
      const audioContext = getAudioContext();
      if (!audioContext) {
        throw new Error('无法创建音频上下文');
      }
      
      // 解码音频数据
      const audioBuffer = await base64ToAudioBuffer(audioContext, queueItem.audioUrl);
      
      // 创建离线音频上下文用于处理
      const offlineCtx = new OfflineAudioContext(
        audioBuffer.numberOfChannels,
        audioBuffer.length,
        audioBuffer.sampleRate
      );
      
      // 创建源节点
      const sourceNode = offlineCtx.createBufferSource();
      sourceNode.buffer = audioBuffer;
      
      // 创建增益节点
      const gainNode = offlineCtx.createGain();
      
      // 应用当前的音频处理参数 - 注意语速已经由IndexTTS服务在生成音频时处理
      console.log(`应用处理参数 - 音量: ${state.options.volume}, 音调: ${state.options.pitch}`);
      
      // 设置音量
      gainNode.gain.value = state.options.volume;
      
      // 设置音调
      sourceNode.detune.value = state.options.pitch * 100;
      
      // 连接节点
      sourceNode.connect(gainNode);
      gainNode.connect(offlineCtx.destination);
      
      // 启动源节点
      sourceNode.start(0);
      
      // 渲染音频
      console.log('开始离线渲染音频...');
      const renderedBuffer = await offlineCtx.startRendering();
      console.log('音频渲染完成');
      
      // 转换为WAV格式
      const wavBlob = audioBufferToWav(renderedBuffer);
      
      // 创建下载链接
      const a = document.createElement('a');
      a.href = URL.createObjectURL(wavBlob);
      a.download = `音频_${index}_处理后.wav`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(a.href);
      
      message.success('音频下载成功');
    } catch (error) {
      console.error('下载处理后的音频失败:', error);
      message.error('下载音频失败');
    }
  };

  /**
   * 刷新模型列表
   */
  const refreshModels = useCallback(async () => {
    try {
      const modelList = await fetchModels();
      setState(prev => ({
        ...prev,
        models: modelList
      }));
    } catch (error) {
      console.error('刷新模型列表失败:', error);
      setState(prev => ({
        ...prev,
        error: '刷新模型列表失败'
      }));
    }
  }, []);

  return {
    ...state,
    setModel,
    setOptions,
    generateSpeech,
    playAudio,
    stopAudio,
    removeAudio,
    setAudioProgress,
    downloadProcessedAudio,
    refreshModels
  };
};