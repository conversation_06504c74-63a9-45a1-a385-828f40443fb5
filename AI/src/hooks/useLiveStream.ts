import { useState, useCallback, useEffect } from 'react';
import { TTSOptions } from './useTTS';

interface LiveStreamState {
  isLive: boolean;
  isPublicScreenConnected: boolean;
  scriptContent: string;
  currentScript: string;
  isPlaying: boolean;
  playbackProgress: number;
  currentTime: string;
  totalTime: string;
  currentPlayingText: string;
}

export const useLiveStream = (options: TTSOptions) => {
  // 初始化时从localStorage读取缓存的脚本内容
  const getInitialScriptContent = () => {
    try {
      // 优先读取实时直播的脚本内容
      const realtimeContent = localStorage.getItem('realtimeScriptContent');
      if (realtimeContent) {
        return realtimeContent;
      }
      // 兼容旧版本的scriptContent
      const legacyContent = localStorage.getItem('scriptContent');
      return legacyContent || '';
    } catch (error) {
      console.error('读取脚本缓存失败:', error);
      return '';
    }
  };

  const [state, setState] = useState<LiveStreamState>({
    isLive: false,
    isPublicScreenConnected: false,
    scriptContent: getInitialScriptContent(),
    currentScript: '',
    isPlaying: true,
    playbackProgress: 0,
    currentTime: '0:00',
    totalTime: '0:00',
    currentPlayingText: ''
  });

  const toggleLiveStream = useCallback(() => {
    setState(prev => ({ ...prev, isLive: !prev.isLive }));
  }, []);

  const togglePublicScreen = useCallback(() => {
    setState(prev => ({ ...prev, isPublicScreenConnected: !prev.isPublicScreenConnected }));
  }, []);

  const setScriptContent = useCallback((content: string) => {
    setState(prev => ({ ...prev, scriptContent: content }));
    // 自动保存到localStorage
    try {
      localStorage.setItem('realtimeScriptContent', content);
      localStorage.setItem('liveMode', 'realtime');
    } catch (error) {
      console.error('保存脚本缓存失败:', error);
    }
  }, []);

  const setCurrentScript = useCallback((script: string) => {
    setState(prev => ({ ...prev, currentScript: script }));
  }, []);

  const togglePlayback = useCallback(() => {
    setState(prev => ({ ...prev, isPlaying: !prev.isPlaying }));
  }, []);

  const setPlaybackProgress = useCallback((progress: number) => {
    setState(prev => ({ ...prev, playbackProgress: progress }));
  }, []);

  const setCurrentTime = useCallback((time: string) => {
    setState(prev => ({ ...prev, currentTime: time }));
  }, []);

  const setTotalTime = useCallback((time: string) => {
    setState(prev => ({ ...prev, totalTime: time }));
  }, []);

  const setCurrentPlayingText = useCallback((text: string) => {
    setState(prev => ({ ...prev, currentPlayingText: text }));
  }, []);

  // 监听localStorage变化，支持跨标签页同步
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'realtimeScriptContent' && e.newValue !== null) {
        setState(prev => ({ ...prev, scriptContent: e.newValue || '' }));
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  return {
    ...state,
    toggleLiveStream,
    togglePublicScreen,
    setScriptContent,
    setCurrentScript,
    togglePlayback,
    setPlaybackProgress,
    setCurrentTime,
    setTotalTime,
    setCurrentPlayingText
  };
};