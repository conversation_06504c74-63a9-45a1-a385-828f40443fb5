import { useState, useCallback, useRef } from 'react';

/**
 * @interface IndexTTSOptions
 * @description IndexTTS服务的配置选项
 */
export interface IndexTTSOptions {
  /** 选择的音色ID */
  model: string;
  /** 语速 (0.5-2.0) */
  speed: number;
  /** 要转换的文本内容 */
  text: string;
  /** 是否为插话内容 */
  isInterrupt?: boolean;
}

/**
 * @interface AudioState
 * @description 音频状态接口
 */
export interface AudioState {
  /** 音频URL */
  audioUrl: string | null;
  /** 是否正在播放 */
  isPlaying: boolean;
  /** 音频总时长(秒) */
  duration: number;
  /** 当前播放时间(秒) */
  currentTime: number;
  /** 错误信息 */
  error: string | null;
  /** 当前正在处理的句子索引 */
  currentSentenceIndex: number;
  /** 总句子数 */
  totalSentences: number;
  /** 生成进度 (0-100) */
  progress: number;
  /** 当前播放的文本内容 */
  currentText: string;
  /** 音量 */
  volume: number;
  /** 是否正在中断 */
  isInterrupt: boolean;
}

/**
 * 分析句子，判断是否使用助播音色
 * @param sentence 句子文本
 * @returns 处理后的信息
 */
const analyzeAssistantContent = (sentence: string) => {
  // 检查是否包含助播标记
  const assistantRegex = /\[助:(.*?)\]/;
  const match = sentence.match(assistantRegex);
  
  if (match) {
    // 提取助播内容并替换原标记
    const assistantContent = match[1];
    const cleanSentence = sentence.replace(match[0], assistantContent);
    return {
      text: cleanSentence,
      isAssistant: true
    };
  }
  
  return {
    text: sentence,
    isAssistant: false
  };
};

/**
 * 将文本按多种常见句子结束符分割成句子，并处理助播标记
 * 改进：避免分割数字中的小数点
 * @param text 要分割的文本
 * @returns 句子数组和是否使用助播音色的标记
 */
export const splitTextToSentences = (text: string): Array<{text: string, isAssistant: boolean}> => {
  // 首先按照助播标记分割文本
  const assistantPattern = /\[助:(.*?)\]/g;
  const parts: Array<{text: string, isAssistant: boolean}> = [];
  let lastIndex = 0;
  let match;
  while ((match = assistantPattern.exec(text)) !== null) {
    if (match.index > lastIndex) {
      const beforeText = text.substring(lastIndex, match.index);
      if (beforeText.trim()) {
        parts.push({ text: beforeText, isAssistant: false });
      }
    }
    const assistantContent = match[1];
    if (assistantContent.trim()) {
      parts.push({ text: assistantContent, isAssistant: true });
    }
    lastIndex = match.index + match[0].length;
  }
  if (lastIndex < text.length) {
    const remainingText = text.substring(lastIndex);
    if (remainingText.trim()) {
      parts.push({ text: remainingText, isAssistant: false });
    }
  }
  const result: Array<{text: string, isAssistant: boolean}> = [];
  const reg = /([。！？；，!;?…]+|(?<!\d)\.(?!\\d))/g;
  for (let partIdx = 0; partIdx < parts.length; partIdx++) {
    const part = parts[partIdx];
    // 助播内容直接单独分一段
    if (part.isAssistant) {
      if (part.text.trim()) {
        result.push({ text: part.text.trim(), isAssistant: true });
      }
      continue;
    }
    // 主播内容按标点分句并合并短句
    const subParts = part.text.split(reg);
    const rawSentences: string[] = [];
    for (let i = 0; i < subParts.length; i++) {
      const content = subParts[i];
      if (content && content.trim()) {
        if (i + 1 < subParts.length && subParts[i+1].match(reg)) {
          rawSentences.push(content.trim() + subParts[i+1]);
          i++;
        } else {
          rawSentences.push(content.trim());
        }
      } else if (content && content.match(reg)) {
        if (rawSentences.length > 0) {
          rawSentences[rawSentences.length - 1] += content;
        } else {
          rawSentences.push(content);
        }
      }
    }
    // 合并短句，最小长度30个字，遇到下一个 part 是助播内容时强制分段
    let buffer = '';
    let bufferLen = 0;
    const minLen = 30;
    for (let i = 0; i < rawSentences.length; i++) {
      let s = rawSentences[i];
      buffer += s;
      bufferLen += s.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '').length;
      // 判断下一个 part 是否是助播内容
      const isNextPartAssistant = (i === rawSentences.length - 1) && (partIdx + 1 < parts.length) && parts[partIdx + 1].isAssistant;
      if ((bufferLen >= minLen && buffer.trim().length > 0) || i === rawSentences.length - 1 || isNextPartAssistant) {
        if (buffer.trim()) {
          result.push({ text: buffer.trim(), isAssistant: false });
        }
        buffer = '';
        bufferLen = 0;
      }
    }
    if (buffer.trim()) {
      result.push({ text: buffer.trim(), isAssistant: false });
    }
  }
  // 如果最后一句很短，合并到上一句（仅主播内容，且不能跨助播）
  const minLen = 30;
  if (result.length > 1) {
    const last = result[result.length - 1];
    const lastLen = last.text.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '').length;
    if (lastLen < minLen && !last.isAssistant) {
      for (let i = result.length - 2; i >= 0; i--) {
        if (!result[i].isAssistant) {
          result[i].text += last.text;
          result.pop();
          break;
        } else {
          // 遇到助播内容就不合并
          break;
        }
      }
    }
  }
  return result.filter(item => item.text.trim().length > 0);
};

/**
 * @function useIndexTTS
 * @description 处理IndexTTS服务的自定义Hook
 */
export const useIndexTTS = () => {
  // 音频状态
  const [audioState, setAudioState] = useState<AudioState>({
    audioUrl: null,
    isPlaying: false,
    duration: 0,
    currentTime: 0,
    error: null,
    currentSentenceIndex: 0,
    totalSentences: 0,
    progress: 0,
    currentText: '',
    volume: 1,
    isInterrupt: false
  });

  // 加载状态
  const [isLoading, setIsLoading] = useState(false);

  // 使用 useRef 来存储当前的音频实例
  const audioRef = useRef<HTMLAudioElement | null>(null);
  // 存储音频队列
  const audioQueueRef = useRef<string[]>([]);
  // 存储文本队列，添加isAssistant标记表示是否使用助播音色
  const sentencesRef = useRef<Array<{text: string, isAssistant: boolean}>>([]);
  // 存储是否正在生成音频
  const isGeneratingRef = useRef(false);

  /**
   * 设置音量
   */
  const setVolume = useCallback((value: number) => {
    const volume = Math.max(0, Math.min(1, value / 100));
    if (audioRef.current) {
      audioRef.current.volume = volume;
    }
    setAudioState(prev => ({
      ...prev,
      volume: volume
    }));
  }, []);

  /**
   * 生成单个句子的音频
   */
  const generateSingleAudio = useCallback(async (options: IndexTTSOptions & { isAssistant?: boolean, assistantModel?: string, speed: number }): Promise<string | null> => {
    try {
      // 设置超时时间为 2 分钟
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 120000);
      
      // 如果是助播内容且提供了助播模型，则使用助播模型
      const modelToUse = options.isAssistant && options.assistantModel ? 
        options.assistantModel : options.model;
      
      const response = await fetch('http://127.0.0.1:8088/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: modelToUse,
          text: options.text,
          speed: options.speed,
          volume: 1.0,
          pitch: 0.0
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        // 处理错误...
        throw new Error(`生成音频失败: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      if (!data.audio) {
        throw new Error('服务返回数据格式错误');
      }

      return data.audio;
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('生成音频超时，请尝试减少文本长度');
        }
        throw error;
      }
      throw new Error('生成音频时发生未知错误');
    }
  }, []);

  /**
   * 播放下一个音频
   */
  const playNextAudio = useCallback(async () => {
    if (audioQueueRef.current.length === 0) {
      setAudioState(prev => ({
        ...prev,
        isPlaying: false,
        currentTime: 0,
        progress: 100,
        currentText: '',
        isInterrupt: false
      }));
      return;
    }
    const audioUrl = audioQueueRef.current[0];
    const currentSentence = sentencesRef.current[0];
    if (!audioUrl || !currentSentence) return;
    try {
      if (audioRef.current) {
        audioRef.current.pause();
      }
      const audio = new Audio(audioUrl);
      audioRef.current = audio;
      await new Promise((resolve, reject) => {
        const handleLoad = () => {
          audio.removeEventListener('loadeddata', handleLoad);
          audio.removeEventListener('error', handleError);
          resolve(null);
        };
        const handleError = (e: ErrorEvent) => {
          audio.removeEventListener('loadeddata', handleLoad);
          audio.removeEventListener('error', handleError);
          reject(new Error('音频加载失败'));
        };
        audio.addEventListener('loadeddata', handleLoad);
        audio.addEventListener('error', handleError);
      });
      const updateProgress = () => {
        setAudioState(prev => ({
          ...prev,
          currentTime: audio.currentTime
        }));
      };
      const handleEnded = () => {
        audio.removeEventListener('timeupdate', updateProgress);
        audio.removeEventListener('ended', handleEnded);
        audioRef.current = null;
        audioQueueRef.current.shift();
        sentencesRef.current.shift();
        setAudioState(prev => ({
          ...prev,
          currentSentenceIndex: prev.currentSentenceIndex + 1,
          isInterrupt: false
        }));
        playNextAudio();
      };
      audio.addEventListener('timeupdate', updateProgress);
      audio.addEventListener('ended', handleEnded);
      setAudioState(prev => ({
        ...prev,
        audioUrl,
        duration: audio.duration || 0,
        currentTime: 0,
        isPlaying: true,
        currentText: currentSentence.text,
        isInterrupt: false
      }));
      audio.volume = audioState.volume;
      await audio.play();
    } catch (error) {
      console.error('播放音频失败:', error);
      setAudioState(prev => ({
        ...prev,
        error: '播放音频失败',
        isPlaying: false
      }));
    }
  }, [audioState.volume]);

  /**
   * 插入插话音频到队列
   */
  const insertInterruptAudio = useCallback(async (options: IndexTTSOptions & { isAssistant?: boolean, assistantModel?: string }) => {
    try {
      const analyzed = analyzeAssistantContent(options.text);
      const audioUrl = await generateSingleAudio({
        ...options,
        text: analyzed.text,
        isAssistant: analyzed.isAssistant,
        speed: options.speed
      });
      if (!audioUrl) {
        throw new Error('插话音频生成失败');
      }
      const isQueueEmpty = audioQueueRef.current.length === 0;
      const insertIndex = isQueueEmpty ? 0 : 1;
      audioQueueRef.current.splice(insertIndex, 0, audioUrl);
      sentencesRef.current.splice(insertIndex, 0, analyzed);
      setAudioState(prev => ({
        ...prev,
        isInterrupt: true
      }));
      if (isQueueEmpty) {
        await playNextAudio();
      }
      return audioUrl;
    } catch (error) {
      console.error('插入插话音频失败:', error);
      setAudioState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : '插入插话音频失败'
      }));
      return null;
    }
  }, [generateSingleAudio, playNextAudio]);

  /**
   * 生成音频
   * @param options IndexTTSOptions
   */
  const generateAudio = useCallback(async (options: IndexTTSOptions & { assistantModel?: string, getCurrentSpeed?: () => number, bufferSize?: number }) => {
    try {
      if (options.isInterrupt) {
        return await insertInterruptAudio(options);
      }
      if (isGeneratingRef.current) return null;
      isGeneratingRef.current = true;
      setIsLoading(true);
      setAudioState(prev => ({
        ...prev,
        error: null,
        currentSentenceIndex: 0,
        progress: 0,
        currentText: '',
        isInterrupt: false
      }));
      audioQueueRef.current = [];
      sentencesRef.current = [];
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
      const sentences = splitTextToSentences(options.text);
      if (sentences.length === 0) {
        throw new Error('没有可以生成的文本');
      }
      setAudioState(prev => ({
        ...prev,
        totalSentences: sentences.length
      }));
      let hasError = false;
      // const bufferSize = 3;
      const bufferSize = typeof options.bufferSize === 'number'
        ? options.bufferSize
        : (Number(localStorage.getItem('live-bufferSize')) || 3);
      const minSentences = Math.min(bufferSize, sentences.length);
      for (let i = 0; i < minSentences; i++) {
        try {
          const sentence = sentences[i];
          const speed = options.getCurrentSpeed ? options.getCurrentSpeed() : options.speed;
          const audioUrl = await generateSingleAudio({
            ...options,
            text: sentence.text,
            isAssistant: sentence.isAssistant,
            speed
          });
          if (audioUrl) {
            audioQueueRef.current.push(audioUrl);
            sentencesRef.current.push(sentence);
          }
        } catch (error) {
          hasError = true;
          setAudioState(prev => ({
            ...prev,
            error: error instanceof Error ? error.message : `预缓冲第 ${i + 1} 句失败`
          }));
          return null;
        }
      }
      if (audioQueueRef.current.length > 0) {
        await playNextAudio();
        for (let i = minSentences; i < sentences.length; i++) {
          try {
            setAudioState(prev => ({
              ...prev,
              error: null,
              progress: ((i + 1) / sentences.length) * 100
            }));
            const sentence = sentences[i];
            const speed = options.getCurrentSpeed ? options.getCurrentSpeed() : options.speed;
            const audioUrl = await generateSingleAudio({
              ...options,
              text: sentence.text,
              isAssistant: sentence.isAssistant,
              speed
            });
            if (audioUrl) {
              audioQueueRef.current.push(audioUrl);
              sentencesRef.current.push(sentence);
            }
          } catch (error) {
            hasError = true;
          }
        }
      }
      if (hasError) {
        setAudioState(prev => ({
          ...prev,
          error: '部分句子生成失败，但其他句子可以正常播放'
        }));
      }
      return audioQueueRef.current[0] || null;
    } catch (error) {
      setAudioState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : '生成音频时发生错误'
      }));
      return null;
    } finally {
      setIsLoading(false);
      isGeneratingRef.current = false;
    }
  }, [insertInterruptAudio, generateSingleAudio, playNextAudio]);

  /**
   * 暂停播放
   */
  const pauseAudio = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause();
      setAudioState(prev => ({
        ...prev,
        isPlaying: false
      }));
    }
  }, []);

  /**
   * 继续播放
   */
  const resumeAudio = useCallback(() => {
    if (!audioRef.current && audioState.audioUrl) {
      // 如果没有音频实例但有URL，创建新的音频实例
      const audio = new Audio(audioState.audioUrl);
      audio.currentTime = audioState.currentTime;
      audio.volume = audioState.volume;
      
      // 添加事件监听
      audio.addEventListener('timeupdate', () => {
        setAudioState(prev => ({
          ...prev,
          currentTime: audio.currentTime,
          progress: (audio.currentTime / audio.duration) * 100
        }));
      });

      audio.addEventListener('ended', () => {
        setAudioState(prev => ({
          ...prev,
          isPlaying: false,
          currentTime: 0,
          progress: 0
        }));
      });

      audioRef.current = audio;
    }

    if (audioRef.current) {
      audioRef.current.play().then(() => {
        setAudioState(prev => ({
          ...prev,
          isPlaying: true
        }));
      }).catch(error => {
        console.error('继续播放失败:', error);
        setAudioState(prev => ({
          ...prev,
          error: '继续播放失败，请重试'
        }));
      });
    }
  }, [audioState.audioUrl, audioState.currentTime, audioState.volume]);

  /**
   * 设置播放进度
   */
  const setProgress = useCallback((time: number) => {
    if (audioRef.current) {
      audioRef.current.currentTime = time;
      setAudioState(prev => ({
        ...prev,
        currentTime: time,
        progress: (time / audioRef.current!.duration) * 100
      }));
    }
  }, []);

  /**
   * 重置所有状态
   */
  const reset = useCallback(() => {
    // 停止并清理当前音频实例
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.removeEventListener('timeupdate', () => {});
      audioRef.current.removeEventListener('ended', () => {});
      audioRef.current = null;
    }

    // 清空音频队列
    audioQueueRef.current = [];
    sentencesRef.current = [];
    
    // 重置生成状态
    isGeneratingRef.current = false;

    // 重置音频状态
    setAudioState({
      audioUrl: null,
      isPlaying: false,
      duration: 0,
      currentTime: 0,
      error: null,
      currentSentenceIndex: 0,
      totalSentences: 0,
      progress: 0,
      currentText: '',
      volume: 1,  // 保持默认音量
      isInterrupt: false
    });

    setIsLoading(false);
  }, []);

  /**
   * 追加文本到音频队列，不打断当前播放
   * @param options IndexTTSOptions
   * @returns {Promise<void>}
   */
  const appendAudioToQueue = useCallback(async (options: IndexTTSOptions & { assistantModel?: string, getCurrentSpeed?: () => number }) => {
    try {
      const sentences = splitTextToSentences(options.text);
      for (const sentence of sentences) {
        const speed = options.getCurrentSpeed ? options.getCurrentSpeed() : options.speed;
        const audioUrl = await generateSingleAudio({
          ...options, 
          text: sentence.text,
          isAssistant: sentence.isAssistant,
          speed
        });
        if (audioUrl) {
          audioQueueRef.current.push(audioUrl);
          sentencesRef.current.push(sentence);
        }
      }
      if (!audioState.isPlaying && audioQueueRef.current.length > 0) {
        playNextAudio();
      }
    } catch (error) {
      setAudioState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : '追加音频队列失败'
      }));
    }
  }, [audioState.isPlaying, playNextAudio, generateSingleAudio]);

  // 新增：直接批量分段生成音频并入队
  const generateSegmentsAudio = useCallback(async (
    segments: Array<{ text: string, isAssistant: boolean }>,
    options: { model: string, assistantModel?: string, speed: number }
  ) => {
    reset();
    for (const seg of segments) {
      const audioUrl = await generateSingleAudio({
        model: seg.isAssistant ? options.assistantModel || options.model : options.model,
        text: seg.text,
        isAssistant: seg.isAssistant,
        speed: options.speed,
      });
      if (audioUrl) {
        audioQueueRef.current.push(audioUrl);
        sentencesRef.current.push(seg);
      }
    }
    if (audioQueueRef.current.length > 0) {
      playNextAudio();
    }
  }, [reset, playNextAudio, generateSingleAudio]);

  const appendSegmentsToQueue = useCallback(async (
    segments: Array<{ text: string, isAssistant: boolean }>,
    options: { model: string, assistantModel?: string, speed: number }
  ) => {
    for (const seg of segments) {
      const audioUrl = await generateSingleAudio({
        model: seg.isAssistant ? options.assistantModel || options.model : options.model,
        text: seg.text,
        isAssistant: seg.isAssistant,
        speed: options.speed,
      });
      if (audioUrl) {
        audioQueueRef.current.push(audioUrl);
        sentencesRef.current.push(seg);
      }
    }
    if (!audioState.isPlaying && audioQueueRef.current.length > 0) {
      playNextAudio();
    }
  }, [audioState.isPlaying, playNextAudio, generateSingleAudio]);

  return {
    audioState,
    setAudioState,
    isLoading,
    generateAudio,
    pauseAudio,
    resumeAudio,
    setProgress,
    reset,
    setVolume,
    audioRef,
    appendAudioToQueue,
    generateSegmentsAudio,
    appendSegmentsToQueue
  };
}; 