import { useEffect, useCallback } from 'react';

/**
 * 快捷键配置项接口
 */
interface HotkeyConfig {
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  handler: () => void;
}

/**
 * 快捷键hook
 * @param hotkeys - 快捷键配置数组
 */
const useHotkeys = (hotkeys: HotkeyConfig[]) => {
  /**
   * 处理键盘事件
   */
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      hotkeys.forEach(({ key, ctrlKey, altKey, shiftKey, handler }) => {
        if (
          event.key.toLowerCase() === key.toLowerCase() &&
          (!ctrlKey || event.ctrlKey) &&
          (!altKey || event.altKey) &&
          (!shiftKey || event.shiftKey)
        ) {
          event.preventDefault();
          handler();
        }
      });
    },
    [hotkeys]
  );

  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);
};

export default useHotkeys; 