import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import { AIModelProvider } from './contexts/AIModelContext';

// 屏蔽MUI警告和其他不必要的警告消息
if (process.env.NODE_ENV !== 'production') {
  // 重写console.warn方法，过滤掉特定的警告消息
  const originalConsoleWarn = console.warn;
  console.warn = (...args) => {
    // 忽略MUI警告：选择了范围外的值（音色模型警告）
    if (typeof args[0] === 'string' && 
        (args[0].includes('out-of-range') || 
         args[0].includes('家电大清哥.pt') || 
         args[0].includes('通品李承泽.pt'))) {
      return;
    }
    originalConsoleWarn(...args);
  };
  
  // 同样屏蔽console.error中的某些消息
  const originalConsoleError = console.error;
  console.error = (...args) => {
    // 忽略特定错误消息
    if (typeof args[0] === 'string' && 
        (args[0].includes('Warning: MUI:') || 
         args[0].includes('Invalid prop'))) {
      return;
    }
    originalConsoleError(...args);
  };
}

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <AIModelProvider>
      <App />
    </AIModelProvider>
  </React.StrictMode>
); 