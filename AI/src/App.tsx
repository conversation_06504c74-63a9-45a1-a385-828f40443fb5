import React, { useState, useEffect, useRef } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useTTS } from './hooks/useTTS';
import Sidebar from './components/Layout/Sidebar';
import { VoiceWorkspace } from './components/Workspace/VoiceWorkspace';
import { LiveStreamWorkspace } from './components/Workspace/LiveStreamWorkspace';
import { InteractionWorkspace } from './components/Workspace/InteractionWorkspace';
import { ProductWorkspace } from './components/Workspace/ProductWorkspace';
import TrainingWorkspace from './components/Workspace/TrainingWorkspace';
import { AIModelWorkspace } from './components/Workspace/AIModelWorkspace';
import { WorkspaceProvider, useWorkspace, WorkspaceId } from './context/WorkspaceContext';
import { LiveReplyContext } from './contexts/LiveReplyContext';
import { LiveConnectionProvider } from './contexts/LiveConnectionContext';
import { ClickAwayListener, Grow, Paper, Popper, <PERSON>uItem, MenuList } from '@mui/material';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Person } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import RegisterPage from './pages/RegisterPage';
import ForgotPasswordPage from './pages/ForgotPasswordPage';
import { useAuth } from './contexts/AuthContext';
import { AuthProvider } from './contexts/AuthContext';
import { TextLiveWorkspace } from './components/Workspace/TextLiveWorkspace';
import { SmartControlWorkspace } from './components/Workspace/SmartControlWorkspace';

// 用户类型
interface User {
  username: string;
  token: string;
}

/**
 * 工作区容器组件，负责有条件地渲染工作区
 */
const WorkspaceContainer: React.FC = () => {
  const { currentWorkspace } = useWorkspace();
  const {
    models,
    currentModel,
    loading,
    error,
    audioQueue,
    serviceStatus,
    options,
    setOptions,
    setModel,
    generateSpeech,
    playAudio,
    stopAudio,
    removeAudio,
    setAudioProgress,
    downloadProcessedAudio,
    refreshModels
  } = useTTS();

  // 用 ref 存储 insertLiveReply 实现（支持 text, type 多个参数）
  const liveReplyRef = React.useRef<(text: string, type?: 'welcome' | 'time' | 'count' | 'broadcast' | 'interactionReply') => void>(() => {});

  // 提供 setter 给 Workspace 注册
  const setInsertLiveReply = (fn: (text: string, type?: 'welcome' | 'time' | 'count' | 'broadcast' | 'interactionReply') => void) => {
    liveReplyRef.current = fn;
  };

  // 供全局调用，支持 type
  const insertLiveReply = (text: string, type?: 'welcome' | 'time' | 'count' | 'broadcast' | 'interactionReply') => {
    liveReplyRef.current(text, type);
  };

  // 渲染所有工作区，但只显示当前选中的工作区
  return (
    <LiveReplyContext.Provider value={{ insertLiveReply }}>
      <div style={{ display: currentWorkspace === 'live' ? 'block' : 'none' }}>
        <LiveStreamWorkspace 
          models={models}
          options={options}
          setInsertLiveReply={setInsertLiveReply}
        />
      </div>
      <div style={{ display: currentWorkspace === 'interaction' ? 'block' : 'none' }}>
        <InteractionWorkspace />
      </div>
      <div style={{ display: currentWorkspace === 'product' ? 'block' : 'none' }}>
        <ProductWorkspace />
      </div>
      <div style={{ display: currentWorkspace === 'smart-control' ? 'block' : 'none' }}>
        <SmartControlWorkspace />
      </div>
      <div style={{ display: currentWorkspace === 'ai-model' ? 'block' : 'none' }}>
        <AIModelWorkspace
          models={models}
          currentModel={currentModel}
          options={options}
        />
      </div>
      <div style={{ display: currentWorkspace === 'voice' ? 'block' : 'none' }}>
        <VoiceWorkspace
          models={models}
          currentModel={currentModel}
          loading={loading}
          error={error}
          audioQueue={audioQueue}
          serviceStatus={serviceStatus}
          options={options}
          setOptions={setOptions}
          setModel={setModel}
          generateSpeech={generateSpeech}
          playAudio={playAudio}
          stopAudio={stopAudio}
          removeAudio={removeAudio}
          setAudioProgress={setAudioProgress}
          downloadProcessedAudio={downloadProcessedAudio}
        />
      </div>
      <div style={{ display: currentWorkspace === 'training' ? 'block' : 'none' }}>
        <TrainingWorkspace 
          models={models}
          setModel={setModel}
          currentModel={currentModel}
          onModelListUpdate={refreshModels}
        />
      </div>
      <div style={{ display: currentWorkspace === 'text-live' ? 'block' : 'none' }}>
        <TextLiveWorkspace setInsertLiveReply={setInsertLiveReply} />
      </div>
    </LiveReplyContext.Provider>
  );
};

/**
 * 主应用组件，包含侧边栏和主内容区
 */
const MainApp: React.FC = () => {
  const {
    models,
    currentModel,
    serviceStatus,
    options,
    setOptions,
    setModel,
  } = useTTS();
  const { currentUser, logout } = useAuth(); // 用 useAuth 获取 currentUser 和 logout
  const [menuOpen, setMenuOpen] = useState(false);
  const anchorRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  const { currentWorkspace } = useWorkspace();
  
  // 切换菜单开关
  const handleToggle = () => {
    setMenuOpen((prevOpen) => !prevOpen);
  };
  
  // 关闭菜单
  const handleClose = (event: Event | React.SyntheticEvent) => {
    if (
      anchorRef.current &&
      anchorRef.current.contains(event.target as HTMLElement)
    ) {
      return;
    }
    setMenuOpen(false);
  };
  
  // 处理菜单项点击
  const handleListKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Tab') {
      event.preventDefault();
      setMenuOpen(false);
    } else if (event.key === 'Escape') {
      setMenuOpen(false);
    }
  };

  return (
      <LiveConnectionProvider>
        <div className="flex h-screen bg-[#0d1117] text-white relative overflow-hidden">
          {/* 背景图案 */}
          <div className="absolute inset-0 z-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgdmlld0JveD0iMCAwIDYwIDYwIj48cGF0aCBkPSJNMjkuNzYsMjcuODZhNiw2LDAsMCwxLDYuMDcsNS45Myw1Ljk0LDUuOTQsMCwwLDEtNiwwIiBmaWxsPSJub25lIiBzdHJva2U9IiMyMDJiM2IiIHN0cm9rZS13aWR0aD0iMC41Ii8+PC9zdmc+')] opacity-20"></div>
          
          {/* 顶部渐变效果 */}
          <div className="absolute top-0 left-0 right-0 h-64 bg-gradient-to-b from-blue-600/10 to-transparent z-0"></div>
          
          {/* 底部渐变效果 */}
          <div className="absolute bottom-0 left-0 right-0 h-64 bg-gradient-to-t from-purple-600/10 to-transparent z-0"></div>
          
        {/* 用户信息和下拉菜单，仅非智能场控页面显示 */}
        {currentUser && currentWorkspace !== 'smart-control' && (
            <div className="absolute top-14 right-12 z-50">
              <div 
                className="flex items-center bg-[#1a1f36]/70 rounded-full px-3 py-1.5 backdrop-blur-md border border-[#2a3153]/50 cursor-pointer"
                onClick={handleToggle}
                ref={anchorRef}
              >
                <div className="w-7 h-7 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white font-medium mr-2">
                  {currentUser.username.charAt(0).toUpperCase()}
                </div>
                <span className="text-sm font-medium text-white">{currentUser.username}</span>
              </div>
              
              <Popper
                open={menuOpen}
                anchorEl={anchorRef.current}
                role={undefined}
                placement="bottom-end"
                transition
                disablePortal
                style={{ zIndex: 1300 }}
              >
                {({ TransitionProps, placement }) => (
                  <Grow
                    {...TransitionProps}
                    style={{
                      transformOrigin:
                        placement === 'bottom-end' ? 'right top' : 'right bottom',
                    }}
                  >
                    <Paper className="bg-[#1a1f36] text-white border border-[#2a3153]/50 mt-2">
                      <ClickAwayListener onClickAway={handleClose}>
                        <MenuList
                          autoFocusItem={menuOpen}
                          id="composition-menu"
                          aria-labelledby="composition-button"
                          onKeyDown={handleListKeyDown}
                        >
                          <MenuItem 
                            onClick={(e) => {
                              handleClose(e);
                              // 这里可以添加个人信息设置功能
                            }}
                            className="flex items-center gap-2 hover:bg-[#252c52]"
                          >
                            <Person fontSize="small" />
                            <span>个人信息</span>
                          </MenuItem>
                          <MenuItem 
                            onClick={(e) => {
                              handleClose(e);
                              // 这里可以添加设置功能
                            }}
                            className="flex items-center gap-2 hover:bg-[#252c52]"
                          >
                            <Settings fontSize="small" />
                            <span>设置</span>
                          </MenuItem>
                          <MenuItem 
                            onClick={(e) => {
                              handleClose(e);
                              logout();
                            }}
                            className="flex items-center gap-2 hover:bg-[#252c52] text-red-400"
                          >
                            <Logout fontSize="small" />
                            <span>退出登录</span>
                          </MenuItem>
                        </MenuList>
                      </ClickAwayListener>
                    </Paper>
                  </Grow>
                )}
              </Popper>
            </div>
          )}
          
          <AppSidebar 
            models={models}
            currentModel={currentModel}
            setModel={setModel}
            options={options}
            setOptions={setOptions}
            serviceStatus={serviceStatus}
          />
          
          <main className="flex-1 overflow-auto p-6 relative z-10">
            <div className="bg-gradient-to-br from-[#1a1f36]/90 to-[#232842]/90 rounded-xl backdrop-blur-md shadow-[0_0_25px_rgba(0,0,0,0.2),0_0_0_1px_rgba(255,255,255,0.05)_inset] p-6 border border-[#2a3153]/50 relative overflow-hidden">
              {/* 内部边框发光效果 */}
              <div className="absolute inset-0 pointer-events-none">
                <div className="absolute inset-0 opacity-50 rounded-xl shimmer-animation"></div>
                <style>
                  {`
                    @keyframes shimmer {
                      0% { background-position: 200% 0 }
                      100% { background-position: -200% 0 }
                    }
                    .shimmer-animation {
                      background: linear-gradient(90deg, transparent, transparent, rgba(120, 140, 255, 0.1), transparent, transparent);
                      background-size: 200% 100%;
                      animation: shimmer 8s infinite linear;
                    }
                  `}
                </style>
              </div>
              
              <WorkspaceContainer />
            </div>
          </main>
        </div>
      </LiveConnectionProvider>
  );
};

/**
 * 包装Sidebar组件，使其与WorkspaceContext集成
 */
const AppSidebar: React.FC<{
  models: any[];
  currentModel: any | null;
  setModel: (model: any) => void;
  options: any;
  setOptions: (options: any) => void;
  serviceStatus?: {
    backend: boolean;
    indexTTS: boolean;
  };
}> = (props) => {
  const { currentWorkspace, setCurrentWorkspace } = useWorkspace();
  
  return (
    <Sidebar
      currentWorkspace={currentWorkspace}
      onWorkspaceChange={(workspace: string) => setCurrentWorkspace(workspace as WorkspaceId)}
      {...props}
    />
  );
};

/**
 * 登录页面组件（科技风UI，背景为beijing.jpg）
 * @returns {JSX.Element} 登录页面
 */
const Login: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { login } = useAuth(); // 在Login组件内部使用useAuth hook

  /**
   * 登录表单提交处理
   * @param e 表单事件
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await login(username, password);
      navigate('/');
    } catch (err: any) {
      let msg = '';
      // 优先展示后端返回的 detail 字段
      if (err.response && err.response.data && err.response.data.detail) {
        msg = err.response.data.detail;
      } else if (err.message) {
        msg = err.message;
      } else {
        msg = '登录失败';
      }
      // 英文错误转中文
      if (msg === 'Incorrect username or password') {
        msg = '用户名或密码错误';
      } else if (msg === 'User not found') {
        msg = '用户不存在';
      } else if (msg === 'Password error') {
        msg = '密码错误';
      }
      setError(msg);
    }
  };

  return (
    <div
      className="h-screen w-screen flex items-center justify-start relative overflow-hidden"
      style={{
        background: 'url("/beijing.jpg") center center / cover no-repeat, #0d1117',
        fontFamily: '微软雅黑, Arial, sans-serif',
      }}
    >
      {/* 左侧登录卡片 */}
      <div style={{ flex: '0 0 600px', display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh', zIndex: 1, marginLeft: '130px' }}>
        <div style={{ position: 'relative', width: 600, height: 780 }}>
          <img
            src="/border.png"
            alt="边框装饰"
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              zIndex: 2,
              pointerEvents: 'none',
              objectFit: 'fill',
              display: 'block',
            }}
          />
          <div
            className="relative z-30 flex flex-col items-center justify-center"
            style={{
              width: '68%',
              height: '82%',
              margin: 'auto',
              padding: '120px 0 32px 40px',
              color: '#fff',
              justifyContent: 'center',
            }}
          >
            {/* 顶部logo和标题 */}
            <div className="flex flex-col items-center" style={{ marginBottom: 32, marginTop: 8 }}>
              <img src="/logo.png" alt="logo" style={{width:48,height:48,marginBottom:12,filter:'drop-shadow(0 0 12px #00cfff)'}} />
              <div style={{ fontSize: 24, fontWeight: 700, color: '#00cfff', letterSpacing: 2, textShadow: '0 0 8px #0ff8', marginBottom: 8 }}>登录系统</div>
              <div style={{ height: 3, width: 80, margin: '10px auto 0', background: 'linear-gradient(90deg,#00cfff,#1e90ff,#00cfff)', borderRadius: 2 }} />
            </div>
            {/* 登录表单 */}
            <form className="w-full" onSubmit={handleSubmit} autoComplete="off">
              <div className="mb-5">
                <div className="flex items-center bg-transparent rounded px-2 border border-[#00cfff] focus-within:border-blue-400" style={{height:44}}>
                  <span className="mr-2" style={{ fontSize: 22, color:'#00cfff' }}>👤</span>
                  <input
                    type="text"
                    value={username}
                    onChange={e => setUsername(e.target.value)}
                    placeholder="请输入你的账号"
                    className="flex-1 py-2 bg-transparent outline-none text-white placeholder-gray-400 text-base"
                    style={{border:'none'}}
                    required
                  />
                </div>
              </div>
              <div className="mb-5">
                <div className="flex items-center bg-transparent rounded px-2 border border-[#00cfff] focus-within:border-blue-400" style={{height:44}}>
                  <span className="mr-2" style={{ fontSize: 22, color:'#b0b6c6' }}>⬤</span>
                  <input
                    type="password"
                    value={password}
                    onChange={e => setPassword(e.target.value)}
                    placeholder="请输入你的密码"
                    className="flex-1 py-2 bg-transparent outline-none text-white placeholder-gray-400 text-base"
                    style={{border:'none'}}
                    required
                  />
                </div>
              </div>
              {/* 记住密码/忘记密码 */}
              <div className="flex justify-between items-center w-full mt-1 mb-3 text-xs text-blue-200" style={{ marginBottom: 24 }}>
                <label className="flex items-center cursor-pointer select-none">
                  <input type="checkbox" className="mr-1" style={{accentColor:'#00cfff'}} />记住账号
                </label>
                <button className="hover:underline text-blue-300" type="button" style={{background:'none',border:'none',color:'#00cfff',cursor:'pointer'}} onClick={() => navigate('/forgot-password')}>忘记密码?</button>
              </div>
              {/* 登录按钮 */}
              <button
                type="submit"
                className="w-full py-2 mt-1 font-bold rounded transition-all duration-300 text-lg tracking-wider"
                style={{background:'linear-gradient(90deg,#00cfff 60%,#1e90ff 100%)',color:'#fff',boxShadow:'0 0 8px #00cfff88',border:'none',height:44,fontWeight:700,fontSize:18, marginBottom: 18}}
              >
                登录
              </button>
              {/* 注册按钮 */}
              <button
                type="button"
                className="w-full py-2 font-bold rounded transition-all duration-300 text-lg border"
                style={{background:'#fff',color:'#1e90ff',border:'none',marginBottom:24,height:44,fontWeight:700,fontSize:18}}
                onClick={() => navigate('/register')}
              >
                注册
              </button>
            </form>
            {error && <div style={{ color: 'red', marginTop: 8 }}>{error}</div>}
            {/* 底部彩色圆点 */}
            <div className="flex justify-center gap-3 mt-2 mb-2" style={{ marginBottom: 8 }}>
              <span className="inline-block w-3 h-3 rounded-full" style={{ background: '#00cfff' }}></span>
              <span className="inline-block w-3 h-3 rounded-full" style={{ background: '#1e90ff' }}></span>
              <span className="inline-block w-3 h-3 rounded-full" style={{ background: '#ffe600' }}></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * 受保护路由组件
 */
const ProtectedRoute: React.FC = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    const storedUser = localStorage.getItem('user');
    setIsAuthenticated(!!storedUser);
    setIsLoading(false);
  }, []);
  
  if (isLoading) {
    return null;
  }
  
  return isAuthenticated ? <MainApp /> : <Navigate to="/login" replace />;
};

/**
 * 根应用组件，包含路由和认证
 */
const App: React.FC = () => (
  <WorkspaceProvider>
    <AuthProvider>
    <Router>
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<RegisterPage />} />
        <Route path="/forgot-password" element={<ForgotPasswordPage />} />
        <Route path="/" element={<ProtectedRoute />} />
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Router>
    </AuthProvider>
  </WorkspaceProvider>
  );

export default App; 