import axios from "axios";

axios.defaults.baseURL = "http://localhost:8088";

export const startAutoMessage = (params: any) =>
  axios.post("/api/automation/auto-message/start", params);

export const stopAutoMessage = () =>
  axios.post("/api/automation/auto-message/stop");

export const startAutoPopup = (params: any) =>
  axios.post("/api/automation/auto-popup/start", params);

export const stopAutoPopup = () =>
  axios.post("/api/automation/auto-popup/stop");

export const startAutoReply = (params: any) =>
  axios.post("/api/automation/auto-reply/start", params);

export const stopAutoReply = () =>
  axios.post("/api/automation/auto-reply/stop"); 