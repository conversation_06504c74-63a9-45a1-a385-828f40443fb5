import React, { createContext, useContext, useState, useEffect, useRef } from 'react';

// 直播连接上下文接口
interface LiveConnectionContextType {
  isConnected: boolean;        // 是否已连接到直播
  latestUsername?: string;     // 最近进入直播间的用户名
  audienceCount: number;       // 当前在线人数
}

// 创建上下文
const LiveConnectionContext = createContext<LiveConnectionContextType>({
  isConnected: false,
  audienceCount: 0
});

/**
 * 直播连接提供器组件
 * 提供直播连接相关的状态和数据给子组件
 */
export const LiveConnectionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // 直播连接状态
  const [isConnected, setIsConnected] = useState<boolean>(false);
  // 最近进入直播间的用户名
  const [latestUsername, setLatestUsername] = useState<string>();
  // 当前在线人数
  const [audienceCount, setAudienceCount] = useState<number>(0);
  // 上一个用户名引用，用于比较
  const prevUsernameRef = useRef<string>();

  // 监听全局事件，从InteractionWorkspace组件中接收数据
  useEffect(() => {
    // 定义连接状态变化的事件处理函数
    const handleConnectionChange = (event: CustomEvent<boolean>) => {
      // console.log('LiveConnectionContext: 收到连接状态变化事件', event.detail);
      setIsConnected(event.detail);
    };

    // 定义用户进入的事件处理函数
    const handleUserEnter = (event: CustomEvent<string>) => {
      const username = event.detail;
      // console.log('LiveConnectionContext: 收到用户进入事件', username);
      
      // 如果用户名不同于上一个用户名，更新最新用户名
      if (username !== prevUsernameRef.current) {
        prevUsernameRef.current = username;
        setLatestUsername(username);
        // console.log('LiveConnectionContext: 更新最新用户名', username);
      }
    };

    // 定义观众人数变化的事件处理函数
    const handleAudienceCountChange = (event: CustomEvent<number>) => {
      // console.log('LiveConnectionContext: 收到观众人数变化事件', event.detail);
      setAudienceCount(event.detail);
    };

    // console.log('LiveConnectionContext: 添加事件监听器');
    // 添加事件监听器
    window.addEventListener('live-connection-change', handleConnectionChange as EventListener);
    window.addEventListener('live-user-enter', handleUserEnter as EventListener);
    window.addEventListener('live-audience-count', handleAudienceCountChange as EventListener);

    // 组件卸载时移除事件监听器
    return () => {
      // console.log('LiveConnectionContext: 移除事件监听器');
      window.removeEventListener('live-connection-change', handleConnectionChange as EventListener);
      window.removeEventListener('live-user-enter', handleUserEnter as EventListener);
      window.removeEventListener('live-audience-count', handleAudienceCountChange as EventListener);
    };
  }, []);

  // 提供上下文值
  const contextValue = {
    isConnected,
    latestUsername,
    audienceCount
  };
  
  // console.log('LiveConnectionContext: 当前上下文值', contextValue);

  return (
    <LiveConnectionContext.Provider value={contextValue}>
      {children}
    </LiveConnectionContext.Provider>
  );
};

/**
 * 使用直播连接上下文的自定义Hook
 * @returns LiveConnectionContextType 直播连接上下文
 */
export const useLiveConnection = (): LiveConnectionContextType => {
  return useContext(LiveConnectionContext);
}; 