import React, { createContext, useContext, useState } from 'react';

// 工作区ID类型
export type WorkspaceId = 'live' | 'interaction' | 'product' | 'ai-model' | 'voice' | 'training';

// 工作区上下文类型
interface WorkspaceContextType {
  currentWorkspace: WorkspaceId;
  setCurrentWorkspace: (workspace: WorkspaceId) => void;
}

// 创建工作区上下文
const WorkspaceContext = createContext<WorkspaceContextType | undefined>(undefined);

/**
 * 工作区上下文提供者
 * @param children 子组件
 * @returns 工作区上下文提供者组件
 */
export const WorkspaceProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentWorkspace, setCurrentWorkspace] = useState<WorkspaceId>('live');

  const value = {
    currentWorkspace,
    setCurrentWorkspace
  };

  return <WorkspaceContext.Provider value={value}>{children}</WorkspaceContext.Provider>;
};

/**
 * 使用工作区上下文的Hook
 * @returns 工作区上下文
 */
export const useWorkspace = (): WorkspaceContextType => {
  const context = useContext(WorkspaceContext);
  if (context === undefined) {
    throw new Error('useWorkspace must be used within a WorkspaceProvider');
  }
  return context;
}; 