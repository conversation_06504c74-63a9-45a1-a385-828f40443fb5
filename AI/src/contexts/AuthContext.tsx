import React, { createContext, useContext, useState, useEffect } from 'react';

const API_BASE_URL = 'http://localhost:8088'; // 后端 FastAPI 服务的地址

interface User {
  username: string;
  token: string;
}

interface AuthContextType {
  currentUser: User | null;
  login: (username: string, password: string) => Promise<void>;
  register: (username: string, password: string) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

/**
 * 认证上下文提供者
 * @param children 子组件
 * @returns 认证上下文提供者组件
 */
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);

  // 从本地存储加载用户信息并在需要时验证令牌
  useEffect(() => {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        const parsedUser = JSON.parse(storedUser);
        // 尝试用令牌验证用户
        const fetchCurrentUser = async () => {
          try {
            const response = await fetch(`${API_BASE_URL}/users/me/`, {
              method: 'GET',
              headers: {
                'Authorization': `Bearer ${parsedUser.token}`,
              },
            });

            if (response.ok) {
              const data = await response.json();
              // 确保从后端获取的用户名与存储的一致
              if (data.username === parsedUser.username) {
                setCurrentUser(parsedUser); // 重新使用存储的用户对象，保持token
                setIsAuthenticated(true);
              } else {
                throw new Error('令牌验证失败：用户名不匹配');
              }
            } else {
              throw new Error('令牌验证失败');
            }
          } catch (error) {
            console.error('验证当前用户失败或令牌过期:', error);
            localStorage.removeItem('user'); // 清除无效令牌
            setCurrentUser(null);
            setIsAuthenticated(false);
          }
        };
        fetchCurrentUser();
      } catch (error) {
        console.error('解析存储的用户信息失败:', error);
        localStorage.removeItem('user');
        setCurrentUser(null);
        setIsAuthenticated(false);
      }
    }
  }, []);

  /**
   * 登录函数
   * @param username 用户名
   * @param password 密码
   */
  const login = async (username: string, password: string): Promise<void> => {
    try {
      const response = await fetch(`${API_BASE_URL}/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '登录失败');
      }

      const data = await response.json();
      const user: User = {
        username,
        token: data.access_token,
      };

      localStorage.setItem('user', JSON.stringify(user));
      setCurrentUser(user);
      setIsAuthenticated(true);
    } catch (error) {
      console.error('登录错误:', error);
      throw error;
    }
  };

  /**
   * 注册函数
   * @param username 用户名
   * @param password 密码
   */
  const register = async (username: string, password: string): Promise<void> => {
    try {
      const response = await fetch(`${API_BASE_URL}/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '注册失败');
      }

      // 注册成功后自动登录
      console.log('用户注册成功:', username);
      await login(username, password); // 自动调用登录函数
    } catch (error) {
      console.error('注册错误:', error);
      throw error;
    }
  };

  /**
   * 注销函数
   */
  const logout = (): void => {
    localStorage.removeItem('user');
    setCurrentUser(null);
    setIsAuthenticated(false);
  };

  const value = {
    currentUser,
    login,
    register,
    logout,
    isAuthenticated
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

/**
 * 使用认证上下文的Hook
 * @returns 认证上下文
 */
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 