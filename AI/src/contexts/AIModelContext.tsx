import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import axios from 'axios';

// 创建一个专用于直接访问后端的axios实例
const backendAPI = axios.create({
  baseURL: 'http://localhost:8088',
  timeout: 300000,  // 增加超时时间到300秒(5分钟)，适应生成长文本的需求
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  },
  validateStatus: function (status) {
    return status >= 200 && status < 500; // 默认值
  }
});

// 模型类型
export type AIModelType = 'zhipu-free' | 'tongyi-qianwen' | 'volcengine' | 'deepseek';

// AI模型配置
export interface AIModelConfig {
  selectedModel: AIModelType;
  apiKeysMap: Record<string, string>;
  modelNicknamesMap: Record<string, string>;
  aiModelEnabled: boolean;
}

// 添加会话相关接口
export interface SessionConfig {
  sessionId: string | null;
  messageCount: number;
  maxMessages: number;
  useSessionApi: boolean;
  lastSystemPrompt: string;
}

// 存储服务 - 直接操作localStorage
export const StorageService = {
  // 保存模型设置
  saveModelSettings: (data: AIModelConfig) => {
    try {
      const serialized = JSON.stringify(data);
      localStorage.setItem('aiModelSettings', serialized);
      return true;
    } catch (error) {
      console.error('保存设置失败:', error);
      return false;
    }
  },

  // 加载模型设置
  loadModelSettings: (): AIModelConfig | null => {
    try {
      const serialized = localStorage.getItem('aiModelSettings');
      if (!serialized) return null;
      return JSON.parse(serialized);
    } catch (error) {
      console.error('加载设置失败:', error);
      return null;
    }
  },

  // 保存会话设置
  saveSessionConfig: (data: SessionConfig) => {
    try {
      const serialized = JSON.stringify(data);
      localStorage.setItem('aiSessionConfig', serialized);
      return true;
    } catch (error) {
      console.error('保存会话设置失败:', error);
      return false;
    }
  },

  // 加载会话设置
  loadSessionConfig: (): SessionConfig | null => {
    try {
      const serialized = localStorage.getItem('aiSessionConfig');
      if (!serialized) return null;
      return JSON.parse(serialized);
    } catch (error) {
      console.error('加载会话设置失败:', error);
      return null;
    }
  }
};

// 上下文接口
interface AIModelContextType {
  config: AIModelConfig;
  updateConfig: (newConfig: Partial<AIModelConfig>) => void;
  generateText: (prompt: string, customSystemPrompt?: string, useSession?: boolean) => Promise<string | null>;
  isProcessing: boolean;
  error: string | null;
  // 添加会话管理相关方法
  sessionConfig: SessionConfig;
  updateSessionConfig: (newConfig: Partial<SessionConfig>) => void;
  createSession: (systemPrompt: string) => Promise<string | null>;
  sendMessageToSession: (message: string, sessionId: string) => Promise<string | null>;
  resetSession: () => Promise<boolean>;
}

// 默认配置
const defaultConfig: AIModelConfig = {
  selectedModel: 'zhipu-free',
  apiKeysMap: {
    "zhipu-free": "",
    "tongyi-qianwen": "",
    "volcengine": "",
    "deepseek": ""
  },
  modelNicknamesMap: {
    "zhipu-free": "",
    "tongyi-qianwen": "qwen-max",
    "volcengine": "",
    "deepseek": ""
  },
  aiModelEnabled: true
};

// 默认会话配置
const defaultSessionConfig: SessionConfig = {
  sessionId: null,
  messageCount: 0,
  maxMessages: 20,
  useSessionApi: true,
  lastSystemPrompt: ""
};

// 创建上下文
const AIModelContext = createContext<AIModelContextType | undefined>(undefined);

// 上下文提供者组件
export const AIModelProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [config, setConfig] = useState<AIModelConfig>(defaultConfig);
  const [sessionConfig, setSessionConfig] = useState<SessionConfig>(defaultSessionConfig);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [initialized, setInitialized] = useState(false);

  // 加载保存的设置
  useEffect(() => {
    const savedSettings = StorageService.loadModelSettings();
    if (savedSettings) {
      setConfig(savedSettings);
    }

    // 加载会话设置
    const savedSessionConfig = StorageService.loadSessionConfig();
    if (savedSessionConfig) {
      setSessionConfig(savedSessionConfig);
    }
    
    setInitialized(true);
  }, []);

  // 更新配置
  const updateConfig = (newConfig: Partial<AIModelConfig>) => {
    setConfig(prevConfig => {
      const updatedConfig = { ...prevConfig, ...newConfig };
      // 保存到本地存储
      StorageService.saveModelSettings(updatedConfig);
      return updatedConfig;
    });
  };

  // 更新会话配置
  const updateSessionConfig = (newConfig: Partial<SessionConfig>) => {
    setSessionConfig(prevConfig => {
      const updatedConfig = { ...prevConfig, ...newConfig };
      // 保存到本地存储
      StorageService.saveSessionConfig(updatedConfig);
      return updatedConfig;
    });
  };

  /**
   * 调用智谱AI生成内容
   * @param prompt 用户输入内容
   * @param systemPrompt 系统提示词
   * @returns 生成的内容
   */
  const generateWithZhipuAI = async (prompt: string, systemPrompt: string): Promise<string | null> => {
    const apiKey = config.apiKeysMap['zhipu-free'];
    if (!apiKey || !config.aiModelEnabled) {
      setError("缺少API密钥或AI模型未启用");
      return null;
    }

    try {
      // 使用模型版本，如果未设置则使用默认的glm-4-flash-250414
      const modelToUse = config.modelNicknamesMap['zhipu-free'] || "glm-4-flash-250414";
      
      console.log("发送请求到智谱AI...");
      console.log("智谱AI请求参数:", {
        prompt: prompt,
        model: modelToUse,
        systemPrompt: systemPrompt
      });
      
      // 使用专用的axios实例通过后端转发请求
      const response = await backendAPI.post(
        '/api/zhipu',
        {
          prompt: prompt,
          model: modelToUse,
          api_key: apiKey,
          parameters: {
            temperature: 0.7,
            top_p: 0.8,
            system: systemPrompt
          }
        }
      );

      console.log("收到智谱AI响应:", response.data);
      
      // 检查后端返回的数据格式
      if (response.data) {
        // 如果返回的是错误信息
        if (response.data.error) {
          setError(response.data.error);
          return null;
        }
        
        // 检查格式化后的输出
        if (response.data.output && response.data.output.text) {
        setError(null);
          return response.data.output.text;
        }
      }
      
      setError("无法解析智谱AI返回的结果");
      return null;
    } catch (error: any) {
      console.error("智谱AI调用失败:", error);
      
      if (error.response) {
        const statusCode = error.response.status;
        const errorData = error.response.data;
        
        if (statusCode === 401) {
          setError("API Key无效或已过期，请检查您的密钥");
        } else if (errorData && errorData.error) {
          setError(`调用失败: ${typeof errorData.error === 'string' ? errorData.error : (errorData.error.message || JSON.stringify(errorData.error))}`);
        } else if (errorData && errorData.detail) {
          setError(`后端错误: ${errorData.detail}`);
        } else {
          setError(`请求错误 (${statusCode}): 请检查API Key或模型配置`);
        }
      } else if (error.request) {
        setError("网络请求失败，请确保后端服务在8088端口运行并可访问");
      } else {
        setError(`发生错误: ${error.message}`);
      }
      return null;
    }
  };

  /**
   * 调用通义千问AI生成内容
   * @param prompt 用户输入内容
   * @param systemPrompt 系统提示词
   * @returns 生成的内容
   */
  const generateWithQianwen = async (prompt: string, systemPrompt: string): Promise<string | null> => {
    const apiKey = config.apiKeysMap['tongyi-qianwen'];
    if (!apiKey || !config.aiModelEnabled) {
      setError("缺少API密钥或AI模型未启用");
      return null;
    }

    try {
      // 使用模型版本，如果未设置则使用默认的qwen-max
      const modelToUse = config.modelNicknamesMap['tongyi-qianwen'] || "qwen-max"; 
      
      console.log("发送请求到通义千问...");
      console.log("通义千问请求参数:", {
        prompt: prompt,
        model: modelToUse,
        systemPrompt: systemPrompt  // 系统提示词参数
      });
      
      // 使用专用的axios实例直接访问后端
      const response = await backendAPI.post(
        '/api/qianwen', 
        {
          prompt: prompt,
          model: modelToUse,
          api_key: apiKey,
          parameters: {
            temperature: 0.7,
            top_p: 0.8,
            result_format: "message",
            system: systemPrompt
          }
        }
      );

      console.log("收到通义千问响应:", response.data);
      
      // 检查后端返回的数据格式
      if (response.data) {
        // 如果返回的是错误信息
        if (response.data.error) {
          setError(response.data.error);
          return null;
        }
        
        // 检查格式化后的输出
        if (response.data.output && response.data.output.text) {
          setError(null);
          return response.data.output.text;
        }
      }
      
      setError("无法解析API返回的结果");
      return null;
    } catch (error: any) {
      console.error("通义千问调用失败:", error);
      
      if (error.response) {
        const statusCode = error.response.status;
        const errorData = error.response.data;
        
        if (statusCode === 401) {
          setError("API Key无效或已过期，请检查您的密钥");
        } else if (errorData && errorData.error) {
          setError(`调用失败: ${typeof errorData.error === 'string' ? errorData.error : (errorData.error.message || JSON.stringify(errorData.error))}`);
        } else if (errorData && errorData.detail) {
          setError(`后端错误: ${errorData.detail}`);
        } else {
          setError(`请求错误 (${statusCode}): 请检查API Key或模型配置`);
        }
      } else if (error.request) {
        setError("网络请求失败，请确保后端服务在8088端口运行并可访问");
      } else {
        setError(`发生错误: ${error.message}`);
      }
      return null;
    }
  };

  /**
   * 调用火山引擎生成内容
   * @param prompt 用户输入内容
   * @param systemPrompt 系统提示词
   * @returns 生成的内容
   */
  const generateWithVolcengine = async (prompt: string, systemPrompt: string): Promise<string | null> => {
    const apiKey = config.apiKeysMap['volcengine'];
    if (!apiKey || !config.aiModelEnabled) {
      setError("缺少API密钥或AI模型未启用");
      return null;
    }

    try {
      // 使用模型版本，如果未设置则使用默认的 'chatglm-6b-model'
      const modelToUse = config.modelNicknamesMap['volcengine'] || "chatglm-6b-model";
      
      console.log("发送请求到火山引擎...");
      console.log("火山引擎请求参数:", {
        prompt: prompt,
        model: modelToUse,
        systemPrompt: systemPrompt  // 系统提示词参数
      });
      
      const response = await backendAPI.post(
        '/api/volcengine',
        {
          prompt: prompt,
          model: modelToUse,
          api_key: apiKey,
          parameters: {
            temperature: 0.7,
            top_p: 0.9,
            max_tokens: 1024,
            system: systemPrompt
          }
        }
      );

      console.log("收到火山引擎响应:", response.data);
      
      if (response.data) {
        if (response.data.error) {
          setError(response.data.error);
          return null;
        }
        
        if (response.data.output && response.data.output.text) {
          setError(null);
          return response.data.output.text;
        }
      }
      
      setError("无法解析火山引擎API返回的结果");
      return null;
    } catch (error: any) {
      console.error("火山引擎调用失败:", error);
      
      if (error.response) {
        const statusCode = error.response.status;
        const errorData = error.response.data;
        
        if (statusCode === 401) {
          setError("API Key无效或已过期，请检查您的密钥");
        } else if (errorData && errorData.error) {
          setError(`调用失败: ${typeof errorData.error === 'string' ? errorData.error : (errorData.error.message || JSON.stringify(errorData.error))}`);
        } else if (errorData && errorData.detail) {
          setError(`后端错误: ${errorData.detail}`);
        } else {
          setError(`请求错误 (${statusCode}): 请检查API Key或模型配置`);
        }
      } else if (error.request) {
        setError("网络请求失败，请确保后端服务在8088端口运行并可访问");
      } else {
        setError(`发生错误: ${error.message}`);
      }
      return null;
    }
  };

  /**
   * 调用DeepSeek生成内容
   * @param prompt 用户输入内容
   * @param systemPrompt 系统提示词
   * @returns 生成的内容
   */
  const generateWithDeepSeek = async (prompt: string, systemPrompt: string): Promise<string | null> => {
    const apiKey = config.apiKeysMap['deepseek'];
    if (!apiKey || !config.aiModelEnabled) {
      setError("缺少API密钥或AI模型未启用");
      return null;
    }

    try {
      // 使用模型版本，如果未设置则使用默认的deepseek-chat
      const modelToUse = config.modelNicknamesMap['deepseek'] || "deepseek-chat";
      
      console.log("发送请求到DeepSeek...");
      console.log("DeepSeek请求参数:", {
        prompt: prompt,
        model: modelToUse,
        systemPrompt: systemPrompt  // 系统提示词参数
      });
      
      const response = await backendAPI.post(
        '/api/deepseek',
        {
          prompt: prompt,
          model: modelToUse,
          api_key: apiKey,
          parameters: {
            temperature: 0.7,
            top_p: 0.9,
            max_tokens: 1024,
            system: systemPrompt
          }
        }
      );

      console.log("收到DeepSeek响应:", response.data);
      
      if (response.data) {
        if (response.data.error) {
          setError(response.data.error);
          return null;
        }
        
        if (response.data.output && response.data.output.text) {
          setError(null);
          return response.data.output.text;
        }
      }
      
      setError("无法解析DeepSeek API返回的结果");
      return null;
    } catch (error: any) {
      console.error("DeepSeek调用失败:", error);
      
      if (error.response) {
        const statusCode = error.response.status;
        const errorData = error.response.data;
        
        if (statusCode === 401) {
          setError("API Key无效或已过期，请检查您的密钥");
        } else if (errorData && errorData.error) {
          setError(`调用失败: ${typeof errorData.error === 'string' ? errorData.error : (errorData.error.message || JSON.stringify(errorData.error))}`);
        } else if (errorData && errorData.detail) {
          setError(`后端错误: ${errorData.detail}`);
        } else {
          setError(`请求错误 (${statusCode}): 请检查API Key或模型配置`);
        }
      } else if (error.request) {
        setError("网络请求失败，请确保后端服务在8088端口运行并可访问");
      } else {
        setError(`发生错误: ${error.message}`);
      }
      return null;
    }
  };

  /**
   * 创建新会话
   * @param systemPrompt 系统提示词
   * @returns 创建的会话ID或null
   */
  const createSession = async (systemPrompt: string): Promise<string | null> => {
    if (!config.aiModelEnabled) {
      setError("AI模型未启用");
      return null;
    }

    try {
      console.log("创建新会话，系统提示词:", systemPrompt);
      
      const response = await backendAPI.post('/api/session', {
        system_prompt: systemPrompt,
      });

      if (response.data && response.data.success) {
        const sessionId = response.data.session_id;
        
        // 更新会话配置
        updateSessionConfig({
          sessionId,
          messageCount: 1, // 初始包含一条系统消息
          lastSystemPrompt: systemPrompt
        });
        
        console.log("会话创建成功，ID:", sessionId);
        return sessionId;
      } else {
        throw new Error(response.data?.error || "创建会话失败");
      }
    } catch (error: any) {
      console.error("创建会话失败:", error);
      setError(`创建会话失败: ${error.message || '未知错误'}`);
      return null;
    }
  };

  /**
   * 向现有会话发送消息
   * @param message 用户消息
   * @param sessionId 会话ID
   * @returns AI回复内容或null
   */
  const sendMessageToSession = async (message: string, sessionId: string): Promise<string | null> => {
    if (!config.aiModelEnabled) {
      setError("AI模型未启用");
      return null;
    }

    if (!sessionId) {
      setError("会话ID无效");
      return null;
    }

    setIsProcessing(true);
    setError(null);

    try {
      console.log(`向会话 ${sessionId} 发送消息:`, message);
      
      // 获取当前模型的API密钥
      const apiKey = config.apiKeysMap[config.selectedModel];
      if (!apiKey) {
        throw new Error(`模型 ${config.selectedModel} 未配置API密钥`);
      }

      // 获取模型名称
      const modelName = config.modelNicknamesMap[config.selectedModel] || config.selectedModel;
      
      const response = await backendAPI.post('/api/session/message', {
        message,
        session_id: sessionId,
        provider: config.selectedModel,
        model: modelName,
        api_key: apiKey
      });

      if (response.data && response.data.success) {
        let reply = response.data.reply;
        const messageCount = response.data.message_count;
        // 自动过滤掉单独的'-'（英文-和中文－）符号
        if (typeof reply === 'string') {
          reply = reply.replace(/^(\s*[-－]+\s*)+$/gm, '').replace(/^\s+|\s+$/g, '');
        }
        // 更新会话消息计数
        updateSessionConfig({ messageCount });
        
        // 如果消息数达到上限，自动创建新会话
        if (messageCount >= sessionConfig.maxMessages) {
          console.log("会话消息数达到上限，下次将自动创建新会话");
        }
        
        return reply;
      } else {
        throw new Error(response.data?.error || "获取回复失败");
      }
    } catch (error: any) {
      console.error("发送会话消息失败:", error);
      
      if (error.response?.status === 404) {
        // 会话不存在，设置为空以便下次自动创建新会话
        updateSessionConfig({ sessionId: null });
        setError("会话已过期，将创建新会话");
      } else {
        setError(`发送消息失败: ${error.message || '未知错误'}`);
      }
      
      return null;
    } finally {
      setIsProcessing(false);
    }
  };

  /**
   * 重置当前会话
   * @returns 是否成功
   */
  const resetSession = async (): Promise<boolean> => {
    try {
      // 如果有当前会话，尝试删除
      if (sessionConfig.sessionId) {
        try {
          await backendAPI.delete(`/api/session/${sessionConfig.sessionId}`);
          console.log("已删除会话:", sessionConfig.sessionId);
        } catch (e) {
          console.error("删除会话失败:", e);
        }
      }
      
      // 重置会话配置
      updateSessionConfig({
        sessionId: null,
        messageCount: 0
      });
      
      return true;
    } catch (error) {
      console.error("重置会话失败:", error);
      return false;
    }
  };

  /**
   * 统一的文本生成接口
   * @param prompt 提示词
   * @param customSystemPrompt 自定义系统提示词，默认为直播助手
   * @param useSession 是否启用会话管理，默认true。直播全自由模式应为false。
   * @returns 生成的文本内容
   */
  const generateText = async (prompt: string, customSystemPrompt?: string, useSession: boolean = true): Promise<string | null> => {
    if (!config.aiModelEnabled) {
      setError("AI模型未启用");
      return null;
    }

    const systemPrompt = customSystemPrompt || "你是一个专业的直播助手，擅长为主播生成多样化的话术表达";
    
    // 添加详细的调试日志
    console.log("=== AI生成请求 ===");
    console.log("选中的模型:", config.selectedModel);
    console.log("用户提示:", prompt);
    console.log("系统提示词:", systemPrompt);
    console.log("使用会话API:", useSession);
    console.log("会话ID:", sessionConfig.sessionId);
    console.log("==================");
    
    setIsProcessing(true);
    setError(null);
    
    try {
      // 判断是否使用会话API
      if (useSession && sessionConfig.useSessionApi) {
        // 如果没有会话ID或系统提示词变了，创建新会话
        if (!sessionConfig.sessionId || systemPrompt !== sessionConfig.lastSystemPrompt || 
            sessionConfig.messageCount >= sessionConfig.maxMessages) {
          console.log("需要创建新会话");
          const newSessionId = await createSession(systemPrompt);
          
          if (!newSessionId) {
            throw new Error("无法创建会话");
          }
          
          // 创建新会话后，发送用户消息
          return await sendMessageToSession(prompt, newSessionId);
        } else {
          // 使用现有会话发送消息
          return await sendMessageToSession(prompt, sessionConfig.sessionId);
        }
      } else {
        // 不使用会话API，使用原来的方式
      let result: string | null = null;
      
      // 根据选择的模型调用不同的API
      if (config.selectedModel === "tongyi-qianwen") {
        result = await generateWithQianwen(prompt, systemPrompt);
      } else if (config.selectedModel === "zhipu-free") {
        result = await generateWithZhipuAI(prompt, systemPrompt);
      } else if (config.selectedModel === "volcengine") {
        result = await generateWithVolcengine(prompt, systemPrompt);
      } else if (config.selectedModel === "deepseek") {
        result = await generateWithDeepSeek(prompt, systemPrompt);
      } else {
        // 默认使用智谱API
        result = await generateWithZhipuAI(prompt, systemPrompt);
      }
      
      // 自动过滤掉单独的'-'（英文-和中文－）符号
      if (typeof result === 'string') {
        result = result.replace(/^(\s*[-－]+\s*)+$/gm, '').replace(/^\s+|\s+$/g, '');
      }
      return result;
      }
    } catch (error: any) {
      console.error("生成失败:", error);
      setError(`生成失败: ${error.message || '未知错误'}`);
      return null;
    } finally {
      setIsProcessing(false);
    }
  };

  // 提供上下文值
  const value = {
    config,
    updateConfig,
    generateText,
    isProcessing,
    error,
    // 添加会话相关方法
    sessionConfig,
    updateSessionConfig,
    createSession,
    sendMessageToSession,
    resetSession
  };

  return <AIModelContext.Provider value={value}>{initialized && children}</AIModelContext.Provider>;
};

// 自定义钩子，用于访问AI模型上下文
export const useAIModel = (): AIModelContextType => {
  const context = useContext(AIModelContext);
  if (context === undefined) {
    throw new Error('useAIModel must be used within an AIModelProvider');
  }
  return context;
}; 