import React, { createContext, useContext } from 'react';

/**
 * 实时插入直播语音回复的上下文
 * @interface LiveReplyContextProps
 * @property {(text: string) => void} insertLiveReply - 插入一条需要语音合成的回复
 */
export interface LiveReplyContextProps {
  insertLiveReply: (text: string, type?: 'welcome' | 'time' | 'count' | 'broadcast' | 'interactionReply') => void;
}

export const LiveReplyContext = createContext<LiveReplyContextProps>({
  insertLiveReply: () => {},
});

/**
 * 获取实时插入直播语音回复的上下文
 * @returns {LiveReplyContextProps} 上下文对象
 */
export const useLiveReply = () => useContext(LiveReplyContext); 