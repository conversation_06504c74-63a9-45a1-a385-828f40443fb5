{"name": "tts-ui", "version": "0.1.0", "private": true, "type": "module", "proxy": "http://localhost:8088", "dependencies": {"@ant-design/icons": "^6.0.0", "@anthropic-ai/sdk": "^0.40.1", "@babel/preset-env": "^7.26.9", "@ctrl/tinycolor": "^4.1.0", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.6", "@mui/material": "^5.15.6", "@stagewise-plugins/react": "^0.4.6", "@stagewise/toolbar-react": "^0.4.6", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.31", "@types/react": "^18.2.6", "@types/react-dom": "^18.2.4", "@types/socket.io-client": "^1.4.36", "antd": "^5.24.8", "autoprefixer": "^10.4.14", "axios": "^1.9.0", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "openai": "^4.97.0", "postcss": "^8.4.23", "qrcode.react": "^4.2.0", "rc-picker": "^4.11.3", "rc-resize-observer": "^1.4.3", "rc-steps": "^6.0.1", "rc-switch": "^4.1.0", "rc-table": "^7.50.4", "rc-tabs": "^15.6.0", "rc-textarea": "^1.10.0", "rc-tooltip": "^6.4.0", "rc-tree": "^5.13.1", "rc-util": "^5.44.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.5.1", "react-scripts": "5.0.1", "scroll-into-view-if-needed": "^3.1.0", "socket.io-client": "^4.8.1", "stream-browserify": "^3.0.0", "tailwindcss": "^3.3.2", "throttle-debounce": "^5.0.2", "typescript": "^4.9.5", "util": "^0.12.5", "web-vitals": "^2.1.4", "zustand": "^4.5.2"}, "scripts": {"start": "react-app-rewired start", "build": "react-app-rewired build", "test": "react-app-rewired test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app"], "rules": {"react/react-in-jsx-scope": "off", "@typescript-eslint/no-unused-vars": "warn"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"react-app-rewired": "^2.2.1", "webpack": "^5.99.7"}, "description": "IndexTTS-FAST AI直播助手是一款专为直播行业设计的AI软件，利用先进的文本转语音(TTS)技术，实现实时语音合成、模型训练、智能对话等功能。本项目采用前后端分离架构，前端使用React+TypeScript开发，后端使用FastAPI构建，并集成了IndexTTS服务作为核心语音合成引擎。", "main": "config-overrides.js", "keywords": [], "author": "", "license": "ISC"}