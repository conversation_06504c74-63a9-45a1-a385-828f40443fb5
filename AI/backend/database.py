from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker, AsyncSession
from sqlalchemy.orm import declarative_base
import os

# 获取当前文件的绝对路径，确保数据库文件始终在AI/backend/sql_app.db
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DATABASE_URL = f"sqlite+aiosqlite:///{os.path.join(BASE_DIR, 'sql_app.db')}"

# 创建异步 SQLAlchemy 引擎
engine = create_async_engine(DATABASE_URL)

# 创建异步数据库会话类
async_session = async_sessionmaker(
    engine, class_=AsyncSession, expire_on_commit=False
)

# declarative_base() 会返回一个基类，我们的 ORM 模型将继承自它
Base = declarative_base() 