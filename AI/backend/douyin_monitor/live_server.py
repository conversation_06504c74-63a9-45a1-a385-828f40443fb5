import sys
import os
import shutil
# 禁用Selenium Manager自动下载chromedriver，提升驱动创建速度
# @see https://github.com/SeleniumHQ/selenium/issues/12163
os.environ["SELENIUM_MANAGER_SKIP_DOWNLOAD"] = "1"

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
import asyncio
from typing import List, Dict
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import logging
import re
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.webdriver import WebDriver as ChromeWebDriver

# 获取当前脚本的目录
script_dir = os.path.dirname(os.path.abspath(__file__))
# 获取项目根目录 (假设项目根目录是 AI 目录的父目录的父目录)
project_root = os.path.abspath(os.path.join(script_dir, os.pardir, os.pardir, os.pardir))
# 将项目根目录添加到 sys.path，以便正确导入 AI 包
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(
    level=logging.INFO,  # 改为INFO级别以查看连接问题
    format='%(asctime)s - %(levelname)s - %(client_id)s - %(message)s',
    datefmt='%H:%M:%S'
)
logger = logging.getLogger(__name__)

app = FastAPI()

# 配置CORS - 修改为具体的前端地址
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # 允许前端地址
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

CHROMEDRIVER_PATH_FILE = "chromedriver_path.txt"

def get_or_download_chromedriver():
    """
    获取可用的chromedriver路径，若本地存在则优先使用本地文件，否则自动下载
    :return: chromedriver路径
    """
    # 1. 优先查找当前目录下的 chromedriver.exe
    local_driver = os.path.join(os.path.dirname(__file__), "chromedriver.exe")
    if os.path.exists(local_driver):
        return local_driver

    # 2. 优先读取本地缓存
    if os.path.exists(CHROMEDRIVER_PATH_FILE):
        with open(CHROMEDRIVER_PATH_FILE, "r", encoding="utf-8") as f:
            path = f.read().strip()
            if os.path.exists(path):
                return path

    # 3. 自动下载匹配的ChromeDriver版本
    try:
        # 尝试自动检测Chrome版本并下载匹配的ChromeDriver
        path = ChromeDriverManager().install()
        # 写入缓存
        with open(CHROMEDRIVER_PATH_FILE, "w", encoding="utf-8") as f:
            f.write(path)
        return path
    except Exception as e:
        print(f"自动下载ChromeDriver失败: {e}")
        # 如果自动下载失败，尝试下载最新版本
        try:
            path = ChromeDriverManager(driver_version="LATEST").install()
            with open(CHROMEDRIVER_PATH_FILE, "w", encoding="utf-8") as f:
                f.write(path)
            return path
        except Exception as e2:
            print(f"下载最新版ChromeDriver也失败: {e2}")
            raise Exception("无法下载匹配的ChromeDriver，请手动下载并放置在项目目录中")

# 导入DouyinWebFetcherEnhancedAdapter
try:
    from AI.backend.douyin_monitor.douyin_webfetcher_enhanced_adapter import DouyinWebFetcherEnhancedAdapter
    ENHANCED_ADAPTER_AVAILABLE = True
except ImportError as e:
    ENHANCED_ADAPTER_AVAILABLE = False

class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.drivers: Dict[str, ChromeWebDriver] = {}
        self.reconnect_attempts: Dict[str, int] = {}
        self.max_reconnect_attempts = 3
        # 修改消息缓存结构，添加时间戳
        self.message_cache: Dict[str, Dict[str, float]] = {}  # 客户端ID -> {消息key: 时间戳}
        self.max_cache_size = 2000
        self.message_timeout = 5  # 消息去重时间窗口（秒）
        # 新增高性能适配器管理
        self.enhanced_adapters: Dict[str, object] = {}
        
    def add_to_message_cache(self, client_id: str, username: str, content: str) -> bool:
        """
        添加消息到缓存，返回是否为新消息
        使用用户名、内容和时间窗口作为唯一标识
        """
        current_time = time.time()
        
        if client_id not in self.message_cache:
            self.message_cache[client_id] = {}
            
        # 生成消息的唯一标识
        message_key = f"{username}_{content}"
        
        # 清理过期消息
        if message_key in self.message_cache[client_id]:
            if current_time - self.message_cache[client_id][message_key] > self.message_timeout:
                # 消息已过期，可以重新发送
                del self.message_cache[client_id][message_key]
            else:
                # 消息在时间窗口内，视为重复
                return False
        
        # 如果缓存达到上限，清理过期消息
        if len(self.message_cache[client_id]) >= self.max_cache_size:
            # 清理所有过期消息
            expired_keys = [
                key for key, timestamp in self.message_cache[client_id].items()
                if current_time - timestamp > self.message_timeout
            ]
            for key in expired_keys:
                del self.message_cache[client_id][key]
            
            # 如果清理后仍然超过上限，则清理最旧的消息
            if len(self.message_cache[client_id]) >= self.max_cache_size:
                sorted_messages = sorted(
                    self.message_cache[client_id].items(),
                    key=lambda x: x[1]
                )
                # 删除最旧的一半消息
                for key, _ in sorted_messages[:len(sorted_messages)//2]:
                    del self.message_cache[client_id][key]
        
        # 添加新消息到缓存
        self.message_cache[client_id][message_key] = current_time
        return True
        
    def clear_message_cache(self, client_id: str):
        """
        清理指定客户端的消息缓存
        """
        if client_id in self.message_cache:
            del self.message_cache[client_id]
            
    async def connect(self, websocket: WebSocket, client_id: str):
        await websocket.accept()
        self.active_connections[client_id] = websocket
        self.reconnect_attempts[client_id] = 0  # 重置重连计数
        # 发送连接成功消息
        await self.send_personal_message(
            json.dumps({"type": "system", "content": "WebSocket连接成功"}),
            client_id
        )
        logger.info(f"客户端 {client_id} 已连接", extra={'client_id': client_id})

    def disconnect(self, client_id: str):
        if client_id in self.active_connections:
            del self.active_connections[client_id]
        if client_id in self.reconnect_attempts:
            del self.reconnect_attempts[client_id]
        # 清理消息缓存
        self.clear_message_cache(client_id)
        # 优先清理高性能适配器
        if client_id in self.enhanced_adapters:
            try:
                self.enhanced_adapters[client_id].stop()
            except Exception as e:
                logger.error(f"清理客户端 {client_id} 的高性能适配器失败: {e}", extra={'client_id': client_id})
            del self.enhanced_adapters[client_id]
        # 断开连接时清理对应的驱动实例
        if client_id in self.drivers:
            driver = self.drivers.pop(client_id)
            try:
                driver.quit()
                logger.info(f"客户端 {client_id} 的Chrome驱动已清理", extra={'client_id': client_id})
            except Exception as e:
                logger.error(f"清理客户端 {client_id} 的Chrome驱动失败: {e}", extra={'client_id': client_id})
        logger.info(f"客户端 {client_id} 已断开连接", extra={'client_id': client_id})
        
    async def send_personal_message(self, message: str, client_id: str):
        if client_id in self.active_connections:
            try:
                await self.active_connections[client_id].send_text(message)
            except Exception as e:
                logger.error(f"发送消息错误: {str(e)}")
                
                # 检查重连次数
                if client_id not in self.reconnect_attempts:
                    self.reconnect_attempts[client_id] = 0
                    
                if self.reconnect_attempts[client_id] < self.max_reconnect_attempts:
                    self.reconnect_attempts[client_id] += 1
                    logger.info(f"尝试重连 ({self.reconnect_attempts[client_id]}/{self.max_reconnect_attempts})")
                    
                    # 添加延迟避免立即重连
                    await asyncio.sleep(2)  # 等待2秒后重试
                    
                    try:
                        if client_id in self.drivers:
                            driver = self.drivers[client_id]
                            await self.send_personal_message(
                                json.dumps({"type": "system", "content": "正在尝试重新连接..."}),
                                client_id
                            )
                    except Exception as reconnect_error:
                        logger.error(f"重连失败: {str(reconnect_error)}")
                else:
                    logger.error(f"达到最大重连次数 ({self.max_reconnect_attempts})")
                    # 发送最终错误消息
                    try:
                        await self.active_connections[client_id].send_text(
                            json.dumps({
                                "type": "error",
                                "content": "连接已断开，请刷新页面重试"
                            })
                        )
                    except:
                        pass
                    # 清理连接
                    self.disconnect(client_id)

    def setup_chrome_driver(self, client_id: str, platform: str) -> webdriver.Chrome:
        logger.info(f"开始创建Chrome驱动 - 客户端ID: {client_id}, 平台: {platform}", extra={'client_id': client_id})
        chrome_options = Options()
        
        # 保留核心的性能选项，移除不必要的选项
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-sync')
        chrome_options.add_argument('--window-size=1920,1080')
        
        # 设置页面加载策略为none，提升加载速度
        chrome_options.page_load_strategy = 'none'
        
        # 禁用日志
        chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
        chrome_options.add_argument('--log-level=3')
        
        # 设置日志首选项
        chrome_options.set_capability('goog:loggingPrefs', {'browser': 'ALL'})
        
        # 根据平台选择是否禁用图片、音频等资源加载
        if platform == 'douyin':
            logger.info("抖音平台，禁用图片、音频等资源加载", extra={'client_id': client_id})
            prefs = {
                "profile.default_content_setting_values": {
                    "images": 2,
                    "plugins": 2,
                    "media_stream": 2,
                    "media_stream_mic": 2,
                    "media_stream_camera": 2,
                }
            }
            chrome_options.add_experimental_option("prefs", prefs)
        else:
             logger.info("非抖音平台，不禁用图片、音频等资源加载 (例如视频号需要二维码)", extra={'client_id': client_id})
        
        logger.info("Chrome驱动配置完成，开始创建实例...", extra={'client_id': client_id})
        
        try:
            # JSDoc: 获取chromedriver路径，优先用缓存，不存在则自动下载
            chromedriver_path = get_or_download_chromedriver()
            service = Service(executable_path=chromedriver_path)
            driver = webdriver.Chrome(service=service, options=chrome_options)
            logger.info(f"Chrome驱动实例创建成功，chromedriver路径: {chromedriver_path}", extra={'client_id': client_id})
            
            # 设置较短的超时时间
            driver.set_page_load_timeout(20)  # 页面加载超时时间
            driver.set_script_timeout(10)     # 脚本执行超时时间
            
            # 设置CDP拦截视频流
            try:
                # JSDoc: 拦截常见视频流请求，节省带宽和资源
                # @param {webdriver.Chrome} driver - Chrome驱动实例
                driver.execute_cdp_cmd(
                    "Network.setBlockedURLs",
                    {"urls": ["*.mp4", "*.flv", "*.m3u8", "*.ts", "*.webm", "*.ogg", "*.mp3"]}
                )
                driver.execute_cdp_cmd("Network.enable", {})
                logger.info("已设置CDP拦截视频流", extra={'client_id': client_id})
            except Exception as e:
                logger.warning(f"CDP拦截设置失败: {str(e)}", extra={'client_id': client_id})
            
            self.drivers[client_id] = driver
            logger.info("Chrome驱动设置完成并保存到管理器", extra={'client_id': client_id})
            return driver
            
        except Exception as e:
            logger.error(f"创建Chrome驱动失败: {str(e)}", extra={'client_id': client_id})
            raise

manager = ConnectionManager()

MONITOR_SCRIPT = r"""
(() => {
    const SELECTORS = {
        chatContainer: '.webcast-chatroom___list',  // 聊天容器
        messageBox: '.TNg5meqw',                    // 消息框
        enterMessage: '.JbdwxQi1',                  // 进入直播间消息
        audience: '.UxWMHF9c',                      // 观众数
        username: '[data-e2e="comment-username"]'   // 用户名选择器
    };

    // 添加消息缓存，用于去重
    const messageCache = new Set();
    const CACHE_MAX_SIZE = 100;

    function handleMessage(node) {
        try {
            const messageText = node.textContent.trim();
            if (!messageText || messageCache.has(messageText)) return;

            // 检查是否是进入直播间的消息
            const isEnterMessage = node.querySelector(SELECTORS.enterMessage);
            
            // 过滤掉系统消息、礼物消息等
            if (!isEnterMessage && (
                messageText.includes('点赞') || 
                messageText.includes('关注') ||
                messageText.includes('送出了') ||    // 屏蔽送礼物消息
                messageText.includes('送给主播') ||  // 屏蔽送礼物消息
                messageText.includes('赠送') ||      // 屏蔽送礼物消息
                /^VM\\d+:|【/.test(messageText) ||
                /送出了.*[x×]?\d+/.test(messageText) // 屏蔽包含数量的礼物消息
            )) {
                return;
            }

            // 添加到缓存
            messageCache.add(messageText);
            if (messageCache.size > CACHE_MAX_SIZE) {
                const items = Array.from(messageCache);
                items.slice(0, CACHE_MAX_SIZE / 2).forEach(item => messageCache.delete(item));
            }

            // 构造消息对象
            const message = {
                type: isEnterMessage ? 'enter' : 'comment',
                content: messageText,
                username: messageText.split(':')[0].trim()  // 从消息中提取用户名
            };

            // 使用样式输出到控制台
            console.log(
                `%c[${new Date().toLocaleTimeString()}] ${messageText}`,
                isEnterMessage ? STYLES.system : STYLES.message
            );

            // 发送消息
            window.postMessage(message, '*');

        } catch (err) {
            console.log(`%c消息处理错误: ${err.message}`, STYLES.error);
        }
    }

    function init() {
        try {
            const chatContainer = document.querySelector(SELECTORS.chatContainer);
            if (!chatContainer) {
                console.log('%c未找到聊天容器，5秒后重试', STYLES.error);
                setTimeout(init, 5000);
                return;
            }

            console.log('%c找到聊天容器，开始监控', STYLES.system);

            const observer = new MutationObserver(mutations => {
                mutations.forEach(mutation => {
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === 1) {
                            if (node.matches(SELECTORS.messageBox)) {
                                handleMessage(node);
                            }
                            node.querySelectorAll(SELECTORS.messageBox).forEach(handleMessage);
                        }
                    });
                });
            });

            observer.observe(chatContainer, {
                childList: true,
                subtree: true
            });

            console.log('%c监控已启动', STYLES.system);

            // 定期检查在线人数
            setInterval(() => {
                const audienceElem = document.querySelector(SELECTORS.audience);
                if (audienceElem) {
                    const count = audienceElem.textContent;
                    console.log(`%c👥 当前观众: ${count}`, STYLES.audience);
                    window.postMessage({
                        type: 'audience',
                        content: `当前在线人数: ${count}`
                    }, '*');
                }
            }, 1000);

            // 发送测试消息
            setTimeout(() => {
                console.log('%c✅ 监控脚本已成功运行', STYLES.system);
                window.postMessage({
                    type: 'system',
                    content: '监控脚本已成功运行'
                }, '*');
            }, 1000);

        } catch (err) {
            console.log(`%c初始化错误: ${err.message}`, STYLES.error);
            setTimeout(init, 5000);
        }
    }

    // 开始设置监控
    console.log('%c开始设置监控...', STYLES.system);
    init();
})();
"""

# 导入视频号监控模块
from AI.backend.douyin_monitor import wechat_channels_monitor

# 导入DouyinLiveWebFetcher
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'DouyinLiveWebFetcher-main'))
from liveMan import DouyinLiveWebFetcher

async def monitor_live(driver: webdriver.Chrome, client_id: str):
    try:
        logger.info(f"开始监控直播 - 客户端ID: {client_id}", extra={'client_id': client_id})
        
        # 等待聊天容器加载
        logger.info("等待聊天容器加载...", extra={'client_id': client_id})
        try:
            logger.info("开始等待聊天容器...", extra={'client_id': client_id})
            # 减少等待时间到10秒
            chat_container = WebDriverWait(driver, 30).until(
                EC.presence_of_element_located((By.CLASS_NAME, "webcast-chatroom___list"))
            )
            logger.info(f"聊天容器已加载: {chat_container is not None}", extra={'client_id': client_id})
            # JSDoc: 页面加载后立即隐藏和暂停视频元素，双保险
            # @param {None}
            driver.execute_script('''
                /**
                 * 隐藏并暂停直播视频元素，防止资源浪费
                 * @returns {void}
                 */
                (function hideAndPauseVideo() {
                    var video = document.querySelector('video');
                    if(video){
                        video.pause();
                        video.src = "";
                        video.style.display = "none";
                    }
                    var player = document.querySelector('.xgplayer');
                    if(player){
                        player.style.display = "none";
                    }
                })();
            ''')
            logger.info("已注入隐藏/暂停视频JS", extra={'client_id': client_id})
        except Exception as e:
            logger.error(f"等待聊天容器超时: {str(e)}", extra={'client_id': client_id})
            current_url = driver.current_url
            logger.info(f"当前页面URL: {current_url}", extra={'client_id': client_id})
            logger.info("当前页面源码片段:", extra={'client_id': client_id})
            try:
                logger.info(driver.page_source[:1000], extra={'client_id': client_id})  # 打印更多页面源码用于调试
            except:
                logger.error("无法获取页面源码", extra={'client_id': client_id})
            
            await manager.send_personal_message(
                json.dumps({
                    "type": "error", 
                    "content": "加载聊天容器超时，请检查直播间地址是否正确，或重试"
                }),
                client_id
            )
            return
            
        # 注入监控脚本
        logger.info("开始注入监控脚本...", extra={'client_id': client_id})
        
        try:
            # 首先清空控制台并注入样式
            driver.execute_script("""
                console.clear();
                window.STYLES = {
                    message: 'color: #4CAF50; font-weight: bold;',  // 普通消息为绿色
                    audience: 'color: #2196F3; font-weight: bold;', // 观众数为蓝色
                    error: 'color: #f44336; font-weight: bold;',    // 错误为红色
                    system: 'color: #9C27B0; font-weight: bold;'    // 系统消息为紫色
                };
                console.log('%c=== 直播监控已启动 ===', 'color: #4CAF50; font-weight: bold;');
            """)
            logger.info("基础样式注入完成", extra={'client_id': client_id})
            
            # 然后注入主监控脚本
            driver.execute_script(MONITOR_SCRIPT)
            logger.info("监控脚本注入完成", extra={'client_id': client_id})
        except Exception as e:
            logger.error(f"脚本注入失败: {str(e)}", extra={'client_id': client_id})
            await manager.send_personal_message(
                json.dumps({"type": "error", "content": f"脚本注入失败: {str(e)}"}),
                client_id
            )
            return
        
        # 发送连接成功消息
        await manager.send_personal_message(
            json.dumps({"type": "system", "content": "成功连接到直播间"}),
            client_id
        )
        
        # 设置消息监听
        retry_count = 0
        max_retries = 3
        
        # 记录开始时间，用于定时刷新
        start_time = time.time()
        current_url = driver.current_url
        logger.info(f"开始监控循环，当前URL: {current_url}", extra={'client_id': client_id})
        
        while client_id in manager.active_connections:
            try:
                # 检查是否需要刷新页面（每5分钟刷新一次）
                current_time = time.time()
                if current_time - start_time >= 300:  # 5分钟 = 300秒
                    logger.info("执行定时刷新...", extra={'client_id': client_id})
                    await manager.send_personal_message(
                        json.dumps({"type": "system", "content": "正在刷新页面..."}),
                        client_id
                    )
                    # 刷新页面
                    driver.get(current_url)
                    logger.info("页面刷新完成，等待聊天容器重新加载...", extra={'client_id': client_id})
                    WebDriverWait(driver, 20).until(
                        EC.presence_of_element_located((By.CLASS_NAME, "webcast-chatroom___list"))
                    )
                    # JSDoc: 页面刷新后再次隐藏和暂停视频元素，双保险
                    # @param {None}
                    driver.execute_script('''
                        /**
                         * 隐藏并暂停直播视频元素，防止资源浪费
                         * @returns {void}
                         */
                        (function hideAndPauseVideo() {
                            var video = document.querySelector('video');
                            if(video){
                                video.pause();
                                video.src = "";
                                video.style.display = "none";
                            }
                            var player = document.querySelector('.xgplayer');
                            if(player){
                                player.style.display = "none";
                            }
                        })();
                    ''')
                    logger.info("已注入隐藏/暂停视频JS（刷新后）", extra={'client_id': client_id})
                    
                    # 重新注入脚本
                    driver.execute_script("""
                        console.clear();
                        window.STYLES = {
                            message: 'color: #4CAF50; font-weight: bold;',
                            audience: 'color: #2196F3; font-weight: bold;',
                            error: 'color: #f44336; font-weight: bold;',
                            system: 'color: #9C27B0; font-weight: bold;'
                        };
                    """)
                    logger.info("基础样式重新注入完成", extra={'client_id': client_id})
                    
                    # 添加自动静音脚本
                    driver.execute_script("""
                        (async function autoMute() {
                            try {
                                // 等待视频元素和控制器加载
                                await new Promise(resolve => {
                                    const checkElements = () => {
                                        const video = document.querySelector('video');
                                        const volumeControl = document.querySelector('.xgplayer-volume');
                                        if (video && volumeControl) {
                                            resolve();
                                        } else {
                                            setTimeout(checkElements, 100);
                                        }
                                    };
                                    checkElements();
                                });

                                // 获取视频元素
                                const videoElement = document.querySelector('video');
                                const volumeControl = document.querySelector('.xgplayer-volume');

                                if (videoElement) {
                                    // 直接设置视频属性
                                    videoElement.muted = true;
                                    videoElement.volume = 0;
                                    
                                    // 确保视频保持静音
                                    if (!videoElement._muteObserverAttached) {
                                        videoElement._muteObserverAttached = true;
                                        
                                        // 监听音量变化
                                        videoElement.addEventListener('volumechange', () => {
                                            if (!videoElement.muted || videoElement.volume > 0) {
                                                videoElement.muted = true;
                                                videoElement.volume = 0;
                                            }
                                        });

                                        // 监听播放状态变化
                                        videoElement.addEventListener('play', () => {
                                            videoElement.muted = true;
                                            videoElement.volume = 0;
                                        });
                                    }
                                }

                                // 点击静音按钮（如果需要）
                                if (volumeControl) {
                                    const volumeIcon = document.querySelector('.xgplayer-icon-volume');
                                    if (volumeIcon && !volumeIcon.parentElement.classList.contains('muted')) {
                                        volumeControl.click();
                                    }
                                }
                                console.log('自动静音设置完成');
                            } catch (err) {
                                console.error('静音设置失败:', err);
                            }
                        })();
                    """)
                    logger.info("静音脚本注入完成", extra={'client_id': client_id})
                    
                    driver.execute_script(MONITOR_SCRIPT)
                    logger.info("监控脚本重新注入完成", extra={'client_id': client_id})
                    # 重置计时器
                    start_time = time.time()
                    logger.info("页面刷新和脚本重注入全部完成", extra={'client_id': client_id})
                    await manager.send_personal_message(
                        json.dumps({"type": "system", "content": "页面刷新完成"}),
                        client_id
                    )

                # 获取并处理日志
                logs = driver.get_log('browser')
                if logs:
                    logger.debug(f"获取到{len(logs)}条浏览器日志", extra={'client_id': client_id})
                
                for log in logs:
                    message = log.get('message', '')
                    if 'console-api' in message:
                        try:
                            # 解析console.log消息
                            if '👥' in message:  # 处理观众数消息
                                match = re.search(r'当前观众: (\d+)', message)
                                if match:
                                    count = match.group(1)
                                    msg_data = {
                                        "type": "audience",
                                        "content": f"当前在线人数: {count}",
                                        "timestamp": time.strftime('%H:%M:%S')
                                    }
                                    # 观众数消息不需要去重，直接发送
                                    await manager.send_personal_message(json.dumps(msg_data), client_id)
                                    logger.info(f"👥 当前观众: {count}", extra={'client_id': client_id})
                            else:  # 处理普通消息
                                # 尝试提取时间戳和实际消息内容
                                match = re.search(r'\[\d{2}:\d{2}:\d{2}\] (.+?)"', message)
                                if match:
                                    content = match.group(1)
                                    timestamp = time.strftime('%H:%M:%S')
                                    
                                    # 判断消息类型
                                    msg_type = "enter" if "来了" in content else "comment"
                                    
                                    if msg_type == "enter":
                                        # 处理进入消息
                                        username = content.split(" ")[0]
                                        msg_content = f"{username} 来了"
                                    else:
                                        # 处理普通消息，检查是否已经包含用户名格式
                                        if "：" in content:  # 注意这里使用中文冒号
                                            username, msg_content = content.split("：", 1)
                                        elif ":" in content:  # 兼容英文冒号
                                            username, msg_content = content.split(":", 1)
                                        else:
                                            username = content
                                            msg_content = content
                                        
                                        username = username.strip()
                                        msg_content = msg_content.strip()
                                    
                                    # 检查消息是否已发送过
                                    if manager.add_to_message_cache(client_id, username, msg_content):
                                        msg_data = {
                                            "type": msg_type,
                                            "username": username,
                                            "content": msg_content,
                                            "timestamp": timestamp
                                        }
                                        await manager.send_personal_message(json.dumps(msg_data), client_id)
                                        logger.info(f"[{timestamp}] {content}", extra={'client_id': client_id})
                                    else:
                                        logger.debug(f"跳过重复消息: {username} - {msg_content}", extra={'client_id': client_id})
                        
                        except Exception as e:
                            logger.error(f"消息处理错误: {str(e)}", extra={'client_id': client_id})
                            continue
                            
            except Exception as e:
                logger.error(f"获取日志错误: {e}", extra={'client_id': client_id})
                retry_count += 1
                if retry_count >= max_retries:
                    raise Exception(f"连续{max_retries}次获取日志失败")
                await asyncio.sleep(1)  # 等待1秒后重试
                continue
            
            await asyncio.sleep(0.1)  # 避免过度占用CPU
            
    except Exception as e:
        error_msg = f"监控错误: {str(e)}"
        logger.error(error_msg, extra={'client_id': client_id})
        await manager.send_personal_message(
            json.dumps({"type": "error", "content": error_msg}),
            client_id
        )

@app.websocket("/ws/live/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    await manager.connect(websocket, client_id)
    driver = None
    webfetcher_adapter = None
    enhanced_adapter = None
    try:
        while True:
            try:
                data = await websocket.receive_text()
                message = json.loads(data)
                logger.info(f"收到消息: {message}", extra={'client_id': client_id})
                if message.get('type') == 'connect':
                    url = message.get('url')
                    platform = message.get('platform', 'douyin')
                    mode = message.get('mode', 'selenium')
                    filter_types = message.get('filterTypes', {
                        'comments': True,
                        'likes': True,
                        'enters': True,
                        'audience': True,
                        'gifts': True,
                        'follows': True
                    })
                    logger.info(f"收到客户端 {client_id} 的连接请求: URL={url}, 平台={platform}, 模式={mode}", extra={'client_id': client_id})
                    logger.info(f"消息过滤设置: {filter_types}", extra={'client_id': client_id})
                    if url:
                        # 清理之前的驱动实例（如果存在）
                        if client_id in manager.drivers:
                            try:
                                manager.drivers[client_id].quit()
                                del manager.drivers[client_id]
                                logger.info(f"已清理客户端 {client_id} 的旧Chrome驱动", extra={'client_id': client_id})
                            except Exception as e:
                                logger.error(f"清理客户端 {client_id} 的旧Chrome驱动失败: {e}", extra={'client_id': client_id})
                        # 清理高性能适配器
                        if client_id in manager.enhanced_adapters:
                            try:
                                manager.enhanced_adapters[client_id].stop()
                                del manager.enhanced_adapters[client_id]
                                logger.info(f"已清理客户端 {client_id} 的旧高性能适配器", extra={'client_id': client_id})
                            except Exception as e:
                                logger.error(f"清理客户端 {client_id} 的旧高性能适配器失败: {e}", extra={'client_id': client_id})
                        try:
                            # 根据平台和模式选择监控方式
                            if platform == 'douyin':
                                if mode == 'high_performance':
                                    if not ENHANCED_ADAPTER_AVAILABLE:
                                        await manager.send_personal_message(json.dumps({"type": "error", "content": "高性能模式不可用"}), client_id)
                                        break
                                    # 启动高性能适配器
                                    enhanced_adapter = DouyinWebFetcherEnhancedAdapter(
                                        live_id=url,
                                        client_id=client_id,
                                        websocket_callback=manager.send_personal_message,
                                        filter_types=filter_types
                                    )
                                    enhanced_adapter.start()
                                    manager.enhanced_adapters[client_id] = enhanced_adapter
                                elif mode == 'webfetcher':
                                    # 使用DouyinLiveWebFetcher模式
                                    logger.info(f"使用DouyinLiveWebFetcher模式监控抖音直播间: {url}", extra={'client_id': client_id})

                                    # 从URL中提取直播间ID
                                    import re
                                    live_id_match = re.search(r'live\.douyin\.com/(\d+)', url)
                                    if not live_id_match:
                                        raise ValueError("无法从URL中提取直播间ID，请确保URL格式正确")

                                    live_id = live_id_match.group(1)
                                    logger.info(f"提取到直播间ID: {live_id}", extra={'client_id': client_id})

                                    # 导入适配器
                                    from AI.backend.douyin_monitor.douyin_webfetcher_adapter import DouyinWebFetcherAdapter

                                    # 创建适配器实例
                                    webfetcher_adapter = DouyinWebFetcherAdapter(
                                        live_id=live_id,
                                        client_id=client_id,
                                        websocket_callback=manager.send_personal_message,
                                        filter_types=filter_types
                                    )

                                    # 启动监控
                                    webfetcher_adapter.start()

                                else:
                                    # 使用Selenium模式
                                    logger.info(f"使用Selenium模式监控抖音直播间: {url}", extra={'client_id': client_id})
                                    driver = manager.setup_chrome_driver(client_id=client_id, platform=platform) # 抖音平台禁用图片加载
                                    manager.drivers[client_id] = driver

                                    # 抖音平台，导航到直播间URL
                                    logger.info(f"导航到抖音直播间: {url}", extra={'client_id': client_id})
                                    driver.get(url)

                                    await monitor_live(driver, client_id) # 调用抖音监控函数
                            elif platform == 'wechat_channels':
                                # 为微信视频号指定用户数据目录
                                # 在项目根目录下创建 chrome_user_data 文件夹用于保存登录状态
                                user_data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "chrome_user_data")
                                os.makedirs(user_data_dir, exist_ok=True) # 确保目录存在
                                logger.info(f"微信视频号使用用户数据目录: {user_data_dir}", extra={'client_id': client_id})

                                driver = wechat_channels_monitor.setup_chrome_driver(client_id=client_id, user_data_dir=user_data_dir)
                                manager.drivers[client_id] = driver
                                await wechat_channels_monitor.monitor_wechat_channels(client_id=client_id, driver=driver, websocket=websocket) # 调用微信视频号监控函数
                            else:
                                await manager.send_personal_message(json.dumps({"type": "error", "content": f"不支持的平台: {platform}"}), client_id)
                                logger.error(f"客户端 {client_id} 请求连接不支持的平台: {platform}", extra={'client_id': client_id})
                                if driver:
                                    try:
                                        driver.quit()
                                    except:
                                        pass
                                    if client_id in manager.drivers:
                                        del manager.drivers[client_id]
                                manager.disconnect(client_id)
                                break
                        except Exception as e:
                            logger.error(f"客户端 {client_id} 启动监控失败: {e}", extra={'client_id': client_id})
                            await manager.send_personal_message(json.dumps({"type": "error", "content": f"启动监控失败: {e}"}), client_id)
                            if driver:
                                try:
                                    driver.quit()
                                except:
                                    pass
                            if client_id in manager.drivers:
                                del manager.drivers[client_id]
                            if webfetcher_adapter:
                                try:
                                    webfetcher_adapter.stop()
                                except:
                                    pass
                            if client_id in manager.enhanced_adapters:
                                try:
                                    manager.enhanced_adapters[client_id].stop()
                                except:
                                    pass
                                del manager.enhanced_adapters[client_id]
                            manager.disconnect(client_id)
                            break
                    else:
                        await manager.send_personal_message(json.dumps({"type": "error", "content": "未提供直播间URL"}), client_id)
                        logger.warning(f"客户端 {client_id} 连接请求未提供URL", extra={'client_id': client_id})
                        manager.disconnect(client_id)
                        break
                # TODO: Handle other message types like 'send_comment', 'send_like', etc.
                # 实时处理前端的 update_filter 消息
                if message.get('type') == 'update_filter':
                    # 仅高性能模式支持实时过滤
                    adapter = manager.enhanced_adapters.get(client_id)
                    if adapter and hasattr(adapter, 'update_filter_types'):
                        adapter.update_filter_types(message.get('filterTypes', {}))
                        await manager.send_personal_message(json.dumps({"type": "system", "content": "已更新消息过滤设置"}), client_id)
                    else:
                        await manager.send_personal_message(json.dumps({"type": "system", "content": "当前模式不支持实时过滤"}), client_id)
                    continue
            except Exception as e:
                if "connection closed" in str(e).lower():
                    logger.info(f"Client {client_id} closed connection", extra={'client_id': client_id})
                    break
                logger.error(f"WebSocket错误: {str(e)}", extra={'client_id': client_id})
                break
    except WebSocketDisconnect:
        manager.disconnect(client_id)
    finally:
        if webfetcher_adapter:
            try:
                webfetcher_adapter.stop()
                logger.info(f"客户端 {client_id} DouyinWebFetcher适配器已停止", extra={'client_id': client_id})
            except Exception as e:
                logger.error(f"客户端 {client_id} 停止DouyinWebFetcher适配器失败: {e}", extra={'client_id': client_id})
        if enhanced_adapter:
            try:
                enhanced_adapter.stop()
                logger.info(f"客户端 {client_id} DouyinWebFetcherEnhancedAdapter已停止", extra={'client_id': client_id})
            except Exception as e:
                logger.error(f"客户端 {client_id} 停止DouyinWebFetcherEnhancedAdapter失败: {e}", extra={'client_id': client_id})
        if client_id in manager.drivers:
            driver_to_close = manager.drivers.pop(client_id)
            logger.info(f"客户端 {client_id} 连接结束，尝试关闭浏览器...", extra={'client_id': client_id})
            try:
                if hasattr(driver_to_close, 'close') and callable(driver_to_close.close) and asyncio.iscoroutinefunction(driver_to_close.close):
                    logger.info(f"客户端 {client_id} 关闭 Playwright 浏览器...", extra={'client_id': client_id})
                    await driver_to_close.close()
                elif hasattr(driver_to_close, 'quit') and callable(driver_to_close.quit):
                    logger.info(f"客户端 {client_id} 关闭 Selenium 浏览器...", extra={'client_id': client_id})
                    driver_to_close.quit()
                else:
                    logger.warning(f"客户端 {client_id} 无法识别的驱动类型，未能关闭浏览器", extra={'client_id': client_id})
                logger.info(f"客户端 {client_id} 浏览器已关闭", extra={'client_id': client_id})
            except Exception as close_e:
                logger.error(f"客户端 {client_id} 关闭浏览器失败: {close_e}", extra={'client_id': client_id})
        manager.disconnect(client_id)
        logger.info(f"客户端 {client_id} 已完成断开和清理", extra={'client_id': client_id})

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)
 