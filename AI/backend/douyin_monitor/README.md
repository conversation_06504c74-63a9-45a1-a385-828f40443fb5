# 抖音直播监控自动化脚本

这个脚本可以自动监控抖音直播间的聊天消息和观众数，支持自动静音和消息过滤功能。

## 功能特点

- 支持输入自定义直播间地址
- 自动静音功能（页面刷新后依然保持静音）
- 智能过滤礼物、点赞等系统消息
- 彩色显示聊天消息和观众数
- 支持快捷键控制（Ctrl+Q 停止监控）
- 定时自动刷新（每5分钟）

## 安装步骤

1. 确保已安装Python 3.7或更高版本
2. 安装依赖包：
```bash
pip install selenium
```
3. 安装Chrome浏览器（如果还没有安装）

## 使用方法

1. 运行脚本：
```bash
python douyin_monitor.py
```

2. 输入直播间地址：
   - 直接输入抖音直播间URL（例如：https://live.douyin.com/123456）
   - 直接按回车使用默认地址
   - 地址格式错误会提示重新输入

3. 监控功能：
   - 自动打开Chrome浏览器和开发者工具
   - 自动静音直播
   - 在控制台显示聊天消息和观众数
   - 每5分钟自动刷新一次
   - 按 Ctrl+Q 可以停止监控
   - 按 Ctrl+C 可以完全退出程序

## 显示效果

- 普通聊天消息：绿色
- 进入直播间消息：紫色
- 观众数统计：蓝色
- 系统提示：紫色

## 过滤规则

自动过滤以下类型的消息：
- 礼物消息（例如："送出了小心心"）
- 点赞消息
- 关注提醒
- 系统通知

## 注意事项

1. 确保网络连接稳定
2. 保持开发者工具的Console面板打开
3. 如遇到问题，可以尝试：
   - 刷新页面
   - 重启程序
   - 检查直播间地址是否正确

## 更新记录

最新更新：
- 添加自定义直播间地址输入功能
- 优化自动静音逻辑，确保刷新后保持静音
- 增强消息过滤功能，过滤礼物消息
- 精简控制台输出，只显示重要信息 