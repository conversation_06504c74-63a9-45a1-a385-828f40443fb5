import os
import shutil
import time
import logging
import base64
import json
import asyncio
import re
import concurrent.futures # 导入 concurrent.futures 用于线程池

# 禁用Selenium Manager自动下载chromedriver
os.environ["SELENIUM_MANAGER_SKIP_DOWNLOAD"] = "1"

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(client_id)s - %(message)s',
    datefmt='%H:%M:%S'
)
logger = logging.getLogger(__name__)

CHROMEDRIVER_PATH_FILE = "chromedriver_path.txt"

WECHAT_CHANNELS_MONITOR_SCRIPT = """
(() => {
    // 定义元素的 CSS 选择器
    const SELECTORS = {
        iframe: 'iframe', // iframe 选择器
        onlineAudienceTitle: '.live-data-item-title', // 在线人数标题的选择器
        onlineAudienceData: '.live-data-item-data', // 在线人数数据选择器
        // 根据您提供的HTML，消息元素是直接添加到某个滚动容器下的
        // 我们需要找到这个滚动容器来监听子节点变化
        chatContainer: '.live-message-scroller-container .vue-recycle-scroller__item-wrapper',
        commentMessage: '.live-message-item', // 单条评论消息的选择器
        commentContent: '.message-content', // 评论消息内容的选择器
        joinMessage: '.live-join-message' // 用户进入直播间消息的选择器
    };

    // 样式定义 (可选，用于控制台输出颜色)
    const STYLES = {
        online: 'color: #2196F3; font-weight: bold;', // 在线人数为蓝色
        comment: 'color: #4CAF50; font-weight: bold;', // 评论为绿色
        join: 'color: #9C27B0; font-weight: bold;', // 进入消息为紫色
        error: 'color: #f44336; font-weight: bold;', // 错误为红色
        system: 'color: #ff9800; font-weight: bold;' // 系统消息为橙色
    };

    let iframeDocument = null;
    // 添加消息缓存，用于简单去重
    const processedMessages = new Set(); // 使用一个缓存集合来存储已处理的消息文本

    const CACHE_MAX_SIZE = 300; // 缓存大小，适当增大以处理快速滚动的消息

    // 查找 iframe 并获取其文档对象
    function getIframeDocument() {
        // 当脚本被注入到 iframe 中时，window.document 就是 iframe 的文档对象
        console.log('%c[系统] 脚本正在 iframe 文档上下文中运行', STYLES.system);
        return window.document;
    }

    /**
     * 消息处理函数，处理评论或进入消息元素
     * @param {Element} element - 需要处理的消息 DOM 元素
     */
    function processMessage(element) {
        if (!element || element.nodeType !== 1) return;

        let messageText = null;
        let messageType = null;

        try {
             if (element.matches(SELECTORS.commentMessage)) {
                 const contentElement = element.querySelector(SELECTORS.commentContent);
                 if (contentElement) {
                     // 对于评论，可能需要更精确地提取文本，避免用户昵称等干扰
                     // 当前使用 textContent，包含了所有子元素的文本
                     messageText = element.textContent.trim();
                     messageType = '评论';

                     // ****** 添加过滤系统通知的逻辑 ******
                     // 检查是否为以 "通知" 开头的系统消息 (更通用的过滤)
                     if (messageText.startsWith('通知')) {
                         // 如果是系统通知，则不处理，直接返回
                         // console.log('%c[系统] 过滤掉系统通知消息', STYLES.system, messageText); // 可选：打印过滤信息用于调试
                         return;
                     }
                     // 检查是否为以 "主题" 开头的消息 (可能也是系统或特殊消息)
                     if (messageText.startsWith('主题')) {
                         // 如果是主题消息，则不处理，直接返回
                         // console.log('%c[系统] 过滤掉主题消息', STYLES.system, messageText); // 可选：打印过滤信息用于调试
                         return;
                     }
                     // ****** 过滤逻辑结束 ******
                 }
             } else if (element.matches(SELECTORS.joinMessage)) {
                 // 对于进入消息，textContent 通常就是完整的提示信息
                 messageText = element.textContent.trim();
                 messageType = '进入提示';
             }

            if (messageText && !processedMessages.has(messageText)) {
                processedMessages.add(messageText);
                // 控制缓存大小
                if (processedMessages.size > CACHE_MAX_SIZE) {
                    const items = Array.from(processedMessages);
                    items.slice(0, CACHE_MAX_SIZE / 2).forEach(item => processedMessages.delete(item));
                }

                // 输出到控制台
                let style = STYLES.system;
                if (messageType === '评论') style = STYLES.comment;
                else if (messageType === '进入提示') style = STYLES.join;

                console.log(`%c[${messageType}] ${messageText}`, style);
            }
        } catch (e) {
            console.log(`%c[系统] 消息处理错误 (${messageType}): ${e.message}`, STYLES.error);
        }
    }

    /**
     * 监控在线人数并输出到控制台
     */
    function monitorOnlineAudience() {
        try {
            let onlineCount = null;
            const titles = document.querySelectorAll(SELECTORS.onlineAudienceTitle);
            for (const title of titles) {
                // 根据实际页面文本调整匹配逻辑
                if (title.textContent.includes('当前在线')) { // 使用更灵活的匹配
                    const parent = title.parentElement;
                    if (parent) {
                        const dataElement = parent.querySelector(SELECTORS.onlineAudienceData);
                        if (dataElement) {
                            onlineCount = dataElement.textContent;
                            break;
                        }
                    }
                }
            }
            // 只有当在线人数变化或首次获取到时才打印，避免刷屏
            // 为了简单，这里每次获取到都打印，你可以根据需要添加判断
            if (onlineCount !== null) {
                 console.log(`%c👥 当前在线人数: ${onlineCount}`, STYLES.online);
            }
        } catch (e) {
             console.log(`%c[系统] 获取在线人数错误: ${e.message}`, STYLES.error);
        }
    }

    /**
     * 监控评论和进入消息 (定时扫描)
     */
    function monitorChatMessages() {
        console.log('%c[系统] 启动消息定时扫描...', STYLES.system);

        // 清空缓存以便重新开始捕获（在 init 中调用一次即可）
        // processedMessages.clear(); // 已经在 init 中处理

        // 使用定时器每隔一段时间扫描一次消息元素
        setInterval(() => {
            try {
                // 查找所有进入提示消息元素
                const joinElements = document.querySelectorAll(SELECTORS.joinMessage);
                joinElements.forEach(processMessage);

                // 查找所有评论消息元素
                const commentElements = document.querySelectorAll(SELECTORS.commentMessage);
                commentElements.forEach(processMessage);

            } catch (e) {
                console.log(`%c[系统] 定时扫描消息错误: ${e.message}`, STYLES.error);
            }
        }, 500); // 每 500 毫秒（0.5 秒）检查一次，可以根据需要调整频率

         console.log('%c[系统] 评论和进入消息定时扫描已启动 (每 500ms)', STYLES.system);

         // 首次立即扫描一次已存在的进入提示和评论，避免遗漏页面加载时已有的消息
         if (document) {
              const initialJoinElements = document.querySelectorAll(SELECTORS.joinMessage);
              initialJoinElements.forEach(processMessage);

              const initialCommentElements = document.querySelectorAll(SELECTORS.commentMessage);
              initialCommentElements.forEach(processMessage);

              if(initialJoinElements.length > 0 || initialCommentElements.length > 0) {
                  console.log('%c[系统] 已处理初始存在的评论和进入提示', STYLES.system);
              }
         }
    }

    /**
     * 初始化脚本，查找 iframe 并启动监控任务
     */
    function init() {
        console.log('%c[系统] 启动微信视频号监控脚本初始化...', STYLES.system);

        // 脚本直接在目标 iframe 中执行，无需查找 iframeDocument
        processedMessages.clear(); // 在初始化成功后清空缓存

        monitorOnlineAudience(); // 启动在线人数监控 (首次立即获取)
        monitorChatMessages(); // 启动评论和进入消息定时扫描

        // 设置定时器定期更新在线人数
        setInterval(monitorOnlineAudience, 5000); // 每 5 秒更新一次在线人数

        console.log('%c[系统] 微信视频号监控脚本初始化完成', STYLES.system);
    }

    // 脚本开始执行
    init();

})();"""

def get_or_download_chromedriver():
    """
    获取可用的chromedriver路径，若本地存在则优先使用本地文件，否则自动下载
    :return: chromedriver路径
    """
    # 1. 优先查找当前目录下的 chromedriver.exe
    local_driver = os.path.join(os.path.dirname(__file__), "chromedriver.exe")
    if os.path.exists(local_driver):
        return local_driver

    # 2. 优先读取本地缓存
    if os.path.exists(CHROMEDRIVER_PATH_FILE):
        with open(CHROMEDRIVER_PATH_FILE, "r", encoding="utf-8") as f:
            path = f.read().strip()
            if os.path.exists(path):
                return path
    # 3. 自动下载
    logger.info("未找到本地chromedriver，尝试自动下载...", extra={'client_id': 'system'})
    try:
        path = ChromeDriverManager().install()
        logger.info(f"chromedriver自动下载成功: {path}", extra={'client_id': 'system'})
        # 写入缓存
        with open(CHROMEDRIVER_PATH_FILE, "w", encoding="utf-8") as f:
            f.write(path)
        return path
    except Exception as e:
        logger.error(f"chromedriver自动下载失败: {e}", extra={'client_id': 'system'})
        raise

def setup_chrome_driver(client_id: str, user_data_dir: str | None = None) -> webdriver.Chrome:
    """
    设置并返回一个配置好的Chrome驱动实例
    :param user_data_dir: 可选，指定用户数据目录，用于保存登录状态等
    :return: 配置好的webdriver.Chrome实例
    """
    logger.info("开始创建Chrome驱动", extra={'client_id': client_id})
    chrome_options = Options()

    # 基础优化选项
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-extensions')
    chrome_options.add_argument('--disable-sync')
    chrome_options.add_argument('--window-size=1920,1080')
    chrome_options.page_load_strategy = 'normal' # 对于需要扫码登录的页面，需要等待页面完全加载

    # 添加用户数据目录参数
    if user_data_dir:
        chrome_options.add_argument(f'--user-data-dir={os.path.abspath(user_data_dir)}')
        logger.info(f"使用用户数据目录: {os.path.abspath(user_data_dir)}", extra={'client_id': client_id})

    # 禁用日志
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    chrome_options.add_argument('--log-level=3')

    # 设置日志首选项（如果需要从控制台获取注入脚本的输出）
    chrome_options.set_capability('goog:loggingPrefs', {'browser': 'ALL'})

    # 禁用图片、音频等资源加载以节省资源，但扫码页需要图片，先不禁用
    # prefs = {"profile.default_content_setting_values": {"images": 2, ...}}
    # chrome_options.add_experimental_option("prefs", prefs)

    logger.info("Chrome驱动配置完成，开始创建实例...", extra={'client_id': client_id})

    try:
        chromedriver_path = get_or_download_chromedriver()
        service = Service(executable_path=chromedriver_path)
        driver = webdriver.Chrome(service=service, options=chrome_options)
        logger.info(f"Chrome驱动实例创建成功，chromedriver路径: {chromedriver_path}", extra={'client_id': client_id})

        # 设置超时时间
        driver.set_page_load_timeout(30) # 页面加载超时时间稍微长一点以应对登录页
        driver.set_script_timeout(10)    # 脚本执行超时时间

        logger.info("Chrome驱动设置完成", extra={'client_id': client_id})

        return driver

    except Exception as e:
        logger.error(f"创建Chrome驱动失败: {str(e)}", extra={'client_id': client_id})
        raise

# 定义同步辅助函数 ----------------------------------------------------------

def _check_already_logged_in_sync(driver: webdriver.Chrome, iframe_selector: str) -> bool:
    """
    同步函数：检查是否已登录 (通过等待登录 iframe 消失)
    在线程池中运行
    """
    try:
        logger.info("同步函数: 检查是否已登录...", extra={'client_id': 'sync'})
        # 短暂等待iframe消失，例如5秒
        WebDriverWait(driver, 5).until(
            EC.invisibility_of_element_located((By.CSS_SELECTOR, iframe_selector))
        )
        logger.info("同步函数: iframe 快速消失，可能已登录。", extra={'client_id': 'sync'})
        return True # 如果iframe消失，返回True
    except TimeoutException:
        logger.info("同步函数: iframe 仍然存在，需要扫码登录。", extra={'client_id': 'sync'})
        return False # 如果超时，返回False
    except Exception as e:
        logger.error(f"同步函数: 检查登录状态时发生错误: {str(e)}", extra={'client_id': 'sync'})
        # 对于其他异常，记录并继续，或者根据需要选择重新抛出
        return False

def _get_qrcode_sync(driver: webdriver.Chrome, iframe_selector: str, qrcode_selector: str) -> str | None:
    """
    同步函数：等待登录 iframe 出现，切换到 iframe，等待二维码，获取二维码 src
    在线程池中运行
    """
    try:
        logger.info("同步函数: 等待登录框出现...", extra={'client_id': 'sync'})
        iframe_element = WebDriverWait(driver, 30).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, iframe_selector))
        )
        logger.info("同步函数: 已找到 iframe 元素，切换到 iframe", extra={'client_id': 'sync'})
        driver.switch_to.frame(iframe_element)
        logger.info("同步函数: 已切换到 iframe，等待二维码...", extra={'client_id': 'sync'})

        # 在iframe内部等待二维码元素出现
        qr_element = WebDriverWait(driver, 30).until(
            EC.visibility_of_element_located((By.CSS_SELECTOR, qrcode_selector))
        )
        logger.info("同步函数: 二维码元素已在iframe中找到，获取 src", extra={'client_id': 'sync'})

        # 获取二维码图片的 src 属性值
        qr_code_src = qr_element.get_attribute('src')
        logger.info(f"同步函数: 获取到二维码 src: {qr_code_src[:50]}...", extra={'client_id': 'sync'})

        # 切换回主页面上下文 (确保后续操作在主页面)
        driver.switch_to.default_content()
        logger.info("同步函数: 已切换回主页面上下文", extra={'client_id': 'sync'})

        return qr_code_src

    except (TimeoutException, NoSuchElementException) as e:
        logger.error(f"同步函数: 获取二维码失败或超时: {str(e)}", extra={'client_id': 'sync'})
        # 切换回主页面再抛出异常，避免在iframe中异常导致驱动状态问题
        try:
            driver.switch_to.default_content()
        except:
            pass # 忽略切换失败的错误
        return None # 返回None表示获取失败
    except Exception as e:
        logger.error(f"同步函数: 获取二维码过程中发生其他错误: {str(e)}", extra={'client_id': 'sync'})
        try:
            driver.switch_to.default_content()
        except:
            pass # 忽略切换失败的错误
        return None

def _wait_for_login_success_sync(driver: webdriver.Chrome, iframe_selector: str) -> bool:
    """
    同步函数：等待扫码登录成功 (判断登录 iframe 是否消失)
    在线程池中运行
    """
    try:
        logger.info("同步函数: 等待登录成功，判断 iframe 是否消失...", extra={'client_id': 'sync'})
        # 等待登录成功：判断之前的iframe是否消失 (假设登录成功后iframe会从页面移除或隐藏)
        WebDriverWait(driver, 120).until(
            EC.invisibility_of_element_located((By.CSS_SELECTOR, iframe_selector)) # 等待iframe不可见或移除
        )
        logger.info("同步函数: 检测到登录成功", extra={'client_id': 'sync'})
        return True # 如果 iframe 消失，返回 True
    except TimeoutException:
        logger.warning("同步函数: 等待登录超时，未能检测到登录成功。", extra={'client_id': 'sync'})
        return False # 如果超时，返回 False
    except Exception as e:
        logger.error(f"同步函数: 等待登录成功时发生错误: {str(e)}", extra={'client_id': 'sync'})
        return False

def _find_live_iframe_and_switch_sync(driver: webdriver.Chrome, initial_url: str, max_retries: int, retry_interval: int, key_element_selector: str) -> bool:
    """
    同步函数：登录成功后，循环查找包含直播内容的 iframe 并切换
    在线程池中运行
    """
    logger.info("同步函数: 准备查找直播内容 iframe...", extra={'client_id': 'sync'})
    live_iframe_found = False
    for attempt in range(max_retries):
        logger.info(f"同步函数: 尝试查找直播内容 iframe (尝试 {attempt + 1}/{max_retries})...", extra={'client_id': 'sync'})

        # 在每次尝试前，确保在主页面并导航
        try:
            driver.get(initial_url) # 导航到目标页面 (Selenium 是同步的)
            logger.info(f"同步函数: 已导航到 {initial_url}", extra={'client_id': 'sync'})
            # 等待页面加载一些时间 (等待 iframe 加载)
            time.sleep(5)
        except Exception as nav_e:
            logger.error(f"同步函数: 导航到 {initial_url} 失败: {nav_e}", extra={'client_id': 'sync'})
            if attempt < max_retries - 1:
                time.sleep(retry_interval)
            continue

        iframes = driver.find_elements(By.TAG_NAME, 'iframe')
        logger.info(f"同步函数: 页面上找到 {len(iframes)} 个 frame", extra={'client_id': 'sync'})

        for iframe in iframes:
            try:
                logger.info(f"同步函数: 尝试切换到 iframe 并查找元素: {key_element_selector}", extra={'client_id': 'sync'})
                driver.switch_to.frame(iframe) # 切换到当前 iframe 元素
                logger.info("同步函数: 已切换到 iframe", extra={'client_id': 'sync'})

                # 在 iframe 内部，等待直播内容的关键元素出现
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, key_element_selector))
                )
                logger.info("同步函数: 在 iframe 内部找到关键元素", extra={'client_id': 'sync'})

                live_iframe_found = True
                break # 跳出遍历frame的循环

            except Exception as e:
                logger.info(f"同步函数: 在 iframe 内部未找到关键元素或超时: {str(e)}", extra={'client_id': 'sync'})
                # 在这个 frame 没找到，切换回主页面继续尝试下一个 iframe
                driver.switch_to.default_content()
                logger.info("同步函数: 已切换回主页面", extra={'client_id': 'sync'})

        if live_iframe_found:
            logger.info("同步函数: 成功找到包含直播内容的 frame.", extra={'client_id': 'sync'})
            break # 找到 iframe，跳出重试循环
        else:
            logger.warning(f"同步函数: 未找到包含直播内容的 frame (尝试 {attempt + 1}/{max_retries})...", extra={'client_id': 'sync'})
            if attempt < max_retries - 1:
                logger.info(f"同步函数: 等待 {retry_interval} 秒后重试...", extra={'client_id': 'sync'})
                time.sleep(retry_interval)
            else:
                logger.error(f"同步函数: 多次尝试后未能找到包含直播内容的 frame，放弃重试.", extra={'client_id': 'sync'})

    if not live_iframe_found:
        logger.error("同步函数: 多次尝试后未能加载到直播公屏页面.", extra={'client_id': 'sync'})

    return live_iframe_found # 返回是否成功找到 iframe

# 异步监控函数 -------------------------------------------------------------

async def monitor_wechat_channels(client_id: str, driver: webdriver.Chrome, websocket):
    """
    监控微信视频号直播，处理扫码登录、导航到监控页面并注入脚本，并从浏览器获取数据
    :param driver: webdriver.Chrome实例
    :param websocket: WebSocket连接对象，用于与前端通信
    """
    WECHAT_CHANNELS_LOGIN_URL = "https://channels.weixin.qq.com/platform/live/liveBuild"
    iframe_selector = 'iframe[src*="login-for-iframe"]'
    qrcode_selector = 'img[src^="data:image/png;base64,"]'
    initial_url_after_login = "https://channels.weixin.qq.com/platform/live/liveBuild" # 登录后的目标URL
    key_element_selector = '.live-data-item-title, .live-message-scroller-container' # 用于判断直播iframe的关键元素选择器
    max_iframe_retries = 10
    iframe_retry_interval = 10

    logger.info(f"开始监控微信视频号 - 访问地址: {WECHAT_CHANNELS_LOGIN_URL}", extra={'client_id': client_id})

    try:
        await websocket.send_text(json.dumps({"type": "system", "content": "正在打开微信视频号登录页面..."}))
        # Selenium get 是同步的，但通常不会阻塞太久，可以放在主事件循环
        driver.get(WECHAT_CHANNELS_LOGIN_URL)
        logger.info("已导航到微信视频号登录页面", extra={'client_id': client_id})

        # 检查是否已登录的逻辑放到线程池
        is_logged_in = await asyncio.get_event_loop().run_in_executor(
            None,
            _check_already_logged_in_sync,
            driver,
            iframe_selector
        )

        if is_logged_in:
            logger.info("检测到已登录状态，跳过扫码流程。", extra={'client_id': client_id})
            await websocket.send_text(json.dumps({
                "type": "login_success",
                "content": "检测到已登录状态，跳过扫码",
                "platform": "wechat_channels"
            }))

        else: # 需要扫码登录
            logger.info("需要扫码登录，等待二维码出现。", extra={'client_id': client_id})
            await websocket.send_text(json.dumps({"type": "system", "content": "等待加载登录框和二维码..."}))

            # 获取二维码的逻辑放到线程池
            qr_code_src = await asyncio.get_event_loop().run_in_executor(
                None,
                _get_qrcode_sync,
                driver,
                iframe_selector,
                qrcode_selector
            )

            if qr_code_src:
                # 将二维码数据发送给前端 (这个是异步操作，不会阻塞)
                await websocket.send_text(json.dumps({
                    "type": "qrcode",
                    "data": qr_code_src,
                    "platform": "wechat_channels"
                }))
                logger.info("已将二维码数据发送到前端", extra={'client_id': client_id})

                await websocket.send_text(json.dumps({"type": "system", "content": "请使用微信扫码登录..."}))

                # 等待扫码登录成功的逻辑放到线程池
                login_successful = await asyncio.get_event_loop().run_in_executor(
                    None,
                    _wait_for_login_success_sync,
                    driver,
                    iframe_selector
                )

                if login_successful:
                    logger.info("检测到登录成功。", extra={'client_id': client_id})
                    await websocket.send_text(json.dumps({
                        "type": "login_success",
                        "content": "微信视频号登录成功",
                        "platform": "wechat_channels"
                    }))
                else:
                    logger.warning("等待登录超时，未能检测到登录成功。", extra={'client_id': client_id})
                    await websocket.send_text(json.dumps({
                        "type": "warning",
                        "content": "等待登录超时，请手动检查浏览器窗口",
                        "platform": "wechat_channels"
                    }))
                    # 超时不代表失败，可能用户已经扫码但页面未按预期跳转，继续尝试查找直播 iframe

            else: # 获取二维码失败
                logger.error("获取二维码失败，无法继续登录流程。", extra={'client_id': client_id})
                await websocket.send_text(json.dumps({"type": "error", "content": "获取二维码失败，请重试。"}))
                # 获取二维码失败是严重错误，直接退出
                return

        # 不管是直接登录还是扫码登录成功（或超时），都尝试查找直播内容 iframe
        live_iframe_found = await asyncio.get_event_loop().run_in_executor(
            None,
            _find_live_iframe_and_switch_sync,
            driver,
            initial_url_after_login,
            max_iframe_retries,
            iframe_retry_interval,
            key_element_selector
        )

        if not live_iframe_found:
            logger.error("未能加载到直播公屏页面，放弃监控。", extra={'client_id': client_id})
            await websocket.send_text(json.dumps({"type": "error", "content": "未能加载到直播公屏页面，请确认账号是否开播或尝试刷新。"}))
            return # 退出函数，不再进行脚本注入和日志监听

        # 如果成功找到直播内容 iframe 并切换，则注入脚本和监听日志
        logger.info("成功找到包含直播内容的 iframe，开始注入监控脚本。", extra={'client_id': client_id})

        # 注入脚本 (这个通常是同步的，但也很快，可以放在主事件循环，或者同样放到线程池以防万一)
        # 为了安全起见，也放到线程池
        await asyncio.get_event_loop().run_in_executor(
            None,
            driver.execute_script,
            WECHAT_CHANNELS_MONITOR_SCRIPT
        )
        logger.info("微信视频号监控脚本已注入。", extra={'client_id': client_id})

        await websocket.send_text(json.dumps({
            "type": "system",
            "content": "微信视频号监控脚本已注入，开始监听数据..."
        }))

        # =========================================================
        # 实现从浏览器控制台日志中捕获脚本输出并发送到 WebSocket 的逻辑
        # 这部分监听日志的循环可以是异步的，但获取日志本身是同步的
        # 因此，日志获取也需要放到线程池中
        # =========================================================
        while True:
            # 在异步循环中，我们需要在获取日志前稍微等待，避免过度调用同步的 get_log
            await asyncio.sleep(0.5) # 适当增加等待时间，比如0.5秒

            try:
                # 获取浏览器控制台日志 (同步操作，放到线程池)
                logs = await asyncio.get_event_loop().run_in_executor(
                    None,
                    driver.get_log,
                    'browser'
                )

                for log in logs:
                    message = log.get('message', '')

                    # 解析脚本输出的特定格式消息 (这部分是异步安全的)
                    # 尝试匹配包含 %c 和样式信息的 [评论] 或 [进入提示] 日志
                    message_match = re.search(r'%c\[(评论|进入提示)\]\s*(.+?)\"\s*\"', message)

                    # 尝试匹配在线人数日志
                    audience_match = re.search(r'当前在线人数:\s*(\d+)', message)

                    # 尝试匹配系统错误或其他脚本错误日志
                    error_match = re.search(r'\[系统错误\]\s*(.+)', message) or \
                                  re.search(r'初始化错误:\s*(.+)', message) or \
                                  re.search(r'消息处理错误:\s*(.+)', message) or \
                                  re.search(r'获取在线人数错误:\s*(.+)', message) or \
                                  re.search(r'定时扫描消息错误:\s*(.+)', message);

                    if audience_match:
                        try:
                            # 处理观众数消息
                            count_str = audience_match.group(1)
                            msg_data = {
                                "type": "audience",
                                "content": f"当前在线人数: {count_str}",
                                "count": int(count_str) if count_str.isdigit() else 0,
                                "timestamp": time.time()
                            }
                            await websocket.send_text(json.dumps(msg_data))
                            logger.info(f"[在线人数] {count_str}", extra={'client_id': client_id})
                        except Exception as e:
                            logger.error(f"解析在线人数日志错误: {str(e)}", extra={'client_id': client_id})

                    elif message_match:
                        # 处理评论或进入提示消息
                        try:
                            msg_type_match = message_match.group(1)
                            raw_content = message_match.group(2).strip()

                            # 清理开头的双引号和末尾的双引号（如果存在）
                            if raw_content.startswith('"') and raw_content.endswith('"'):
                                content = raw_content[1:-1].strip()
                            else:
                                 content = raw_content.strip()

                            msg_type = "comment"
                            if msg_type_match == "进入提示":
                                msg_type = "enter"

                            # 尝试从内容中提取用户名和消息 (如果存在中文冒号或英文冒号)
                            if "：" in content:
                                parts = content.split("：", 1)
                                username = parts[0].strip()
                                msg_content = parts[1].strip()
                            elif ":" in content:
                                parts = content.split(":", 1)
                                username = parts[0].strip()
                                msg_content = parts[1].strip()
                            else:
                                # 如果没有冒号，整个内容作为消息，用户名设为"用户"或尝试从"来了"格式提取
                                msg_content = content
                                username = "用户"
                                if msg_type == "enter" and msg_content.endswith("来了"):
                                     # 尝试从"XXX 来了"格式中提取用户名
                                     name_match = re.search(r'(.+?)\s+来了', msg_content)
                                     if name_match:
                                         username = name_match.group(1).strip()

                            # 检查消息是否已发送过（去重）
                            # 注意：这里的去重逻辑需要与 ConnectionManager 中的去重逻辑协调一致
                            # 如果 ConnectionManager 负责WebSocket消息的去重，这里可以省略或简化
                            # 暂时保留，但以后可能需要调整
                            # if manager.add_to_message_cache(client_id, username, msg_content): # 如果在monitor_wechat_channels中直接发送，ConnectionManager的缓存可能不适用

                            msg_data = {
                                "type": msg_type,
                                "username": username,
                                "content": msg_content,
                                "timestamp": time.time() # 使用后端时间戳更准确
                            }
                            await websocket.send_text(json.dumps(msg_data))
                            if msg_type == "comment":
                                logger.info(f"[评论] {username}: {msg_content}", extra={'client_id': client_id})
                            elif msg_type == "enter":
                                logger.info(f"[进入提示] {username} {msg_content}", extra={'client_id': client_id})

                        except Exception as e:
                            logger.error(f"解析消息日志错误: {str(e)} - 原始日志: {message}", extra={'client_id': client_id})

                    elif error_match:
                        # 处理系统错误或其他脚本错误日志
                        if error_match:
                            error_content = error_match.group(1).strip()
                        else:
                             error_content = message.strip()

                        await websocket.send_text(json.dumps({
                            "type": "error",
                            "content": f"浏览器脚本错误: {error_content}",
                            "timestamp": time.time()
                        }))
                        logger.error(f"[系统错误] {error_content}", extra={'client_id': client_id})

            except WebDriverException as e:
                # 捕获WebDriverException，可能是浏览器已关闭
                logger.error(f"获取浏览器日志时WebDriver错误: {str(e)}", extra={'client_id': client_id})
                # 此时浏览器可能已意外关闭，退出监控循环
                break
            except Exception as e:
                # 监控循环中出现其他错误
                logger.error(f"微信视频号监控循环错误: {str(e)}", extra={'client_id': client_id})
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "content": f"监控过程中出现错误: {str(e)}",
                    "timestamp": time.time()
                }))
                # 发生错误时退出循环
                break

        # 监控循环结束
        logger.info("微信视频号监控循环结束。", extra={'client_id': client_id})

    except Exception as e:
        logger.error(f"微信视频号监控启动失败: {str(e)}", extra={'client_id': client_id})
        await websocket.send_text(json.dumps({"type": "error", "content": f"微信视频号监控启动失败: {str(e)}"}))
        # 清理驱动（这部分应该在 ConnectionManager 的 disconnect 中处理，这里作为备用）
        # try:
        #     driver.quit()
        # except:
        #     pass


# 注意：这个文件本身不是一个 FastAPI 应用，它将被 live_server.py 调用

# 用于测试的占位符，实际会被 live_server.py 替换或调用
# async def placeholder_monitor_wechat_channels(client_id: str, driver: webdriver.Chrome, websocket):
#      logger.info("Placeholder monitor_wechat_channels called.", extra={'client_id': client_id})
#      await websocket.send_text(json.dumps({"type": "system", "content": "微信视频号监控模块正在开发中..."}))
#      # 模拟等待和结束
#      await asyncio.sleep(10)
#      await websocket.send_text(json.dumps({"type": "system", "content": "微信视频号监控Placeholder结束."}))
#      try:
#         driver.quit()
#      except:
#         pass 