import asyncio
import json
import logging
import threading
import time
import re
from typing import Optional, Dict, Callable, Set

# 导入DouyinLiveWebFetcher
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'DouyinLiveWebFetcher-main'))
from liveMan import DouyinLiveWebFetcher

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(client_id)s - %(message)s',
    datefmt='%H:%M:%S'
)
logger = logging.getLogger(__name__)

class DouyinWebFetcherEnhancedAdapter:
    """
    DouyinLiveWebFetcher增强适配器
    将DouyinLiveWebFetcher的消息转换为互动回复系统需要的格式
    支持消息过滤、去重、格式化和统计
    """
    
    def __init__(self, live_id: str, client_id: str, websocket_callback: Callable, filter_types: Optional[Dict] = None):
        """
        初始化适配器
        
        Args:
            live_id: 直播间ID（支持URL或纯ID）
            client_id: 客户端ID
            websocket_callback: WebSocket消息发送回调函数
            filter_types: 消息类型过滤器
        """
        # 处理live_id，支持URL格式
        self.live_id = self._extract_live_id(live_id)
        self.client_id = client_id
        self.websocket_callback = websocket_callback
        self.filter_types = filter_types or {
            'comments': True,
            'likes': False,      # 默认关闭点赞，避免刷屏
            'enters': True,
            'audience': True,
            'gifts': False,      # 默认关闭礼物，避免刷屏
            'follows': True
        }
        
        self.fetcher: Optional[DouyinLiveWebFetcher] = None
        self.is_running = False
        self.message_cache: Set[str] = set()
        self.max_cache_size = 1000
        
        # 消息去重时间窗口
        self.message_timeout = 5
        self.message_timestamps: Dict[str, float] = {}
        
        # 统计信息
        self.stats = {
            'total_messages': 0,
            'filtered_messages': 0,
            'duplicate_messages': 0,
            'start_time': None,
            'by_type': {
                'comments': 0,
                'enters': 0,
                'likes': 0,
                'gifts': 0,
                'follows': 0,
                'audience': 0
            }
        }
        
    def _extract_live_id(self, live_input: str) -> str:
        """
        从输入中提取直播间ID
        支持完整URL和纯ID两种格式
        """
        if not live_input:
            raise ValueError("直播间ID不能为空")
            
        # 如果是URL格式，提取ID
        if 'live.douyin.com' in live_input:
            match = re.search(r'live\.douyin\.com/(\d+)', live_input)
            if match:
                return match.group(1)
            else:
                raise ValueError("无法从URL中提取直播间ID")
        
        # 如果是纯数字ID
        if live_input.isdigit():
            return live_input
            
        raise ValueError("无效的直播间ID格式")
        
    def start(self):
        """启动监控"""
        try:
            logger.info(f"启动DouyinWebFetcher增强监控 - 直播间ID: {self.live_id}", extra={'client_id': self.client_id})
            
            # 创建DouyinLiveWebFetcher实例
            self.fetcher = DouyinLiveWebFetcher(self.live_id)
            
            # 替换原有的消息处理方法
            self._patch_fetcher_methods()
            
            # 检查直播间状态
            self.fetcher.get_room_status()
            
            # 发送连接成功消息
            self._send_message_sync({
                "type": "system",
                "content": f"高性能监控已连接直播间 {self.live_id}，开始监控..."
            })
            
            # 记录启动时间
            self.stats['start_time'] = time.time()
            
            # 在新线程中启动WebSocket连接
            self.is_running = True
            thread = threading.Thread(target=self._run_fetcher, daemon=True)
            thread.start()
            
            logger.info("DouyinWebFetcher增强监控启动成功", extra={'client_id': self.client_id})
            
        except Exception as e:
            logger.error(f"启动DouyinWebFetcher增强监控失败: {str(e)}", extra={'client_id': self.client_id})
            self._send_message_sync({
                "type": "error",
                "content": f"启动监控失败: {str(e)}"
            })
            raise
            
    def stop(self):
        """停止监控"""
        try:
            logger.info("停止DouyinWebFetcher增强监控", extra={'client_id': self.client_id})
            self.is_running = False
            
            if self.fetcher:
                self.fetcher.stop()
                
            # 发送统计信息
            self._send_stats_summary()
            
            self._send_message_sync({
                "type": "system",
                "content": "高性能监控已停止"
            })
            
        except Exception as e:
            logger.error(f"停止监控失败: {str(e)}", extra={'client_id': self.client_id})
            
    def _run_fetcher(self):
        """在线程中运行fetcher"""
        try:
            self.fetcher.start()
        except Exception as e:
            logger.error(f"DouyinWebFetcher运行错误: {str(e)}", extra={'client_id': self.client_id})
            self._send_message_sync({
                "type": "error",
                "content": f"监控运行错误: {str(e)}"
            })
            
    def _patch_fetcher_methods(self):
        """替换fetcher的消息处理方法"""
        # 保存原始方法的引用（用于调试）
        self.fetcher._original_parseChatMsg = self.fetcher._parseChatMsg
        self.fetcher._original_parseGiftMsg = self.fetcher._parseGiftMsg
        self.fetcher._original_parseLikeMsg = self.fetcher._parseLikeMsg
        self.fetcher._original_parseMemberMsg = self.fetcher._parseMemberMsg
        self.fetcher._original_parseSocialMsg = self.fetcher._parseSocialMsg
        self.fetcher._original_parseRoomUserSeqMsg = self.fetcher._parseRoomUserSeqMsg
        
        # 替换为适配器的方法
        self.fetcher._parseChatMsg = self._handle_chat_message
        self.fetcher._parseGiftMsg = self._handle_gift_message
        self.fetcher._parseLikeMsg = self._handle_like_message
        self.fetcher._parseMemberMsg = self._handle_member_message
        self.fetcher._parseSocialMsg = self._handle_social_message
        self.fetcher._parseRoomUserSeqMsg = self._handle_room_stats_message
        
        # 替换WebSocket事件处理
        self.fetcher._original_wsOnOpen = self.fetcher._wsOnOpen
        self.fetcher._original_wsOnClose = self.fetcher._wsOnClose
        self.fetcher._original_wsOnError = self.fetcher._wsOnError
        
        self.fetcher._wsOnOpen = self._handle_ws_open
        self.fetcher._wsOnClose = self._handle_ws_close
        self.fetcher._wsOnError = self._handle_ws_error
        
    def _send_message_sync(self, message: dict):
        """同步发送消息到WebSocket"""
        try:
            # 使用线程安全的方式调用异步函数
            import concurrent.futures

            def run_in_thread():
                # 在新线程中创建事件循环
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    return loop.run_until_complete(self.websocket_callback(json.dumps(message), self.client_id))
                finally:
                    loop.close()

            # 在线程池中执行
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_in_thread)
                future.result(timeout=5.0)  # 5秒超时

        except Exception as e:
            logger.error(f"发送WebSocket消息失败: {str(e)}", extra={'client_id': self.client_id})
            
    def _is_duplicate_message(self, message_key: str) -> bool:
        """检查消息是否重复"""
        current_time = time.time()
        
        # 清理过期的时间戳
        expired_keys = [
            key for key, timestamp in self.message_timestamps.items()
            if current_time - timestamp > self.message_timeout
        ]
        for key in expired_keys:
            del self.message_timestamps[key]
            self.message_cache.discard(key)
            
        # 检查是否重复
        if message_key in self.message_cache:
            self.stats['duplicate_messages'] += 1
            return True
            
        # 添加到缓存
        self.message_cache.add(message_key)
        self.message_timestamps[message_key] = current_time
        
        # 控制缓存大小
        if len(self.message_cache) > self.max_cache_size:
            # 移除最旧的消息
            oldest_key = min(self.message_timestamps.keys(), key=lambda k: self.message_timestamps[k])
            self.message_cache.discard(oldest_key)
            del self.message_timestamps[oldest_key]
            
        return False
        
    def _send_stats_summary(self):
        """发送统计摘要"""
        if self.stats['start_time']:
            duration = time.time() - self.stats['start_time']
            summary = {
                "type": "system",
                "content": f"监控统计: 总消息{self.stats['total_messages']}条, "
                          f"过滤{self.stats['filtered_messages']}条, "
                          f"去重{self.stats['duplicate_messages']}条, "
                          f"运行{duration:.1f}秒"
            }
            self._send_message_sync(summary)

    # ==================== 消息处理方法 ====================

    def _handle_chat_message(self, payload):
        """处理聊天消息"""
        if not self.filter_types.get('comments', True):
            self.stats['filtered_messages'] += 1
            return

        try:
            # 调用原始方法获取消息信息
            from protobuf.douyin import ChatMessage
            message = ChatMessage().parse(payload)

            username = message.user.nick_name
            content = message.content
            user_id = str(message.user.id)

            # 生成消息唯一标识
            message_key = f"chat_{username}_{content}_{int(time.time() // 2)}"  # 2秒内去重

            if self._is_duplicate_message(message_key):
                return

            # 构造适配前端的消息格式
            msg_data = {
                "type": "comment",
                "username": username,
                "content": content,
                "user_id": user_id,
                "timestamp": time.time()
            }

            self.stats['total_messages'] += 1
            self.stats['by_type']['comments'] += 1

            logger.info(f"[评论] {username}: {content}", extra={'client_id': self.client_id})
            self._send_message_sync(msg_data)

        except Exception as e:
            logger.error(f"处理聊天消息失败: {str(e)}", extra={'client_id': self.client_id})

    def _handle_member_message(self, payload):
        """处理进入直播间消息"""
        if not self.filter_types.get('enters', True):
            self.stats['filtered_messages'] += 1
            return

        try:
            from protobuf.douyin import MemberMessage
            message = MemberMessage().parse(payload)

            username = message.user.nick_name
            user_id = str(message.user.id)
            gender = ["女", "男"][message.user.gender] if message.user.gender in [0, 1] else "未知"

            # 生成消息唯一标识
            message_key = f"enter_{username}_{int(time.time() // 10)}"  # 10秒内去重

            if self._is_duplicate_message(message_key):
                return

            # 构造适配前端的消息格式
            msg_data = {
                "type": "enter",
                "username": username,
                "content": f"{username} 来了",
                "user_id": user_id,
                "gender": gender,
                "timestamp": time.time()
            }

            self.stats['total_messages'] += 1
            self.stats['by_type']['enters'] += 1

            logger.info(f"[进入] {username} 来了", extra={'client_id': self.client_id})
            self._send_message_sync(msg_data)

        except Exception as e:
            logger.error(f"处理进入消息失败: {str(e)}", extra={'client_id': self.client_id})

    def _handle_like_message(self, payload):
        """处理点赞消息"""
        if not self.filter_types.get('likes', False):
            self.stats['filtered_messages'] += 1
            return

        try:
            from protobuf.douyin import LikeMessage
            message = LikeMessage().parse(payload)

            username = message.user.nick_name
            count = message.count
            user_id = str(message.user.id)

            # 点赞消息合并处理，避免刷屏
            message_key = f"like_{username}_{int(time.time() // 5)}"  # 5秒内合并

            if self._is_duplicate_message(message_key):
                return

            # 构造适配前端的消息格式
            msg_data = {
                "type": "like",
                "username": username,
                "content": f"{username} 点了{count}个赞",
                "user_id": user_id,
                "count": count,
                "timestamp": time.time()
            }

            self.stats['total_messages'] += 1
            self.stats['by_type']['likes'] += 1

            logger.info(f"[点赞] {username} 点了{count}个赞", extra={'client_id': self.client_id})
            self._send_message_sync(msg_data)

        except Exception as e:
            logger.error(f"处理点赞消息失败: {str(e)}", extra={'client_id': self.client_id})

    def _handle_gift_message(self, payload):
        """处理礼物消息"""
        if not self.filter_types.get('gifts', False):
            self.stats['filtered_messages'] += 1
            return

        try:
            from protobuf.douyin import GiftMessage
            message = GiftMessage().parse(payload)

            username = message.user.nick_name
            gift_name = message.gift.name
            gift_count = message.combo_count
            user_id = str(message.user.id)

            # 生成消息唯一标识
            message_key = f"gift_{username}_{gift_name}_{int(time.time() // 3)}"  # 3秒内去重

            if self._is_duplicate_message(message_key):
                return

            # 构造适配前端的消息格式
            msg_data = {
                "type": "gift",
                "username": username,
                "content": f"{username} 送出了 {gift_name}x{gift_count}",
                "user_id": user_id,
                "gift_name": gift_name,
                "gift_count": gift_count,
                "timestamp": time.time()
            }

            self.stats['total_messages'] += 1
            self.stats['by_type']['gifts'] += 1

            logger.info(f"[礼物] {username} 送出了 {gift_name}x{gift_count}", extra={'client_id': self.client_id})
            self._send_message_sync(msg_data)

        except Exception as e:
            logger.error(f"处理礼物消息失败: {str(e)}", extra={'client_id': self.client_id})

    def _handle_social_message(self, payload):
        """处理关注消息"""
        if not self.filter_types.get('follows', True):
            self.stats['filtered_messages'] += 1
            return

        try:
            from protobuf.douyin import SocialMessage
            message = SocialMessage().parse(payload)

            username = message.user.nick_name
            user_id = str(message.user.id)

            # 生成消息唯一标识
            message_key = f"follow_{username}_{int(time.time() // 10)}"  # 10秒内去重

            if self._is_duplicate_message(message_key):
                return

            # 构造适配前端的消息格式
            msg_data = {
                "type": "follow",
                "username": username,
                "content": f"{username} 关注了主播",
                "user_id": user_id,
                "timestamp": time.time()
            }

            self.stats['total_messages'] += 1
            self.stats['by_type']['follows'] += 1

            logger.info(f"[关注] {username} 关注了主播", extra={'client_id': self.client_id})
            self._send_message_sync(msg_data)

        except Exception as e:
            logger.error(f"处理关注消息失败: {str(e)}", extra={'client_id': self.client_id})

    def _handle_room_stats_message(self, payload):
        """处理直播间统计消息（在线人数）"""
        if not self.filter_types.get('audience', True):
            self.stats['filtered_messages'] += 1
            return

        try:
            from protobuf.douyin import RoomUserSeqMessage
            message = RoomUserSeqMessage().parse(payload)

            current_count = message.total
            total_pv = message.total_pv_for_anchor

            # 在线人数消息，每5秒最多发送一次
            message_key = f"audience_{int(time.time() // 5)}"

            if self._is_duplicate_message(message_key):
                return

            # 构造适配前端的消息格式
            msg_data = {
                "type": "audience",
                "content": f"当前在线人数: {current_count}",
                "current": current_count,
                "total_pv": total_pv,
                "timestamp": time.time()
            }

            self.stats['total_messages'] += 1
            self.stats['by_type']['audience'] += 1

            logger.info(f"[统计] 当前观看人数: {current_count}, 累计观看人数: {total_pv}", extra={'client_id': self.client_id})
            self._send_message_sync(msg_data)

        except Exception as e:
            logger.error(f"处理统计消息失败: {str(e)}", extra={'client_id': self.client_id})

    # ==================== WebSocket事件处理 ====================

    def _handle_ws_open(self, ws):
        """WebSocket连接打开"""
        logger.info("DouyinWebFetcher WebSocket连接已建立", extra={'client_id': self.client_id})
        self._send_message_sync({
            "type": "system",
            "content": "高性能监控连接已建立，开始接收数据..."
        })

        # 调用原始的打开处理（启动心跳等）
        if hasattr(self.fetcher, '_original_wsOnOpen'):
            self.fetcher._original_wsOnOpen(ws)

    def _handle_ws_close(self, ws, *args):
        """WebSocket连接关闭"""
        logger.info("DouyinWebFetcher WebSocket连接已关闭", extra={'client_id': self.client_id})
        self._send_message_sync({
            "type": "system",
            "content": "高性能监控连接已关闭"
        })

        # 发送统计摘要
        self._send_stats_summary()

        # 调用原始的关闭处理
        if hasattr(self.fetcher, '_original_wsOnClose'):
            self.fetcher._original_wsOnClose(ws, *args)

    def _handle_ws_error(self, ws, error):
        """WebSocket连接错误"""
        logger.error(f"DouyinWebFetcher WebSocket错误: {error}", extra={'client_id': self.client_id})
        self._send_message_sync({
            "type": "error",
            "content": f"高性能监控连接错误: {error}"
        })

        # 调用原始的错误处理
        if hasattr(self.fetcher, '_original_wsOnError'):
            self.fetcher._original_wsOnError(ws, error)

    # ==================== 工具方法 ====================

    def get_stats(self) -> Dict:
        """获取统计信息"""
        stats = self.stats.copy()
        if stats['start_time']:
            stats['duration'] = time.time() - stats['start_time']
        return stats

    def update_filter_types(self, new_filters: Dict):
        """更新消息过滤设置"""
        self.filter_types.update(new_filters)
        logger.info(f"消息过滤设置已更新: {self.filter_types}", extra={'client_id': self.client_id})

        # 通知前端过滤设置已更新
        self._send_message_sync({
            "type": "system",
            "content": f"消息过滤设置已更新: {', '.join([k for k, v in new_filters.items() if v])}"
        })
