(() => {
    // 定义元素的 CSS 选择器
    const SELECTORS = {
        iframe: 'iframe', // iframe 选择器
        onlineAudienceTitle: '.live-data-item-title', // 在线人数标题的选择器
        onlineAudienceData: '.live-data-item-data', // 在线人数数据选择器
        // 根据您提供的HTML，消息元素是直接添加到某个滚动容器下的
        // 我们需要找到这个滚动容器来监听子节点变化
        chatContainer: '.live-message-scroller-container .vue-recycle-scroller__item-wrapper',
        commentMessage: '.live-message-item', // 单条评论消息的选择器
        commentContent: '.message-content', // 评论消息内容的选择器
        joinMessage: '.live-join-message' // 用户进入直播间消息的选择器
    };

    // 样式定义 (可选，用于控制台输出颜色)
    const STYLES = {
        online: 'color: #2196F3; font-weight: bold;', // 在线人数为蓝色
        comment: 'color: #4CAF50; font-weight: bold;', // 评论为绿色
        join: 'color: #9C27B0; font-weight: bold;', // 进入消息为紫色
        error: 'color: #f44336; font-weight: bold;', // 错误为红色
        system: 'color: #ff9800; font-weight: bold;' // 系统消息为橙色
    };

    let iframeDocument = null;
    // 添加消息缓存，用于简单去重
    const processedMessages = new Set(); // 使用一个缓存集合来存储已处理的消息文本

    const CACHE_MAX_SIZE = 300; // 缓存大小，适当增大以处理快速滚动的消息

    // 查找 iframe 并获取其文档对象
    function getIframeDocument() {
        try {
            const iframe = document.querySelector(SELECTORS.iframe);
            if (iframe && iframe.contentDocument) {
                iframeDocument = iframe.contentDocument;
                console.log('%c[系统] 成功找到 iframe 并获取文档对象', STYLES.system);
                return iframeDocument;
            } else {
                console.log('%c[系统] 未找到 iframe 或无法获取文档对象，5秒后重试', STYLES.error);
                return null;
            }
        } catch (e) {
             console.log(`%c[系统] 获取 iframe 文档错误: ${e.message}`, STYLES.error);
             return null;
        }
    }

    /**
     * 消息处理函数，处理评论或进入消息元素
     * @param {Element} element - 需要处理的消息 DOM 元素
     */
    function processMessage(element) {
        if (!element || element.nodeType !== 1) return;

        let messageText = null;
        let messageType = null;

        try {
             if (element.matches(SELECTORS.commentMessage)) {
                 const contentElement = element.querySelector(SELECTORS.commentContent);
                 if (contentElement) {
                     // 对于评论，可能需要更精确地提取文本，避免用户昵称等干扰
                     // 当前使用 textContent，包含了所有子元素的文本
                     messageText = element.textContent.trim();
                     messageType = '评论';

                     // ****** 添加过滤系统通知的逻辑 ******
                     // 检查是否为以 "通知欢迎来到直播间" 开头的系统消息
                     if (messageText.startsWith('通知欢迎来到直播间')) {
                         // 如果是系统通知，则不处理，直接返回
                         // console.log('%c[系统] 过滤掉系统通知消息', STYLES.system, messageText); // 可选：打印过滤信息用于调试
                         return;
                     }
                     // 检查是否为以 "主题" 开头的消息 (可能也是系统或特殊消息)
                     if (messageText.startsWith('主题')) {
                         // 如果是主题消息，则不处理，直接返回
                         // console.log('%c[系统] 过滤掉主题消息', STYLES.system, messageText); // 可选：打印过滤信息用于调试
                         return;
                     }
                     // ****** 过滤逻辑结束 ******
                 }
             } else if (element.matches(SELECTORS.joinMessage)) {
                 // 对于进入消息，textContent 通常就是完整的提示信息
                 messageText = element.textContent.trim();
                 messageType = '进入提示';

                 // ****** 添加过滤特定进入提示的逻辑 ******
                 // 检查是否为特定的 "来了" 进入提示消息
                 if (messageText === '来了') {
                     // 如果是这条特定的消息，则不处理，直接返回
                     // console.log('%c[系统] 过滤掉特定的进入提示消息', STYLES.system, messageText); // 可选：打印过滤信息用于调试
                     return;
                 }
                 // ****** 过滤逻辑结束 ******
             }

            if (messageText && !processedMessages.has(messageText)) {
                processedMessages.add(messageText);
                // 控制缓存大小
                if (processedMessages.size > CACHE_MAX_SIZE) {
                    const items = Array.from(processedMessages);
                    items.slice(0, CACHE_MAX_SIZE / 2).forEach(item => processedMessages.delete(item));
                }

                // 输出到控制台
                let style = STYLES.system;
                if (messageType === '评论') style = STYLES.comment;
                else if (messageType === '进入提示') style = STYLES.join;

                console.log(`%c[${messageType}] ${messageText}`, style);
            }
        } catch (e) {
            console.log(`%c[系统] 消息处理错误 (${messageType}): ${e.message}`, STYLES.error);
        }
    }

    /**
     * 监控在线人数并输出到控制台
     */
    function monitorOnlineAudience() {
        if (!iframeDocument) return;

        try {
            let onlineCount = null;
            const titles = iframeDocument.querySelectorAll(SELECTORS.onlineAudienceTitle);
            for (const title of titles) {
                // 根据实际页面文本调整匹配逻辑
                if (title.textContent.includes('当前在线')) { // 使用更灵活的匹配
                    const parent = title.parentElement;
                    if (parent) {
                        const dataElement = parent.querySelector(SELECTORS.onlineAudienceData);
                        if (dataElement) {
                            onlineCount = dataElement.textContent;
                            break;
                        }
                    }
                }
            }
            // 只有当在线人数变化或首次获取到时才打印，避免刷屏
            // 为了简单，这里每次获取到都打印，你可以根据需要添加判断
            if (onlineCount !== null) {
                 console.log(`%c👥 当前在线人数: ${onlineCount}`, STYLES.online);
            }
        } catch (e) {
             console.log(`%c[系统] 获取在线人数错误: ${e.message}`, STYLES.error);
        }
    }

    /**
     * 监控评论和进入消息 (定时扫描)
     */
    function monitorChatMessages() {
         if (!iframeDocument) {
             console.log('%c[系统] iframe 文档不可用，无法监控消息 (定时扫描)', STYLES.error);
             return;
         }

        console.log('%c[系统] 启动消息定时扫描...', STYLES.system);

        // 清空缓存以便重新开始捕获（在 init 中调用一次即可）
        // processedMessages.clear(); // 已经在 init 中处理

        // 使用定时器每隔一段时间扫描一次消息元素
        setInterval(() => {
            // 在每次定时器执行前再次确认 iframeDocument 是否有效
            if (!iframeDocument) {
                 // console.log('%c[系统] iframe 文档在扫描时变得不可用', STYLES.error); // 避免刷屏
                return; // 跳过当前扫描周期
            }

            try {
                // 查找所有进入提示消息元素
                const joinElements = iframeDocument.querySelectorAll(SELECTORS.joinMessage);
                joinElements.forEach(processMessage);

                // 查找所有评论消息元素
                const commentElements = iframeDocument.querySelectorAll(SELECTORS.commentMessage);
                commentElements.forEach(processMessage);

            } catch (e) {
                console.log(`%c[系统] 定时扫描消息错误: ${e.message}`, STYLES.error);
            }
        }, 500); // 每 500 毫秒（0.5 秒）检查一次，可以根据需要调整频率

         console.log('%c[系统] 评论和进入消息定时扫描已启动 (每 500ms)', STYLES.system);

         // 首次立即扫描一次已存在的进入提示和评论，避免遗漏页面加载时已有的消息
         if (iframeDocument) {
              const initialJoinElements = iframeDocument.querySelectorAll(SELECTORS.joinMessage);
              initialJoinElements.forEach(processMessage);

              const initialCommentElements = iframeDocument.querySelectorAll(SELECTORS.commentMessage);
              initialCommentElements.forEach(processMessage);

              if(initialJoinElements.length > 0 || initialCommentElements.length > 0) {
                  console.log('%c[系统] 已处理初始存在的评论和进入提示', STYLES.system);
              }
         }
    }

    /**
     * 初始化脚本，查找 iframe 并启动监控任务
     */
    function init() {
        console.log('%c[系统] 启动微信视频号监控脚本初始化...', STYLES.system);

        iframeDocument = getIframeDocument();

        if (iframeDocument) {
            // 如果成功获取到 iframe，则启动监控任务
            processedMessages.clear(); // 在初始化成功后清空缓存

            monitorOnlineAudience(); // 启动在线人数监控 (首次立即获取)
            monitorChatMessages(); // 启动评论和进入消息定时扫描

            // 设置定时器定期更新在线人数
            setInterval(monitorOnlineAudience, 5000); // 每 5 秒更新一次在线人数

             console.log('%c[系统] 微信视频号监控脚本初始化完成', STYLES.system);
        } else {
             // 如果首次未找到 iframe，则设置定时器定期重试初始化
             setTimeout(init, 5000); // 如果没找到 iframe，5秒后重试
        }
    }

    // 脚本开始执行
    init();

})();