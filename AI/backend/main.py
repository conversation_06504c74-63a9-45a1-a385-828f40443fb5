from fastapi import FastAPI, WebSocket, Response, UploadFile, File, Form, HTTPException, Body, BackgroundTasks, Query, Request
from fastapi.middleware.cors import CORSMiddleware
import asyncio
import json
import os
import re
from typing import List, Dict, Optional
from gradio_client import Client, handle_file
import time
from datetime import datetime, timedelta
import logging
import base64
import sys
from pydantic import BaseModel
import shutil
import requests
from uuid import uuid4
from jose import jwt, JWTError
from threading import Thread
from playwright.sync_api import sync_playwright, TimeoutError as PlaywrightTimeoutError
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError
# from playwright.async_api import async_playwright  # 移除异步用法

playwright = None
playwright_browser = None
playwright_page = None
is_connected = False

# 创建logs目录（如果不存在）
if not os.path.exists("logs"):
    os.makedirs("logs")

# 创建大模型API专用的日志记录器
llm_logger = logging.getLogger("llm_api")
llm_logger.setLevel(logging.INFO)

# 创建一个新的日志文件，使用时间戳命名
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
llm_log_file = os.path.join("logs", f"llm_conversation_{timestamp}.txt")
file_handler = logging.FileHandler(llm_log_file, encoding='utf-8')
file_handler.setLevel(logging.INFO)

# 设置日志格式（不使用logging的格式化，直接写入内容）
class CustomFormatter(logging.Formatter):
    def format(self, record):
        return record.msg

formatter = CustomFormatter()
file_handler.setFormatter(formatter)

# 确保日志不会重复
llm_logger.handlers = []
llm_logger.addHandler(file_handler)
llm_logger.propagate = False

# 配置日志
logging.basicConfig(
    level=logging.INFO,  # 修改为INFO级别以显示重要信息
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建自定义的日志过滤器
class CustomFilter(logging.Filter):
    def __init__(self):
        super().__init__()
        self.last_api_load_time = 0
        self.api_load_pattern = re.compile(r'Loaded as API: http://127\.0\.0\.1:7860/')
        
    def filter(self, record):
        msg = str(record.msg)
        
        # 1. 始终显示API请求和响应日志
        if any(keyword in msg for keyword in ['API请求:', 'API响应:', '请求体:', '响应内容:']):
            return True
            
        # 2. 过滤重复的API加载消息
        if self.api_load_pattern.search(msg):
            current_time = time.time()
            if current_time - self.last_api_load_time > 3600:  # 改为1小时
                self.last_api_load_time = current_time
                return True
            return False
            
        # 3. 默认显示其他日志
        return True

# 创建自定义的日志处理器
class CustomHandler(logging.Handler):
    def __init__(self):
        super().__init__()
        self.setLevel(logging.INFO)
        self.addFilter(CustomFilter())
        
    def emit(self, record):
        try:
            msg = self.format(record)
            # 确保消息输出到控制台
            print(msg, flush=True)
        except Exception:
            self.handleError(record)

# 配置根日志记录器
root_logger = logging.getLogger()
root_logger.handlers = []
custom_handler = CustomHandler()
custom_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
root_logger.addHandler(custom_handler)
root_logger.setLevel(logging.INFO)

# 禁用特定模块的日志
modules_to_disable = [
    "uvicorn.access",  # 仅禁用访问日志
    "gradio.client",   # 禁用gradio client的重复消息
    "httpx",
]

for module in modules_to_disable:
    logger = logging.getLogger(module)
    logger.handlers = []
    logger.propagate = False
    logger.setLevel(logging.WARNING)

# 确保我们的主日志记录器不会被禁用
logger = logging.getLogger(__name__)
logger.handlers = []
logger.addHandler(custom_handler)
logger.setLevel(logging.INFO)
logger.propagate = False

# 重定向标准输出
class NullIO:
    def write(self, *args, **kwargs):
        pass
    def flush(self, *args, **kwargs):
        pass

# 保存原始的标准输出
original_stdout = sys.stdout

# 不要完全重定向标准输出，让有用的信息能够显示
# sys.stdout = NullIO()  # 注释掉这行

# 导入数据库、模型和安全模块
from database import engine, async_session, Base
from models import User
from security import create_access_token, get_password_hash, ACCESS_TOKEN_EXPIRE_MINUTES, SECRET_KEY, ALGORITHM, oauth2_scheme, verify_password # 移除 authenticate_user 和 get_current_active_user

from sqlalchemy.orm import Session
from sqlalchemy import select
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
from fastapi import Depends, HTTPException, status

class ModelManager:
    """音色模型管理类"""
    
    def __init__(self, host="http://127.0.0.1:7860"):
        """
        初始化
        @param host: IndexTTS服务地址
        """
        self.client = Client(host)
        # 获取当前工作目录
        self.current_dir = os.getcwd()
        # 查找IndexTTS根目录
        self.index_tts_dir = self.find_index_tts_dir()
        if not self.index_tts_dir:
            raise Exception("未找到IndexTTS目录")
        logger.info(f"找到IndexTTS目录: {self.index_tts_dir}")
    
    def find_index_tts_dir(self):
        """
        查找IndexTTS根目录
        @return: IndexTTS根目录路径
        """
        # 根据main.py文件本身的路径来定位index-tts-liuyue目录
        current_file_dir = os.path.dirname(os.path.abspath(__file__))
        # 向上两级目录到达项目根目录
        project_root = os.path.dirname(os.path.dirname(current_file_dir))
        index_tts_path = os.path.join(project_root, "index-tts-liuyue")
        
        if os.path.exists(index_tts_path):
            return index_tts_path
        
        # 如果通过相对路径没找到，作为后备方案，可以保留原有的搜索逻辑（可选）
        # for root, dirs, files in os.walk(self.current_dir):
        #     if "index-tts-liuyue" in dirs:
        #         return os.path.join(root, "index-tts-liuyue")
                
        logger.error(f"未能在项目根目录 ({project_root}) 找到 index-tts-liuyue 目录")
        return None
    
    def normalize_model_name(self, name):
        """
        标准化模型名称
        @param name: 模型名称
        @return: 标准化后的名称
        """
        # 移除所有.pt后缀
        while name.endswith('.pt'):
            name = name[:-3]
        # 添加一个.pt后缀
        return f"{name}.pt"
    
    def find_model_file(self, model_name):
        """
        查找模型文件
        @param model_name: 模型名称
        @return: 模型文件路径
        """
        # 标准化模型名称
        model_name = self.normalize_model_name(model_name)
        logger.info(f"查找模型文件: {model_name}")
        
        # 在IndexTTS目录中查找
        for root, dirs, files in os.walk(self.index_tts_dir):
            for file in files:
                if file.lower() == model_name.lower():
                    file_path = os.path.join(root, file)
                    logger.info(f"找到模型文件: {file_path}")
                    return file_path
        logger.error(f"未找到模型文件: {model_name}")
        return None
    
    def rename_model(self, old_name, new_name):
        """
        重命名模型
        @param old_name: 原模型名称
        @param new_name: 新模型名称
        @return: 是否成功
        """
        try:
            # 查找原模型文件
            old_path = self.find_model_file(old_name)
            if not old_path:
                logger.error(f"未找到模型文件: {old_name}")
                return False
            
            # 标准化新名称
            new_name = self.normalize_model_name(new_name)
            new_path = os.path.join(os.path.dirname(old_path), new_name)
            
            logger.info(f"尝试重命名: {old_path} -> {new_path}")
            
            # 检查新名称是否已存在
            if os.path.exists(new_path):
                logger.error(f"新模型名称已存在: {new_path}")
                return False
            
            # 重命名文件
            shutil.move(old_path, new_path)
            logger.info(f"模型重命名成功: {old_name} -> {new_name}")
            return True
            
        except Exception as e:
            logger.error(f"重命名模型失败: {str(e)}")
            return False
    
    def delete_model(self, model_name):
        """
        删除模型
        @param model_name: 模型名称
        @return: 是否成功
        """
        try:
            # 查找模型文件
            model_path = self.find_model_file(model_name)
            if not model_path:
                logger.error(f"未找到模型文件: {model_name}")
                return False
            
            logger.info(f"尝试删除: {model_path}")
            
            # 删除文件
            os.remove(model_path)
            logger.info(f"模型删除成功: {model_name}")
            return True
            
        except Exception as e:
            logger.error(f"删除模型失败: {str(e)}")
            return False

# 创建全局ModelManager实例
model_manager = None

app = FastAPI()

# 添加CORS中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 或 ["http://localhost:3000"]
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量存储gradio client实例和最后检查时间
_gradio_client = None
_last_check_time = 0
_client_lock = asyncio.Lock()

async def create_gradio_client():
    """
    创建或获取gradio client实例（单例模式）
    """
    global _gradio_client, _last_check_time
    
    # 如果已经有实例且距离上次检查不超过5分钟，直接返回
    current_time = time.time()
    if _gradio_client is not None and current_time - _last_check_time < 300:
        return _gradio_client
        
    async with _client_lock:  # 使用锁防止并发创建
        try:
            if _gradio_client is None:
                # 临时重定向标准输出
                old_stdout = sys.stdout
                sys.stdout = NullIO()
                try:
                    from gradio_client import Client
                    _gradio_client = Client(
                        "http://127.0.0.1:7860/",
                        verbose=False
                    )
                finally:
                    # 恢复标准输出
                    sys.stdout = old_stdout
            
            # 更新检查时间
            _last_check_time = current_time
            return _gradio_client
            
        except Exception as e:
            logger.error(f"创建Gradio客户端失败: {str(e)}")
            return None

class TTSRequest(BaseModel):
    text: str
    model: str
    speed: float = 1.0
    volume: float = 1.0
    pitch: float = 0.0

class TrainModelRequest(BaseModel):
    model_name: str
    test_text: str = "你好，我是测试音色。"

# 通义千问API代理请求模型
class QianwenRequest(BaseModel):
    prompt: str
    model: str = "qwen-max"
    api_key: str
    parameters: dict = None

class VolcengineRequest(BaseModel):
    """火山引擎API请求模型"""
    prompt: str
    model: str
    api_key: str
    parameters: dict = None

class DeepseekRequest(BaseModel):
    """DeepSeek API请求模型"""
    prompt: str
    model: str = "deepseek-chat"
    api_key: str
    parameters: dict = None

class ZhipuRequest(BaseModel):
    """智谱API请求模型"""
    prompt: str
    model: str = "glm-4"
    api_key: str
    parameters: dict = None

# 添加会话相关的请求模型
class CreateSessionRequest(BaseModel):
    """创建会话请求模型"""
    system_prompt: str

class SessionMessageRequest(BaseModel):
    """会话消息请求模型"""
    message: str
    session_id: str
    api_key: str
    model: str
    provider: str = "tongyi-qianwen"  # 默认使用通义千问

class UserCreate(BaseModel):
    username: str
    password: str

class UserLogin(BaseModel):
    username: str
    password: str

class UserResponse(BaseModel):
    id: int
    username: str

    class Config:
        from_attributes = True # 兼容ORM对象

# 会话管理类
class SessionManager:
    def __init__(self):
        """初始化会话管理器"""
        self.sessions: Dict[str, Dict] = {}  # 存储所有会话
        self.session_file = "sessions.json"
        self.try_load_sessions()
    
    def try_load_sessions(self):
        """尝试从文件加载会话数据"""
        try:
            if os.path.exists(self.session_file):
                with open(self.session_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    if isinstance(data, dict):
                        self.sessions = data
                        logger.info(f"已加载 {len(self.sessions)} 个会话")
        except Exception as e:
            logger.error(f"加载会话数据失败: {str(e)}")
    
    def try_save_sessions(self):
        """尝试保存会话数据到文件"""
        try:
            # 创建临时会话副本，仅保留最后20条消息以减小文件大小
            sessions_to_save = {}
            for session_id, session in self.sessions.items():
                session_copy = session.copy()
                if len(session_copy.get("messages", [])) > 20:
                    # 保留system消息和最后19条消息
                    system_messages = [msg for msg in session_copy["messages"] if msg["role"] == "system"]
                    other_messages = [msg for msg in session_copy["messages"] if msg["role"] != "system"][-19:]
                    session_copy["messages"] = system_messages + other_messages
                sessions_to_save[session_id] = session_copy
                
            with open(self.session_file, "w", encoding="utf-8") as f:
                json.dump(sessions_to_save, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存会话数据失败: {str(e)}")
    
    def create_session(self, system_prompt: str) -> str:
        """创建新会话"""
        session_id = str(uuid4())
        self.sessions[session_id] = {
            "id": session_id,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "messages": [
                {"role": "system", "content": system_prompt}
            ],
            "message_count": 1
        }
        self.try_save_sessions()
        return session_id
    
    def add_message(self, session_id: str, role: str, content: str) -> bool:
        """添加消息到会话"""
        if session_id not in self.sessions:
            return False
        
        self.sessions[session_id]["messages"].append({
            "role": role,
            "content": content
        })
        self.sessions[session_id]["message_count"] += 1
        self.sessions[session_id]["updated_at"] = datetime.now().isoformat()
        self.try_save_sessions()
        return True
    
    def get_session(self, session_id: str) -> Optional[Dict]:
        """获取会话"""
        return self.sessions.get(session_id)
    
    def delete_session(self, session_id: str) -> bool:
        """删除会话"""
        if session_id in self.sessions:
            del self.sessions[session_id]
            self.try_save_sessions()
            return True
        return False
    
    def clean_expired_sessions(self, max_age_hours: int = 24) -> int:
        """清理过期会话，返回清理的会话数量"""
        now = datetime.now()
        expired_sessions = []
        
        for session_id, session in self.sessions.items():
            try:
                updated_at = datetime.fromisoformat(session["updated_at"])
                age = (now - updated_at).total_seconds() / 3600  # 小时
                if age > max_age_hours:
                    expired_sessions.append(session_id)
            except (ValueError, KeyError) as e:
                logger.error(f"处理会话时间时出错: {str(e)}")
                expired_sessions.append(session_id)
        
        # 删除过期会话
        for session_id in expired_sessions:
            self.delete_session(session_id)
        
        return len(expired_sessions)

# 创建会话管理器实例
session_manager = SessionManager()

# 数据库会话依赖
async def get_db():
    async with async_session() as db:
        try:
            yield db
        finally:
            await db.close()

async def get_user(db: Session = Depends(get_db), username: str = None) -> Optional[User]:
    """
    @summary: 从数据库获取用户
    @param db: 数据库会话
    @param username: 用户名
    @return: 用户对象或 None
    """
    query = select(User).where(User.username == username)
    result = await db.execute(query)
    user = result.scalars().first()
    return user

async def authenticate_user(db: Session = Depends(get_db), username: str = None, password: str = None):
    """
    @summary: 验证用户凭据
    @param db: 数据库会话
    @param username: 用户名
    @param password: 密码
    @return: 用户对象或 False
    """
    user = await get_user(db, username)
    if not user:
        return False
    if not verify_password(password, user.hashed_password):
        return False
    return user

async def create_user(db: Session = Depends(get_db), user: UserCreate = None) -> User:
    """
    @summary: 创建新用户
    @param db: 数据库会话
    @param user: 用户创建模型
    @return: 新创建的用户对象
    """
    hashed_password = get_password_hash(user.password)
    new_user = User(username=user.username, hashed_password=hashed_password)
    db.add(new_user)
    await db.commit()
    await db.refresh(new_user)
    return new_user

async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)) -> User:
    """
    @summary: 从JWT令牌中获取当前认证用户
    @param token: JWT令牌
    @param db: 数据库会话
    @return: 当前认证用户对象
    @raises HTTPException: 如果令牌无效或用户不存在
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = decode_access_token(token)
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    user = await get_user(db, username=username)
    if user is None:
        raise credentials_exception
    return user

async def check_service_status():
    """检查IndexTTS服务状态"""
    try:
        logger.info("开始检查IndexTTS服务状态...")
        client = await create_gradio_client()
        if client is None:
            logger.error("无法创建Gradio客户端")
            return False
        
        # 尝试获取模型列表来验证服务是否正常
        logger.info("尝试获取模型列表...")
        response = client.predict(api_name="/change_choices")
        logger.info(f"获取模型列表响应: {response}")
        
        if response:
            logger.info("IndexTTS服务正常运行")
            return True
        else:
            logger.error("IndexTTS服务返回空响应")
            return False
    except Exception as e:
        logger.error(f"检查服务状态失败: {str(e)}", exc_info=True)
        return False

@app.get("/status")
async def get_status():
    """
    检查服务状态
    """
    try:
        # 检查后端服务状态
        backend_status = True
        
        # 检查IndexTTS服务状态
        index_tts_status = True
        try:
            client = await create_gradio_client()
            if client:
                # 尝试获取模型列表
                response = await get_models()
                if not response or "choices" not in response:
                    index_tts_status = False
        except Exception as e:
            index_tts_status = False
        
        return {
            "backend": backend_status,
            "index_tts": index_tts_status
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/models")
async def get_models():
    """获取可用的模型列表"""
    try:
        client = await create_gradio_client()
        if client is None:
            raise Exception("无法连接到IndexTTS服务")
            
        response = client.predict(api_name="/change_choices")
        
        # 从响应中提取模型列表
        models = []
        if isinstance(response, dict) and "choices" in response:
            models = [choice[0] for choice in response["choices"]]
        elif isinstance(response, list):
            models = response
            
        return Response(
            content=json.dumps(models, ensure_ascii=False),
            media_type="application/json",
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type"
            }
        )
    except Exception as e:
        logger.error(f"获取模型列表失败: {str(e)}")
        return Response(
            content=json.dumps({"error": str(e)}, ensure_ascii=False),
            media_type="application/json",
            status_code=500,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type"
            }
        )

def wait_for_service(url, max_retries=20, delay=2):
    """等待服务启动"""
    from urllib3.exceptions import InsecureRequestWarning
    requests.packages.urllib3.disable_warnings(category=InsecureRequestWarning)
    
    logger.info(f"正在等待服务启动: {url}")
    for i in range(max_retries):
        try:
            print(f"尝试连接服务: {url}")
            response = requests.get(url, timeout=10)
            print(f"服务响应状态码: {response.status_code}")
            if response.status_code == 200:
                logger.info(f"服务已就绪: {url}")
                return True
        except requests.exceptions.Timeout:
            logger.info(f"等待服务响应超时... ({i+1}/{max_retries})")
        except requests.exceptions.ConnectionError:
            logger.info(f"连接服务失败... ({i+1}/{max_retries})")
        except Exception as e:
            logger.info(f"等待服务启动... ({i+1}/{max_retries})")
            logger.debug(f"错误信息: {str(e)}")
        time.sleep(delay)
    logger.error(f"服务启动等待超时: {url}")
    return False

def save_audio_file(audio_data, output_dir="outputs"):
    """保存音频文件并返回base64编码的音频数据"""
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        
    # 生成文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(output_dir, f"tts_output_{timestamp}.wav")
    
    # 如果是base64编码的数据
    if isinstance(audio_data, str) and audio_data.startswith("data:audio"):
        # 提取base64数据
        audio_base64 = audio_data.split(",")[1]
        with open(output_file, "wb") as f:
            f.write(base64.b64decode(audio_base64))
        return audio_data  # 直接返回原始的base64数据
    # 如果是文件路径
    elif isinstance(audio_data, str) and os.path.exists(audio_data):
        # 读取文件并转换为base64
        with open(audio_data, "rb") as f:
            audio_bytes = f.read()
        audio_base64 = base64.b64encode(audio_bytes).decode('utf-8')
        # 复制文件
        import shutil
        shutil.copy2(audio_data, output_file)
        return f"data:audio/wav;base64,{audio_base64}"
    else:
        raise ValueError("不支持的音频数据格式")

# 存储活动的WebSocket连接
active_connections: List[WebSocket] = []

@app.websocket("/ws/tts")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    active_connections.append(websocket)
    
    # 创建一个client实例并复用
    client = await create_gradio_client()
    if client is None:
        await websocket.send_json({
            "status": "error",
            "message": "无法连接到IndexTTS服务"
        })
        return
        
    try:
        while True:
            data = await websocket.receive_text()
            logger.info(f"收到WebSocket消息: {data}")
            
            # 解析接收到的数据
            request_data = json.loads(data)
            text = request_data.get("text", "")
            model = request_data.get("model", "")
            options = request_data.get("options", {})
            
            # 分割文本为句子（已由前端处理，这里直接用原始text）
            sentences = [text]
            total_sentences = len(sentences)
            
            logger.info(f"文本已分割为 {total_sentences} 个句子")
            
            # 逐句生成语音
            for i, sentence in enumerate(sentences, 1):
                try:
                    logger.info(f"正在处理第 {i}/{total_sentences} 句: {sentence}")
                    
                    # 使用已创建的client实例
                    audio_stream = client.predict(
                        name=model,
                        text=sentence,
                        speed=options.get("speed", 1.0),
                        voice=None,
                        pitch=options.get("pitch", 0.0),
                        api_name="/infer"
                    )
                    
                    if not audio_stream:
                        raise Exception("生成的音频流为空")

                    # 创建临时文件
                    output_dir = "音频输出"
                    if not os.path.exists(output_dir):
                        os.makedirs(output_dir)
                    
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    output_file = os.path.join(output_dir, f"tts_output_{timestamp}_{i}.wav")
                    
                    # 保存音频流到文件
                    if isinstance(audio_stream, str) and os.path.exists(audio_stream):
                        # 如果返回的是文件路径，直接复制
                        import shutil
                        shutil.copy2(audio_stream, output_file)
                        logger.info(f"已复制音频文件到: {output_file}")
                    else:
                        # 如果返回的是音频数据，直接写入
                        with open(output_file, "wb") as f:
                            if isinstance(audio_stream, bytes):
                                f.write(audio_stream)
                            else:
                                f.write(str(audio_stream).encode())
                        logger.info(f"已保存音频数据到: {output_file}")
                    
                    # 读取生成的音频文件
                    if os.path.exists(output_file):
                        with open(output_file, "rb") as f:
                            audio_data = f.read()
                        
                        # 转换为base64
                        audio_base64 = base64.b64encode(audio_data).decode('utf-8')
                        
                        # 删除临时文件
                        try:
                            os.remove(output_file)
                            logger.info(f"已删除临时音频文件: {output_file}")
                        except Exception as e:
                            logger.warning(f"删除临时文件失败: {e}")
                        
                        # 发送音频数据
                        await websocket.send_json({
                            "status": "success",
                            "message": f"已生成第 {i}/{total_sentences} 句语音",
                            "progress": i / total_sentences,
                            "sentence": sentence,
                            "audio": f"data:audio/wav;base64,{audio_base64}"
                        })
                    else:
                        raise Exception(f"无法找到生成的音频文件: {output_file}")
                    
                except Exception as e:
                    error_msg = f"生成第 {i} 句语音时出错: {str(e)}"
                    logger.error(error_msg)
                    await websocket.send_json({
                        "status": "error",
                        "message": error_msg,
                        "sentence": sentence
                    })
            
            # 发送完成消息
            await websocket.send_json({
                "status": "complete",
                "message": f"全部 {total_sentences} 句语音生成完成"
            })
                
    except Exception as e:
        logger.error(f"WebSocket错误: {str(e)}", exc_info=True)
        try:
            await websocket.send_json({
                "status": "error",
                "message": f"WebSocket错误: {str(e)}"
            })
        except:
            pass
    finally:
        if websocket in active_connections:
            active_connections.remove(websocket)

@app.on_event("startup")
async def startup_event():
    """服务启动时的初始化"""
    logger.info("服务正在启动...")
    try:
        # 尝试创建gradio客户端
        client = await create_gradio_client()
        if client is None:
            logger.error("无法连接到IndexTTS服务")
        else:
            logger.info("成功连接到IndexTTS服务")
            
        # 开始定期清理过期会话的任务
        asyncio.create_task(periodic_session_cleanup())
    except Exception as e:
        logger.error(f"启动时出错: {str(e)}")

async def periodic_session_cleanup():
    """定期清理过期会话"""
    while True:
        try:
            # 每6小时清理一次过期会话（超过24小时未更新的会话）
            await asyncio.sleep(6 * 3600)
            cleaned = session_manager.clean_expired_sessions(24)
            if cleaned > 0:
                logger.info(f"已清理 {cleaned} 个过期会话")
        except Exception as e:
            logger.error(f"清理会话时出错: {str(e)}")
            await asyncio.sleep(3600)  # 出错后1小时后重试

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "ok"}

@app.post("/generate")
async def generate_speech(request: TTSRequest):
    """生成语音"""
    try:
        logger.info(f"收到生成请求: model={request.model}, text={request.text}, speed={request.speed}, volume={request.volume}, pitch={request.pitch}")
        
        # 检查文本是否为空
        if not request.text.strip():
            logger.error("请求的文本为空")
            return Response(
                content=json.dumps({"message": "请输入要转换的文本"}, ensure_ascii=False),
                media_type="application/json",
                status_code=400
            )

        client = await create_gradio_client()
        if client is None:
            logger.error("无法创建Gradio客户端")
            return Response(
                content=json.dumps({"message": "无法连接到IndexTTS服务"}, ensure_ascii=False),
                media_type="application/json",
                status_code=500
            )

        try:
            # 准备参数 - 只传递IndexTTS支持的参数
            params = {
                "name": request.model,
                "text": request.text,
                "speed": request.speed,  # 只使用语速参数
                "voice": None,
                "api_name": "/infer"
            }
            
            logger.info(f"调用IndexTTS参数: {params}")
            
            # 调用模型生成音频流
            audio_stream = client.predict(**params)
            
            if not audio_stream:
                logger.error("生成音频流失败")
                return Response(
                    content=json.dumps({"message": "生成语音失败：无法生成音频流"}, ensure_ascii=False),
                    media_type="application/json",
                    status_code=500
                )

            # 创建输出目录
            output_dir = "音频输出"
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # 生成临时文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(output_dir, f"tts_output_{timestamp}.wav")

            # 保存音频流到文件
            if isinstance(audio_stream, str) and os.path.exists(audio_stream):
                # 如果返回的是文件路径，直接复制
                import shutil
                shutil.copy2(audio_stream, output_file)
                logger.info(f"已复制音频文件到: {output_file}")
            else:
                # 如果返回的是音频数据，直接写入
                with open(output_file, "wb") as f:
                    if isinstance(audio_stream, bytes):
                        f.write(audio_stream)
                    else:
                        f.write(str(audio_stream).encode())
                logger.info(f"已保存音频数据到: {output_file}")

            # 读取生成的音频文件
            if os.path.exists(output_file):
                with open(output_file, "rb") as f:
                    audio_data = f.read()
                
                # 转换为base64
                audio_base64 = base64.b64encode(audio_data).decode('utf-8')
                
                # 删除临时文件
                try:
                    os.remove(output_file)
                    logger.info(f"已删除临时音频文件: {output_file}")
                except Exception as e:
                    logger.warning(f"删除临时文件失败: {e}")
                
                return Response(
                    content=json.dumps({
                        "message": "语音生成成功",
                        "audio": f"data:audio/wav;base64,{audio_base64}"
                    }, ensure_ascii=False),
                    media_type="application/json"
                )
            else:
                logger.error(f"音频文件不存在: {output_file}")
                return Response(
                    content=json.dumps({"message": "生成语音失败：无法保存音频文件"}, ensure_ascii=False),
                    media_type="application/json",
                    status_code=500
                )

        except Exception as e:
            logger.error(f"生成语音失败: {str(e)}", exc_info=True)
            return Response(
                content=json.dumps({"message": f"生成语音失败: {str(e)}"}, ensure_ascii=False),
                media_type="application/json",
                status_code=500
            )
            
    except Exception as e:
        logger.error(f"处理请求失败: {str(e)}", exc_info=True)
        return Response(
            content=json.dumps({"message": f"处理请求失败: {str(e)}"}, ensure_ascii=False),
            media_type="application/json",
            status_code=500
        )

@app.post("/train")
async def train_model(
    audio_file: UploadFile = File(...),
    model_name: str = Form(...),
    test_text: str = Form("你好，我是测试音色。")
):
    """
    训练新的声音模型
    :param model_name: 模型名称
    :param test_text: 测试文本
    :param audio_file: 音频文件
    """
    try:
        # 检查音频文件格式
        if not audio_file.filename.lower().endswith('.wav'):
            return Response(
                content=json.dumps({"error": "音频文件必须是WAV格式"}, ensure_ascii=False),
                media_type="application/json",
                status_code=400
            )
            
        # 创建临时目录保存上传的音频文件
        temp_dir = os.path.join("outputs", "temp")
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)
            
        # 保存上传的音频文件
        audio_path = os.path.join(temp_dir, audio_file.filename)
        with open(audio_path, "wb") as buffer:
            shutil.copyfileobj(audio_file.file, buffer)
            
        # 创建Gradio客户端
        client = await create_gradio_client()
        if client is None:
            return Response(
                content=json.dumps({"error": "无法连接到IndexTTS服务"}, ensure_ascii=False),
                media_type="application/json",
                status_code=500
            )
            
        # 获取当前模型列表
        current_models = client.predict(api_name="/change_choices")
        logger.info(f"当前可用模型：{current_models}")
        
        # 处理音频文件
        processed_audio = handle_file(audio_path)
        logger.info(f"音频文件处理完成：{processed_audio}")
        
        # 更新提示音频
        update_result = client.predict(api_name="/update_prompt_audio")
        logger.info(f"更新提示音频结果：{update_result}")
        time.sleep(2)
        
        # 进行推理
        try:
            result = client.predict(
                name="使用参考音频",
                voice=processed_audio,
                text=test_text,
                speed=1.0,
                api_name="/infer"
            )
            logger.info(f"推理结果：{result}")
        except Exception as e:
            logger.warning(f"推理过程中出现错误（这是正常的）：{str(e)}")
        
        # 等待模型处理
        time.sleep(5)
        
        # 保存为新模型
        save_result = client.predict(
            name=model_name,
            api_name="/save_audio"
        )
        logger.info(f"保存模型结果：{save_result}")
        
        # 等待保存完成
        time.sleep(5)
        
        # 验证模型是否创建成功
        new_models = client.predict(api_name="/change_choices")
        model_file = f"{model_name}.pt"
        if any(model_file in choice[0] for choice in new_models.get('choices', [])):
            # 清理临时文件
            if os.path.exists(audio_path):
                os.remove(audio_path)
                
            return Response(
                content=json.dumps({
                    "success": True,
                    "message": "模型创建成功",
                    "model_name": model_name
                }, ensure_ascii=False),
                media_type="application/json"
            )
        else:
            return Response(
                content=json.dumps({
                    "success": False,
                    "error": "模型创建失败，未在列表中找到新模型"
                }, ensure_ascii=False),
                media_type="application/json",
                status_code=500
            )
            
    except Exception as e:
        logger.error(f"创建模型时发生错误：{str(e)}", exc_info=True)
        return Response(
            content=json.dumps({
                "success": False,
                "error": f"创建模型时发生错误：{str(e)}"
            }, ensure_ascii=False),
            media_type="application/json",
            status_code=500
        )

@app.post("/models/rename")
async def rename_model_endpoint(old_name: str, new_name: str):
    """重命名模型API端点"""
    try:
        logger.info(f"收到重命名请求: {old_name} -> {new_name}")
        
        global model_manager
        if model_manager is None:
            logger.info("创建新的ModelManager实例")
            model_manager = ModelManager()
        
        # 检查原模型是否存在
        old_model_path = model_manager.find_model_file(old_name)
        if not old_model_path:
            logger.error(f"未找到原模型文件: {old_name}")
            return Response(
                content=json.dumps({
                    "success": False,
                    "message": f"未找到原模型文件: {old_name}"
                }, ensure_ascii=False),
                media_type="application/json",
                status_code=404,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type"
                }
            )
        
        # 检查新名称的模型是否已存在
        new_model_path = model_manager.find_model_file(new_name)
        if new_model_path:
            logger.error(f"新名称的模型已存在: {new_name}")
            return Response(
                content=json.dumps({
                    "success": False,
                    "message": f"新名称的模型已存在: {new_name}"
                }, ensure_ascii=False),
                media_type="application/json",
                status_code=400,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type"
                }
            )
        
        logger.info(f"开始重命名模型: {old_name} -> {new_name}")
        success = model_manager.rename_model(old_name, new_name)
        
        if success:
            logger.info(f"模型重命名成功: {old_name} -> {new_name}")
            return Response(
                content=json.dumps({
                    "success": True,
                    "message": f"模型重命名成功: {old_name} -> {new_name}"
                }, ensure_ascii=False),
                media_type="application/json",
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type"
                }
            )
        else:
            logger.error(f"模型重命名失败: {old_name} -> {new_name}")
            return Response(
                content=json.dumps({
                    "success": False,
                    "message": "模型重命名失败"
                }, ensure_ascii=False),
                media_type="application/json",
                status_code=400,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type"
                }
            )
    except Exception as e:
        logger.error(f"重命名模型时发生错误: {str(e)}", exc_info=True)
        return Response(
            content=json.dumps({
                "success": False,
                "message": f"重命名模型时发生错误: {str(e)}"
            }, ensure_ascii=False),
            media_type="application/json",
            status_code=500,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type"
            }
        )

# 添加OPTIONS请求处理
@app.options("/models/rename")
async def options_rename_endpoint():
    return Response(
        content="",
        media_type="application/json",
        headers={
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "POST, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type"
        }
    )

@app.delete("/models/{model_name}")
async def delete_model_endpoint(model_name: str):
    """删除模型API端点"""
    try:
        logger.info(f"收到删除模型请求: {model_name}")
        
        global model_manager
        if model_manager is None:
            logger.info("创建新的ModelManager实例")
            model_manager = ModelManager()
        
        # 查找模型文件
        model_path = model_manager.find_model_file(model_name)
        if not model_path:
            logger.error(f"未找到模型文件: {model_name}")
            return Response(
                content=json.dumps({
                    "success": False,
                    "message": f"未找到模型文件: {model_name}"
                }, ensure_ascii=False),
                media_type="application/json",
                status_code=404,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "DELETE, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type"
                }
            )
        
        logger.info(f"开始删除模型文件: {model_path}")
        success = model_manager.delete_model(model_name)
        
        if success:
            logger.info(f"模型删除成功: {model_name}")
            return Response(
                content=json.dumps({
                    "success": True,
                    "message": f"模型删除成功: {model_name}"
                }, ensure_ascii=False),
                media_type="application/json",
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "DELETE, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type"
                }
            )
        else:
            logger.error(f"模型删除失败: {model_name}")
            return Response(
                content=json.dumps({
                    "success": False,
                    "message": f"模型删除失败: {model_name}"
                }, ensure_ascii=False),
                media_type="application/json",
                status_code=400,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "DELETE, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type"
                }
            )
    except Exception as e:
        logger.error(f"删除模型时发生错误: {str(e)}", exc_info=True)
        return Response(
            content=json.dumps({
                "success": False,
                "message": f"删除模型时发生错误: {str(e)}"
            }, ensure_ascii=False),
            media_type="application/json",
            status_code=500,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "DELETE, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type"
            }
        )

# 添加OPTIONS请求处理
@app.options("/models/{model_name}")
async def options_model_endpoint(model_name: str):
    return Response(
        content="",
        media_type="application/json",
        headers={
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "DELETE, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type"
        }
    )

def format_llm_log(prompt, response, model_name):
    """格式化大模型日志输出"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S,%f")[:-3]
    
    try:
        # 首先尝试处理常见的情况：提示词+【原文本】的格式
        if "】里面的话术内容进行重写" in prompt and "【" in prompt and "】" in prompt:
            # 这是重写提示的情况，直接提取【】中的内容
            matches = re.findall(r"【([\s\S]*?)】", prompt)
            if matches:
                original_text = matches[-1].strip()
            else:
                original_text = prompt.strip()
        elif "【" in prompt and "】" in prompt:
            # 普通的【】包含原文本的情况
            # 先尝试找到最后一个【】对
            last_start = prompt.rfind("【")
            if last_start >= 0:
                last_end = prompt.find("】", last_start)
                if last_end >= 0:
                    original_text = prompt[last_start+1:last_end].strip()
                else:
                    original_text = prompt.strip()
            else:
                original_text = prompt.strip()
        else:
            # 没有明确标记的情况
            original_text = prompt.strip()
            
            # 尝试去除常见的提示词前缀
            prefixes = [
                "】里面的话术内容进行重写，要求意思一样文字完全不一样，不要出现抖音违禁词，只输出重写内容，要口语化不要书面语。",
                "原文本:",
                "请对以下内容进行改写:",
                "请改写以下内容:"
            ]
            
            for prefix in prefixes:
                if prefix in original_text:
                    original_text = original_text.split(prefix, 1)[1].strip()
                    break
        
        # 简化的日志格式，只包含时间戳、原文本和AI回复
            log_text = f"""{timestamp}

原文本:

{original_text}

AI回复 ({model_name}):
{response.strip()}
{'='*100}
"""
        return log_text
    except Exception as e:
        # 如果解析失败，使用简单格式记录
        logger.error(f"日志格式化失败: {str(e)}")
        return f"""{timestamp}

原文本:

{prompt}

AI回复 ({model_name}):
{response.strip()}
{'='*100}
"""

@app.post("/api/qianwen")
async def proxy_qianwen_api(request: QianwenRequest):
    """
    通义千问API代理接口
    """
    try:
        # 记录请求信息到控制台
        logger.info(f"输入文本: {request.prompt[:20000]}")
        
        # 构建API请求
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {request.api_key}'
        }
        
        # 从参数中获取系统提示词，如果没有则使用默认值
        system_prompt = "你是一个专业的直播助手，擅长为主播生成多样化的话术表达"
        if request.parameters and "system" in request.parameters:
            system_prompt = request.parameters.pop("system")
            logger.info(f"使用自定义系统提示词: {system_prompt[:200]}")
        
        # 构建请求体
        request_body = {
            "model": request.model,
            "input": {
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": request.prompt}
                ]
            }
        }
        
        # 添加可选参数
        if request.parameters:
            request_body["parameters"] = request.parameters
        
        # 发送请求
        api_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
        response = requests.post(api_url, headers=headers, json=request_body)
        
        try:
            response_data = response.json()
            # 提取实际的输出文本
            output_text = ""
            if "output" in response_data and "choices" in response_data["output"]:
                if "message" in response_data["output"]["choices"][0]:
                    output_text = response_data["output"]["choices"][0]["message"]["content"]
                elif "text" in response_data["output"]["choices"][0]:
                    output_text = response_data["output"]["choices"][0]["text"]
            
            # 记录响应信息到控制台
            logger.info(f"生成文本: {output_text[:20000]}")
            
            # 格式化并记录到文件
            log_text = format_llm_log(request.prompt, output_text, "通义千问")
            llm_logger.info(log_text)
            
            # 格式化输出
            formatted_output = {
                "output": {
                    "text": output_text
                }
            }
            
            return Response(
                content=json.dumps(formatted_output, ensure_ascii=False),
                status_code=200,
                media_type="application/json",
                headers={
                    "Content-Type": "application/json; charset=utf-8",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                }
            )
            
        except ValueError as e:
            error_msg = f"解析响应失败: {str(e)}"
            logger.error(error_msg)
            llm_logger.error(error_msg)
            return Response(
                content=json.dumps({"error": "无法解析API响应"}, ensure_ascii=False),
                status_code=500,
                media_type="application/json",
                headers={
                    "Content-Type": "application/json; charset=utf-8",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                }
            )
            
    except Exception as e:
        error_msg = f"请求失败: {str(e)}"
        logger.error(error_msg)
        llm_logger.error(error_msg)
        return Response(
            content=json.dumps({"error": f"请求失败: {str(e)}"}, ensure_ascii=False),
            status_code=500,
            media_type="application/json",
            headers={
                "Content-Type": "application/json; charset=utf-8",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            }
        )

@app.options("/api/qianwen")
async def options_qianwen_api():
    """处理预检请求"""
    return Response(
        content="",
        headers={
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "POST, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type, Authorization"
        }
    )

@app.post("/api/volcengine")
async def proxy_volcengine_api(request: VolcengineRequest):
    """
    代理火山引擎豆包大模型API请求
    """
    try:
        # 记录请求信息到控制台
        logger.info(f"输入文本: {request.prompt[:20000]}")
        
        # 验证API密钥
        if not request.api_key:
            raise HTTPException(status_code=400, detail="缺少API密钥")

        # 准备请求参数
        headers = {
            "Authorization": f"Bearer {request.api_key}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        # 从参数中获取系统提示词，如果没有则使用默认值
        system_prompt = "你是一个专业的直播助手，擅长为主播生成多样化的话术表达"
        if request.parameters and "system" in request.parameters:
            system_prompt = request.parameters.pop("system")
            logger.info(f"使用自定义系统提示词: {system_prompt[:200]}")
        
        # 构建请求体
        data = {
            "model": request.model or "doubao-1-5-thinking-pro",
            "messages": [
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": request.prompt
                }
            ]
        }
        
        # 添加其他参数
        if request.parameters:
            data.update(request.parameters)
        
        # 发送请求
        api_url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
        response = requests.post(api_url, headers=headers, json=data, timeout=300)
        
        try:
            response_data = response.json()
            # 提取实际的输出文本
            output_text = ""
            if "choices" in response_data and len(response_data["choices"]) > 0:
                if "message" in response_data["choices"][0]:
                    output_text = response_data["choices"][0]["message"]["content"]
                elif "text" in response_data["choices"][0]:
                    output_text = response_data["choices"][0]["text"]
            
            # 记录响应信息到控制台
            logger.info(f"生成文本: {output_text[:20000]}")
            
            # 格式化并记录到文件
            log_text = format_llm_log(request.prompt, output_text, "火山引擎")
            llm_logger.info(log_text)
            
            # 格式化输出
            formatted_output = {
                "output": {
                    "text": output_text
                }
            }
            
            return Response(
                content=json.dumps(formatted_output, ensure_ascii=False),
                status_code=200,
                media_type="application/json",
                headers={
                    "Content-Type": "application/json; charset=utf-8",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                }
            )
            
        except ValueError as e:
            error_msg = f"解析响应失败: {str(e)}"
            logger.error(error_msg)
            llm_logger.error(error_msg)
            return Response(
                content=json.dumps({"error": "无法解析API响应"}, ensure_ascii=False),
                status_code=500,
                media_type="application/json",
                headers={
                    "Content-Type": "application/json; charset=utf-8",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                }
            )
            
    except Exception as e:
        error_msg = f"请求失败: {str(e)}"
        logger.error(error_msg)
        llm_logger.error(error_msg)
        return Response(
            content=json.dumps({"error": f"请求失败: {str(e)}"}, ensure_ascii=False),
            status_code=500,
            media_type="application/json",
            headers={
                "Content-Type": "application/json; charset=utf-8",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            }
        )

@app.options("/api/volcengine")
async def options_volcengine_api():
    """处理火山引擎API的预检请求"""
    return Response(
        content="",
        media_type="application/json",
        headers={
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "POST, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type, Authorization"
        }
    )

@app.post("/api/deepseek")
async def proxy_deepseek_api(request: DeepseekRequest):
    """
    代理DeepSeek API请求
    """
    try:
        # 记录请求信息到控制台
        logger.info(f"输入文本: {request.prompt[:20000]}")
        
        # 验证API密钥
        if not request.api_key:
            raise HTTPException(status_code=400, detail="缺少API密钥")

        # 准备请求参数
        headers = {
            "Authorization": f"Bearer {request.api_key}",
            "Content-Type": "application/json"
        }
        
        # 从参数中获取系统提示词，如果没有则使用默认值
        system_prompt = "你是一个专业的直播助手，擅长为主播生成多样化的话术表达"
        if request.parameters and "system" in request.parameters:
            system_prompt = request.parameters.pop("system")
            logger.info(f"使用自定义系统提示词: {system_prompt[:200]}")
        
        # 构建请求体
        data = {
            "model": request.model,
            "messages": [
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": request.prompt
                }
            ]
        }
        
        # 添加其他参数
        if request.parameters:
            data.update(request.parameters)
        
        # 发送请求
        api_url = "https://api.deepseek.com/v1/chat/completions"
        response = requests.post(api_url, headers=headers, json=data, timeout=300)
        
        try:
            response_data = response.json()
            # 提取实际的输出文本
            output_text = ""
            if "choices" in response_data and len(response_data["choices"]) > 0:
                if "message" in response_data["choices"][0]:
                    output_text = response_data["choices"][0]["message"]["content"]
                elif "text" in response_data["choices"][0]:
                    output_text = response_data["choices"][0]["text"]
            
            # 记录响应信息到控制台
            logger.info(f"生成文本: {output_text[:20000]}")
            
            # 格式化并记录到文件
            log_text = format_llm_log(request.prompt, output_text, "DeepSeek")
            llm_logger.info(log_text)
            
            # 格式化输出
            formatted_output = {
                "output": {
                    "text": output_text
                }
            }
            
            return Response(
                content=json.dumps(formatted_output, ensure_ascii=False),
                status_code=200,
                media_type="application/json",
                headers={
                    "Content-Type": "application/json; charset=utf-8",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                }
            )
            
        except ValueError as e:
            error_msg = f"解析响应失败: {str(e)}"
            logger.error(error_msg)
            llm_logger.error(error_msg)
            return Response(
                content=json.dumps({"error": "无法解析API响应"}, ensure_ascii=False),
                status_code=500,
                media_type="application/json",
                headers={
                    "Content-Type": "application/json; charset=utf-8",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                }
            )
            
    except Exception as e:
        error_msg = f"请求失败: {str(e)}"
        logger.error(error_msg)
        llm_logger.error(error_msg)
        return Response(
            content=json.dumps({"error": f"请求失败: {str(e)}"}, ensure_ascii=False),
            status_code=500,
            media_type="application/json",
            headers={
                "Content-Type": "application/json; charset=utf-8",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            }
        )

@app.options("/api/deepseek")
async def options_deepseek_api():
    """处理DeepSeek API的预检请求"""
    return Response(
        content="",
        media_type="application/json",
        headers={
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "POST, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type, Authorization"
        }
    )

@app.post("/api/zhipu")
async def proxy_zhipu_api(request: ZhipuRequest):
    """
    代理智谱GLM大模型API请求
    """
    try:
        # 记录请求信息到控制台
        logger.info(f"输入文本: {request.prompt[:20000]}")
        
        # 验证API密钥
        if not request.api_key:
            raise HTTPException(status_code=400, detail="缺少API密钥")

        # 准备请求参数
        headers = {
            "Authorization": f"Bearer {request.api_key}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        # 从参数中获取系统提示词，如果没有则使用默认值
        system_prompt = "你是一个专业的直播助手，擅长为主播生成多样化的话术表达"
        if request.parameters and "system" in request.parameters:
            system_prompt = request.parameters.pop("system")
            logger.info(f"使用自定义系统提示词: {system_prompt[:200]}")
        
        # 构建请求体
        data = {
            "model": request.model or "glm-4",
            "messages": [
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": request.prompt
                }
            ]
        }
        
        # 添加其他参数
        if request.parameters:
            temperature = request.parameters.get("temperature", 0.7)
            top_p = request.parameters.get("top_p", 0.8)
            data.update({
                "temperature": temperature,
                "top_p": top_p
            })
        
        # 发送请求
        api_url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
        
        # 记录请求内容
        logger.info(f"智谱API请求参数: {json.dumps(data, ensure_ascii=False)[:1000]}...")
        
        # 发送请求
        response = requests.post(api_url, headers=headers, json=data, timeout=300)
        
        try:
            response_data = response.json()
            # 提取实际的输出文本
            output_text = ""
            if "choices" in response_data and len(response_data["choices"]) > 0:
                if "message" in response_data["choices"][0]:
                    output_text = response_data["choices"][0]["message"]["content"]
                elif "text" in response_data["choices"][0]:
                    output_text = response_data["choices"][0]["text"]
            
            # 记录响应信息到控制台
            logger.info(f"生成文本: {output_text[:20000]}")
            
            # 格式化并记录到文件
            log_text = format_llm_log(request.prompt, output_text, "智谱GLM")
            llm_logger.info(log_text)
            
            # 格式化输出
            formatted_output = {
                "output": {
                    "text": output_text
                }
            }
            
            return Response(
                content=json.dumps(formatted_output, ensure_ascii=False),
                status_code=200,
                media_type="application/json",
                headers={
                    "Content-Type": "application/json; charset=utf-8",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                }
            )
            
        except ValueError as e:
            error_msg = f"解析响应失败: {str(e)}"
            logger.error(error_msg)
            llm_logger.error(error_msg)
            return Response(
                content=json.dumps({"error": "无法解析API响应"}, ensure_ascii=False),
                status_code=500,
                media_type="application/json",
                headers={
                    "Content-Type": "application/json; charset=utf-8",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                }
            )
            
    except Exception as e:
        error_msg = f"请求失败: {str(e)}"
        logger.error(error_msg)
        llm_logger.error(error_msg)
        return Response(
            content=json.dumps({"error": f"请求失败: {str(e)}"}, ensure_ascii=False),
            status_code=500,
            media_type="application/json",
            headers={
                "Content-Type": "application/json; charset=utf-8",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            }
        )

@app.options("/api/zhipu")
async def options_zhipu_api():
    """处理智谱API的预检请求"""
    return Response(
        content="",
        media_type="application/json",
        headers={
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "POST, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type, Authorization"
        }
    )

# 添加会话管理API端点
@app.post("/api/session")
async def create_session(request: CreateSessionRequest):
    """创建新会话"""
    try:
        system_prompt = request.system_prompt
        logger.info(f"创建新会话，系统提示词: {system_prompt[:100]}...")
        
        session_id = session_manager.create_session(system_prompt)
        
        return Response(
            content=json.dumps({
                "success": True,
                "session_id": session_id,
                "message_count": 1
            }, ensure_ascii=False),
            media_type="application/json",
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            }
        )
    except Exception as e:
        logger.error(f"创建会话失败: {str(e)}")
        return Response(
            content=json.dumps({
                "success": False,
                "error": f"创建会话失败: {str(e)}"
            }, ensure_ascii=False),
            media_type="application/json",
            status_code=500,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            }
        )

@app.post("/api/session/message")
async def add_session_message(request: SessionMessageRequest):
    """向会话添加消息并获取响应"""
    try:
        session_id = request.session_id
        user_message = request.message
        provider = request.provider
        model = request.model
        api_key = request.api_key
        
        # 调试日志
        logger.info(f"接收到会话消息请求: session_id={session_id}, provider={provider}, model={model}")
        
        # 获取会话
        session = session_manager.get_session(session_id)
        if not session:
            logger.error(f"会话不存在或已过期: {session_id}")
            return Response(
                content=json.dumps({
                    "success": False,
                    "error": "会话不存在或已过期"
                }, ensure_ascii=False),
                media_type="application/json",
                status_code=404,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                }
            )
        
        # 添加用户消息
        session_manager.add_message(session_id, "user", user_message)
        logger.info(f"已添加用户消息到会话 {session_id}")
        
        # 获取会话消息历史
        messages = session["messages"]
        
        # 记录完整消息历史(用于调试)
        logger.info(f"会话消息历史(共 {len(messages)} 条):")
        for i, msg in enumerate(messages[-3:]):  # 只记录最后3条以避免日志过长
            logger.info(f"  消息 {i+1}/{len(messages)}: role={msg.get('role')}, content={msg.get('content')[:50]}...")
        
        # 根据提供商选择不同的处理方式
        result = None
        logger.info(f"向会话 {session_id} 发送消息，使用提供商: {provider}")
        
        try:
            if provider == "tongyi-qianwen":
                # 使用通义千问API
                api_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
                headers = {
                    'Content-Type': 'application/json',
                    'Authorization': f'Bearer {api_key}'
                }
                
                # 构建请求体 - 通义千问格式
                request_body = {
                    "model": model or "qwen-max",
                    "input": {
                        "messages": messages
                    }
                }
                
                # 记录请求内容
                logger.info(f"通义千问API请求URL: {api_url}")
                logger.info(f"通义千问API请求模型: {model or 'qwen-max'}")
                
                # 发送请求
                logger.info("发送请求到通义千问API...")
                response = requests.post(api_url, headers=headers, json=request_body, timeout=300)
                logger.info(f"通义千问API响应状态码: {response.status_code}")
                
                if response.status_code != 200:
                    logger.error(f"通义千问API请求失败: HTTP {response.status_code}")
                    logger.error(f"响应内容: {response.text[:1000]}")
                    raise Exception(f"通义千问API请求失败: HTTP {response.status_code}, {response.text[:200]}")
                
                response_data = response.json()
                logger.info(f"通义千问API响应: {str(response_data)[:500]}...")
                
                # 提取回复
                if "output" in response_data and "choices" in response_data["output"]:
                    if "message" in response_data["output"]["choices"][0]:
                        result = response_data["output"]["choices"][0]["message"]["content"]
                    elif "text" in response_data["output"]["choices"][0]:
                        result = response_data["output"]["choices"][0]["text"]
                    else:
                        logger.error(f"无法从通义千问响应中提取回复: {str(response_data)[:500]}")
                        raise Exception("无法从通义千问响应中提取回复")
                # 支持直接output.text格式（通义千问特有）
                elif "output" in response_data and "text" in response_data["output"]:
                    result = response_data["output"]["text"]
                    logger.info("通义千问使用output.text格式返回结果")
                else:
                    logger.error(f"通义千问响应格式不符合预期: {str(response_data)[:500]}")
                    raise Exception("通义千问响应格式不符合预期")
                    
            elif provider == "volcengine":
                # 使用火山引擎API
                api_url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
                headers = {
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json",
                    "Accept": "application/json"
                }
                
                # 构建请求体 - 火山引擎格式
                request_body = {
                    "model": model or "doubao-1-5-thinking-pro",
                    "messages": messages
                }
                
                # 记录请求内容
                logger.info(f"火山引擎API请求URL: {api_url}")
                logger.info(f"火山引擎API请求模型: {model or 'doubao-1-5-thinking-pro'}")
                
                # 发送请求
                logger.info("发送请求到火山引擎API...")
                response = requests.post(api_url, headers=headers, json=request_body, timeout=300)
                logger.info(f"火山引擎API响应状态码: {response.status_code}")
                
                if response.status_code != 200:
                    logger.error(f"火山引擎API请求失败: HTTP {response.status_code}")
                    logger.error(f"响应内容: {response.text[:1000]}")
                    raise Exception(f"火山引擎API请求失败: HTTP {response.status_code}, {response.text[:200]}")
                
                response_data = response.json()
                logger.info(f"火山引擎API响应: {str(response_data)[:500]}...")
                
                # 提取回复
                if "choices" in response_data and len(response_data["choices"]) > 0:
                    if "message" in response_data["choices"][0]:
                        result = response_data["choices"][0]["message"]["content"]
                    elif "text" in response_data["choices"][0]:
                        result = response_data["choices"][0]["text"]
                    else:
                        logger.error(f"无法从火山引擎响应中提取回复: {str(response_data)[:500]}")
                        raise Exception("无法从火山引擎响应中提取回复")
                else:
                    logger.error(f"火山引擎响应格式不符合预期: {str(response_data)[:500]}")
                    raise Exception("火山引擎响应格式不符合预期")
                    
            elif provider == "deepseek":
                # 使用DeepSeek API
                api_url = "https://api.deepseek.com/v1/chat/completions"
                headers = {
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json"
                }
                
                # 构建请求体 - DeepSeek格式
                request_body = {
                    "model": model or "deepseek-chat",
                    "messages": messages
                }
                
                # 记录请求内容
                logger.info(f"DeepSeek API请求URL: {api_url}")
                logger.info(f"DeepSeek API请求模型: {model or 'deepseek-chat'}")
                
                # 发送请求
                logger.info("发送请求到DeepSeek API...")
                response = requests.post(api_url, headers=headers, json=request_body, timeout=300)
                logger.info(f"DeepSeek API响应状态码: {response.status_code}")
                
                if response.status_code != 200:
                    logger.error(f"DeepSeek API请求失败: HTTP {response.status_code}")
                    logger.error(f"响应内容: {response.text[:1000]}")
                    raise Exception(f"DeepSeek API请求失败: HTTP {response.status_code}, {response.text[:200]}")
                
                response_data = response.json()
                logger.info(f"DeepSeek API响应: {str(response_data)[:500]}...")
                
                # 提取回复
                if "choices" in response_data and len(response_data["choices"]) > 0:
                    if "message" in response_data["choices"][0]:
                        result = response_data["choices"][0]["message"]["content"]
                    elif "text" in response_data["choices"][0]:
                        result = response_data["choices"][0]["text"]
                    else:
                        logger.error(f"无法从DeepSeek响应中提取回复: {str(response_data)[:500]}")
                        raise Exception("无法从DeepSeek响应中提取回复")
                else:
                    logger.error(f"DeepSeek响应格式不符合预期: {str(response_data)[:500]}")
                    raise Exception("DeepSeek响应格式不符合预期")
            
            elif provider == "zhipu-free":
                # 使用智谱API
                api_url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
                headers = {
                    'Content-Type': 'application/json',
                    'Authorization': f'Bearer {api_key}'
                }
                # 模型名映射，防止传递zhipu-free
                real_model = model if model and model not in ["zhipu-free", ""] else "glm-4"
                request_body = {
                    "model": real_model,
                    "messages": messages
                }
                # 记录请求内容
                logger.info(f"智谱API请求URL: {api_url}")
                logger.info(f"智谱API请求模型: {real_model}")
                # 发送请求
                logger.info("发送请求到智谱API...")
                response = requests.post(api_url, headers=headers, json=request_body, timeout=300)
                logger.info(f"智谱API响应状态码: {response.status_code}")
                if response.status_code != 200:
                    logger.error(f"智谱API请求失败: HTTP {response.status_code}")
                    logger.error(f"响应内容: {response.text[:1000]}")
                    raise Exception(f"智谱API请求失败: HTTP {response.status_code}, {response.text[:200]}")
                response_data = response.json()
                logger.info(f"智谱API响应: {str(response_data)[:500]}...")
                # 提取回复
                if "choices" in response_data and len(response_data["choices"]) > 0:
                    if "message" in response_data["choices"][0]:
                        result = response_data["choices"][0]["message"]["content"]
                    else:
                        logger.error(f"无法从智谱响应中提取回复: {str(response_data)[:500]}")
                        raise Exception("无法从智谱响应中提取回复")
                else:
                    logger.error(f"智谱响应格式不符合预期: {str(response_data)[:500]}")
                    raise Exception("智谱响应格式不符合预期")
            
            else:
                logger.error(f"未支持的提供商: {provider}")
                return Response(
                    content=json.dumps({
                        "success": False,
                        "error": f"未支持的提供商: {provider}"
                    }, ensure_ascii=False),
                    media_type="application/json",
                    status_code=400,
                    headers={
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    }
                )
                
        except requests.exceptions.RequestException as e:
            # 处理网络请求异常
            error_message = f"API请求异常: {str(e)}"
            logger.error(error_message)
            return Response(
                content=json.dumps({
                    "success": False,
                    "error": error_message
                }, ensure_ascii=False),
                media_type="application/json",
                status_code=500,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                }
            )
        except Exception as e:
            # 处理其他异常
            error_message = f"处理会话消息时出错: {str(e)}"
            logger.error(error_message)
            return Response(
                content=json.dumps({
                    "success": False,
                    "error": error_message
                }, ensure_ascii=False),
                media_type="application/json",
                status_code=500,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                }
            )
        
        # 如果获取到结果，添加到会话中
        if result:
            logger.info(f"成功获取到AI回复, 长度: {len(result)}")
            session_manager.add_message(session_id, "assistant", result)
            
            # 格式化并记录到文件
            log_text = format_llm_log(user_message, result, f"{provider}-{model}")
            llm_logger.info(log_text)
            
            return Response(
                content=json.dumps({
                    "success": True,
                    "reply": result,
                    "message_count": session["message_count"]
                }, ensure_ascii=False),
                media_type="application/json",
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                }
            )
        else:
            logger.error("未能获取AI回复")
            return Response(
                content=json.dumps({
                    "success": False,
                    "error": "未能获取AI回复"
                }, ensure_ascii=False),
                media_type="application/json",
                status_code=500,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                }
            )
    
    except Exception as e:
        logger.error(f"处理会话消息失败: {str(e)}")
        return Response(
            content=json.dumps({
                "success": False,
                "error": f"处理会话消息失败: {str(e)}"
            }, ensure_ascii=False),
            media_type="application/json",
            status_code=500,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            }
        )

@app.delete("/api/session/{session_id}")
async def delete_session(session_id: str):
    """删除会话"""
    try:
        success = session_manager.delete_session(session_id)
        if success:
            return Response(
                content=json.dumps({
                    "success": True,
                    "message": "会话已删除"
                }, ensure_ascii=False),
                media_type="application/json",
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "GET, DELETE, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type"
                }
            )
        else:
            return Response(
                content=json.dumps({
                    "success": False,
                    "error": "会话不存在或已删除"
                }, ensure_ascii=False),
                media_type="application/json",
                status_code=404,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "GET, DELETE, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type"
                }
            )
    except Exception as e:
        logger.error(f"删除会话失败: {str(e)}")
        return Response(
            content=json.dumps({
                "success": False,
                "error": f"删除会话失败: {str(e)}"
            }, ensure_ascii=False),
            media_type="application/json",
            status_code=500,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, DELETE, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type"
            }
        )

@app.get("/api/session/{session_id}")
async def get_session(session_id: str):
    """获取会话信息"""
    try:
        session = session_manager.get_session(session_id)
        if session:
            return Response(
                content=json.dumps({
                    "success": True,
                    "session": session
                }, ensure_ascii=False),
                media_type="application/json",
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "GET, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type"
                }
            )
        else:
            return Response(
                content=json.dumps({
                    "success": False,
                    "error": "会话不存在或已过期"
                }, ensure_ascii=False),
                media_type="application/json",
                status_code=404,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "GET, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type"
                }
            )
    except Exception as e:
        logger.error(f"获取会话失败: {str(e)}")
        return Response(
            content=json.dumps({
                "success": False,
                "error": f"获取会话失败: {str(e)}"
            }, ensure_ascii=False),
            media_type="application/json",
            status_code=500,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type"
            }
        )

# 添加OPTIONS请求处理
@app.options("/api/session")
@app.options("/api/session/message")
@app.options("/api/session/{session_id}")
async def options_session_endpoint():
    """处理会话相关API的预检请求"""
    return Response(
        content="",
        headers={
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, POST, DELETE, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type, Authorization"
        }
    )

@app.post("/register", response_model=UserResponse)
async def register(user: UserCreate, db: Session = Depends(get_db)):
    existing_user = await get_user(db, username=user.username)
    if existing_user:
        raise HTTPException(status_code=400, detail="用户名已存在")
    new_user = await create_user(db, user)
    return UserResponse(id=new_user.id, username=new_user.username)

from fastapi.security import OAuth2PasswordRequestForm
from jose import JWTError

@app.post("/token")
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    user = await authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token = create_access_token(data={"sub": user.username})
    return {"access_token": access_token, "token_type": "bearer"}

@app.get("/users/me", response_model=UserResponse)
async def read_users_me(current_user: User = Depends(get_current_user)):
    return UserResponse(id=current_user.id, username=current_user.username)

if __name__ == "__main__":
    import uvicorn
    
    # 配置uvicorn的日志
    log_config = {
        "version": 1,
        "disable_existing_loggers": False,  # 改为False以允许其他日志器工作
        "formatters": {
            "default": {
                "format": "%(levelname)s: %(message)s"
            }
        },
        "handlers": {
            "default": {
                "formatter": "default",
                "class": "logging.StreamHandler",
                "stream": "ext://sys.stdout"
            }
        },
        "loggers": {
            "uvicorn": {"handlers": ["default"], "level": "INFO"},
            "uvicorn.error": {"handlers": ["default"], "level": "INFO"},
            # 仅禁用访问日志
            "uvicorn.access": {"handlers": ["default"], "level": "WARNING"},
        }
    }
    
    # 启动服务器
    uvicorn.run(
        "main:app",
        host="127.0.0.1",
        port=8088,  # 默认端口改为8088
        log_config=log_config,
        log_level="info",  # 改为info级别
        access_log=True  # 启用访问日志，但会被logger配置过滤
    )

# 示例代码
# client = Client("http://http://localhost:7860/?/")
# result = client.predict(
#     name="使用参考音?,
#     voice=handle_file('https://github.com/gradio-app/gradio/raw/main/test/test_files/audio_sample.wav'),
#     text="Hello!!",
#     speed=1,
#     api_name="/infer"
# )
# print(result) 

# JWT 解码函数
def decode_access_token(token: str):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token is invalid or expired",
            headers={"WWW-Authenticate": "Bearer"},
        )

# --- 自动发言/弹窗/自动回复自动化接口 ---

# 自动发言
auto_message_thread = None
auto_message_running = False

class AutoMessageParams(BaseModel):
    platform: str
    messages: list
    interval_min: int = 5
    interval_max: int = 10
    anti_block: bool = False
    random: bool = False

auto_message_task_handle = None

def insert_random_spaces(text, insertion_probability=0.2):
    import random
    if not text or insertion_probability <= 0:
        return text
    max_spaces = min(50 - len(text), 5)
    if max_spaces <= 0:
        return text
    probability = min(max(insertion_probability, 0), 0.5)
    result = []
    last_was_space = False
    SPACE_CHAR = ' '
    spaces_inserted = 0
    for i, char in enumerate(text):
        result.append(char)
        if spaces_inserted >= max_spaces:
            continue
        # 不在空格后、不在末尾、不在已有空格后插入
        if (
            not last_was_space
            and char != SPACE_CHAR
            and i < len(text) - 1
            and text[i + 1] != SPACE_CHAR
            and random.random() < probability
        ):
            spaces_to_insert = min(max_spaces - spaces_inserted, 1 if random.random() < 0.9 else 2)
            result.append(SPACE_CHAR * spaces_to_insert)
            spaces_inserted += spaces_to_insert
            last_was_space = True
        else:
            last_was_space = (char == SPACE_CHAR)
    # 如果没插入空格，就随便找个地方插一个
    if spaces_inserted == 0 and len(result) > 1:
        index = random.randint(0, len(result) - 2)
        result.insert(index, SPACE_CHAR)
    return ''.join(result)

async def auto_message_task(params: AutoMessageParams):
    global auto_message_running, is_connected, playwright_page
    import random
    platform = getattr(params, "platform", "douyin")
    # 选择器分支
    if platform in ["douyin", "buyin"]:
        comment_selector = '#input-comment-block-id textarea'
    elif platform == "douyin_group":
        comment_selector = 'textarea[class^="input"]'
    elif platform == "xhs":
        comment_selector = '.comment-input textarea'
    elif platform == "wxvideo":
        comment_selector = '.live-message-input-container textarea'
    elif platform == "kuaishou":
        comment_selector = 'div[class^=reply-content] input[type=text]'
    elif platform == "taobao":
        comment_selector = '#comment-page > div.tc-comment-reply > textarea'
    else:
        print(f"未知平台: {platform}")
        return
    pinned_msgs = []
    normal_msgs = []
    for m in params.messages:
        if isinstance(m, dict) and m.get('pinned'):
            pinned_msgs.append(m['text'])
            print(f"[DEBUG] 发现置顶消息: {m['text']}")
        elif isinstance(m, dict):
            normal_msgs.append(m['text'])
            print(f"[DEBUG] 发现普通消息: {m['text']}")
        else:
            normal_msgs.append(m)
            print(f"[DEBUG] 发现字符串消息: {m}")

    print(f"[DEBUG] 置顶消息总数: {len(pinned_msgs)}")
    print(f"[DEBUG] 普通消息总数: {len(normal_msgs)}")
    print(f"[DEBUG] 置顶消息列表: {pinned_msgs}")
    print(f"[DEBUG] 普通消息列表: {normal_msgs}")

    pinned_idx = 0
    normal_idx = 0
    print(f"[DEBUG] auto_message_task params.random: {getattr(params, 'random', False)}")
    while auto_message_running and is_connected and playwright_page is not None:
        # 1. 先发一条置顶消息（循环）
        if pinned_msgs:
            msg = pinned_msgs[pinned_idx]
            pinned_idx = (pinned_idx + 1) % len(pinned_msgs)
            msg_to_send = msg
            if getattr(params, 'anti_block', False):
                msg_to_send = insert_random_spaces(msg)
            textarea = await playwright_page.query_selector(comment_selector)
            if textarea:
                await textarea.focus()
                await textarea.fill('')
                await textarea.type(msg_to_send, delay=50)
                await playwright_page.evaluate(
                    '(el) => { el.dispatchEvent(new Event("input", { bubbles: true })); el.dispatchEvent(new Event("change", { bubbles: true })); }',
                    textarea
                )
                # ====== 自动点击置顶按钮 ======
                pin_success = False
                pin_top_selectors = []

                if platform in ["douyin", "douyin_shop", "buyin"]:
                    # 抖音系列平台的置顶按钮选择器（基于实际HTML结构）
                    pin_top_selectors = [
                        'input.auxo-checkbox-input[type="checkbox"]',  # 实际的置顶复选框
                        'input[type="checkbox"][class*="auxo-checkbox"]',  # auxo复选框变体
                        'input[type="checkbox"][class*="pin"], input[type="checkbox"][class*="top"]',  # 置顶复选框
                        'label[class*="pin"], label[class*="top"]',  # 置顶标签
                        '#input-comment-block-id input[type="checkbox"]',  # 评论区复选框
                        '#input-comment-block-id label',  # 评论区标签
                        'div[class*="comment"] input[type="checkbox"]',  # 评论相关复选框
                        'div[class*="comment"] label'  # 评论相关标签
                    ]
                elif platform == "douyin_group":
                    # 抖音团购(EOS)平台的置顶按钮选择器
                    pin_top_selectors = [
                        'div[class^="comment-wrap"] input[type="checkbox"]',  # EOS评论区复选框
                        'div[class^="comment-wrap"] label',  # EOS评论区标签
                        'input[type="checkbox"][class*="pin"], input[type="checkbox"][class*="top"]',  # 置顶复选框
                        'label[class*="pin"], label[class*="top"]'  # 置顶标签
                    ]
                elif platform == "kuaishou":
                    pin_top_selectors = [
                        'div[class^=reply-content] input[type="checkbox"]',
                        'div[class^=reply-content] label',
                        'input[class*="pin"], input[class*="top"]'
                    ]
                elif platform in ["xhs", "redbook"]:
                    pin_top_selectors = [
                        '.comment-input input[type="checkbox"]',
                        '.comment-input label',
                        'input[class*="pin"], input[class*="top"]'
                    ]
                elif platform in ["wxvideo", "wxchannel"]:
                    pin_top_selectors = [
                        '.live-message-input-container input[type="checkbox"]',
                        '.live-message-input-container label'
                    ]
                elif platform == "taobao":
                    pin_top_selectors = [
                        '#comment-page input[type="checkbox"]',
                        '#comment-page label'
                    ]

                # 尝试多个选择器找到置顶按钮
                print(f"[{platform}] 开始查找置顶按钮，尝试 {len(pin_top_selectors)} 个选择器")
                for i, selector in enumerate(pin_top_selectors, 1):
                    try:
                        print(f"[{platform}] 尝试选择器 {i}: {selector}")
                        pin_elements = await playwright_page.query_selector_all(selector)
                        print(f"[{platform}] 选择器 {i} 找到 {len(pin_elements)} 个元素")

                        for j, pin_element in enumerate(pin_elements):
                            # 检查元素文本或属性，确保是置顶相关的
                            element_text = ""
                            element_class = ""
                            try:
                                element_text = await pin_element.inner_text()
                                element_class = await pin_element.get_attribute('class') or ""
                            except:
                                pass

                            print(f"[{platform}] 元素 {j+1}: 文本='{element_text}', class='{element_class}'")

                            # 对于auxo-checkbox-input，直接处理（这是抖店的置顶复选框）
                            if "auxo-checkbox" in element_class:
                                print(f"[{platform}] 发现auxo复选框，直接处理")
                                is_checked = await pin_element.is_checked()
                                print(f"[{platform}] 复选框状态: {'已选中' if is_checked else '未选中'}")
                                if not is_checked:
                                    await pin_element.click()
                                    print(f"[{platform}] 已勾选置顶复选框 (auxo-checkbox)")
                                    pin_success = True
                                    break
                                else:
                                    print(f"[{platform}] 置顶复选框已经选中，无需重复点击")
                                    pin_success = True
                                    break

                            # 检查是否是置顶相关的元素
                            elif any(keyword in element_text.lower() for keyword in ["置顶", "pin", "top", "固定"]) or \
                               any(keyword in selector.lower() for keyword in ["pin", "top", "auxo"]):
                                print(f"[{platform}] 发现置顶相关元素")
                                # 如果是复选框，检查是否已选中
                                if 'input[type="checkbox"]' in selector:
                                    is_checked = await pin_element.is_checked()
                                    print(f"[{platform}] 复选框状态: {'已选中' if is_checked else '未选中'}")
                                    if not is_checked:
                                        await pin_element.click()
                                        print(f"[{platform}] 已勾选置顶复选框")
                                        pin_success = True
                                        break
                                else:
                                    # 如果是标签，直接点击
                                    await pin_element.click()
                                    print(f"[{platform}] 已点击置顶按钮: {element_text}")
                                    pin_success = True
                                    break
                        if pin_success:
                            break
                    except Exception as e:
                        print(f"[{platform}] 选择器 {i} 处理异常: {e}")
                        continue

                if not pin_success:
                    print(f"[{platform}] 未找到有效的置顶按钮，直接发送普通消息")
                # ====== 置顶按钮处理结束 ======
                # 发送消息
                if platform == "douyin_group":
                    # 抖音团购使用点击发送按钮
                    send_button = await playwright_page.query_selector('div[class^="comment-wrap"] div[class^="button"]')
                    if send_button:
                        await send_button.click()
                        print(f"[{platform}] 已点击发送按钮发送置顶消息: {msg_to_send}")
                    else:
                        await textarea.press('Enter')
                        print(f"[{platform}] 未找到发送按钮，使用回车发送置顶消息: {msg_to_send}")
                else:
                    await textarea.press('Enter')
                    print(f"[{platform}] 已回车发送置顶消息: {msg_to_send}")
            else:
                print(f"[{platform}] 未找到评论输入框")
            next_interval = random.randint(params.interval_min, params.interval_max)
            print(f"[{platform}] 下一次发送置顶消息将在 {next_interval} 秒后，内容: {msg_to_send}")
            await asyncio.sleep(next_interval)
        # 2. 再发一条普通消息（顺序或随机）
        if normal_msgs:
            if getattr(params, 'random', False):
                msg = random.choice(normal_msgs)
            else:
                msg = normal_msgs[normal_idx]
                normal_idx = (normal_idx + 1) % len(normal_msgs)
            msg_to_send = msg
            if getattr(params, 'anti_block', False):
                msg_to_send = insert_random_spaces(msg)
            textarea = await playwright_page.query_selector(comment_selector)
            if textarea:
                await textarea.focus()
                await textarea.fill('')
                await textarea.type(msg_to_send, delay=50)
                await playwright_page.evaluate(
                    '(el) => { el.dispatchEvent(new Event("input", { bubbles: true })); el.dispatchEvent(new Event("change", { bubbles: true })); }',
                    textarea
                )
                # 发送消息
                if platform == "douyin_group":
                    # 抖音团购使用点击发送按钮
                    send_button = await playwright_page.query_selector('div[class^="comment-wrap"] div[class^="button"]')
                    if send_button:
                        await send_button.click()
                        print(f"[{platform}] 已点击发送按钮发送普通消息: {msg_to_send}")
                    else:
                        await textarea.press('Enter')
                        print(f"[{platform}] 未找到发送按钮，使用回车发送普通消息: {msg_to_send}")
                else:
                    await textarea.press('Enter')
                    print(f"[{platform}] 已回车发送普通消息: {msg_to_send}")
            else:
                print(f"[{platform}] 未找到评论输入框")
            next_interval = random.randint(params.interval_min, params.interval_max)
            print(f"[{platform}] 下一次发送普通消息将在 {next_interval} 秒后，内容: {msg_to_send}")
            await asyncio.sleep(next_interval)

@app.post("/api/automation/auto-message/start")
async def start_auto_message(params: AutoMessageParams):
    global auto_message_running, is_connected, playwright_page, auto_message_task_handle
    if not is_connected or playwright_page is None:
        return {"status": "not_connected"}
    if auto_message_running:
        return {"status": "already_running"}
    auto_message_running = True
    auto_message_task_handle = asyncio.create_task(auto_message_task(params))
    return {"status": "started"}

@app.post("/api/automation/auto-message/stop")
async def stop_auto_message():
    global auto_message_running, auto_message_task_handle
    auto_message_running = False
    if auto_message_task_handle:
        auto_message_task_handle.cancel()
    return {"status": "stopped"}

# 自动弹窗
auto_popup_thread = None
auto_popup_running = False

class AutoPopupParams(BaseModel):
    platform: str
    goods_list: list
    interval_min: int = 10
    interval_max: int = 20
    random_popup: bool = False

# === 平台商品弹窗配置（基于参考代码优化） ===
PLATFORM_POPUP_CONFIG = {
    # 抖音系列平台
    "douyin": {
        "goods_selectors": ['div[class^="goodsItem"]', 'div[class*="goods-item"]', 'tr'],
        "button_selector": 'div[class*="wrapper"]:has(button) button',
        "button_texts": ["讲解", "弹窗", "推荐", "上架", ""],
        "type": "goods_item"
    },
    "douyin_shop": {
        "goods_selectors": ['div[class^="goodsItem"]', 'div[class*="goods-item"]', 'tr'],
        "button_selector": 'div[class*="wrapper"]:has(button) button',
        "button_texts": ["讲解", "弹窗", "推荐", "上架", ""],
        "type": "goods_item"
    },
    "buyin": {
        "goods_selectors": ['div[class^="goodsItem"]', '[class^="goodsPanel"]', 'tr'],
        "button_selector": 'div[class*="wrapper"]:has(button) button',
        "button_texts": ["讲解", "弹窗", "推荐", "上架", ""],
        "type": "goods_item"
    },
    "douyin_group": {
        "goods_selectors": ['#live-card-list div[class^="render-item"]', 'div[class^="render-item"]', '#live-card-list > div > div'],
        "button_selector": '[class^="talking-btn"]',
        "button_texts": ["讲解", "弹窗", "推荐", "上架", ""],
        "type": "goods_item"
    },
    # 快手
    "kuaishou": {
        "goods_selectors": ['[class^=list-container] [class^=list-item]'],
        "button_selector": '[class^=cardBtn] > button:last-child',
        "button_texts": ["开始讲解", "结束讲解", ""],
        "type": "goods_item",
        "confirm_modal": '.ant-modal-root button:last-child'  # 快手需要确认弹窗
    },
    # 小红书
    "redbook": {
        "goods_selectors": ['.goods-list .table-wrap > div > div > table tbody tr'],
        "button_selector": 'span[data-track="live_control_popup_card"]',
        "button_texts": ["弹卡"],
        "type": "table_row"
    },
    "xhs": {
        "goods_selectors": ['.goods-list .table-wrap > div > div > table tbody tr'],
        "button_selector": 'span[data-track="live_control_popup_card"]',
        "button_texts": ["弹卡"],
        "type": "table_row"
    },
    # 淘宝
    "taobao": {
        "goods_selectors": ['#livePushed-tcl-unique-scroll-list .list-item'],
        "button_selector": 'button[data-tblalog-id="tanPin"]',
        "button_texts": ["弹窗", ""],
        "type": "goods_item"
    },
    # 微信视频号
    "wxchannel": {
        "goods_selectors": ['.commodity-list-wrap .table-body-wrap > div > span div.table-row-wrap'],
        "button_selector": '.promoting-wrap .action-link span[class*="promoting"]',
        "button_texts": ["讲解", "结束讲解", "推广", ""],
        "type": "goods_item",
        "separate_page": True  # 视频号需要独立商品页面
    },
    "wxvideo": {
        "goods_selectors": ['.commodity-list-wrap .table-body-wrap > div > span div.table-row-wrap'],
        "button_selector": '.promoting-wrap .action-link span[class*="promoting"]',
        "button_texts": ["讲解", "结束讲解", "推广", ""],
        "type": "goods_item",
        "separate_page": True  # 视频号需要独立商品页面
    },
}

# 保持向后兼容的简单选择器配置
PLATFORM_POPUP_SELECTORS = {
    platform: config["button_selector"]
    for platform, config in PLATFORM_POPUP_CONFIG.items()
}

async def auto_popup_task(params: AutoPopupParams):
    global auto_popup_running, playwright_page
    import asyncio
    import random
    if playwright_page is None:
        print("未连接控制台，无法执行自动弹窗任务")
        return
    page = playwright_page
    platform = params.platform
    while auto_popup_running:
        goods = params.goods_list.copy()
        # 实时读取最新 random_popup 参数
        random_popup = automation_params.get("random_popup", params.random_popup)
        if random_popup:
            random.shuffle(goods)
        for good in goods:
            wait_time = random.uniform(params.interval_min, params.interval_max)
            now_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            print(f"[{now_str}] 下次执行「自动弹窗」将在 {wait_time:.3f} 秒后")
            await asyncio.sleep(wait_time)
            now_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            print(f"[{now_str}] 自动弹窗商品: {good}")
            try:
                # 检查页面是否还在中控台
                current_url = page.url
                if platform in ("douyin", "douyin_shop"):
                    # 检查是否在正确的中控台页面
                    if "live/control" not in current_url or "fxg.jinritemai.com" not in current_url:
                        print(f"[{now_str}] 警告：页面已跳转到 {current_url}，不在中控台页面")
                        # 尝试返回中控台
                        await page.goto("https://fxg.jinritemai.com/ffa/buyin/dashboard/live/control")
                        await asyncio.sleep(3)  # 等待页面加载
                        print(f"[{now_str}] 已尝试返回中控台页面")
                    elif "content-tool/live/control" in current_url:
                        print(f"[{now_str}] 检测到在内容工具页面，尝试跳转到商品管理页面")
                        # 如果在content-tool页面，这可能是新版本的抖店界面
                        # 先尝试当前页面查找商品，如果没有再跳转
                        pass

                selector_conf = PLATFORM_POPUP_SELECTORS.get(platform)
                if not selector_conf:
                    print(f"未配置平台 {platform} 的商品弹窗 selector")
                    continue
                if platform in ("redbook", "xhs"):
                    # 小红书平台的弹窗逻辑
                    rows = await page.query_selector_all('table tbody tr')
                    idx = int(good) - 1
                    if 0 <= idx < len(rows):
                        row = rows[idx]
                        btns = await row.query_selector_all('span[data-track=\"live_control_popup_card\"]')
                        found = False
                        for btn in btns:
                            text = await btn.inner_text()
                            if "弹卡" in text:
                                await btn.click()
                                found = True
                                print(f"[{now_str}] 商品 {good} 弹窗成功")
                                break
                        if not found:
                            print(f"[{now_str}] 第{good}行未找到弹卡按钮")
                    else:
                        print(f"[{now_str}] 商品序号 {good} 超出范围")
                elif platform in PLATFORM_POPUP_CONFIG:
                    # 使用平台特定的弹窗配置
                    config = PLATFORM_POPUP_CONFIG[platform]

                    # 处理视频号的独立商品页面
                    if platform in ["wxchannel", "wxvideo"]:
                        try:
                            # 获取浏览器上下文
                            browser_context = playwright_page.context

                            # 检查是否已有商品管理页面
                            goods_page = None
                            for existing_page in browser_context.pages:
                                if 'commodity/onsale/index' in existing_page.url:
                                    goods_page = existing_page
                                    break

                            # 没有则创建新页面
                            if not goods_page:
                                goods_page = await browser_context.new_page()
                                await goods_page.goto('https://channels.weixin.qq.com/platform/live/commodity/onsale/index')
                                await goods_page.wait_for_load_state('networkidle')
                                print(f"[{now_str}] 已打开视频号商品管理页面")

                            page = goods_page  # 切换到商品页面

                            # 关闭可能的弹窗
                            try:
                                close_button = await page.query_selector('.weui-desktop-dialog button[class*="close-btn"]')
                                if close_button:
                                    await close_button.click()
                                    print(f"[{now_str}] 已关闭视频号提示弹窗")
                            except:
                                pass
                        except Exception as e:
                            print(f"[{now_str}] 打开商品页面失败: {e}")
                            continue

                    # 尝试找到商品元素
                    goods_items = []
                    used_selector = None

                    for selector in config["goods_selectors"]:
                        try:
                            goods_items = await page.query_selector_all(selector)
                            if goods_items:
                                used_selector = selector
                                print(f"[{now_str}] 使用选择器 '{selector}' 找到 {len(goods_items)} 个商品")
                                break
                        except Exception as e:
                            print(f"[{now_str}] 选择器 '{selector}' 失败: {e}")
                            continue

                    if not goods_items:
                        print(f"[{now_str}] 未找到任何商品，尝试的选择器: {config['goods_selectors']}")
                        page_title = await page.title()
                        print(f"[{now_str}] 当前页面标题: {page_title}")
                        continue

                    # 视频号特殊处理：检查并修正商品排序
                    if platform in ["wxchannel", "wxvideo"] and len(goods_items) > 1:
                        try:
                            # 获取第一个和最后一个商品的ID
                            first_id_element = await goods_items[0].query_selector('input + span')
                            last_id_element = await goods_items[-1].query_selector('input + span')

                            if first_id_element and last_id_element:
                                first_id = int((await first_id_element.inner_text()).strip())
                                last_id = int((await last_id_element.inner_text()).strip())

                                # 如果是倒序，则反转列表
                                if first_id > last_id:
                                    goods_items.reverse()
                                    print(f"[{now_str}] 检测到倒序商品列表，已自动修正为正序")
                        except Exception as e:
                            print(f"[{now_str}] 商品排序检查失败: {e}")
                            # 排序失败不影响主流程，继续执行

                    idx = int(good) - 1
                    if 0 <= idx < len(goods_items):
                        goods_item = goods_items[idx]

                        # 根据平台类型处理弹窗逻辑
                        if config["type"] == "table_row" and platform in ("redbook", "xhs"):
                            # 小红书特殊处理：在表格行中查找弹卡按钮
                            btns = await goods_item.query_selector_all(config["button_selector"])
                            found = False
                            for btn in btns:
                                try:
                                    text = await btn.inner_text()
                                    if any(btn_text in text for btn_text in config["button_texts"] if btn_text):
                                        await btn.click()
                                        if "弹卡" in text:
                                            print(f"[{now_str}] 商品 {good} 弹卡成功 (按钮文本: '{text}')")
                                        else:
                                            print(f"[{now_str}] 商品 {good} 操作成功 (按钮文本: '{text}')")
                                        found = True
                                        break
                                except:
                                    continue
                            if not found:
                                print(f"[{now_str}] 商品 {good} 未找到有效的弹卡按钮")
                        else:
                            # 其他平台的通用处理
                            try:
                                popup_button = await goods_item.query_selector(config["button_selector"])
                                if popup_button:
                                    # 验证按钮文本
                                    try:
                                        button_text = await popup_button.inner_text()
                                        print(f"[{now_str}] 找到按钮，文本: '{button_text}'")

                                        # 检查按钮文本是否匹配
                                        text_match = any(
                                            btn_text in button_text or button_text.strip() == btn_text
                                            for btn_text in config["button_texts"]
                                        )

                                        if text_match:
                                            # 视频号和快手特殊处理：智能切换逻辑
                                            if platform in ["wxchannel", "wxvideo"]:
                                                # 循环点击直到状态变为"讲解"
                                                max_attempts = 3
                                                for attempt in range(max_attempts):
                                                    current_text = await popup_button.inner_text()
                                                    await popup_button.click()
                                                    await asyncio.sleep(1)  # 等待状态更新

                                                    # 重新获取按钮文本
                                                    try:
                                                        new_button = await goods_item.query_selector(config["button_selector"])
                                                        if new_button:
                                                            new_text = await new_button.inner_text()
                                                            if "讲解" in new_text and "结束" not in new_text:
                                                                print(f"[{now_str}] 商品 {good} 讲解成功")
                                                                break
                                                            popup_button = new_button  # 更新按钮引用
                                                    except:
                                                        break
                                                else:
                                                    print(f"[{now_str}] 商品 {good} 状态切换可能未完成")
                                            elif platform == "kuaishou":
                                                # 快手智能切换逻辑：结束讲解 -> 开始讲解
                                                current_text = await popup_button.inner_text()
                                                if current_text.strip() not in ["开始讲解", "结束讲解"]:
                                                    print(f"[{now_str}] 商品 {good} 按钮文本异常: '{current_text}'")

                                                await popup_button.click()
                                                print(f"[{now_str}] 快手商品 {good} 点击按钮: '{current_text}'")

                                                # 处理确认弹窗
                                                if config.get("confirm_modal"):
                                                    try:
                                                        await asyncio.sleep(1)  # 等待弹窗出现
                                                        confirm_btn = await page.query_selector(config["confirm_modal"])
                                                        if confirm_btn:
                                                            await confirm_btn.click()
                                                            print(f"[{now_str}] 快手确认弹窗已点击")
                                                    except:
                                                        pass

                                                # 如果是"结束讲解"，需要再次点击"开始讲解"
                                                if "结束讲解" in current_text:
                                                    await asyncio.sleep(1)  # 等待状态更新
                                                    try:
                                                        new_button = await goods_item.query_selector(config["button_selector"])
                                                        if new_button:
                                                            new_text = await new_button.inner_text()
                                                            if "开始讲解" in new_text:
                                                                await new_button.click()
                                                                print(f"[{now_str}] 快手商品 {good} 开始新的讲解")
                                                            else:
                                                                print(f"[{now_str}] 快手商品 {good} 状态异常: '{new_text}'")
                                                    except Exception as e:
                                                        print(f"[{now_str}] 快手商品 {good} 重新点击失败: {e}")
                                                else:
                                                    print(f"[{now_str}] 快手商品 {good} 讲解成功")
                                            else:
                                                await popup_button.click()
                                                # 根据按钮文本显示不同的成功消息
                                                if "讲解" in button_text:
                                                    print(f"[{now_str}] 商品 {good} 讲解成功")
                                                elif "弹窗" in button_text:
                                                    print(f"[{now_str}] 商品 {good} 弹窗成功")
                                                elif "弹卡" in button_text:
                                                    print(f"[{now_str}] 商品 {good} 弹卡成功")
                                                elif "推广" in button_text:
                                                    print(f"[{now_str}] 商品 {good} 推广成功")
                                                else:
                                                    print(f"[{now_str}] 商品 {good} 操作成功")

                                            # 快手确认弹窗处理已移至上方智能切换逻辑中
                                        else:
                                            print(f"[{now_str}] 商品 {good} 的按钮文本不匹配: '{button_text}' (期望: {config['button_texts']})")
                                    except Exception as e:
                                        print(f"[{now_str}] 获取按钮文本失败: {e}")
                                        # 如果获取文本失败，尝试直接点击
                                        await popup_button.click()
                                        print(f"[{now_str}] 商品 {good} 尝试直接点击按钮")
                                else:
                                    print(f"[{now_str}] 商品 {good} 未找到弹窗按钮 (选择器: {config['button_selector']})")
                            except Exception as e:
                                print(f"[{now_str}] 商品 {good} 弹窗操作失败: {e}")
                    else:
                        print(f"[{now_str}] 商品序号 {good} 超出范围，当前共有 {len(goods_items)} 个商品")
                else:
                    # 其他平台使用通用选择器
                    await page.click(selector_conf)
                    print(f"[{now_str}] 商品 {good} 弹窗成功")
            except Exception as e:
                print(f"[{now_str}] 自动弹窗点击异常: {e}")
    print("自动弹窗任务结束")

@app.post("/api/automation/auto-popup/start")
async def start_auto_popup(params: AutoPopupParams):
    global auto_popup_running
    if auto_popup_running:
        return {"status": "already_running"}
    auto_popup_running = True
    asyncio.create_task(auto_popup_task(params))
    return {"status": "started"}

@app.post("/api/automation/auto-popup/stop")
def stop_auto_popup():
    global auto_popup_running
    auto_popup_running = False
    return {"status": "stopped"}

class ManualPopupParams(BaseModel):
    platform: str
    goods_id: str

@app.post("/api/automation/auto-popup/manual")
async def manual_popup(params: ManualPopupParams):
    """手动弹窗指定商品"""
    global playwright_page
    if not is_connected or playwright_page is None:
        return {"status": "not_connected", "message": "未连接到控制台"}

    try:
        platform = params.platform
        goods_id = params.goods_id

        selector_conf = PLATFORM_POPUP_SELECTORS.get(platform)
        if not selector_conf:
            return {"status": "error", "message": f"未配置平台 {platform} 的商品弹窗选择器"}

        page = playwright_page
        now_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        if platform in ("redbook", "xhs"):
            # 小红书特殊处理
            rows = await page.query_selector_all('table tbody tr')
            idx = int(goods_id) - 1
            if 0 <= idx < len(rows):
                row = rows[idx]
                btns = await row.query_selector_all('span[data-track="live_control_popup_card"]')
                found = False
                for btn in btns:
                    text = await btn.inner_text()
                    if "弹卡" in text:
                        await btn.click()
                        found = True
                        print(f"[{now_str}] 手动弹窗商品 {goods_id} 成功")
                        break
                if not found:
                    return {"status": "error", "message": f"第{goods_id}行未找到弹卡按钮"}
            else:
                return {"status": "error", "message": f"商品序号 {goods_id} 超出范围"}
        else:
            # 其他平台通用处理
            await page.click(selector_conf)
            print(f"[{now_str}] 手动弹窗商品 {goods_id} 成功")

        return {"status": "success", "message": f"商品 {goods_id} 弹窗成功"}

    except Exception as e:
        error_msg = f"手动弹窗失败: {str(e)}"
        print(error_msg)
        return {"status": "error", "message": error_msg}

# 自动回复功能已删除 - 系统简化

playwright_browser = None
playwright_page = None
is_connected = False

async def disconnect_console():
    global playwright, playwright_browser, playwright_page, is_connected, auto_popup_running
    try:
        if playwright_browser:
            await playwright_browser.close()
    except Exception as e:
        print(f"关闭浏览器异常: {e}")
    playwright_browser = None
    playwright_page = None
    is_connected = False
    auto_popup_running = False  # 断开时强制停止自动弹窗任务
    return True

async def connect_to_console(platform: str):
    global playwright, playwright_browser, playwright_page, is_connected

    print(f"[启动] 开始连接 {platform} 平台")

    # 断开之前的连接
    await disconnect_console()

    try:
        if playwright is None:
            playwright = await async_playwright().start()
    except Exception as e:
        print(f"[错误] Playwright启动失败: {str(e)}")
        return {"success": False, "error": f"Playwright启动失败: {str(e)}"}

    # 简化的存储文件
    storage_path = os.path.join("sessions", "login_state.json")

    if os.path.exists(storage_path):
        print(f"[状态] 发现历史登录状态，自动加载")
        try:
            playwright_browser = await playwright.chromium.launch(
                headless=False,
                args=[
                    '--no-first-run',
                    '--no-default-browser-check',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding'
                ]
            )
            context = await playwright_browser.new_context(storage_state=storage_path)
            playwright_page = await context.new_page()
        except Exception as e:
            print(f"[错误] 浏览器启动失败: {str(e)}")
            raise e
    else:
        print(f"[新建] 首次登录，创建新的浏览器会话")
        try:
            playwright_browser = await playwright.chromium.launch(
                headless=False,
                args=[
                    '--no-first-run',
                    '--no-default-browser-check',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding'
                ]
            )
            playwright_page = await playwright_browser.new_page()
        except Exception as e:
            print(f"[错误] 浏览器启动失败: {str(e)}")
            raise e

    # 平台参数分支
    if platform in ["douyin_shop", "douyin"]:
        # 抖店登录URL，包含重定向到中控台的参数
        login_url = "https://fxg.jinritemai.com/login/common?extra=%7B%22target_url%22%3A%22https%3A%2F%2Ffxg.jinritemai.com%2Fffa%2Fbuyin%2Fdashboard%2Flive%2Fcontrol%22%7D"
        live_control_url = "https://fxg.jinritemai.com/ffa/buyin/dashboard/live/control"
        is_logged_in_selector = '.header-user-info, .index_userName, [class^="header"]'
        is_in_live_control_selector = '[class^="goodsPanel"], .main-content, .index_userName, .header-user-info'
    elif platform == "buyin":
        login_url = "https://buyin.jinritemai.com/mpa/account/login?log_out=1&type=24"
        live_control_url = "https://buyin.jinritemai.com/dashboard/live/control"
        is_logged_in_selector = '[class^="header"]'
        is_in_live_control_selector = '[class^="goodsPanel"]'
    elif platform == "douyin_group":
        login_url = "https://eos.douyin.com/livesite/login"
        live_control_url = "https://eos.douyin.com/livesite/live/current"
        is_logged_in_selector = '[class^="head-container"]'
        is_in_live_control_selector = '[class^="layout-container"]'
    elif platform == "douyin_live":
        login_url = "https://live.douyin.com/"
        live_control_url = "https://live.douyin.com/"
        is_logged_in_selector = '.webcast-chatroom, .webcast-room-footer, .webcast-chatroom__input'
        is_in_live_control_selector = '.webcast-chatroom, .webcast-room-footer, .webcast-chatroom__input'
    elif platform == "xhs" or platform == "redbook":
        login_url = "https://customer.xiaohongshu.com/login?service=https%3A%2F%2Fark.xiaohongshu.com%2Flive_center_control"
        backend_selector = '.app-root-topbar-wrapper'
    elif platform == "wxvideo" or platform == "wxchannel":
        login_url = "https://channels.weixin.qq.com/login.html"
        live_control_url = "https://channels.weixin.qq.com/platform/live/liveBuild"
        is_logged_in_selector = '.account-info .name'
        is_in_live_control_selector = '.live-message-input-container'
        # 视频号需要额外的商品管理页面
        goods_page_url = "https://channels.weixin.qq.com/platform/live/commodity/onsale/index"
    elif platform == "kuaishou":
        login_url = "https://login.kwaixiaodian.com/?biz=merchantLivePc&redirect_url=https%3A%2F%2Fzs.kwaixiaodian.com%2Fpage%2Fhelper"
        live_control_url = "https://zs.kwaixiaodian.com/page/helper"
        is_logged_in_selector = '[class^=nickname] [class^=name]'
        is_in_live_control_selector = 'div[class^=live-panel]'
    elif platform == "taobao":
        login_url = "https://login.taobao.com/havanaone/login/login.htm?bizName=taobao&sub=true&redirectURL=https%3A%2F%2Fliveplatform.taobao.com%2Frestful%2Findex%2Flive%2Flist"
        live_control_url = "https://liveplatform.taobao.com/restful/index/live/list"
        is_logged_in_selector = 'span[class^=header-anchor-name]'
        is_in_live_control_selector = 'div.tblalm-lm-content'
    else:
        print(f"未知平台: {platform}")
        if playwright_browser:
            await playwright_browser.close()
        return {"success": False, "error": f"不支持的平台: {platform}"}

    # 主流程
    if platform == "xhs" or platform == "redbook":
        await playwright_page.goto(login_url)
        print(f"请手动扫码或输入账号密码登录{platform}...")
        try:
            await playwright_page.wait_for_selector(backend_selector, timeout=120000)
            print(f"[{platform}] 已进入后台，特征元素 {backend_selector} 已出现，判定为已连接。")
            is_connected = True
            storage_path = os.path.join("sessions", "login_state.json")
            await playwright_page.context.storage_state(path=storage_path)
            print(f"[保存] 登录状态已保存到: {storage_path}")

            # 获取账号名称
            account_name = await get_account_name(playwright_page, platform)
            print(f"[账号] 当前登录的{platform}账号: {account_name}")

            # 视频号特殊处理：同时打开商品管理页面
            if platform in ["wxvideo", "wxchannel"]:
                try:
                    context = playwright_page.context
                    goods_page = await context.new_page()
                    await goods_page.goto(goods_page_url)
                    await goods_page.wait_for_load_state('networkidle')
                    print(f"[商品] 已同时打开视频号商品管理页面")

                    # 关闭可能的弹窗
                    try:
                        close_button = await goods_page.query_selector('.weui-desktop-dialog button[class*="close-btn"]')
                        if close_button:
                            await close_button.click()
                            print(f"[成功] 已关闭商品页面提示弹窗")
                    except:
                        pass
                except Exception as e:
                    print(f"[警告] 打开商品管理页面失败: {e}")

            import asyncio
            await asyncio.sleep(5)
            return {"success": True, "account_name": account_name, "platform": platform}
        except Exception as e:
            error_msg = str(e)
            print(f"[{platform}] 登录或跳转失败。错误详情: {e}")
            print(f"[{platform}] 当前页面URL: {playwright_page.url if playwright_page else '未知'}")
            is_connected = False
            if playwright_browser:
                await playwright_browser.close()

            # 根据错误类型返回友好的错误消息
            if "browser has been closed" in error_msg.lower() or "target closed" in error_msg.lower():
                return {"success": False, "error": "连接失败，请重新连接登录"}
            elif "timeout" in error_msg.lower() or "waiting for selector" in error_msg.lower():
                return {"success": False, "error": "登录超时，请重新连接登录"}
            elif "navigation" in error_msg.lower() or "net::" in error_msg.lower():
                return {"success": False, "error": "网络连接失败，请检查网络后重新连接登录"}
            else:
                return {"success": False, "error": "连接失败，请重新连接登录"}
    else:
        await playwright_page.goto(login_url)
        print(f"请手动扫码或输入账号密码登录{platform}...")
        try:
            # 等待登录成功
            print(f"[{platform}] 等待登录成功，检测元素: {is_logged_in_selector}")
            await playwright_page.wait_for_selector(is_logged_in_selector, timeout=120000)
            print(f"[{platform}] 登录成功！正在跳转到中控台...")

            # 跳转到中控台
            print(f"[{platform}] 跳转到中控台: {live_control_url}")
            await playwright_page.goto(live_control_url)

            # 等待中控台页面加载
            print(f"[{platform}] 等待中控台页面加载，检测元素: {is_in_live_control_selector}")
            await playwright_page.wait_for_selector(is_in_live_control_selector, timeout=60000)
            print(f"[{platform}] 已成功进入中控台，特征元素 {is_in_live_control_selector} 已出现，判定为已连接。")

            is_connected = True
            storage_path = os.path.join("sessions", "login_state.json")
            await playwright_page.context.storage_state(path=storage_path)
            print(f"[保存] 登录状态已保存到: {storage_path}")

            # 获取账号名称
            account_name = await get_account_name(playwright_page, platform)
            print(f"[账号] 当前登录的{platform}账号: {account_name}")
            print(f"[成功] 连接成功！下次连接时会自动登录")

            # 视频号特殊处理：同时打开商品管理页面
            if platform in ["wxvideo", "wxchannel"]:
                try:
                    context = playwright_page.context
                    goods_page = await context.new_page()
                    await goods_page.goto(goods_page_url)
                    await goods_page.wait_for_load_state('networkidle')
                    print(f"[商品] 已同时打开视频号商品管理页面")

                    # 关闭可能的弹窗
                    try:
                        close_button = await goods_page.query_selector('.weui-desktop-dialog button[class*="close-btn"]')
                        if close_button:
                            await close_button.click()
                            print(f"[成功] 已关闭商品页面提示弹窗")
                    except:
                        pass
                except Exception as e:
                    print(f"[警告] 打开商品管理页面失败: {e}")

            import asyncio
            await asyncio.sleep(5)
            return {"success": True, "account_name": account_name, "platform": platform}
        except Exception as e:
            error_msg = str(e)
            print(f"[{platform}] 登录或跳转失败。错误详情: {e}")
            print(f"[{platform}] 当前页面URL: {playwright_page.url if playwright_page else '未知'}")
            is_connected = False
            if playwright_browser:
                await playwright_browser.close()

            # 根据错误类型返回友好的错误消息
            if "browser has been closed" in error_msg.lower() or "target closed" in error_msg.lower():
                return {"success": False, "error": "连接失败，请重新连接登录"}
            elif "timeout" in error_msg.lower() or "waiting for selector" in error_msg.lower():
                return {"success": False, "error": "登录超时，请重新连接登录"}
            elif "navigation" in error_msg.lower() or "net::" in error_msg.lower():
                return {"success": False, "error": "网络连接失败，请检查网络后重新连接登录"}
            else:
                return {"success": False, "error": "连接失败，请重新连接登录"}

async def get_account_name(page, platform: str):
    """获取当前登录账号的名称"""
    try:
        if platform in ["douyin_shop", "douyin"]:
            # 抖音小店账号名称选择器（更新为更准确的选择器）
            selectors = [
                '[class^="index_userName"]',
                '.header-user-info .name',
                '[class^="header"] .name',
                '.user-info .name',
                '[class^="username"]'
            ]
        elif platform == "buyin":
            # 巨量百应账号名称选择器
            selectors = [
                '[class^="header"] .name',
                '.user-name',
                '.account-name'
            ]
        elif platform == "douyin_group":
            # 抖音团购账号名称选择器
            selectors = [
                '[class^="head-container"] .name',
                '.user-info .name'
            ]
        elif platform == "xhs":
            # 小红书账号名称选择器
            selectors = [
                '.app-root-topbar-wrapper .name',
                '.user-name',
                '.account-info .name'
            ]
        elif platform in ["wxvideo", "wxchannel"]:
            # 微信视频号账号名称选择器
            selectors = [
                '.account-info .name',
                '.user-name'
            ]
        elif platform == "kuaishou":
            # 快手账号名称选择器
            selectors = [
                '[class^=nickname] [class^=name]',
                '.user-name'
            ]
        elif platform == "taobao":
            # 淘宝账号名称选择器
            selectors = [
                'span[class^=header-anchor-name]',
                '.user-name'
            ]
        else:
            return "未知账号"

        # 尝试获取账号名称
        for selector in selectors:
            try:
                element = await page.wait_for_selector(selector, timeout=3000)
                if element:
                    name = await element.text_content()
                    if name and name.strip():
                        return name.strip()
            except:
                continue

        return "已登录账号"
    except Exception as e:
        print(f"获取账号名称失败: {e}")
        return "已登录账号"

from fastapi import Query
# 账号管理API已删除 - 系统简化

@app.post("/api/automation/connect")
async def api_connect_console(params: AutoMessageParams):
    print(f"调用了 connect_to_console，平台: {params.platform}", flush=True)
    if is_connected:
        return {"status": "already_connected", "message": "已经连接到控制台"}

    try:
        result = await connect_to_console(params.platform)
        if result.get("success"):
            return {"status": "connected", "message": "连接成功", "account_name": result.get("account_name")}
        else:
            error_msg = result.get("error", "连接失败")
            return {"status": "failed", "message": error_msg}
    except Exception as e:
        error_msg = str(e)
        print(f"连接控制台异常: {e}", flush=True)

        # 根据异常类型返回友好的错误消息
        if "browser has been closed" in error_msg.lower() or "target closed" in error_msg.lower():
            return {"status": "failed", "message": "连接失败，请重新连接登录"}
        elif "timeout" in error_msg.lower():
            return {"status": "failed", "message": "连接超时，请重新连接登录"}
        elif "network" in error_msg.lower() or "net::" in error_msg.lower():
            return {"status": "failed", "message": "网络连接失败，请检查网络后重新连接登录"}
        else:
            return {"status": "failed", "message": "连接失败，请重新连接登录"}

@app.post("/api/automation/disconnect")
async def api_disconnect_console():
    await disconnect_console()
    return {"status": "disconnected"}

@app.get("/api/automation/status")
def api_status():
    return {
        "is_connected": is_connected,
        "auto_message_running": auto_message_running,
        "auto_popup_running": auto_popup_running
    }

# 全局自动化参数，支持运行中动态切换
automation_params = {
    "random_popup": False,
    # 可扩展更多参数，如 random_send、insert_random_space 等
}

from fastapi import Request

@app.post("/api/automation/params/update")
async def update_automation_params(params: dict):
    automation_params.update(params)
    return {"status": "ok", "params": automation_params}

class BatchMessageParams(BaseModel):
    platform: str
    account_id: str = "default"
    messages: list
    count: int = 5
    interval: float = 0.5  # 每条评论间隔秒数，防风控
    anti_block: bool = False
    random: bool = False

@app.post("/api/automation/auto-message/batch")
async def batch_send_messages(params: BatchMessageParams):
    global playwright, playwright_browser, playwright_page
    import asyncio
    import random
    if playwright_page is None:
        return {"status": "error", "msg": "未连接控制台，无法发送评论"}
    page = playwright_page
    platform = params.platform
    messages = params.messages
    count = params.count
    interval = params.interval
    anti_block = getattr(params, 'anti_block', False)
    use_random = getattr(params, 'random', False)
    for i in range(count):
        if use_random:
            msg = random.choice(messages)
        else:
            msg = messages[i % len(messages)]
        if anti_block:
            msg_to_send = insert_random_spaces(msg)
        else:
            msg_to_send = msg
        try:
            # 这里只做简单示例，实际应按平台适配评论输入框和发送按钮
            if platform in ("redbook", "xhs"):
                textarea = await page.query_selector('.comment-input textarea')
                submit_btn = await page.query_selector('.comment-input button')
                if textarea and submit_btn:
                    await textarea.fill(msg_to_send)
                    await submit_btn.click()
                    print(f"[一键刷屏] 第{i+1}条，评论内容: {msg_to_send}")
                else:
                    print(f"[一键刷屏] 未找到评论输入框或按钮")
            else:
                # 其它平台可扩展
                print(f"[一键刷屏] 暂未适配平台: {platform}")
        except Exception as e:
            print(f"[一键刷屏] 评论异常: {e}")
        await asyncio.sleep(interval)
    return {"status": "ok", "msg": f"已批量发送 {count} 条评论"}

from fastapi import WebSocket, WebSocketDisconnect
import asyncio

@app.websocket("/ws/automation/comments")
async def comments_websocket(websocket: WebSocket):
    await websocket.accept()
    try:
        last_comments = set()
        while True:
            comments = []
            platform = None
            try:
                if playwright_page is not None:
                    # 平台自动识别
                    url = playwright_page.url if hasattr(playwright_page, 'url') else ''
                    if 'xiaohongshu' in url or 'xhs' in url:
                        platform = 'xhs'
                    elif 'douyin' in url or 'jinritemai' in url:
                        platform = 'douyin'
                    elif 'kuaishou' in url:
                        platform = 'kuaishou'
                    elif 'taobao' in url:
                        platform = 'taobao'
                    elif 'weixin.qq.com' in url or 'wxvideo' in url or 'wxchannel' in url:
                        platform = 'wxvideo'
                    # 各平台评论区抓取
                    if platform == 'xhs':
                        comment_items = await playwright_page.query_selector_all('div.comment-list-item')
                        for item in comment_items:
                            spans = await item.query_selector_all('span')
                            all_text = ' '.join([await s.inner_text() for s in spans]).strip()
                            import re
                            m = re.match(r'^(.*?)[：:：]{1,2}\s*(.*)$', all_text)
                            if m:
                                user_name = m.group(1).strip()
                                text = m.group(2).strip()
                            else:
                                if len(spans) >= 2:
                                    user_name = (await spans[0].inner_text()).rstrip(':：').strip()
                                    text = (await spans[1].inner_text()).strip()
                                elif len(spans) == 1:
                                    user_name = (await spans[0].inner_text()).strip()
                                    text = ''
                                else:
                                    user_name = ''
                                    text = all_text
                            comments.append({'user': user_name, 'text': text})
                    elif platform == 'douyin':
                        comment_items = await playwright_page.query_selector_all('.webcast-chatroom__message')
                        for item in comment_items:
                            user = await item.query_selector('.webcast-chatroom__user-name')
                            content = await item.query_selector('.webcast-chatroom__content')
                            if user and content:
                                user_name = await user.inner_text()
                                text = await content.inner_text()
                                comments.append({'user': user_name, 'text': text})
                    elif platform == 'kuaishou':
                        comment_items = await playwright_page.query_selector_all('.chat-message-list .message-item')
                        for item in comment_items:
                            user = await item.query_selector('.user-name, .nickname')
                            content = await item.query_selector('.message-content, .content')
                            if user and content:
                                user_name = await user.inner_text()
                                text = await content.inner_text()
                                comments.append({'user': user_name, 'text': text})
                    elif platform == 'taobao':
                        comment_items = await playwright_page.query_selector_all('.comment-list .comment-item')
                        for item in comment_items:
                            user = await item.query_selector('.user-name, .nickname')
                            content = await item.query_selector('.comment-content, .content')
                            if user and content:
                                user_name = await user.inner_text()
                                text = await content.inner_text()
                                comments.append({'user': user_name, 'text': text})
                    elif platform == 'wxvideo':
                        comment_items = await playwright_page.query_selector_all('.live-message-list .live-message-item')
                        for item in comment_items:
                            user = await item.query_selector('.user-name, .nickname')
                            content = await item.query_selector('.live-message-content, .content')
                            if user and content:
                                user_name = await user.inner_text()
                                text = await content.inner_text()
                                comments.append({'user': user_name, 'text': text})
            except Exception as e:
                print(f"抓取评论异常: {e}")
            # 只推送新评论
            new_comments = [c for c in comments if (c['user'], c['text']) not in last_comments]
            if new_comments:
                await websocket.send_json({'comments': new_comments})
                last_comments.update((c['user'], c['text']) for c in new_comments)
            await asyncio.sleep(1)
    except WebSocketDisconnect:
        print("评论监听 WebSocket 断开")
    except Exception as e:
        print(f"评论监听异常: {e}")
