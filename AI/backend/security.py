from passlib.context import CryptContext
from datetime import datetime, timedelta
from typing import Union, Any
from jose import jwt, JWTError
from fastapi.security import OAuth2PasswordBearer
from fastapi import Depends, HTTPException, status

# 密码哈希上下文，使用 bcrypt 算法
PWD_CONTEXT = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT 认证密钥和算法（请在生产环境中从环境变量或安全配置中获取）
SECRET_KEY = "your-secret-key"  # 替换为复杂的随机字符串
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 9999  # 访问令牌的过期时间（分钟）

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    验证明文密码是否与哈希密码匹配。
    @param plain_password: 明文密码
    @param hashed_password: 哈希密码
    @return: 如果匹配则为 True，否则为 False
    """
    return PWD_CONTEXT.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """
    对密码进行哈希处理。
    @param password: 明文密码
    @return: 哈希后的密码字符串
    """
    return PWD_CONTEXT.hash(password)


def create_access_token(
    data: dict, expires_delta: Union[timedelta, None] = None
) -> str:
    """
    创建 JWT 访问令牌。
    @param data: 包含用户信息的字典（如 username）
    @param expires_delta: 令牌的有效期，默认为 30 分钟
    @return: JWT 访问令牌字符串
    """
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=30)  # 默认 30 分钟
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def decode_access_token(token: str) -> Union[dict, None]:
    """
    解码 JWT 访问令牌并返回其有效负载。
    @param token: JWT 访问令牌字符串
    @return: 解码后的有效负载字典，如果验证失败则为 None
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except JWTError:
        return None 