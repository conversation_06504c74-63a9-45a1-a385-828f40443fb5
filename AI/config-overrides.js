import NodePolyfillPlugin from 'node-polyfill-webpack-plugin';
import webpack from 'webpack';

/**
 * 重写webpack配置，添加Node.js Polyfill
 * @param {Object} config - 原始webpack配置
 * @returns {Object} 修改后的webpack配置
 */
export default function override(config, env) {
  // 添加Node.js polyfill插件
  config.plugins.push(new NodePolyfillPlugin({
    excludeAliases: ['console']
  }));
  
  // 添加各种Node.js核心模块的fallback配置
  if (!config.resolve) config.resolve = {};
  if (!config.resolve.fallback) config.resolve.fallback = {};
  
  Object.assign(config.resolve.fallback, {
    crypto: 'crypto-browserify',
    stream: 'stream-browserify',
    // 直接使用buffer包，避免通过node-stdlib-browser间接引用
    buffer: 'buffer/',
    util: 'util',
    fs: false,
    path: false,
    os: false,
    zlib: 'browserify-zlib',
    http: false,
    https: false,
    net: false
  });

  // 为buffer提供全局变量
  if (!config.plugins) config.plugins = [];
  config.plugins.push(
    new webpack.ProvidePlugin({
      Buffer: ['buffer', 'Buffer'],
    })
  );

  // 处理node:前缀
  config.module.rules.push({
    test: /\.(js|mjs|jsx|ts|tsx)$/,
    loader: 'babel-loader',
    include: /node_modules/,
    options: {
      presets: ['@babel/preset-env'],
      plugins: [
        function() {
          return {
            visitor: {
              ImportDeclaration(path) {
                if (path.node.source.value.startsWith('node:')) {
                  path.node.source.value = path.node.source.value.slice(5);
                }
              }
            }
          };
        }
      ]
    }
  });
  
  return config;
}; 