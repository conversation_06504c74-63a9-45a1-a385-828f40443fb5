@echo off
echo 正在安装打包依赖...
pip install pyinstaller

echo 正在打包launcher_preview.py（高级模式）...
pyinstaller ^
    --onefile ^
    --windowed ^
    --icon=app.ico ^
    --name="AI语音直播系统启动器" ^
    --add-data "app.ico;." ^
    --add-data "logo.png;." ^
    --hidden-import=PyQt5.QtCore ^
    --hidden-import=PyQt5.QtGui ^
    --hidden-import=PyQt5.QtWidgets ^
    --hidden-import=psutil ^
    --hidden-import=threading ^
    --hidden-import=subprocess ^
    --hidden-import=signal ^
    --hidden-import=os ^
    --hidden-import=sys ^
    --collect-all=PyQt5 ^
    --optimize=2 ^
    launcher_preview.py

echo 打包完成！可执行文件在 dist 目录中
pause 