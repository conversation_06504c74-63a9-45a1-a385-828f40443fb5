import sys
from cx_Freeze import setup, Executable

# 依赖包
build_exe_options = {
    "packages": [
        "PyQt5", 
        "psutil", 
        "threading", 
        "subprocess", 
        "signal", 
        "os", 
        "sys",
        "ctypes"
    ],
    "excludes": [],
    "include_files": [
        ("app.ico", "app.ico"),
        ("logo.png", "logo.png"),
    ],
    "optimize": 2,
}

# 目标可执行文件
base = None
if sys.platform == "win32":
    base = "Win32GUI"

executables = [
    Executable(
        "launcher_preview.py",
        base=base,
        target_name="AI语音直播系统启动器.exe",
        icon="app.ico",
    )
]

setup(
    name="AI语音直播系统启动器",
    version="1.0",
    description="AI语音直播系统启动器",
    options={"build_exe": build_exe_options},
    executables=executables,
) 