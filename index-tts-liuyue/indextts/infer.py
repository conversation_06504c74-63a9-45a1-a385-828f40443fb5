import os
import re
import sys
import time
import pickle
from pathlib import Path

import sentencepiece as spm
import torch
import torchaudio
from omegaconf import OmegaConf

from indextts.BigVGAN.models import BigVGAN as Generator
from indextts.gpt.model import UnifiedVoice
from indextts.utils.checkpoint import load_checkpoint
from indextts.utils.feature_extractors import MelSpectrogramFeatures
from indextts.utils.common import tokenize_by_CJK_char
from indextts.vqvae.xtts_dvae import DiscreteVAE

from indextts.utils.front import TextNormalizer

from torch.cuda.amp import autocast
# 通用加速参数建议
torch.set_float32_matmul_precision('high')  # 在文件开头添加
torch.backends.cuda.enable_flash_sdp(True)  # 启用FlashAttention
torch.backends.cuda.enable_mem_efficient_sdp(True)  # 内存优化

def get_file_extension(filename):
    """
    使用 os.path.splitext 获取文件后缀名，避免将多个后缀名组合在一起。

    Args:
        filename: 文件名（字符串）。

    Returns:
        文件的后缀名（字符串），如果没有后缀名则返回空字符串。
    """
    base, ext = os.path.splitext(filename)
    return ext[1:]  # 去掉 '.'

class CachedTextNormalizer:
    """带缓存的文本正则化器"""
    def __init__(self, cache_dir=None):
        self.normalizer = None
        self.cache_dir = cache_dir or os.environ.get('WETEXT_CACHE_DIR', 'cache')
        self.cache_file = os.path.join(self.cache_dir, 'text_normalizer.pkl')
        Path(self.cache_dir).mkdir(parents=True, exist_ok=True)
        print(f">> 缓存目录设置为: {os.path.abspath(self.cache_dir)}")
        print(f">> 缓存文件路径: {os.path.abspath(self.cache_file)}")
        
    def load(self):
        """加载正则化器，优先从缓存加载"""
        if os.path.exists(self.cache_file):
            print(f">> 发现缓存文件: {self.cache_file}")
            try:
                with open(self.cache_file, 'rb') as f:
                    self.normalizer = pickle.load(f)
                print(">> TextNormalizer从缓存加载完成！")
                return
            except Exception as e:
                print(f">> 缓存加载失败: {e}，将重新构建FST模型...")
        else:
            print(f">> 未找到缓存文件: {self.cache_file}")
        
        print(">> 首次运行，构建FST模型（这可能需要几分钟）...")
        self.normalizer = TextNormalizer()
        self.normalizer.load()
        
        print(">> 保存FST模型到缓存...")
        try:
            os.makedirs(os.path.dirname(self.cache_file), exist_ok=True)
            with open(self.cache_file, 'wb') as f:
                pickle.dump(self.normalizer, f)
            print(f">> FST模型缓存保存成功！保存位置: {self.cache_file}")
        except Exception as e:
            print(f">> 警告：缓存保存失败: {e}")
            print(f">> 当前工作目录: {os.getcwd()}")
            print(f">> 缓存目录是否存在: {os.path.exists(self.cache_dir)}")
            print(f">> 缓存目录权限: {os.access(self.cache_dir, os.W_OK) if os.path.exists(self.cache_dir) else 'N/A'}")
    
    def infer(self, text):
        """执行文本正则化"""
        if self.normalizer is None:
            self.load()
        return self.normalizer.infer(text)

class IndexTTS:
    def __init__(self, cfg_path='checkpoints/config.yaml', model_dir='checkpoints'):
        self.cfg = OmegaConf.load(cfg_path)
        self.device = 'cuda:0'
        self.model_dir = model_dir
        self.dtype = torch.float16
        
        print(">> 正在初始化模型...")
        
        # 初始化GPT模型
        print(">> 加载GPT模型...")
        self.gpt = UnifiedVoice(**self.cfg.gpt)
        self.gpt_path = os.path.join(self.model_dir, self.cfg.gpt_checkpoint)
        load_checkpoint(self.gpt, self.gpt_path)
        self.gpt = self.gpt.to(self.device)
        self.gpt.eval().half()
        print(">> GPT模型加载完成！")
        self.gpt.post_init_gpt2_config(use_deepspeed=False, kv_cache=True, half=True)

        # 初始化BigVGAN
        print(">> 加载BigVGAN模型...")
        self.bigvgan = Generator(self.cfg.bigvgan)
        self.bigvgan_path = os.path.join(self.model_dir, self.cfg.bigvgan_checkpoint)
        vocoder_dict = torch.load(self.bigvgan_path, map_location='cpu')
        self.bigvgan.load_state_dict(vocoder_dict['generator'])
        self.bigvgan = self.bigvgan.to(self.device)
        self.bigvgan.eval()
        print(">> BigVGAN模型加载完成！")

        # 初始化文本正则化器（使用缓存版本）
        self.normalizer = CachedTextNormalizer()
        
        # 加载分词器
        print(">> 加载分词器...")
        self.tokenizer = spm.SentencePieceProcessor()
        self.tokenizer.load(os.path.join(self.model_dir,self.cfg.dataset['bpe_model']))
        print(">> 分词器加载完成！")
        
        print(">> 所有模型初始化完成！")

    def preprocess_text(self, text):
        # chinese_punctuation = "，。！？；：""''()[]<>"
        # english_punctuation = ",.!?;:\"\"''()[]<>"
        #
        # # 创建一个映射字典
        # punctuation_map = str.maketrans(chinese_punctuation, english_punctuation)

        # 使用translate方法替换标点符号
        # return text.translate(punctuation_map)
        return self.normalizer.infer(text)


    def split_long_sentence(self,sentence, max_length=30):
        punctuation = ["!", "?", ".", ";", "！", "？", "。", "；", ",", "，", "、", ":", "："]
        parts = []
        start = 0
        n = len(sentence)
        
        while start < n:
            end = start + max_length
            last_punct = -1
            
            # 在当前窗口内查找最后一个标点
            for i in range(min(end, n)-1, start-1, -1):
                if sentence[i] in punctuation:
                    last_punct = i
                    break
            
            # 如果未找到则扩展窗口查找
            if last_punct == -1:
                extended_end = min(start + max_length + 5, n)
                for i in range(extended_end-1, start-1, -1):
                    if sentence[i] in punctuation:
                        last_punct = i
                        break
            
            # 处理找到的标点或整段文本
            if last_punct != -1:
                parts.append(sentence[start:last_punct+1])
                start = last_punct + 1
            else:
                parts.append(sentence[start:])
                break
        
        return parts


    def infer(self, audio_prompt, text, output_path,pt=""):
        time1=time.time()
        print(f"origin text:{text}")
        text = self.preprocess_text(text)
        print(f"normalized text:{text}")

        if get_file_extension(audio_prompt) in ["WAV","wav","mp3","MP3"]:
            audio, sr = torchaudio.load(audio_prompt)
            audio = torch.mean(audio, dim=0, keepdim=True)
            if audio.shape[0] > 1:
                audio = audio[0].unsqueeze(0)
            audio = torchaudio.transforms.Resample(sr, 24000)(audio)
            torch.save(audio,'output.pt')
        else:
            audio = torch.load(f"voices/{audio_prompt}")

        cond_mel = MelSpectrogramFeatures()(audio).to(self.device)
        print(f"cond_mel shape: {cond_mel.shape}")

        # audio, sr = torchaudio.load(audio_prompt)
        # audio = torch.mean(audio, dim=0, keepdim=True)
        # if audio.shape[0] > 1:
        #     audio = audio[0].unsqueeze(0)
        # audio = torchaudio.transforms.Resample(sr, 24000)(audio)
        # cond_mel = MelSpectrogramFeatures()(audio).to(self.device)
        # print(f"cond_mel shape: {cond_mel.shape}")
       
        # data_dict = {"cond_mel":cond_mel}
        # torch.save(data_dict, 'output.pt')

        # data_dict = torch.load('output.pt')
        # cond_mel = data_dict["cond_mel"]
       

        auto_conditioning = cond_mel

        # self.tokenizer = spm.SentencePieceProcessor()
        # self.tokenizer.load(os.path.join(self.model_dir,self.cfg.dataset['bpe_model']))

        punctuation = ["!", "?", ".", ";", "！", "？", "。", "；"]
        pattern = r"(?<=[{0}])\s*".format("".join(punctuation))
        sentences = [i for i in re.split(pattern, text) if i.strip() != ""]
        print(sentences)
        # 处理长句子
        # 阶段2：超长句优化分割‌:ml-citation{ref="3,5" data="citationList"}
        # 二次处理长句子
        final_sentences = []
        for sent in sentences:
            if len(sent) <= 30:
                final_sentences.append(sent)
            else:
                final_sentences.extend(self.split_long_sentence(sent))
            

        print(final_sentences)

        top_p = .8
        top_k = 30
        temperature = 1.0
        autoregressive_batch_size = 1
        length_penalty = 0.0
        num_beams = 1
        repetition_penalty = 10.0
        max_mel_tokens = 600
        sampling_rate = 24000
        lang = "EN"
        lang = "ZH"
        wavs = []
        wavs1 = []

        for sent in final_sentences:
            print(sent)
            # sent = " ".join([char for char in sent.upper()]) if lang == "ZH" else sent.upper()
            cleand_text = tokenize_by_CJK_char(sent)
            # cleand_text = "他 那 像 HONG3 小 孩 似 的 话 , 引 得 人 们 HONG1 堂 大 笑 , 大 家 听 了 一 HONG3 而 散 ."
            print(cleand_text)
            text_tokens = torch.IntTensor(self.tokenizer.encode(cleand_text)).unsqueeze(0).to(self.device)

            # text_tokens = F.pad(text_tokens, (0, 1))  # This may not be necessary.
            # text_tokens = F.pad(text_tokens, (1, 0), value=0)
            # text_tokens = F.pad(text_tokens, (0, 1), value=1)
            text_tokens = text_tokens.to(self.device)
            print(text_tokens)
            print(f"text_tokens shape: {text_tokens.shape}")
            text_token_syms = [self.tokenizer.IdToPiece(idx) for idx in text_tokens[0].tolist()]
            print(text_token_syms)
            text_len = [text_tokens.size(1)]
            text_len = torch.IntTensor(text_len).to(self.device)
            print(text_len)
            
            print(f'开始')

            with torch.no_grad():
                with torch.amp.autocast(self.device, enabled=self.dtype is not None, dtype=self.dtype):
                    codes = self.gpt.inference_speech(auto_conditioning, text_tokens,
                                                    cond_mel_lengths=torch.tensor([auto_conditioning.shape[-1]],
                                                                                    device=text_tokens.device),
                                                    # text_lengths=text_len,
                                                    do_sample=True,
                                                    top_p=top_p,
                                                    top_k=top_k,
                                                    temperature=temperature,
                                                    num_return_sequences=autoregressive_batch_size,
                                                    length_penalty=length_penalty,
                                                    num_beams=num_beams,
                                                    repetition_penalty=repetition_penalty,
                                                    max_generate_length=max_mel_tokens)
                print(codes)
                print(f"codes shape: {codes.shape}")
                

                codes = codes[:, :-2]

                # latent, text_lens_out, code_lens_out = \
                print(1111)
                with torch.amp.autocast(self.device, enabled=self.dtype is not None, dtype=self.dtype):
                    latent = \
                        self.gpt(auto_conditioning, text_tokens,
                                torch.tensor([text_tokens.shape[-1]], device=text_tokens.device), codes,
                                torch.tensor([codes.shape[-1] * self.gpt.mel_length_compression], device=text_tokens.device),
                                cond_mel_lengths=torch.tensor([auto_conditioning.shape[-1]], device=text_tokens.device),
                                return_latent=True, clip_inputs=False)
                latent = latent.transpose(1, 2)


                
                with autocast():
                    wav, _ = self.bigvgan(latent.transpose(1, 2), auto_conditioning.transpose(1, 2))
                wav = wav.squeeze(1).cpu()

                wav = 32767 * wav
                torch.clip(wav, -32767.0, 32767.0)
                print(f"wav shape: {wav.shape}")
                # wavs.append(wav[:, :-512])
                wavs.append(wav)
                

        wav = torch.cat(wavs, dim=1)
        #wav=wav*2
        torchaudio.save(output_path, wav.type(torch.int16), 24000)
        torch.cuda.empty_cache()
        print(f'用时{time.time()-time1}')



    #初级方案 时间节省一半
    def infer_xd(self, audio_prompt, text, output_path,pt=""):

        
        time1=time.time()
        
        print(f"origin text:{text}")
        text = self.preprocess_text(text)
        print(f"normalized text:{text}")

        if pt=="":

            audio, sr = torchaudio.load(audio_prompt)
            audio = torch.mean(audio, dim=0, keepdim=True)
            if audio.shape[0] > 1:
                audio = audio[0].unsqueeze(0)
            audio = torchaudio.transforms.Resample(sr, 24000)(audio)
            cond_mel = MelSpectrogramFeatures()(audio).to(self.device)
            print(f"cond_mel shape: {cond_mel.shape}")



        
            data_dict = {"cond_mel":cond_mel}
            torch.save(data_dict, 'output.pt')
        else:
            data_dict = torch.load(f"voices/{pt}.pt")
            cond_mel = data_dict["cond_mel"]


        
       





        auto_conditioning = cond_mel


        punctuation = ["!", "?", ".", ";", "！", "？", "。", "；"]
        pattern = r"(?<=[{0}])\s*".format("".join(punctuation))
        sentences = [i for i in re.split(pattern, text) if i.strip() != ""]
        print(sentences)
        # 处理长句子
        # 阶段2：超长句优化分割‌:ml-citation{ref="3,5" data="citationList"}
        # 二次处理长句子
        final_sentences = []
        for sent in sentences:
            if len(sent) <= 30:
                final_sentences.append(sent)
            else:
                final_sentences.extend(self.split_long_sentence(sent))
            

        print(final_sentences)

        top_p = .8
        top_k = 30
        temperature = 1.0
        autoregressive_batch_size = 1
        length_penalty = 0.0
        num_beams = 1
        repetition_penalty = 10.0
        max_mel_tokens = 400
        sampling_rate = 24000
        lang = "EN"
        lang = "ZH"
        wavs = []
        wavs1 = []

        for sent in final_sentences:
            print(sent)
            # sent = " ".join([char for char in sent.upper()]) if lang == "ZH" else sent.upper()
            cleand_text = tokenize_by_CJK_char(sent)
            # cleand_text = "他 那 像 HONG3 小 孩 似 的 话 , 引 得 人 们 HONG1 堂 大 笑 , 大 家 听 了 一 HONG3 而 散 ."
            print(cleand_text)
            text_tokens = torch.IntTensor(self.tokenizer.encode(cleand_text)).unsqueeze(0).to(self.device)

            # text_tokens = F.pad(text_tokens, (0, 1))  # This may not be necessary.
            # text_tokens = F.pad(text_tokens, (1, 0), value=0)
            # text_tokens = F.pad(text_tokens, (0, 1), value=1)
            text_tokens = text_tokens.to(self.device)
            print(text_tokens)
            print(f"text_tokens shape: {text_tokens.shape}")
            text_token_syms = [self.tokenizer.IdToPiece(idx) for idx in text_tokens[0].tolist()]
            print(text_token_syms)
            text_len = [text_tokens.size(1)]
            text_len = torch.IntTensor(text_len).to(self.device)
            print(text_len)
            
            print(f'开始')

            with torch.no_grad():
                with torch.amp.autocast(self.device, enabled=self.dtype is not None, dtype=self.dtype):
                    # 启用FlashAttention优化
                    with torch.backends.cuda.sdp_kernel(enable_flash=True): 
                        codes = self.gpt.inference_speech(auto_conditioning, text_tokens,
                                                        cond_mel_lengths=torch.tensor([auto_conditioning.shape[-1]],
                                                                                        device=text_tokens.device),
                                                        # text_lengths=text_len,
                                                        do_sample=True,
                                                        top_p=top_p,
                                                        top_k=top_k,
                                                        temperature=temperature,
                                                        num_return_sequences=autoregressive_batch_size,
                                                        length_penalty=length_penalty,
                                                        num_beams=num_beams,
                                                        repetition_penalty=repetition_penalty,
                                                        max_generate_length=max_mel_tokens)
                print(codes)
                print(f"codes shape: {codes.shape}")
                

                codes = codes[:, :-2]

                # latent, text_lens_out, code_lens_out = \
                print(1111)
                with torch.amp.autocast(self.device, enabled=self.dtype is not None, dtype=self.dtype):
                    latent = \
                        self.gpt(auto_conditioning, text_tokens,
                                torch.tensor([text_tokens.shape[-1]], device=text_tokens.device), codes,
                                torch.tensor([codes.shape[-1] * self.gpt.mel_length_compression], device=text_tokens.device),
                                cond_mel_lengths=torch.tensor([auto_conditioning.shape[-1]], device=text_tokens.device),
                                return_latent=True, clip_inputs=False)
                latent = latent.transpose(1, 2)


                

                wav, _ = self.bigvgan(latent.transpose(1, 2), auto_conditioning.transpose(1, 2))
                wav = wav.squeeze(1).cpu()

                wav = 32767 * wav
                torch.clip(wav, -32767.0, 32767.0)
                print(f"wav shape: {wav.shape}")
                # wavs.append(wav[:, :-512])
                wavs.append(wav)

        wav = torch.cat(wavs, dim=1)
        torchaudio.save(output_path, wav.type(torch.int16), 24000)
        print(f'用时{time.time()-time1}')
        torch.cuda.empty_cache()
        

    # 生成pt文件
    def infer_sub(self, audio_path,file_name):
        file_path=os.path.abspath(__file__)
        root_dir = os.path.dirname(os.path.dirname(file_path))
        file_name=file_name.split(".")[0].strip()

        audio, sr = torchaudio.load(audio_path)
        audio = torch.mean(audio, dim=0, keepdim=True)
        if audio.shape[0] > 1:
            audio = audio[0].unsqueeze(0)
        audio = torchaudio.transforms.Resample(sr, 24000)(audio)

        project_file= root_dir+ f"/voices/{file_name}.pt"
        torch.save(audio, project_file)

if __name__ == "__main__":
    tts = IndexTTS(cfg_path="checkpoints/config.yaml", model_dir="checkpoints")
    tts.infer(audio_prompt='test_data/input.wav', text='大家好，我现在正在bilibili 体验 ai 科技，说实话，来之前我绝对想不到！AI技术已经发展到这样匪夷所思的地步了！', output_path="gen.wav")
