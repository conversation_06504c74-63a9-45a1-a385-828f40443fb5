import os
import sys
import threading
import logging

# 禁用所有日志输出
logging.getLogger('werkzeug').disabled = True
os.environ['WERKZEUG_RUN_MAIN'] = 'true'
os.environ['WERKZEUG_QUIET'] = 'true'

# 在导入任何其他模块之前禁用浏览器启动
import webbrowser
webbrowser.open = lambda *args, **kwargs: None
webbrowser.open_new = lambda *args, **kwargs: None
webbrowser.open_new_tab = lambda *args, **kwargs: None

# 设置环境变量
os.environ['GRADIO_ANALYTICS_ENABLED'] = 'false'
os.environ['GRADIO_BROWSER_LAUNCH'] = 'false'
os.environ['GRADIO_LAUNCH_BROWSER'] = 'false'
os.environ['GRADIO_QUIET'] = 'true'
os.environ['BROWSER'] = 'none'
os.environ['DISPLAY'] = ''

# 修改gradio的launch函数默认行为
import gradio as gr
# 完全禁用gradio的输出
gr.launch = lambda *args, **kwargs: gr.launch(*args, **kwargs, show_error=False, quiet=True, launch_browser=False)

from cy_app import *

current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def silent_start_service(*args):
    # 重定向标准输出
    original_stdout = sys.stdout
    sys.stdout = open(os.devnull, 'w')
    try:
        start_service(*args)
    finally:
        sys.stdout = original_stdout

def silent_start_gradio(*args):
    # 重定向标准输出
    original_stdout = sys.stdout
    sys.stdout = open(os.devnull, 'w')
    try:
        start_gradio(*args)
    finally:
        sys.stdout = original_stdout

if __name__ == "__main__":
    event = threading.Event()

    # 使用自定义参数启动服务
    fastapi_thread = threading.Thread(target=silent_start_service, args=(event,))
    gradio_thread = threading.Thread(target=silent_start_gradio, args=(event,))

    fastapi_thread.start()
    gradio_thread.start()

    fastapi_thread.join()
    gradio_thread.join()