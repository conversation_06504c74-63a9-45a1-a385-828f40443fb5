# 大模型对接方式说明文档

## 1. 项目概述

本项目实现了多个大模型API的统一接入和管理，支持智谱AI、通义千问、火山引擎和DeepSeek等多个大模型平台，主要用于生成直播话术和内容。

## 2. 技术架构

### 2.1 前端技术栈
- React + TypeScript
- Material-UI (MUI) 组件库
- Axios 用于API请求
- WebSocket 用于实时通信

### 2.2 后端技术栈
- FastAPI 框架
- Python 3.x
- uvicorn 服务器
- 自定义日志系统

## 3. 功能模块

### 3.1 模型管理
- 支持多个AI模型平台切换
- API密钥管理
- 模型版本配置
- 模型状态开关控制

### 3.2 提示词模板管理
- 欢迎语模板
- 报时提示词
- 产品话术
- 自定义模板
- 支持变量替换

### 3.3 话术生成
- 多样化话术生成
- 实时语音合成
- 批量处理支持

## 4. API接口说明

### 4.1 智谱AI（ZhipuAI）API
```python
# 后端API
后端接口地址: /api/zhipu  # 本地后端代理接口
实际接口地址: https://open.bigmodel.cn/api/paas/v4/chat/completions
请求方式: POST
请求参数:
{
    "model": "glm-4",  # 可选: glm-4, glm-3-turbo 等
    "api_key": "your_zhipu_api_key",
    "messages": [
        { "role": "system", "content": "你是一个专业的直播助手，擅长为主播生成多样化的话术表达" },
        { "role": "user", "content": "请基于以下话术，生成3种不同的表达方式，保持原意但使用不同的语言风格：\\n\\n[原文本]" }
    ]
}

# 实际调用格式（通过后端代理）
const response = await backendAPI.post(
    '/api/zhipu',  // 通过本地后端代理
    {
        model: modelToUse,  // 如 "glm-4"
        api_key: apiKey,
        messages: [
            { role: "system", content: "你是一个专业的直播助手，擅长为主播生成多样化的话术表达" },
            { role: "user", content: prompt }
        ]
    }
);
```

#### 智谱AI特别说明
1. 认证方式：
   - 官方API需要在header中携带`Authorization: Bearer <api_key>`
   - 通过后端代理可直接传递`api_key`字段，由后端处理认证

2. 模型选择：
   - glm-4：最新的大规模语言模型，性能最优
   - glm-3-turbo：平衡性能和成本的选择
   
3. 注意事项：
   - 响应格式会被后端统一处理，与其他模型保持一致
   - 建议在系统提示词中明确指定助手角色
   - 支持流式输出，但当前实现为完整输出

### 4.2 通义千问API
```python
# 后端API
后端接口地址: /api/qianwen  # 本地后端代理接口
实际接口地址: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
请求方式: POST
请求参数:
{
    "prompt": "请基于以下话术，生成3种不同的表达方式，保持原意但使用不同的语言风格：\n\n[原文本]",
    "model": "qwen-max",  # 可选: qwen-max, qwen-turbo, qwen-plus
    "api_key": "your_dashscope_api_key",
    "parameters": {
        "temperature": 0.7,
        "top_p": 0.8,
        "result_format": "message"
    }
}

# 实际调用格式（通过后端代理）
const response = await backendAPI.post(
    '/api/qianwen',  # 通过本地后端代理
    {
        prompt: content,
        model: modelToUse,
        api_key: apiKey,
        parameters: {
            temperature: 0.7,
            top_p: 0.8,
            result_format: "message"
        }
    }
);
```

### 4.3 火山引擎API
```python
# 后端API
后端接口地址: /api/volcengine  # 本地后端代理接口
实际接口地址: https://ark.cn-beijing.volces.com/api/v3/chat/completions
请求方式: POST
请求参数:
{
    "prompt": "请基于以下话术，生成3种不同的表达方式，保持原意但使用不同的语言风格：\n\n[原文本]",
    "model": "chatglm-6b-model",  # 默认模型
    "api_key": "your_volcengine_api_key",
    "parameters": {
        "temperature": 0.7,
        "top_p": 0.9,
        "max_tokens": 1024
    }
}

# 实际调用格式（通过后端代理）
const response = await backendAPI.post(
    '/api/volcengine',  # 通过本地后端代理
    {
        prompt: content,
        model: modelToUse,
        api_key: apiKey,
        parameters: {
            temperature: 0.7,
            top_p: 0.9,
            max_tokens: 1024
        }
    }
);
```

### 4.4 DeepSeek API
```python
# 后端API
后端接口地址: /api/deepseek  # 本地后端代理接口
实际接口地址: https://api.deepseek.com/v1/chat/completions
请求方式: POST
请求参数:
{
    "prompt": "请基于以下话术，生成3种不同的表达方式，保持原意但使用不同的语言风格：\n\n[原文本]",
    "model": "deepseek-chat",  # 默认模型
    "api_key": "your_deepseek_api_key",
    "parameters": {
        "temperature": 0.7,
        "top_p": 0.9,
        "max_tokens": 1024
    }
}

# 实际调用格式（通过后端代理）
const response = await backendAPI.post(
    '/api/deepseek',  # 通过本地后端代理
    {
        prompt: content,
        model: modelToUse,
        api_key: apiKey,
        parameters: {
            temperature: 0.7,
            top_p: 0.9,
            max_tokens: 1024
        }
    }
);
```

### 4.5 接口说明
1. 所有API调用都通过本地后端代理（http://localhost:8088）进行，这样做的原因是：
   - 避免跨域问题
   - 统一错误处理
   - 规范化接口格式
   - 保护API密钥安全
   
2. 实际的API调用流程：
   前端 -> 本地后端代理(8088端口) -> 各大模型官方API

3. 注意事项：
   - 确保后端服务正常运行在8088端口
   - API密钥在后端进行验证
   - 所有请求都会经过统一的错误处理
   - 响应格式会被统一化处理

### 4.6 统一响应格式
```python
# 成功响应
{
    "output": {
        "text": "生成的内容"
    }
}

# 错误响应
{
    "error": "错误信息描述"
}
```

### 4.7 前端初始化配置
```typescript
// 创建专用的axios实例
const backendAPI = axios.create({
    baseURL: 'http://localhost:8088',
    timeout: 30000,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
});

// 添加请求拦截器
backendAPI.interceptors.request.use(
    config => {
        console.log('发送请求:', config.url);
        return config;
    },
    error => {
        console.error('请求错误:', error);
        return Promise.reject(error);
    }
);

// 添加响应拦截器
backendAPI.interceptors.response.use(
    response => {
        console.log('收到响应:', response.status, response.data);
        return response;
    },
    error => {
        console.error('响应错误:', error);
        return Promise.reject(error);
    }
);
```

### 4.8 错误处理示例
```typescript
try {
    const response = await backendAPI.post('/api/模型名称', {
        // 请求参数
    });
    
    if (response.data.error) {
        setErrorMessage(response.data.error);
        return null;
    }
    
    if (response.data.output?.text) {
        setErrorMessage(""); // 清除错误信息
        return response.data.output.text;
    }
    
} catch (error: any) {
    if (error.response) {
        const statusCode = error.response.status;
        const errorData = error.response.data;
        
        if (statusCode === 401) {
            setErrorMessage("API Key无效或已过期");
        } else if (errorData?.error) {
            setErrorMessage(`调用失败: ${errorData.error}`);
        } else {
            setErrorMessage(`请求错误 (${statusCode})`);
        }
    } else if (error.request) {
        setErrorMessage("网络请求失败，请检查后端服务");
    } else {
        setErrorMessage(`发生错误: ${error.message}`);
    }
    return null;
}
```

## 5. 使用方式

### 5.1 环境配置
1. 确保后端服务运行在8088端口
2. 前端需要配置正确的后端API地址
3. 准备各平台的API密钥

### 5.2 API密钥配置
- 通义千问: 在阿里云控制台获取DASHSCOPE_API_KEY
- 火山引擎: 在火山引擎控制台获取API密钥
- DeepSeek: 在DeepSeek控制台获取API密钥

### 5.3 模型选择
1. 在界面选择需要使用的模型
2. 输入对应的API密钥
3. 配置模型版本（如需要）
4. 启用AI模型开关

### 5.4 提示词使用
1. 选择或创建合适的提示词模板
2. 可使用变量进行内容替换
3. 支持实时预览和语音播放
4. 可以保存常用模板

## 6. 日志系统

### 6.1 日志格式
```
时间戳
提示语:
[系统指令]

原文本:
[用户输入内容]

AI回复 (模型名称):
[生成的内容]
===============================
```

### 6.2 日志配置
- 日志文件位置: logs/llm_conversation_时间戳.txt
- 日志级别: INFO
- 包含请求和响应详情

## 7. 错误处理

### 7.1 常见错误
- API密钥无效
- 网络连接问题
- 模型响应超时
- 参数格式错误

### 7.2 错误响应
- 详细的错误信息
- 友好的用户提示
- 错误日志记录

## 8. 安全性考虑

### 8.1 API密钥保护
- 密钥加密存储
- 界面密码显示切换
- 会话期间临时保存

### 8.2 请求安全
- CORS策略配置
- 请求频率限制
- 错误重试机制

## 9. 性能优化

### 9.1 前端优化
- 状态管理优化
- 组件按需加载
- 本地存储利用

### 9.2 后端优化
- 异步处理
- 连接池管理
- 缓存机制

## 10. 扩展性

### 10.1 新增模型
1. 后端添加新的路由
2. 实现对应的处理函数
3. 前端添加模型选项
4. 配置相应的参数

### 10.2 功能扩展
- 支持更多模型参数
- 添加新的模板类型
- 扩展提示词变量
- 自定义处理流程 