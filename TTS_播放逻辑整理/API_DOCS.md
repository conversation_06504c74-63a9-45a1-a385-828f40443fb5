# IndexTTS API 使用文档

## 目录
- [服务连接配置](#服务连接配置)
- [API接口说明](#api接口说明)
- [使用示例](#使用示例)
- [创建新音色模型](#创建新音色模型)
- [注意事项](#注意事项)
- [启动流程](#启动流程)

## 服务连接配置

### 基础配置
```python
from gradio_client import Client

# 创建Gradio客户端实例
client = Client("http://127.0.0.1:7860/")
```

### 服务状态检查
```python
def check_service_status():
    try:
        client = Client("http://127.0.0.1:7860/")
        response = client.predict(api_name="/change_choices")
        return bool(response)
    except Exception as e:
        return False
```

## API接口说明

### 1. 获取可用模型列表
```python
# API名称: /change_choices
# 方法: GET
# 参数: 无
# 返回: 模型列表
response = client.predict(api_name="/change_choices")
# 返回值示例: ['使用参考音源', 'jok老师.pt', '叶亲法.pt', '好听女.pt', '步非烟.pt']
```

### 2. 更新提示音频
```python
# API名称: /update_prompt_audio
# 方法: GET
# 参数: 无
# 返回: 更新状态
response = client.predict(api_name="/update_prompt_audio")
```

### 3. 保存音频
```python
# API名称: /save_audio
# 方法: POST
# 参数:
# - name: str (必需) - 音频文件名
# 返回: 保存状态
response = client.predict(
    name="Hello!",
    api_name="/save_audio"
)
```

### 4. 语音推理
```python
# API名称: /infer
# 方法: POST
# 参数:
# - name: str - 模型名称（默认: "使用参考音源"）
# - voice: filepath - 音频文件路径（必需）
# - text: str - 要转换的文本（必需）
# - speed: float - 语速（默认: 1.0）
# 返回: 生成的音频数据
response = client.predict(
    name="使用参考音源",
    voice=handle_file("path/to/audio.wav"),
    text="Hello!",
    speed=1,
    api_name="/infer"
)
```

## 使用示例

### 1. 基础文本转语音
```python
def generate_speech(text: str, model: str, speed: float = 1.0):
    try:
        client = Client("http://127.0.0.1:7860/")
        result = client.predict(
            name=model,
            text=text,
            speed=speed,
            api_name="/infer"
        )
        return result
    except Exception as e:
        print(f"生成语音失败: {str(e)}")
        return None
```

### 2. 获取可用模型
```python
def get_available_models():
    try:
        client = Client("http://127.0.0.1:7860/")
        models = client.predict(api_name="/change_choices")
        return models
    except Exception as e:
        print(f"获取模型列表失败: {str(e)}")
        return []
```

### 3. 分句处理长文本
```python
def split_text_to_sentences(text: str) -> List[str]:
    """将文本分割成句子"""
    pattern = r'([。])'
    sentences = re.split(pattern, text)
    result = []
    
    for i in range(0, len(sentences)-1, 2):
        if sentences[i]:
            sentence = sentences[i].strip()
            if i+1 < len(sentences):
                sentence += sentences[i+1].strip()
            if sentence:
                result.append(sentence)
    
    if len(sentences) % 2 == 1 and sentences[-1].strip():
        result.append(sentences[-1].strip())
    
    return result
```

## 创建新音色模型

### 1. 基础创建流程
```python
from gradio_client import Client, handle_file
import os
import time

def create_new_voice_model(audio_path: str, model_name: str, test_text: str = "你好，我是测试音色。"):
    """
    创建新的声音模型
    :param audio_path: 音频文件路径（wav格式）
    :param model_name: 新模型名称
    :param test_text: 测试文本
    """
    try:
        # 创建Gradio客户端
        client = Client("http://localhost:7860/")
        
        # 检查音频文件
        if not os.path.exists(audio_path):
            print(f"错误：音频文件 {audio_path} 不存在")
            return False
            
        if not audio_path.lower().endswith('.wav'):
            print("错误：音频文件必须是WAV格式")
            return False
            
        # 获取当前模型列表
        current_models = client.predict(api_name="/change_choices")
        print(f"当前可用模型：{current_models}")
        
        # 处理音频文件
        processed_audio = handle_file(audio_path)
        
        # 更新提示音频
        update_result = client.predict(api_name="/update_prompt_audio")
        time.sleep(2)
        
        # 进行推理
        try:
            result = client.predict(
                name="使用参考音频",
                voice=processed_audio,
                text=test_text,
                speed=1.0,
                api_name="/infer"
            )
        except Exception as e:
            print(f"推理过程中出现错误（这是正常的）：{str(e)}")
        
        # 等待模型处理
        time.sleep(5)
        
        # 保存为新模型
        save_result = client.predict(
            name=model_name,
            api_name="/save_audio"
        )
        
        # 等待保存完成
        time.sleep(5)
        
        # 验证模型是否创建成功
        new_models = client.predict(api_name="/change_choices")
        model_file = f"{model_name}.pt"
        if any(model_file in choice[0] for choice in new_models.get('choices', [])):
            print("模型创建成功！")
            return True
        else:
            print("模型创建失败")
            return False
            
    except Exception as e:
        print(f"创建模型时发生错误：{str(e)}")
        return False
```

### 2. 使用示例
```python
# 创建新音色模型
audio_file = r"C:\Users\<USER>\Desktop\女装嗲.WAV"
model_name = "女装嗲"
test_text = "你好，我是女装嗲。"

success = create_new_voice_model(audio_file, model_name, test_text)
if success:
    print("新音色模型创建成功！")
else:
    print("新音色模型创建失败")
```

### 3. 注意事项
- 音频文件必须是WAV格式
- 音频文件时长建议在10秒左右
- 音频质量要好，背景噪音要小
- 模型名称不能包含特殊字符
- 创建过程中需要等待足够的时间让模型处理完成

### 4. 常见问题
- 如果模型创建失败，检查音频文件格式是否正确
- 如果推理过程出错，这是正常的，只要最终模型创建成功即可
- 如果模型列表中没有新模型，可以尝试重新运行创建流程

## 注意事项

### 1. 服务连接
- IndexTTS服务默认运行在`http://127.0.0.1:7860/`
- 确保在使用API前服务已经正常启动
- 建议实现重试机制处理连接异常

### 2. 参数说明
- 语速(speed)范围：建议在0.5-2.0之间
- 音频文件格式：支持WAV格式
- 文本长度：建议按句分割处理长文本

### 3. 错误处理
- 建议对每个API调用进行try-catch处理
- 实现日志记录以便追踪问题
- 添加超时处理机制

### 4. 性能优化
- 对于长文本，建议使用分句处理
- 可以实现并发处理提高效率
- 考虑添加缓存机制

## 启动流程

### 1. 启动IndexTTS服务
```bash
# 进入index-tts-liuyue目录
cd index-tts-liuyue
# 运行启动脚本
运行.bat
```

### 2. 启动后端服务
```bash
# 进入backend目录
cd AI/backend
# 运行FastAPI服务
python main.py
```

### 3. 验证服务状态
```python
# 检查服务是否正常运行
status = check_service_status()
print(f"服务状态: {'正常' if status else '异常'}")
```

## 常见问题解决

### 1. 服务连接失败
- 检查IndexTTS服务是否正常运行
- 确认端口7860是否被占用
- 检查网络连接状态

### 2. 音频生成失败
- 检查文本编码是否正确
- 确认模型文件是否存在
- 验证音频文件格式是否支持

### 3. 性能问题
- 考虑使用批处理模式
- 优化文本分割策略
- 实现并发处理机制

## 开发建议

### 1. 代码组织
- 使用模块化设计
- 实现错误处理机制
- 添加详细的日志记录

### 2. 测试策略
- 单元测试覆盖主要功能
- 压力测试验证性能
- 异常测试保证稳定性

### 3. 部署考虑
- 环境依赖管理
- 配置文件管理
- 日志文件管理

## 更新日志

### v1.0.0 (2024-03-19)
- 初始版本
- 基础API文档
- 使用示例
- 常见问题解决方案 