# IndexTTS-FAST AI语音直播系统

## 项目概述

IndexTTS-FAST是一款基于先进文本转语音(TTS)技术的AI直播助手系统，专为直播行业设计。系统支持实时语音合成、模型训练、智能对话等功能，能够让虚拟主播以自然流畅的声音进行直播。本项目采用前后端分离架构，前端基于Next.js+React+TypeScript开发，后端使用FastAPI构建，并集成IndexTTS作为核心语音合成引擎。

## 系统架构

项目采用三层架构设计：
1. **前端**：Next.js + React 构建的Web应用
2. **后端**：FastAPI构建的REST API和WebSocket服务
3. **TTS引擎**：独立运行的IndexTTS服务（基于Gradio）

### 整体架构图
```
用户 <--> 前端应用(Next.js) <--> 后端服务(FastAPI) <--> IndexTTS服务(Gradio)
```

## 项目结构

```
IndexTTS-FAST/
├── AI/                        # 前端与后端代码
│   ├── backend/               # 后端服务
│   │   ├── main.py           # 主要API实现
│   │   ├── douyin_monitor/   # 抖音直播监控模块
│   │   └── requirements.txt  # 后端依赖
│   ├── src/                  # 前端源码
│   │   ├── components/       # 前端组件
│   │   ├── hooks/            # React钩子
│   │   ├── pages/            # 页面组件
│   │   ├── services/         # API服务
│   │   ├── stores/           # 状态管理
│   │   ├── styles/           # 样式文件
│   │   └── utils/            # 工具函数
│   └── package.json          # 前端依赖
├── index-tts-liuyue/         # IndexTTS服务
│   ├── indextts/             # TTS核心代码
│   ├── voices/               # 声音模型目录
│   ├── 运行.bat              # TTS服务启动脚本
│   ├── 启动接口服务.bat      # Web服务启动脚本
│   └── app.py               # Gradio应用入口
└── README.md                # 项目文档
```

## 功能模块

### 1. 语音合成核心模块
- **实时文本转语音**：将输入文本实时转换为自然流畅的语音
- **参数调节**：支持调节语速等参数
- **批量处理**：支持批量处理长文本并生成音频
- **音频下载**：支持将生成的语音下载为WAV格式

### 2. 模型训练与管理模块
- **自定义声音模型**：只需上传10秒WAV格式音频即可训练新的声音模型
- **模型管理**：支持模型的创建、重命名、删除等管理功能
- **模型预览**：提供模型试听功能，快速体验不同声音效果

### 3. 实时直播模块
- **实时语音直播**：将文本内容实时转换为直播语音
- **文字转语音直播**：根据准备好的话术实时生成语音进行直播
- **讲解模式**：支持商品介绍等特定场景的语音输出模式

### 4. AI对话集成模块
- **AI对话**：集成AI模型实现智能对话
- **提示词设置**：配置与AI模型交互的提示词
- **话术模板**：设置各种场景的话术模板

### 5. 监控与管理模块
- **直播监控**：实时监控直播状态和质量
- **语音历史**：记录生成的语音历史
- **系统设置**：提供系统配置和参数设置

## 页面结构

### 主要页面
1. **首页(/)**: 主界面，包含AI直播控制台
   - 文本输入区域
   - 直播控制组件
   - 语音预览与控制

2. **监控页(/monitor)**: 直播监控页面
   - 直播状态监控
   - 系统性能监控
   - 语音生成监控

### 主要组件
- **LiveStream组件**:
  - LiveStreamController: 直播控制组件
  - 负责文本到语音的转换和播放控制

- **AudioPlayer组件**: 
  - 音频播放和控制
  - 提供播放、暂停、速度控制等功能

- **ModelPreview组件**:
  - 模型预览和选择组件
  - 展示可用声音模型并提供试听功能

- **Input组件**:
  - 文本输入和处理组件
  - 提供多行文本输入和处理功能

- **Button组件**:
  - 自定义按钮组件
  - 提供各种交互按钮样式

- **Workspace组件**:
  - 工作区布局组件
  - 提供不同功能模块的工作区布局

## 数据流

### 前端到后端
1. **文本转语音流程**:
   ```
   用户输入文本 → React组件状态更新 → API请求(/api/tts) → 后端处理 → IndexTTS服务生成音频 → 返回音频数据 → 前端播放
   ```

2. **WebSocket实时通信流程**:
   ```
   用户发送文本 → WebSocket连接(/ws/tts) → 后端处理 → 分句处理 → IndexTTS服务生成音频 → 实时返回音频片段 → 前端连续播放
   ```

3. **模型训练流程**:
   ```
   用户上传音频 → API请求(/train) → 后端处理 → IndexTTS服务训练模型 → 保存模型 → 返回状态 → 前端更新模型列表
   ```

### 后端处理流程
1. **请求处理**:
   - FastAPI接收HTTP/WebSocket请求
   - 请求验证和参数处理
   - 业务逻辑处理

2. **TTS服务调用**:
   - 通过Gradio客户端接口调用IndexTTS服务
   - 处理模型选择、文本处理、参数配置等
   - 获取生成的音频数据

3. **音频处理**:
   - 处理音频格式和编码
   - 处理长文本分句
   - 处理并发请求

## 技术栈

### 前端
- **核心框架**: Next.js (v15.x)
- **UI框架**: React (v19.x)
- **语言**: TypeScript
- **状态管理**: Zustand
- **UI组件**: Material UI, Ant Design
- **样式**: Tailwind CSS, Styled Components
- **HTTP客户端**: Axios
- **WebSocket客户端**: Socket.io-client
- **音频处理**: WaveSurfer.js, Tone.js, lamejs

### 后端
- **Web框架**: FastAPI
- **异步处理**: asyncio
- **TTS客户端**: Gradio Client
- **音频处理**: 
  - 格式转换与处理
  - 文本分句处理
  - 模型管理

### TTS引擎
- **核心引擎**: IndexTTS (基于Transformer架构)
- **接口**: Gradio Web UI
- **运行环境**: Python, PyTorch

## 依赖库

### 前端依赖
```json
{
  "@gradio/client": "^1.14.2",
  "@mui/icons-material": "^7.0.2",
  "antd": "^5.24.6",
  "axios": "^1.8.4",
  "clsx": "^2.1.1",
  "lamejs": "^1.2.1",
  "next": "^15.3.1",
  "socket.io-client": "^4.8.1",
  "styled-components": "^6.1.17",
  "tailwind-merge": "^3.2.0",
  "tone": "^15.0.4",
  "wavesurfer.js": "^7.9.4",
  "zustand": "^5.0.3"
}
```

### 后端依赖
```
fastapi==0.104.1
uvicorn==0.24.0
python-multipart==0.0.6
websockets==12.0
gradio_client>=3.50.2
numpy>=1.24.3
soundfile>=0.12.1
librosa>=0.10.1
scipy>=1.11.3
```

### IndexTTS引擎依赖
```
torch>=2.0.0
torchaudio>=2.0.0
gradio>=3.50.2
```

## 详细安装指南

### 1. 系统要求
- **操作系统**: Windows 10/11
- **Python**: 3.12或更高
- **Node.js**: v18.0.0或更高
- **存储空间**: 至少10GB可用空间
- **内存**: 至少8GB RAM
- **GPU**: 可选，但推荐使用NVIDIA GPU以加速模型训练和推理

### 2. 环境准备

#### 安装Python环境
1. 下载并安装Python 3.12：https://www.python.org/downloads/
2. 在安装时勾选"Add Python to PATH"选项
3. 验证安装：在命令提示符中运行`python --version`

#### 安装Node.js环境
1. 下载并安装Node.js：https://nodejs.org/
2. 验证安装：在命令提示符中运行`node --version`和`npm --version`

#### 安装CUDA（可选，用于GPU加速）
1. 下载并安装适合您GPU的CUDA工具包：https://developer.nvidia.com/cuda-downloads
2. 验证安装：在命令提示符中运行`nvcc --version`

### 3. 克隆或下载项目
1. 通过Git克隆项目：`git clone https://github.com/your-repo/IndexTTS-FAST.git`
2. 或直接下载项目ZIP包并解压

### 4. 安装后端依赖
1. 打开命令提示符，进入到后端目录：`cd IndexTTS-FAST/AI/backend`
2. 创建虚拟环境：`python -m venv .venv`
3. 激活虚拟环境：`.venv\Scripts\activate`
4. 安装依赖：`pip install -r requirements.txt`

### 5. 安装前端依赖
1. 打开命令提示符，进入到前端目录：`cd IndexTTS-FAST/AI`
2. 安装依赖：`npm install`

### 6. 配置IndexTTS服务
1. 确保IndexTTS服务文件已正确放置在`index-tts-liuyue`目录中
2. 如果需要，可以修改`index-tts-liuyue/speaker_config.py`文件自定义配置

## 启动步骤
1. **启动IndexTTS服务**:
   ```
   cd index-tts-liuyue
   运行.bat
   ```
   或直接双击`运行.bat`文件

2. **启动后端服务**:
   ```
   cd AI/backend
   # 激活虚拟环境
   .venv\Scripts\activate
   # 启动后端服务
   python main.py
   ```

3. **启动前端服务**:
   ```
   cd AI
   npm run dev
   ```

4. **一键启动**:
   可使用`启动接口服务.bat`脚本一键启动所有服务

5. **访问应用**:
   在浏览器中打开 http://localhost:3000 访问应用

## 使用指南

### 基础语音合成
1. 打开应用首页
2. 在文本输入框中输入要转换的文本
3. 选择想要使用的语音模型
4. 调整语速等参数
5. 点击"生成"按钮
6. 在音频播放器中预览生成的语音
7. 可以下载生成的音频或继续编辑

### 训练新模型
1. 准备一段清晰的WAV格式音频文件（约10秒左右）
2. 进入模型训练页面
3. 上传音频文件
4. 填写模型名称
5. 点击"开始训练"
6. 等待训练完成（通常需要几分钟）
7. 训练完成后，新模型会出现在模型列表中

### 实时直播
1. 进入直播控制页面
2. 选择要使用的语音模型
3. 准备直播话术文本
4. 调整语速等参数
5. 点击"开始直播"
6. 系统会自动将文本转换为语音进行播放
7. 可以随时暂停或停止直播

### 模型管理
1. 进入模型管理页面
2. 查看所有可用模型
3. 可以试听、重命名或删除模型
4. 点击"新建模型"可以训练新的语音模型
5. 点击"导入模型"可以导入已有的模型文件

## API使用示例

### 1. 生成语音（同步方式）
```python
import requests
import json
import base64
import wave
import numpy as np

# 配置
api_url = "http://localhost:8088/generate"
model_name = "好听女.pt"
text = "你好，这是一个测试。"
speed = 1.0

# 发送请求
response = requests.post(
    api_url,
    json={
        "text": text,
        "model": model_name,
        "speed": speed
    }
)

# 处理响应
if response.status_code == 200:
    data = response.json()
    audio_base64 = data["audio"]
    
    # 将Base64转换为音频文件
    audio_data = base64.b64decode(audio_base64)
    with open("output.wav", "wb") as f:
        f.write(audio_data)
    print("语音已生成并保存为output.wav")
else:
    print(f"生成失败: {response.text}")
```

### 2. 训练新模型
```python
import requests
import os

# 配置
api_url = "http://localhost:8088/train"
audio_path = "sample.wav"
model_name = "新模型"

# 准备文件
with open(audio_path, "rb") as f:
    audio_data = f.read()

# 发送请求
response = requests.post(
    api_url,
    files={"audio_file": ("sample.wav", audio_data, "audio/wav")},
    data={
        "model_name": model_name,
        "test_text": "这是一个测试文本"
    }
)

# 处理响应
if response.status_code == 200:
    data = response.json()
    if data["success"]:
        print(f"模型训练成功: {data['model_name']}")
    else:
        print(f"模型训练失败: {data['message']}")
else:
    print(f"请求失败: {response.text}")
```

### 3. 使用WebSocket进行实时文本转语音
```javascript
// 前端JavaScript示例
const socket = new WebSocket('ws://localhost:8088/ws/tts');

socket.onopen = () => {
  console.log('连接已建立');
  
  // 发送TTS请求
  const message = {
    text: "这是一段较长的文本，将会被分段处理并实时返回音频数据。",
    model: "好听女.pt",
    speed: 1.0
  };
  
  socket.send(JSON.stringify(message));
};

socket.onmessage = (event) => {
  const data = JSON.parse(event.data);
  
  if (data.type === 'audio') {
    // 处理音频数据
    const audioData = base64ToArrayBuffer(data.audio);
    playAudio(audioData);
  } else if (data.type === 'status') {
    console.log(`状态更新: ${data.message}`);
  } else if (data.type === 'complete') {
    console.log('处理完成');
  }
};

socket.onclose = () => {
  console.log('连接已关闭');
};

// 辅助函数
function base64ToArrayBuffer(base64) {
  const binary = window.atob(base64);
  const array = new Uint8Array(binary.length);
  for (let i = 0; i < binary.length; i++) {
    array[i] = binary.charCodeAt(i);
  }
  return array.buffer;
}

function playAudio(arrayBuffer) {
  const audioContext = new (window.AudioContext || window.webkitAudioContext)();
  audioContext.decodeAudioData(arrayBuffer, (buffer) => {
    const source = audioContext.createBufferSource();
    source.buffer = buffer;
    source.connect(audioContext.destination);
    source.start(0);
  });
}
```

## 常见问题与解决方案

### 1. IndexTTS服务无法启动
- **问题**: 运行`运行.bat`后，IndexTTS服务无法正常启动
- **解决方案**:
  - 检查Python版本是否为3.12或更高
  - 检查是否已安装所有依赖库
  - 检查端口7860是否被占用，可以使用`netstat -ano | findstr 7860`查看
  - 检查日志输出，查看具体错误信息

### 2. 模型训练失败
- **问题**: 上传音频后，模型训练失败
- **解决方案**:
  - 确保音频文件是WAV格式，采样率为16kHz
  - 确保音频质量良好，没有背景噪音
  - 音频长度应在8-15秒之间
  - 检查IndexTTS服务是否正常运行
  - 查看后端日志获取详细错误信息

### 3. 语音生成速度慢
- **问题**: 生成语音时响应时间较长
- **解决方案**:
  - 开启GPU加速（如果有NVIDIA GPU）
  - 对长文本进行分段处理
  - 使用WebSocket连接代替HTTP请求
  - 优化网络连接，确保服务器和客户端在同一网络

### 4. 前端无法连接后端
- **问题**: 前端界面无法连接到后端服务
- **解决方案**:
  - 确保后端服务已正常启动
  - 检查前端配置中的API地址是否正确
  - 检查网络连接和防火墙设置
  - 查看浏览器控制台中的错误信息

### 5. 音频播放问题
- **问题**: 生成的音频无法正常播放
- **解决方案**:
  - 检查浏览器是否支持AudioContext API
  - 确保音频格式正确（WAV格式）
  - 尝试使用不同的音频播放库
  - 检查音频数据是否完整

## 性能优化建议

### 1. 服务端优化
- 开启GPU加速处理（推荐NVIDIA显卡）
- 使用PyTorch的JIT编译提高模型推理速度
- 配置合适的工作线程数量，避免资源竞争
- 实现请求队列和缓存机制

### 2. 网络传输优化
- 使用WebSocket进行实时数据传输
- 实现音频数据压缩，减少传输量
- 设置合理的分块大小，平衡实时性和效率

### 3. 前端优化
- 实现音频流式播放，边接收边播放
- 实现语音缓存机制，避免重复请求
- 优化React组件渲染性能
- 使用Web Workers处理音频数据

## 贡献指南

欢迎对项目进行贡献！如果您想要参与开发，请遵循以下步骤：

1. Fork项目仓库
2. 创建您的特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交您的更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 提交Pull Request

## 版本历史

- **v1.0.0** (2024-06-01)
  - 初始版本发布
  - 基础TTS功能实现
  - 模型训练功能
  - 直播控制台

- **v1.1.0** (计划中)
  - 增强模型管理功能
  - 优化直播控制能力
  - 添加更多语音效果

## 许可信息
本项目仅供学习和研究使用，请勿用于商业目的。

## 联系方式
如有问题或建议，请通过以下方式联系我们：
- 项目官网: https://www.indextts.com
- 电子邮件: <EMAIL>
- GitHub: https://github.com/your-username/IndexTTS-FAST 