IndexTTS服务连接教程
====================

一、环境准备
-----------------
1. 安装必要的Python包：
   ```bash
   pip install gradio_client
   ```

2. 确保IndexTTS服务已启动：
   - 进入index-tts-liuyue目录
   - 运行 运行.bat 文件
   - 等待服务完全启动（看到Gradio界面出现）

二、连接IndexTTS服务
-----------------
1. 基本连接代码：
   ```python
   from gradio_client import Client
   
   # 创建客户端实例
   client = Client("http://127.0.0.1:7860")
   ```

2. 连接参数说明：
   - 地址：http://127.0.0.1:7860
   - 端口：7860（默认端口）
   - 协议：http

3. 连接错误处理：
   ```python
   try:
       client = Client("http://127.0.0.1:7860")
       print("连接成功！")
   except Exception as e:
       print(f"连接失败: {str(e)}")
   ```

三、获取音色模型列表
-----------------
1. 基本获取代码：
   ```python
   # 调用API获取模型列表
   result = client.predict(
       fn_index=0  # 使用第一个API接口
   )
   ```

2. 完整的模型列表获取代码：
   ```python
   try:
       # 获取模型列表
       result = client.predict(fn_index=0)
       
       # 处理返回的数据
       if isinstance(result, dict) and 'choices' in result:
           models = [choice[0] for choice in result['choices']]
           print("\n可用音色模型列表:")
           print("=" * 50)
           for i, model in enumerate(models, 1):
               print(f"{i:2d}. {model}")
           print("=" * 50)
           print(f"总计: {len(models)} 个音色模型")
   except Exception as e:
       print(f"获取模型列表失败: {str(e)}")
   ```

3. 返回数据格式说明：
   - 成功返回格式：{'choices': [['模型1.pt'], ['模型2.pt'], ...]}
   - 第一个模型通常是："使用参考音频"
   - 其他模型名称以.pt结尾

四、创建新音色模型
-----------------
1. 创建流程说明：
   - 准备参考音频（WAV格式，建议10秒左右）
   - 更新提示音频
   - 进行推理测试
   - 保存新模型
   - 验证模型是否创建成功

2. 完整创建代码：
   ```python
   def create_new_voice_model(audio_path, model_name, test_text="你好，我是测试音色。"):
       """创建新的声音模型
       @param audio_path: 音频文件路径（wav格式）
       @param model_name: 新模型名称
       @param test_text: 测试文本
       @return: 是否成功
       """
       try:
           # 检查音频文件
           if not os.path.exists(audio_path):
               print(f"音频文件不存在: {audio_path}")
               return False
               
           if not audio_path.lower().endswith('.wav'):
               print("音频文件必须是WAV格式")
               return False
           
           # 处理音频文件
           processed_audio = handle_file(audio_path)
           
           # 更新提示音频
           update_result = client.predict(api_name="/update_prompt_audio")
           time.sleep(2)
           
           # 进行推理
           try:
               result = client.predict(
                   name="使用参考音频",
                   voice=processed_audio,
                   text=test_text,
                   speed=1.0,
                   api_name="/infer"
               )
           except Exception as e:
               print(f"推理过程中出现错误（这是正常的）: {str(e)}")
           
           # 等待模型处理
           time.sleep(5)
           
           # 保存为新模型
           save_result = client.predict(
               name=model_name,
               api_name="/save_audio"
           )
           
           # 等待保存完成
           time.sleep(5)
           
           # 验证模型是否创建成功
           return check_model_exists(model_name)
               
       except Exception as e:
           print(f"创建模型时发生错误: {str(e)}")
           return False
   ```

3. 验证模型创建：
   ```python
   def check_model_exists(model_name, max_retries=5, retry_interval=3):
       """检查模型是否创建成功
       @param model_name: 模型名称
       @param max_retries: 最大重试次数
       @param retry_interval: 重试间隔（秒）
       @return: 是否成功
       """
       # 给模型名称添加.pt后缀
       model_name_with_pt = f"{model_name}.pt"
       
       for i in range(max_retries):
           # 获取模型列表
           models = get_models()
           
           # 检查带.pt后缀的模型名称是否在列表中
           if model_name_with_pt in models:
               print(f"模型 {model_name_with_pt} 已成功创建！")
               return True
               
           if i < max_retries - 1:
               time.sleep(retry_interval)
       
       print(f"经过 {max_retries} 次检查，未找到模型 {model_name_with_pt}")
       return False
   ```

4. 使用示例：
   ```python
   # 创建客户端
   client = Client("http://127.0.0.1:7860")
   
   # 输入参数
   audio_file = "参考音频.wav"  # 参考音频文件路径
   model_name = "新音色"        # 新模型名称
   test_text = "你好，我是新音色" # 测试文本
   
   # 创建新模型
   success = create_new_voice_model(audio_file, model_name, test_text)
   
   if success:
       print(f"模型创建成功！新模型名称: {model_name}.pt")
   else:
       print("模型创建失败")
   ```

五、文本转语音功能
-----------------
1. API调用说明：
   ```python
   # 调用文本转语音API
   audio_result = client.predict(
       name="模型名称.pt",  # 音色模型名称
       voice=None,         # 参考音频（可选）
       text="要转换的文本", # 要转换的文本内容
       speed=1.0,          # 语速（0.5-2.0）
       api_name="/infer"   # API名称
   )
   ```

2. 参数说明：
   - name: 音色模型名称，必须包含.pt后缀
   - voice: 参考音频文件路径，如果使用预训练模型则设为None
   - text: 要转换的文本内容
   - speed: 语速，范围0.5-2.0，默认1.0
   - api_name: 固定为"/infer"

3. 完整的文本转语音代码：
   ```python
   def text_to_speech(text, model_name, speed=1.0):
       """文本转语音
       @param text: 要转换的文本
       @param model_name: 使用的音色模型名称
       @param speed: 语速（默认1.0）
       @return: 生成的音频文件路径
       """
       try:
           # 调用API生成语音
           audio_result = client.predict(
               name=model_name,  # 音色名称
               voice=None,       # 参考音频（可选）
               text=text,        # 文本内容
               speed=speed,      # 语速
               api_name="/infer" # API名称
           )
           
           # 处理返回结果
           if isinstance(audio_result, str) and os.path.exists(audio_result):
               # 如果返回的是文件路径，复制到输出目录
               import shutil
               output_file = f"output_{datetime.now().strftime('%Y%m%d_%H%M%S')}.wav"
               shutil.copy2(audio_result, output_file)
               return output_file
           elif isinstance(audio_result, bytes):
               # 如果返回的是音频数据，直接保存
               output_file = f"output_{datetime.now().strftime('%Y%m%d_%H%M%S')}.wav"
               with open(output_file, "wb") as f:
                   f.write(audio_result)
               return output_file
           else:
               print("不支持的音频返回格式")
               return None
               
       except Exception as e:
           print(f"生成语音失败: {str(e)}")
           return None
   ```

4. 使用示例：
   ```python
   # 创建客户端
   client = Client("http://127.0.0.1:7860")
   
   # 获取模型列表
   models = get_models(client)
   
   # 选择第一个模型
   model_name = models[1]  # 跳过"使用参考音频"
   
   # 转换文本
   text = "你好，欢迎使用IndexTTS语音合成系统"
   output_file = text_to_speech(text, model_name, speed=1.0)
   
   if output_file:
       print(f"语音生成成功！输出文件: {output_file}")
   else:
       print("语音生成失败")
   ```

六、常见问题解决
-----------------
1. 连接失败：
   - 检查IndexTTS服务是否启动
   - 确认端口7860是否被占用
   - 检查防火墙设置

2. 获取模型列表失败：
   - 确保服务正常运行
   - 检查网络连接
   - 查看服务器日志

3. 创建模型失败：
   - 检查参考音频格式是否正确（必须是WAV格式）
   - 确保音频时长合适（建议10秒左右）
   - 检查音频质量（背景噪音要小）
   - 验证模型名称是否合法（不能包含特殊字符）
   - 等待足够的时间让模型处理完成

4. 文本转语音失败：
   - 检查音色模型名称是否正确（必须包含.pt后缀）
   - 确认文本内容不为空
   - 检查语速是否在有效范围内（0.5-2.0）
   - 查看API返回的错误信息

七、注意事项
-----------------
1. 服务启动顺序：
   - 先启动IndexTTS服务
   - 等待服务完全启动后再连接

2. 错误处理：
   - 建议添加适当的错误处理机制
   - 记录日志便于排查问题
   - 对API返回结果进行类型检查

3. 资源释放：
   - 使用完毕后及时关闭连接
   - 处理异常情况下的资源释放
   - 定期清理生成的音频文件

八、模型管理功能
-----------------
1. 重命名模型：
   ```python
   def rename_model(old_name, new_name):
       """
       重命名模型
       @param old_name: 原模型名称
       @param new_name: 新模型名称
       @return: 是否成功
       """
       try:
           # 标准化模型名称
           old_name = normalize_model_name(old_name)
           new_name = normalize_model_name(new_name)
           
           # 查找原模型文件
           old_path = find_model_file(old_name)
           if not old_path:
               print(f"未找到模型文件: {old_name}")
               return False
           
           # 获取新文件路径
           new_path = os.path.join(os.path.dirname(old_path), new_name)
           
           # 检查新名称是否已存在
           if os.path.exists(new_path):
               print(f"新模型名称已存在: {new_path}")
               return False
           
           # 重命名文件
           shutil.move(old_path, new_path)
           print(f"模型重命名成功: {old_name} -> {new_name}")
           return True
           
       except Exception as e:
           print(f"重命名模型失败: {str(e)}")
           return False
   ```

2. 删除模型：
   ```python
   def delete_model(model_name):
       """
       删除模型
       @param model_name: 模型名称
       @return: 是否成功
       """
       try:
           # 标准化模型名称
           model_name = normalize_model_name(model_name)
           
           # 查找模型文件
           model_path = find_model_file(model_name)
           if not model_path:
               print(f"未找到模型文件: {model_name}")
               return False
           
           # 删除文件
           os.remove(model_path)
           print(f"模型删除成功: {model_name}")
           return True
           
       except Exception as e:
           print(f"删除模型失败: {str(e)}")
           return False
   ```

3. 辅助函数：
   ```python
   def normalize_model_name(name):
       """
       标准化模型名称
       @param name: 模型名称
       @return: 标准化后的名称
       """
       # 移除所有.pt后缀
       while name.endswith('.pt'):
           name = name[:-3]
       # 添加一个.pt后缀
       return f"{name}.pt"
   
   def find_model_file(model_name):
       """
       查找模型文件
       @param model_name: 模型名称
       @return: 模型文件路径
       """
       # 标准化模型名称
       model_name = normalize_model_name(model_name)
       
       # 在IndexTTS目录中查找
       for root, dirs, files in os.walk(index_tts_dir):
           for file in files:
               if file.lower() == model_name.lower():
                   return os.path.join(root, file)
       return None
   ```

4. 使用示例：
   ```python
   # 创建客户端
   client = Client("http://127.0.0.1:7860")
   
   # 获取模型列表
   models = get_models()
   
   # 重命名模型
   old_name = "原模型名称"
   new_name = "新模型名称"
   if rename_model(old_name, new_name):
       print(f"模型重命名成功！")
   else:
       print("模型重命名失败")
   
   # 删除模型
   model_name = "要删除的模型名称"
   if delete_model(model_name):
       print(f"模型删除成功！")
   else:
       print("模型删除失败")
   ```

5. 注意事项：
   - 模型名称会自动添加.pt后缀，无需手动添加
   - 重命名时会检查新名称是否已存在
   - 删除操作需要确认
   - 所有操作都有日志记录
   - 建议在操作前备份重要模型

八、后续开发建议
-----------------
1. 可以基于此基础开发更多功能：
   - 文本转语音
   - 音色训练
   - 批量处理
   - 实时转换
   - 音频格式转换
   - 语音合成参数优化

2. 性能优化：
   - 添加连接池
   - 实现重试机制
   - 优化错误处理
   - 添加缓存机制
   - 实现异步处理 