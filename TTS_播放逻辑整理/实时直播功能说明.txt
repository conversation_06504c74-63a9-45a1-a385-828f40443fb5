# 实时直播功能说明文档

## 1. 功能概述
实时直播功能是一个基于AI的文本转语音直播系统，支持实时语音生成、音频播放控制、语速调节等功能。该功能主要用于AI主播场景，可以将文本内容实时转换为自然的语音输出。

## 2. 工作逻辑
### 2.1 启动流程
1. 选择音色模型（必须以.pt结尾）
2. 输入直播话术文本（支持导入/导出）
3. 点击"启动AI主播"按钮
4. 进入音频生成缓冲状态（显示"组织语言中..."）
5. 音频开始播放后切换为直播状态（显示"停止直播"）

### 2.2 音频生成和播放
1. 调用IndexTTS服务生成音频
2. 音频生成后自动开始播放
3. 支持暂停/继续播放控制
4. 支持音量调节和进度条控制
5. 支持音频下载功能
6. 支持实时显示当前播放文本
7. 支持音频进度实时显示
8. 支持音量实时调节和静音功能

### 2.3 智能控制
1. 支持全自由/半自由/关闭三种AI模式
2. 支持用户接待功能（欢迎/回复/报时）
3. 支持实时插话功能
4. 支持脚本保存功能（可导出为txt文件）
5. 支持脚本导入功能（支持txt格式）
6. 支持自动保存设置（音色、语速、脚本内容）

## 3. 功能模块
### 3.1 主要组件
- LiveStreamWorkspace：直播工作区主组件
- useIndexTTS：音频生成和控制Hook
- useLiveStream：直播状态管理Hook

### 3.2 子功能模块
1. 直播状态控制
   - 音色选择（自动保存选择）
   - 语速调节（0.5-2.0范围）
   - 直播状态显示
   - 音量控制（支持静音）
   - 播放进度控制

2. 话术脚本管理
   - 文本输入（自动保存）
   - 脚本保存（导出为txt文件）
   - 脚本导入（支持txt格式）
   - 脚本展开/收起
   - 智能发挥设置
   - 用户接待设置

3. 音频播放器
   - 播放控制（播放/暂停）
   - 进度条（支持拖拽）
   - 音量控制（支持滑动调节）
   - 下载功能（支持保存音频文件）
   - 当前播放文本显示
   - 播放时间显示

4. AI响应区域
   - 实时播放状态
   - 当前播放文本显示（带闪烁指示器）
   - 插话功能（支持实时插入）
   - 插话成功提示
   - 错误提示显示

## 4. 页面结构
```
LiveStreamWorkspace
├── 直播状态区域
│   ├── 状态显示
│   ├── 音色选择
│   └── 语速调节
├── 话术脚本区域
│   ├── 脚本输入框
│   ├── 智能发挥设置
│   ├── 用户接待设置
│   ├── 保存脚本按钮
│   ├── 导入脚本按钮
│   └── 启动按钮
└── AI响应区域
    ├── 音频播放器
    │   ├── 播放控制
    │   ├── 进度条
    │   ├── 音量控制
    │   ├── 下载按钮
    │   └── 时间显示
    ├── 当前播放文本
    └── 插话输入框
```

## 5. 数据流
### 5.1 状态管理
- currentModel: 当前选择的音色模型（自动保存）
- speed: 语速设置（自动保存）
- isLiveStreaming: 直播状态
- isLoading: 加载状态
- aiMode: AI模式设置
- userGreeting: 用户接待设置
- scriptContent: 话术脚本内容（自动保存）
- audioState: 音频状态（播放、暂停、进度等）
- interruptText: 插话内容
- showInterruptSuccess: 插话成功提示
- volume: 音量设置
- currentTime: 当前播放时间
- duration: 音频总时长

### 5.2 数据流向
1. 用户输入 -> 状态更新 -> 自动保存
2. 状态变化 -> UI更新
3. 音频生成请求 -> IndexTTS服务
4. 音频数据 -> 播放器渲染
5. 播放状态 -> UI反馈
6. 插话内容 -> 音频队列 -> 播放
7. 脚本导入 -> 内容更新 -> 自动保存
8. 脚本导出 -> 文件下载

## 6. 依赖库
### 6.1 主要依赖
- React: ^18.0.0
- @mui/material: ^5.0.0
- @mui/icons-material: ^5.0.0

### 6.2 UI组件
- Material-UI组件库
  - Box
  - Button
  - TextField
  - Select
  - Slider
  - IconButton
  - Typography
  - Alert
  - Collapse
  - Snackbar
  等

### 6.3 图标
- @mui/icons-material
  - PlayArrow
  - Stop
  - Mic
  - VideoCall
  - SmartToy
  - Save
  - Download
  - Upload
  - VolumeUp
  - VolumeOff
  等

## 7. 样式主题
### 7.1 颜色方案
- 背景色：深色主题
  - primary: #0A0C14
  - secondary: #141824
  - tertiary: #1E2235

- 强调色：
  - primary: #4F7DF9
  - secondary: #7C4DFF
  - success: #4AEDC4
  - error: #FF4D4D

### 7.2 特效
- 毛玻璃效果（backdrop-filter）
- 渐变背景
- 动画过渡
- 悬浮效果
- 闪烁指示器
- 进度条动画
- 音量滑块动画

## 8. 注意事项
1. 确保IndexTTS服务正常运行
2. 音色模型文件必须以.pt结尾
3. 需要正确配置后端服务地址
4. 音频生成可能需要一定时间，注意loading状态处理
5. 注意内存管理，及时清理不需要的音频资源
6. 插话功能需要确保当前有选中的音色模型
7. 脚本内容会自动保存到localStorage
8. 音频下载功能需要确保有可用的音频URL
9. 导入脚本仅支持txt格式
10. 音量调节支持实时预览

## 9. 后续优化方向
1. 添加音频缓存机制
2. 优化音频加载速度
3. 添加更多的音频处理选项
4. 增强错误处理机制
5. 添加更多的用户交互反馈
6. 优化移动端适配
7. 添加批量导入脚本功能
8. 支持更多音频格式
9. 添加音频效果调节
10. 支持定时切换音色
11. 添加播放列表功能
12. 支持快捷键操作
13. 添加音频波形显示
14. 支持多语言界面
15. 添加云端同步功能 