# IndexTTS-FAST AI语音直播系统技术栈

## 项目概述

IndexTTS-FAST是一款基于先进文本转语音(TTS)技术的AI直播助手系统，专为直播行业设计。系统支持实时语音合成、模型训练、智能对话等功能，能够让虚拟主播以自然流畅的声音进行直播。

## 系统架构

项目采用三层架构设计：
1. **前端**：Next.js + React 构建的Web应用
2. **后端**：FastAPI构建的REST API和WebSocket服务
3. **TTS引擎**：独立运行的IndexTTS服务（基于Gradio）

### 整体架构图
```
用户 <--> 前端应用(Next.js) <--> 后端服务(FastAPI) <--> IndexTTS服务(Gradio)
```

## 技术栈详情

### 前端技术栈

#### 核心框架
- **Next.js**: v15.x - React框架，用于服务端渲染和静态网站生成
- **React**: v19.x - 用户界面构建库
- **TypeScript**: 静态类型检查的JavaScript超集

#### 状态管理
- **Zustand**: v5.x - 轻量级状态管理库

#### UI组件库
- **Material UI**: v7.x - 基于Material Design的React组件库
- **Ant Design**: v5.x - 企业级UI设计语言和React组件库

#### 样式解决方案
- **Tailwind CSS**: v4.x - 功能类优先的CSS框架
- **Styled Components**: v6.x - CSS-in-JS解决方案

#### 网络请求
- **Axios**: v1.x - 基于Promise的HTTP客户端
- **Socket.io-client**: v4.x - WebSocket客户端库，用于实时通信

#### 音频处理
- **WaveSurfer.js**: v7.x - 音频可视化库
- **Tone.js**: v15.x - Web Audio框架，用于创建交互式音乐
- **lamejs**: v1.x - MP3编码器库

#### 工具库
- **clsx**: v2.x - 条件类名构建工具
- **tailwind-merge**: v3.x - 合并Tailwind CSS类名

### 后端技术栈

#### Web框架
- **FastAPI**: v0.104.x - 高性能异步Python Web框架
- **Uvicorn**: v0.24.x - ASGI服务器

#### 异步处理
- **asyncio**: Python标准库，用于异步编程

#### 音频处理
- **NumPy**: v1.24+ - 科学计算库
- **PyTorch**: v2.0+ - 深度学习框架
- **torchaudio**: v2.0+ - PyTorch音频处理库
- **SoundFile**: v0.12+ - 音频文件读写库
- **librosa**: v0.10+ - 音频和音乐分析库
- **scipy**: v1.11+ - 科学计算库

#### API集成
- **Gradio Client**: v3.50+ - Gradio API客户端
- **python-multipart**: v0.0.6 - 处理表单数据
- **websockets**: v12.0 - WebSocket服务端库
- **python-dotenv**: v1.0.0 - 环境变量管理

### TTS引擎技术栈

#### 核心引擎
- **IndexTTS**: 基于Transformer架构的文本到语音转换引擎
- **PyTorch**: 深度学习框架，用于模型推理

#### Web接口
- **Gradio**: v3.50+ - 用于创建机器学习模型的Web界面

### 开发工具

#### 包管理
- **npm/yarn**: JavaScript包管理器
- **pip**: Python包管理器

#### 构建工具
- **webpack**: JavaScript模块打包器

#### 类型定义
- **@types/node**: v22.x
- **@types/react**: v19.x
- **@types/react-dom**: v19.x
- **@types/styled-components**: v5.x
- **@types/uuid**: v10.x

## 数据流

### 前端到后端
1. **文本转语音流程**:
   ```
   用户输入文本 → React组件状态更新 → API请求(/api/tts) → 后端处理 → IndexTTS服务生成音频 → 返回音频数据 → 前端播放
   ```

2. **WebSocket实时通信流程**:
   ```
   用户发送文本 → WebSocket连接(/ws/tts) → 后端处理 → 分句处理 → IndexTTS服务生成音频 → 实时返回音频片段 → 前端连续播放
   ```

3. **模型训练流程**:
   ```
   用户上传音频 → API请求(/train) → 后端处理 → IndexTTS服务训练模型 → 保存模型 → 返回状态 → 前端更新模型列表
   ```

## 部署环境

- **前端**: Node.js环境
- **后端**: Python 3.12+环境
- **TTS引擎**: Python环境，需要CUDA支持（用于GPU加速）

## 项目结构

```
IndexTTS-FAST/
├── AI/                        # 前端与后端代码
│   ├── backend/               # 后端服务
│   │   ├── main.py           # 主要API实现
│   │   ├── douyin_monitor/   # 抖音直播监控模块
│   │   └── requirements.txt  # 后端依赖
│   ├── src/                  # 前端源码
│   │   ├── components/       # 前端组件
│   │   ├── hooks/            # React钩子
│   │   ├── pages/            # 页面组件
│   │   ├── services/         # API服务
│   │   ├── store/            # 状态管理
│   │   ├── styles/           # 样式文件
│   │   └── utils/            # 工具函数
│   └── package.json          # 前端依赖
├── index-tts-liuyue/         # IndexTTS服务
│   ├── indextts/             # TTS核心代码
│   ├── voices/               # 声音模型目录
│   ├── 运行.bat              # TTS服务启动脚本
│   ├── 启动接口服务.bat      # Web服务启动脚本
│   └── app.py               # Gradio应用入口
└── README.md                # 项目文档
``` 