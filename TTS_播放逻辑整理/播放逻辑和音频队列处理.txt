# IndexTTS 系统播放逻辑和音频队列处理流程

## 一、整体架构

项目中的音频播放和队列处理主要由以下几个部分组成：

1. `useIndexTTS` Hook - 核心音频处理逻辑
2. `LiveStreamWorkspace` 组件 - 主播直播界面逻辑
3. 文本预处理 - 变量替换和随机词处理
4. 音色切换 - 主播与助播音色的切换

## 二、音频队列处理流程

### 1. 文本分句处理 (`splitTextToSentences`)

```typescript
const splitTextToSentences = (text: string): Array<{text: string, isAssistant: boolean}> => {
  // 使用标点符号分割文本
  const reg = /([。！？；，!;?…]+)/g;
  const parts = text.split(reg);
  const rawSentences: string[] = [];
  
  // 处理分割后的文本片段
  for (let i = 0; i < parts.length; i += 2) {
    const content = parts[i]?.trim();
    const sep = parts[i + 1] || '';
    if (content) {
      rawSentences.push(content + sep);
    }
  }

  // 合并短句（小于25个字符的句子会合并）
  const merged: string[] = [];
  let buffer = '';
  let bufferLen = 0;
  const minLen = 25;
  
  for (let i = 0; i < rawSentences.length; i++) {
    const s = rawSentences[i];
    buffer += s;
    bufferLen += s.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '').length;
    
    // 达到最小长度或是最后一个句子时，添加到结果中
    if (bufferLen >= minLen || i === rawSentences.length - 1) {
      merged.push(buffer);
      buffer = '';
      bufferLen = 0;
    }
  }
  
  // 处理助播标记并返回最终数组
  return merged.filter(Boolean).map(sentence => analyzeAssistantContent(sentence));
};

// 分析句子，判断是否使用助播音色
const analyzeAssistantContent = (sentence: string) => {
  // 检查是否包含助播标记
  const assistantRegex = /\[助:(.*?)\]/;
  const match = sentence.match(assistantRegex);
  
  if (match) {
    // 提取助播内容并替换原标记
    const assistantContent = match[1];
    const cleanSentence = sentence.replace(match[0], assistantContent);
    return {
      text: cleanSentence,
      isAssistant: true
    };
  }
  
  return {
    text: sentence,
    isAssistant: false
  };
};
```

### 2. 生成音频的主要流程 (`generateAudio`)

```typescript
const generateAudio = async (options: IndexTTSOptions & { assistantModel?: string }) => {
  try {
    // 1. 如果是插话，使用插入函数处理
    if (options.isInterrupt) {
      return await insertInterruptAudio(options);
    }

    // 2. 防止重复生成，设置标志位
    if (isGeneratingRef.current) return null;
    isGeneratingRef.current = true;

    // 3. 初始化状态，清空队列
    setIsLoading(true);
    audioQueueRef.current = [];
    sentencesRef.current = [];

    // 4. 分割文本为句子，每个句子都有isAssistant标记
    const sentences = splitTextToSentences(options.text);
    
    // 5. 生成每个句子的音频，根据isAssistant标记选择使用主播或助播音色
    for (const sentence of sentences) {
      // 根据isAssistant标记决定使用哪个模型
      const modelToUse = sentence.isAssistant && options.assistantModel ? 
                         options.assistantModel : options.model;
      
      // 生成单句音频
      const audioUrl = await generateSingleAudio({
        ...options,
        model: modelToUse,  // 使用选定的模型
        text: sentence.text // 使用处理后的文本
      });
      
      // 添加到队列
      if (audioUrl) {
        audioQueueRef.current.push(audioUrl);
        sentencesRef.current.push(sentence);
      }
    }
    
    // 6. 开始播放
    if (audioQueueRef.current.length > 0) {
      playNextAudio();
    }
    
    return audioQueueRef.current[0] || null;
  } catch (error) {
    console.error('生成音频错误:', error);
    return null;
  } finally {
    setIsLoading(false);
    isGeneratingRef.current = false;
  }
};
```

### 3. 实时播放流程

在实时直播模式下，系统通过以下方式处理播放队列：

1. **音频队列管理**：
   - 使用 `audioQueueRef` 存储音频URL
   - 使用 `sentencesRef` 存储句子文本和是否使用助播音色的标记

2. **播放控制**：
   - `playNextAudio` 函数负责从队列中取出并播放下一条音频
   - 播放完成后自动移除队列头部元素并播放下一条

3. **队列补充**：
   - 当队列中剩余的句子数量低于阈值时，自动生成新的句子并添加到队列
   - 全自由模式下通过 `autoGenerateAndEnqueue` 函数生成新内容
   - 关闭模式下通过 `autoEnqueueScriptContent` 函数重复使用脚本内容

## 三、音色切换机制

### 1. 助播音色标记使用方法

在话术脚本中，可以使用 `[助:内容]` 格式来标记需要使用助播音色播放的部分：

```
大家好，欢迎来到直播间。[助:接下来将由小助手为大家介绍今天的活动内容。]感谢小助手的介绍！
```

上面的例子中，"接下来将由小助手为大家介绍今天的活动内容。"会使用助播音色播放，其他内容使用主播音色。

### 2. 处理流程

1. 文本预处理：
   - 首先按照正常流程将文本分句
   - 然后分析每个句子是否包含助播标记
   - 如果包含，提取出助播内容并标记该句为助播音色

2. 音频生成：
   - 生成音频时检查每个句子的 `isAssistant` 标记
   - 如果为 `true`，使用助播音色模型
   - 如果为 `false`，使用主播音色模型

3. 播放控制：
   - 按照文本原有顺序播放，只是在播放不同句子时切换模型
   - 保持连贯的播放体验，用户感知为多人对话

### 3. 技术实现

主要修改了以下部分来支持助播音色功能：

1. `useIndexTTS` Hook:
   - 修改 `splitTextToSentences` 函数，返回带有 `isAssistant` 标记的对象数组
   - 修改 `generateSingleAudio` 函数，根据 `isAssistant` 标记选择模型
   - 修改 `audioQueueRef` 和 `sentencesRef` 的数据结构，保存音色信息

2. `LiveStreamWorkspace` 组件:
   - 将助播音色模型参数传递给 `generateAudio` 和 `appendAudioToQueue` 等函数
   - 提供助播音色标记的使用说明和示例

这种实现方式确保了话术可以按照原有顺序分句和播放，只是在播放特定句子时切换音色，从而实现主播和助播的"对话"效果。

### 4. 音频播放流程 (`playNextAudio`)

```typescript
const playNextAudio = async () => {
  // 1. 检查队列是否为空
  if (audioQueueRef.current.length === 0) {
    setAudioState(/* 重置播放状态 */);
    return;
  }

  // 2. 获取队列中的第一个音频和对应文本
  const audioUrl = audioQueueRef.current[0];
  const currentText = sentencesRef.current[0];
  
  try {
    // 3. 停止当前播放的音频
    if (audioRef.current) {
      audioRef.current.pause();
    }

    // 4. 创建新的音频实例
    const audio = new Audio(audioUrl);
    audioRef.current = audio;
    
    // 5. 等待音频加载完成
    await new Promise(/* 音频加载事件监听 */);
    
    // 6. 设置进度更新和播放结束的事件监听
    audio.addEventListener('timeupdate', /* 更新进度函数 */);
    
    audio.addEventListener('ended', () => {
      // 7. 清理事件监听
      audio.removeEventListener(/* 清理事件监听 */);
      audioRef.current = null;
      
      // 8. 移除已播放的音频和文本
      audioQueueRef.current.shift();
      sentencesRef.current.shift();
      
      // 9. 更新当前播放位置
      setAudioState(prev => ({
        ...prev,
        currentSentenceIndex: prev.currentSentenceIndex + 1,
        isInterrupt: false
      }));
      
      // 10. 播放下一句
      playNextAudio();
    });

    // 11. 更新状态并播放
    setAudioState(/* 更新为播放状态 */);
    audio.volume = audioState.volume;
    await audio.play();
  } catch (error) {
    console.error('播放音频失败:', error);
    setAudioState(/* 设置错误状态 */);
  }
};
```

### 5. 插话处理机制 (`insertInterruptAudio`)

```typescript
const insertInterruptAudio = async (options) => {
  try {
    // 1. 生成插话音频
    const audioUrl = await generateSingleAudio(options);
    
    // 2. 插入到队列第二位（当前播放的后面）
    const insertIndex = 1;
    audioQueueRef.current.splice(insertIndex, 0, audioUrl);
    sentencesRef.current.splice(insertIndex, 0, options.text);
    
    // 3. 更新状态
    setAudioState(prev => ({
      ...prev,
      isInterrupt: true
    }));
    
    return audioUrl;
  } catch (error) {
    console.error('插入插话音频失败:', error);
    setAudioState(/* 设置错误状态 */);
    return null;
  }
};
```

### 6. 追加音频到队列 (`appendAudioToQueue`)

```typescript
const appendAudioToQueue = async (options) => {
  try {
    // 1. 分割文本为句子
    const sentences = splitTextToSentences(options.text);
    
    // 2. 逐句生成音频并添加到队列
    for (const sentence of sentences) {
      const audioUrl = await generateSingleAudio({ ...options, text: sentence });
      if (audioUrl) {
        audioQueueRef.current.push(audioUrl);
        sentencesRef.current.push(sentence);
      }
    }
    
    // 3. 如果当前没有在播放，自动开始播放
    if (!audioState.isPlaying && audioQueueRef.current.length > 0) {
      playNextAudio();
    }
  } catch (error) {
    setAudioState(/* 设置错误状态 */);
  }
};
```

## 三、直播场景下的音频处理流程

直播组件（LiveStreamWorkspace）中有几种不同的音频生成模式：

### 1. 全自由模式 (aiMode === 'full')

```typescript
// 自动生成并入队
const autoGenerateAndEnqueue = async () => {
  // 只在全自由模式下使用
  if (aiMode !== 'full') return;
  if (!isLiveStreaming) return;
  if (!currentModel?.id) return;
  
  // 设置标志位防止重复调用
  isAutoGeneratingRef.current = true;
  
  try {
    // 1. 使用AI大模型生成新话术
    const newScript = await generateText(lastGeneratedText, prompt);
    
    if (newScript) {
      // 2. 保存生成的文本
      setLastGeneratedText(newScript);
      
      // 3. 生成音频并添加到队列
      const modelId = currentModel.id.endsWith('.pt') ? currentModel.id : `${currentModel.id}.pt`;
      await appendAudioToQueue({
        model: modelId,
        speed,
        text: newScript
      });
    }
  } finally {
    isAutoGeneratingRef.current = false;
  }
};
```

### 2. 关闭模式 (aiMode === 'off')

```typescript
// 在关闭模式下直接使用脚本内容重复生成音频
const autoEnqueueScriptContent = async () => {
  // 只在关闭模式下使用
  if (aiMode !== 'off') return;
  if (!isLiveStreaming) return;
  if (!currentModel?.id) return;
  if (!scriptContent.trim()) return;
  
  // 设置标志位防止重复调用
  isAutoGeneratingRef.current = true;
  
  try {
    // 1. 导入模板处理工具
    const { getRandomString, replacePlaceholders, getVariablesForTemplate } = await import('../../utils/templateUtils');
    
    // 2. 获取当前变量值并替换变量标记
    const variables = getVariablesForTemplate('custom', {
      nickname: '家人们',
      audienceCount: 0
    });
    let processedText = replacePlaceholders(scriptContent, variables);
    
    // 3. 处理随机词语
    processedText = getRandomString(processedText);
    
    // 4. 生成音频并添加到队列
    const modelId = currentModel.id.endsWith('.pt') ? currentModel.id : `${currentModel.id}.pt`;
    await appendAudioToQueue({
      model: modelId,
      speed,
      text: processedText
    });
  } finally {
    isAutoGeneratingRef.current = false;
  }
};
```

### 3. 自动补充队列

```typescript
// 监听音频队列长度，自动补充
useEffect(() => {
  if (!isLiveStreaming) return;
  
  // 计算剩余句子数量
  const remain = audioState.totalSentences - audioState.currentSentenceIndex;
  
  // 当剩余句子数量小于阈值时，自动补充
  if (remain < 10 && !isAIGenerating && !isAutoGeneratingRef.current) {
    if (aiMode === 'full') {
      autoGenerateAndEnqueue(); // 使用AI生成新话术
    } else if (aiMode === 'off') {
      autoEnqueueScriptContent(); // 重复使用脚本内容
    }
  }
}, [isLiveStreaming, audioState.currentSentenceIndex, audioState.totalSentences, isAIGenerating, aiMode]);
```

### 4. 公屏回复功能 (`insertLiveReply`)

```typescript
const insertLiveReply = async (text) => {
  // 1. 检查是否在直播状态
  if (!isLiveStreaming) return;
  
  // 2. 判断消息类型并检查对应开关
  let shouldGenerate = false;
  let messageType = '';
  
  if (text.includes('来了') || text.includes('进入直播间')) {
    messageType = '欢迎播报';
    shouldGenerate = userGreeting.welcome;
  } else if (text.includes('当前在线人数') || text.includes('人数变为')) {
    messageType = '人数播报';
    shouldGenerate = userGreeting.count;
  } else if (text.includes('现在时间是') || (text.includes('点') && text.includes('分'))) {
    messageType = '时间播报';
    shouldGenerate = userGreeting.time;
  } else {
    messageType = '互动回复';
    shouldGenerate = userGreeting.reply;
  }
  
  // 3. 根据开关状态决定是否生成
  if (!shouldGenerate) return;
  if (!currentModel) return;
  if (!text.trim()) return;
  
  try {
    // 4. 处理变量和随机词
    const { getRandomString, replacePlaceholders, getVariablesForTemplate } = await import('../../utils/templateUtils');
    const variables = getVariablesForTemplate('custom', {
      nickname: '家人们',
      audienceCount: 0
    });
    let processedText = replacePlaceholders(text, variables);
    processedText = getRandomString(processedText);
    
    // 5. 生成音频并插入队列
    const modelId = currentModel.id.endsWith('.pt') ? currentModel.id : `${currentModel.id}.pt`;
    await generateAudio({
      model: modelId,
      text: processedText,
      speed,
      isInterrupt: true  // 标记为插话，优先播放
    });
  } catch (error) {
    console.error(`${messageType}音频生成过程中发生错误:`, error);
  }
};
```

## 四、文本预处理机制

### 1. 变量替换 (`replacePlaceholders`)

系统支持在话术中使用以下变量:
- `[昵称]` - 替换为观众昵称
- `[人数]` - 替换为当前观众人数
- `[时间]` - 替换为当前时间，例如"18点30分"
- `[整点]` - 替换为距离下一个整点的时间，例如"距离19点还有30分钟"

### 2. 随机词处理 (`getRandomString`)

系统支持在话术中使用随机词，格式为：`{选项1|选项2|选项3}`

例如：`{欢迎|热烈欢迎|恭迎}{家人们|宝子们|朋友们}{来到|走进|进入}直播间`

每次生成语音时，系统会从每个花括号中随机选择一个选项，组成最终的话术。 