# 贡献指南

感谢您对IndexTTS-FAST项目的关注！我们欢迎所有形式的贡献，包括但不限于代码、文档、测试、反馈和建议。

## 🤝 如何贡献

### 报告问题
如果您发现了bug或有功能建议，请：
1. 检查[现有Issues](https://github.com/your-repo/issues)确保问题未被报告
2. 使用合适的Issue模板创建新Issue
3. 提供详细的问题描述和复现步骤
4. 包含系统环境信息和错误日志

### 提交代码
1. **Fork项目** - 点击右上角的Fork按钮
2. **创建分支** - 从main分支创建功能分支
   ```bash
   git checkout -b feature/amazing-feature
   ```
3. **编写代码** - 遵循项目的代码规范
4. **测试代码** - 确保所有测试通过
5. **提交更改** - 使用清晰的提交信息
   ```bash
   git commit -m "feat: add amazing feature"
   ```
6. **推送分支** - 推送到您的Fork仓库
   ```bash
   git push origin feature/amazing-feature
   ```
7. **创建PR** - 在GitHub上创建Pull Request

## 📝 代码规范

### 前端代码规范 (TypeScript/React)
```typescript
// 使用函数式组件和Hooks
const MyComponent: React.FC<Props> = ({ prop1, prop2 }) => {
  const [state, setState] = useState<string>('');
  
  // 使用useCallback优化性能
  const handleClick = useCallback(() => {
    setState('new value');
  }, []);
  
  return (
    <div className="my-component">
      <button onClick={handleClick}>Click me</button>
    </div>
  );
};

// 导出组件
export default MyComponent;
```

### 后端代码规范 (Python/FastAPI)
```python
from fastapi import APIRouter, Depends, HTTPException
from typing import List, Optional
from pydantic import BaseModel

# 使用类型注解
class UserCreate(BaseModel):
    username: str
    email: str
    password: str

# 路由函数
@router.post("/users/", response_model=User)
async def create_user(
    user: UserCreate,
    db: Session = Depends(get_db)
) -> User:
    """创建新用户"""
    # 业务逻辑
    return created_user
```

### 命名规范
- **文件名**: 使用kebab-case (如: `user-profile.tsx`)
- **组件名**: 使用PascalCase (如: `UserProfile`)
- **变量名**: 使用camelCase (如: `userName`)
- **常量名**: 使用UPPER_SNAKE_CASE (如: `API_BASE_URL`)
- **函数名**: 使用camelCase (如: `getUserProfile`)

### 注释规范
```typescript
/**
 * 用户配置文件组件
 * @param userId - 用户ID
 * @param onUpdate - 更新回调函数
 */
const UserProfile: React.FC<UserProfileProps> = ({ userId, onUpdate }) => {
  // 组件实现
};
```

## 🧪 测试指南

### 运行测试
```bash
# 前端测试
cd AI
npm test

# 后端测试
cd AI/backend
python -m pytest

# 端到端测试
npm run test:e2e
```

### 编写测试
```typescript
// 组件测试示例
import { render, screen, fireEvent } from '@testing-library/react';
import UserProfile from './UserProfile';

describe('UserProfile', () => {
  it('should render user information', () => {
    render(<UserProfile userId="123" />);
    expect(screen.getByText('User Profile')).toBeInTheDocument();
  });
  
  it('should handle update action', () => {
    const onUpdate = jest.fn();
    render(<UserProfile userId="123" onUpdate={onUpdate} />);
    
    fireEvent.click(screen.getByText('Update'));
    expect(onUpdate).toHaveBeenCalled();
  });
});
```

## 📋 提交信息规范

使用[Conventional Commits](https://www.conventionalcommits.org/)规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### 提交类型
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 示例
```bash
feat(auth): add user registration functionality

- Add registration form component
- Implement user validation
- Add email verification

Closes #123
```

## 🔍 代码审查

### 审查清单
- [ ] 代码符合项目规范
- [ ] 包含适当的测试
- [ ] 文档已更新
- [ ] 没有引入安全漏洞
- [ ] 性能影响可接受
- [ ] 向后兼容性

### 审查流程
1. 自动化检查通过
2. 至少一位维护者审查
3. 解决所有审查意见
4. 合并到主分支

## 🏗️ 开发环境设置

### 1. 克隆项目
```bash
git clone https://github.com/your-repo/IndexTTS-FAST.git
cd IndexTTS-FAST
```

### 2. 安装依赖
```bash
# 后端依赖
cd AI/backend
python -m venv .venv
source .venv/bin/activate  # Linux/macOS
.venv\Scripts\activate     # Windows
pip install -r requirements.txt

# 前端依赖
cd ../
npm install
```

### 3. 配置环境
```bash
# 复制环境变量模板
cp AI/backend/.env.example AI/backend/.env

# 编辑配置文件
# 设置API密钥和其他配置
```

### 4. 启动开发服务
```bash
# 启动后端 (终端1)
cd AI/backend
uvicorn main:app --reload

# 启动前端 (终端2)
cd AI
npm start

# 启动TTS服务 (终端3)
cd index-tts-liuyue
python app.py
```

## 📚 开发资源

### 技术文档
- [React官方文档](https://react.dev/)
- [TypeScript文档](https://www.typescriptlang.org/docs/)
- [FastAPI文档](https://fastapi.tiangolo.com/)
- [Ant Design组件库](https://ant.design/)

### 项目文档
- [API接口文档](./TTS_播放逻辑整理/API_DOCS.md)
- [技术栈说明](./TTS_播放逻辑整理/技术栈.md)
- [功能特性详解](./FEATURES.md)
- [安装部署指南](./INSTALLATION.md)

### 开发工具
- **IDE**: VS Code, WebStorm, PyCharm
- **调试**: Chrome DevTools, Python Debugger
- **API测试**: Postman, Insomnia
- **版本控制**: Git, GitHub Desktop

## 🐛 问题排查

### 常见问题
1. **依赖安装失败**
   ```bash
   # 清理缓存
   npm cache clean --force
   pip cache purge
   
   # 重新安装
   rm -rf node_modules package-lock.json
   npm install
   ```

2. **服务启动失败**
   ```bash
   # 检查端口占用
   lsof -i :3000  # macOS/Linux
   netstat -ano | findstr :3000  # Windows
   
   # 修改端口
   export PORT=3001
   ```

3. **API连接失败**
   - 检查后端服务是否启动
   - 确认API地址配置正确
   - 查看网络防火墙设置

### 获取帮助
- 📖 查看[FAQ文档](./FAQ.md)
- 💬 在[Discussions](https://github.com/your-repo/discussions)提问
- 🐛 在[Issues](https://github.com/your-repo/issues)报告问题
- 📧 发送邮件到 <EMAIL>

## 🎯 贡献指导

### 新手友好的任务
- 📝 改进文档和注释
- 🐛 修复简单的bug
- 🧪 添加测试用例
- 🎨 优化UI/UX
- 🌐 翻译和国际化

### 高级任务
- ⚡ 性能优化
- 🔒 安全增强
- 🏗️ 架构改进
- 🚀 新功能开发
- 📊 监控和分析

### 贡献认可
- 贡献者将被添加到项目README
- 重要贡献者可获得维护者权限
- 定期发布贡献者感谢公告

## 📞 联系我们

- **项目维护者**: @maintainer1, @maintainer2
- **邮箱**: <EMAIL>
- **QQ群**: 123456789
- **微信群**: 扫描二维码加入

感谢您的贡献！🎉
