# IndexTTS-FAST 安装指南

## 📋 系统要求

### 最低配置要求
| 项目 | 要求 |
|------|------|
| 操作系统 | Windows 10/11, macOS 10.15+, Ubuntu 18.04+ |
| CPU | Intel i5 或 AMD Ryzen 5 同等性能 |
| 内存 | 8GB RAM |
| 存储 | 10GB 可用空间 |
| 网络 | 稳定的互联网连接 |

### 推荐配置
| 项目 | 推荐 |
|------|------|
| 操作系统 | Windows 11, macOS 12+, Ubuntu 20.04+ |
| CPU | Intel i7 或 AMD Ryzen 7 同等性能 |
| 内存 | 16GB+ RAM |
| 存储 | 20GB+ SSD |
| GPU | NVIDIA GTX 1060 或更高 (可选，用于加速) |
| 网络 | 10Mbps+ 带宽 |

## 🛠️ 环境准备

### 1. 安装Python
```bash
# 下载Python 3.10-3.12
# 官网: https://www.python.org/downloads/

# 验证安装
python --version
pip --version
```

### 2. 安装Node.js
```bash
# 下载Node.js 16+
# 官网: https://nodejs.org/

# 验证安装
node --version
npm --version
```

### 3. 安装Git (可选)
```bash
# 下载Git
# 官网: https://git-scm.com/

# 验证安装
git --version
```

### 4. 安装CUDA (可选，GPU加速)
```bash
# 下载CUDA Toolkit 11.8+
# 官网: https://developer.nvidia.com/cuda-downloads

# 验证安装
nvcc --version
nvidia-smi
```

## 📦 项目安装

### 方式一：从源码安装

#### 1. 克隆项目
```bash
# 使用Git克隆
git clone https://github.com/your-repo/IndexTTS-FAST.git
cd IndexTTS-FAST

# 或下载ZIP文件并解压
```

#### 2. 安装后端依赖
```bash
# 进入后端目录
cd AI/backend

# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境
# Windows:
.venv\Scripts\activate
# macOS/Linux:
source .venv/bin/activate

# 升级pip
python -m pip install --upgrade pip

# 安装依赖
pip install -r requirements.txt
```

#### 3. 安装前端依赖
```bash
# 返回AI目录
cd ..

# 安装Node.js依赖
npm install

# 或使用yarn
yarn install
```

#### 4. 配置IndexTTS
```bash
# 进入TTS目录
cd ../index-tts-liuyue

# 检查Python环境
python --version

# 安装额外依赖(如果需要)
pip install gradio torch torchaudio
```

### 方式二：使用预编译版本

#### 1. 下载发布版本
```bash
# 从Releases页面下载最新版本
# https://github.com/your-repo/IndexTTS-FAST/releases

# 解压到目标目录
```

#### 2. 运行安装脚本
```bash
# Windows
install.bat

# macOS/Linux
chmod +x install.sh
./install.sh
```

## ⚙️ 配置设置

### 1. 环境变量配置
创建 `AI/backend/.env` 文件：
```env
# 数据库配置
DATABASE_URL=sqlite:///./sql_app.db

# JWT安全配置
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# TTS服务配置
TTS_SERVICE_URL=http://localhost:7860
TTS_TIMEOUT=30

# AI模型API配置
ZHIPU_API_KEY=your-zhipu-api-key-here
QWEN_API_KEY=your-qwen-api-key-here
OPENAI_API_KEY=your-openai-api-key-here

# 直播监控配置
DOUYIN_MONITOR_ENABLED=true
WECHAT_MONITOR_ENABLED=false

# 日志配置
LOG_LEVEL=INFO
LOG_FILE_MAX_SIZE=10MB
LOG_FILE_BACKUP_COUNT=5
```

### 2. 数据库初始化
```bash
cd AI/backend
python init_db.py
```

### 3. 模型文件准备
```bash
# 确保voices目录存在
mkdir -p index-tts-liuyue/voices

# 下载预训练模型(如果需要)
# 将模型文件放置在voices目录下
```

## 🚀 启动服务

### 方式一：一键启动(推荐)
```bash
# 使用启动器
python launcher_preview.py

# 或运行可执行文件
./AI语音直播系统.exe
```

### 方式二：手动启动

#### 1. 启动IndexTTS服务
```bash
cd index-tts-liuyue

# Windows
qidong.bat

# macOS/Linux
python app.py
```

#### 2. 启动后端服务
```bash
cd AI/backend

# 激活虚拟环境
source .venv/bin/activate  # macOS/Linux
.venv\Scripts\activate     # Windows

# 启动FastAPI服务
uvicorn main:app --host 0.0.0.0 --port 8088 --reload
```

#### 3. 启动前端服务
```bash
cd AI

# 启动React开发服务器
npm start

# 或使用yarn
yarn start
```

## 🔍 验证安装

### 1. 检查服务状态
访问以下地址确认服务正常：
- 前端应用: http://localhost:3000
- 后端API: http://localhost:8088
- API文档: http://localhost:8088/docs
- IndexTTS: http://localhost:7860

### 2. 功能测试
1. **用户注册登录** - 创建账户并登录
2. **语音生成测试** - 在语音工作区测试TTS功能
3. **模型训练测试** - 上传音频文件测试模型训练
4. **AI对话测试** - 配置API密钥测试AI对话功能

## 🐛 常见问题解决

### 安装问题

#### Python依赖安装失败
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 单独安装问题包
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cpu
```

#### Node.js依赖安装失败
```bash
# 清理缓存
npm cache clean --force

# 使用国内镜像
npm install --registry https://registry.npmmirror.com

# 或使用yarn
yarn install --registry https://registry.npmmirror.com
```

### 运行问题

#### 端口被占用
```bash
# 查看端口占用
netstat -ano | findstr :3000  # Windows
lsof -i :3000                 # macOS/Linux

# 修改端口配置
# 在package.json中修改start脚本
"start": "react-scripts start --port 3001"
```

#### 服务连接失败
1. 检查防火墙设置
2. 确认服务启动顺序
3. 检查配置文件中的URL设置
4. 查看日志文件排查错误

#### 模型加载失败
1. 检查模型文件路径
2. 确认模型文件完整性
3. 检查Python环境和依赖
4. 查看TTS服务日志

## 📚 进阶配置

### Docker部署
```bash
# 构建镜像
docker build -t indextts-fast .

# 运行容器
docker run -p 3000:3000 -p 8088:8088 -p 7860:7860 indextts-fast
```

### 生产环境部署
```bash
# 构建前端
cd AI
npm run build

# 配置Nginx反向代理
# 使用PM2管理Node.js进程
# 配置SSL证书
```

### 性能优化
```bash
# 启用GPU加速
export CUDA_VISIBLE_DEVICES=0

# 调整内存限制
export NODE_OPTIONS="--max-old-space-size=4096"

# 配置缓存
# 优化数据库连接池
```

## 📞 获取帮助

如果在安装过程中遇到问题，可以通过以下方式获取帮助：

- 📖 查看详细文档: [README.md](./README.md)
- 🐛 提交问题: [GitHub Issues](https://github.com/your-repo/issues)
- 💬 社区讨论: [GitHub Discussions](https://github.com/your-repo/discussions)
- 📧 邮件支持: <EMAIL>
