@echo off
chcp 65001 >nul
echo ================================
echo    IndexTTS-FAST 一键部署脚本
echo ================================
echo.

echo 正在检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js未安装！
    echo 请先安装Node.js: https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js已安装

echo.
echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装！
    echo 请先安装Python: https://www.python.org/
    pause
    exit /b 1
)
echo ✅ Python已安装

echo.
echo ================================
echo    开始安装项目依赖
echo ================================

echo.
echo [1/4] 安装主项目依赖...
npm install
if errorlevel 1 (
    echo ❌ 主项目依赖安装失败！
    pause
    exit /b 1
)
echo ✅ 主项目依赖安装完成

echo.
echo [2/4] 安装AI模块依赖...
cd AI
npm install
if errorlevel 1 (
    echo ❌ AI模块依赖安装失败！
    pause
    exit /b 1
)
echo ✅ AI模块依赖安装完成

echo.
echo [3/4] 安装自动化后端依赖...
cd ..\automation-backend
npm install
if errorlevel 1 (
    echo ❌ 自动化后端依赖安装失败！
    pause
    exit /b 1
)
echo ✅ 自动化后端依赖安装完成

echo.
echo [4/4] 安装Python依赖...
cd ..\AI
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Python依赖安装失败！
    pause
    exit /b 1
)
echo ✅ Python依赖安装完成

cd ..

echo.
echo ================================
echo    部署完成！
echo ================================
echo.
echo 现在你可以：
echo 1. 运行AI模块：cd AI && npm start
echo 2. 运行后端服务：cd AI\backend && python main.py
echo 3. 运行IndexTTS：cd index-tts-liuyue && python app.py
echo.
echo 详细使用说明请查看 README.md
echo.
pause
