{"dependencies": {"@gradio/client": "^1.14.2", "@mui/icons-material": "^7.0.2", "@stagewise-plugins/react": "^0.4.6", "@stagewise/toolbar-next": "^0.4.6", "ahooks": "^3.9.0", "antd": "^5.24.9", "axios": "^1.8.4", "clsx": "^2.1.1", "lamejs": "^1.2.1", "next": "^15.3.1", "node-polyfill-webpack-plugin": "^4.1.0", "react-beautiful-dnd": "^13.1.1", "socket.io-client": "^4.8.1", "styled-components": "^6.1.17", "tailwind-merge": "^3.2.0", "tone": "^15.0.4", "wavesurfer.js": "^7.9.4", "zhipuai": "^2.0.0", "zustand": "^5.0.6"}, "devDependencies": {"@types/node": "^22.14.1", "@types/react": "^19.1.2", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^19.1.2", "@types/styled-components": "^5.1.34", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "postcss": "^8.5.3", "process": "^0.11.10", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "tailwindcss": "^4.1.4", "url": "^0.11.4"}, "name": "indextts-fast", "version": "1.0.0", "description": "## 项目概述", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC"}