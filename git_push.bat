@echo off
chcp 65001 >nul
echo ================================
echo    Git 推送脚本
echo ================================
echo.

echo 正在检查Git状态...
git status --porcelain >nul 2>&1
if errorlevel 1 (
    echo ❌ 当前目录不是Git仓库！
    echo 请先运行 git_init.bat 初始化项目
    pause
    exit /b 1
)

echo 当前Git状态：
git status --short

echo.
set /p continue="是否继续提交和推送？(y/n): "
if /i not "%continue%"=="y" (
    echo 操作已取消
    pause
    exit /b 0
)

echo.
echo 正在添加文件...
git add .
echo ✅ 文件添加完成

echo.
set /p message="请输入提交信息: "
if "%message%"=="" set message=更新代码

echo 正在提交...
git commit -m "%message%"
if errorlevel 1 (
    echo ⚠️  没有新的更改需要提交
) else (
    echo ✅ 提交完成
)

echo.
echo 正在检查远程仓库...
git remote -v >nul 2>&1
if errorlevel 1 (
    echo ❌ 未配置远程仓库！
    echo.
    echo 请先配置远程仓库：
    echo 1. 在GitHub或Gitee创建仓库
    echo 2. 运行命令：git remote add origin https://github.com/你的用户名/仓库名.git
    echo.
    set /p remote_url="或者现在输入远程仓库地址: "
    if not "%remote_url%"=="" (
        git remote add origin "%remote_url%"
        echo ✅ 远程仓库配置完成
    ) else (
        echo 操作已取消
        pause
        exit /b 1
    )
)

echo.
echo 正在推送到远程仓库...
git push
if errorlevel 1 (
    echo ⚠️  推送失败，尝试设置上游分支...
    git push -u origin main
    if errorlevel 1 (
        echo ❌ 推送失败！
        echo 可能的原因：
        echo 1. 网络连接问题
        echo 2. 认证失败（需要输入用户名密码或配置SSH密钥）
        echo 3. 远程仓库地址错误
        echo.
        echo 请检查网络连接和认证信息后重试
        pause
        exit /b 1
    )
)

echo ✅ 推送完成！

echo.
echo ================================
echo    操作完成！
echo ================================
echo.
echo 你的代码已成功推送到远程仓库
echo 可以在GitHub/Gitee上查看你的项目了
echo.
pause
