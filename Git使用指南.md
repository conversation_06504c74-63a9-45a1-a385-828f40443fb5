# Git 版本控制完整使用指南

## 📋 目录
1. [Git 安装和配置](#git-安装和配置)
2. [初始化项目](#初始化项目)
3. [基本操作流程](#基本操作流程)
4. [远程仓库设置](#远程仓库设置)
5. [日常使用命令](#日常使用命令)
6. [分支管理](#分支管理)
7. [常见问题解决](#常见问题解决)

---

## 🔧 Git 安装和配置

### 1. 下载安装 Git
- 访问 [Git官网](https://git-scm.com/download/win) 下载Windows版本
- 安装时选择默认选项即可
- 安装完成后，右键桌面会出现 "Git Bash Here" 选项

### 2. 首次配置（必须做）
打开 Git Bash 或命令提示符，输入以下命令：

```bash
# 设置你的用户名（用你的真实姓名或昵称）
git config --global user.name "陈铎昊"

# 设置你的邮箱（建议用GitHub/Gitee的注册邮箱）
git config --global user.email "<EMAIL>"

# 查看配置是否成功
git config --list
```

**示例：**
```bash
git config --global user.name "张三"
git config --global user.email "<EMAIL>"
```

---

## 🚀 初始化项目

### 方法一：在现有项目中初始化Git
在你的项目根目录（E:/IndexTTS-FAST）打开Git Bash：

```bash
# 初始化Git仓库
git init

# 查看当前状态
git status
```

### 方法二：克隆远程仓库（如果已有远程仓库）
```bash
git clone https://github.com/用户名/仓库名.git
```

---

## 📝 基本操作流程

### 1. 添加文件到暂存区
```bash
# 添加单个文件
git add 文件名

# 添加所有文件
git add .

# 添加指定类型文件
git add *.js
```

### 2. 提交更改
```bash
# 提交暂存区的文件
git commit -m "提交说明"

# 示例
git commit -m "初始化项目"
git commit -m "添加智能场控功能"
git commit -m "修复登录bug"
```

### 3. 查看状态和历史
```bash
# 查看当前状态
git status

# 查看提交历史
git log

# 查看简洁的提交历史
git log --oneline
```

---

## 🌐 远程仓库设置

### 1. 在GitHub/Gitee创建仓库
- **GitHub**: 访问 [github.com](https://github.com)，点击 "New repository"
- **Gitee**: 访问 [gitee.com](https://gitee.com)，点击 "新建仓库"

创建时：
- 仓库名：`IndexTTS-FAST` 或其他你喜欢的名字
- 描述：`AI语音直播系统`
- 选择 "公开" 或 "私有"
- **不要**勾选 "Initialize this repository with a README"

### 2. 连接本地仓库到远程仓库
```bash
# 添加远程仓库地址（GitHub示例）
git remote add origin https://github.com/你的用户名/仓库名.git

# 添加远程仓库地址（Gitee示例）
git remote add origin https://gitee.com/你的用户名/仓库名.git

# 查看远程仓库
git remote -v
```

### 3. 首次推送
```bash
# 推送到远程仓库的main分支
git push -u origin main

# 如果提示没有main分支，先创建
git branch -M main
git push -u origin main
```

---

## 💻 日常使用命令

### 完整的工作流程：
```bash
# 1. 查看当前状态
git status

# 2. 添加修改的文件
git add .

# 3. 提交更改
git commit -m "描述你做了什么修改"

# 4. 推送到远程仓库
git push

# 5. 如果有其他人的更新，先拉取
git pull
```

### 常用命令速查：
```bash
# 查看文件差异
git diff

# 撤销工作区的修改
git checkout -- 文件名

# 撤销暂存区的文件
git reset HEAD 文件名

# 查看分支
git branch

# 切换分支
git checkout 分支名

# 创建并切换到新分支
git checkout -b 新分支名
```

---

## 🌿 分支管理

### 基本分支操作：
```bash
# 查看所有分支
git branch -a

# 创建新分支
git branch 分支名

# 切换分支
git checkout 分支名

# 创建并切换到新分支
git checkout -b 分支名

# 合并分支到当前分支
git merge 分支名

# 删除分支
git branch -d 分支名
```

### 推荐的分支策略：
- `main`: 主分支，存放稳定版本
- `develop`: 开发分支，日常开发
- `feature/功能名`: 功能分支，开发新功能
- `hotfix/修复名`: 热修复分支，紧急修复

---

## 🔧 常见问题解决

### 1. 忘记添加 .gitignore 文件
创建 `.gitignore` 文件，添加不需要版本控制的文件：

```
# 依赖文件夹
node_modules/
__pycache__/
.venv/

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp

# 系统文件
.DS_Store
Thumbs.db

# IDE配置
.vscode/
.idea/

# 输出文件
outputs/
音频输出/

# 数据库文件
*.db
*.sqlite

# 配置文件（如果包含敏感信息）
config.json
sessions.json
```

### 2. 推送时要求输入用户名密码
```bash
# 设置记住密码（Windows）
git config --global credential.helper manager

# 或者使用SSH密钥（推荐）
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
```

### 3. 文件太大无法推送
```bash
# 查看大文件
git ls-files --others --ignored --exclude-standard

# 移除大文件并添加到.gitignore
git rm --cached 大文件名
echo "大文件名" >> .gitignore
```

### 4. 撤销最后一次提交
```bash
# 撤销提交但保留修改
git reset --soft HEAD~1

# 完全撤销提交和修改
git reset --hard HEAD~1
```

---

## 🎯 针对你的项目的具体建议

### 1. 初始化步骤：
```bash
cd E:/IndexTTS-FAST
git init
git add .
git commit -m "初始化AI语音直播系统项目"
```

### 2. 推荐的 .gitignore 内容：
```
# Python
__pycache__/
*.py[cod]
.venv/
*.egg-info/

# Node.js
node_modules/
npm-debug.log*

# 输出和临时文件
outputs/
音频输出/
logs/
*.log

# 数据库和会话
*.db
sessions.json

# 系统和IDE
.DS_Store
.vscode/
Thumbs.db

# 大文件和模型
*.pt
*.pyd
checkpoints/
cache/
```

### 3. 提交信息建议：
- `feat: 添加智能场控功能`
- `fix: 修复抖音登录问题`
- `docs: 更新README文档`
- `style: 优化UI界面`
- `refactor: 重构音频处理模块`

---

## 📚 学习资源

- [Git官方文档](https://git-scm.com/doc)
- [GitHub使用指南](https://docs.github.com/cn)
- [Gitee使用帮助](https://help.gitee.com/)

记住：Git是一个强大的工具，刚开始可能觉得复杂，但掌握基本操作后会大大提高你的开发效率！

有任何问题随时问我！ 🚀
