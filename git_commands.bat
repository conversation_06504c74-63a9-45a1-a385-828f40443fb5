@echo off
chcp 65001 >nul
:menu
cls
echo ================================
echo    Git 常用命令工具
echo ================================
echo.
echo 1. 查看状态 (git status)
echo 2. 查看提交历史 (git log)
echo 3. 添加并提交更改
echo 4. 推送到远程仓库
echo 5. 拉取远程更新
echo 6. 查看分支
echo 7. 创建新分支
echo 8. 切换分支
echo 9. 撤销工作区修改
echo 0. 退出
echo.
set /p choice="请选择操作 (0-9): "

if "%choice%"=="1" goto status
if "%choice%"=="2" goto log
if "%choice%"=="3" goto commit
if "%choice%"=="4" goto push
if "%choice%"=="5" goto pull
if "%choice%"=="6" goto branch
if "%choice%"=="7" goto newbranch
if "%choice%"=="8" goto checkout
if "%choice%"=="9" goto reset
if "%choice%"=="0" goto exit
goto menu

:status
echo.
echo 当前Git状态：
git status
echo.
pause
goto menu

:log
echo.
echo 提交历史：
git log --oneline -10
echo.
pause
goto menu

:commit
echo.
echo 当前状态：
git status --short
echo.
set /p continue="是否继续提交？(y/n): "
if /i not "%continue%"=="y" goto menu

git add .
echo ✅ 文件已添加到暂存区

set /p message="请输入提交信息: "
if "%message%"=="" set message=更新代码

git commit -m "%message%"
echo ✅ 提交完成
echo.
pause
goto menu

:push
echo.
echo 正在推送到远程仓库...
git push
if errorlevel 1 (
    echo ❌ 推送失败
) else (
    echo ✅ 推送成功
)
echo.
pause
goto menu

:pull
echo.
echo 正在拉取远程更新...
git pull
if errorlevel 1 (
    echo ❌ 拉取失败
) else (
    echo ✅ 拉取成功
)
echo.
pause
goto menu

:branch
echo.
echo 当前分支：
git branch
echo.
echo 所有分支（包括远程）：
git branch -a
echo.
pause
goto menu

:newbranch
echo.
set /p branchname="请输入新分支名称: "
if "%branchname%"=="" (
    echo 分支名称不能为空
    pause
    goto menu
)

git checkout -b "%branchname%"
echo ✅ 分支 "%branchname%" 创建并切换成功
echo.
pause
goto menu

:checkout
echo.
echo 当前分支：
git branch
echo.
set /p branchname="请输入要切换的分支名称: "
if "%branchname%"=="" goto menu

git checkout "%branchname%"
if errorlevel 1 (
    echo ❌ 切换失败
) else (
    echo ✅ 切换到分支 "%branchname%" 成功
)
echo.
pause
goto menu

:reset
echo.
echo ⚠️  这将撤销所有工作区的修改！
set /p confirm="确定要继续吗？(y/n): "
if /i not "%confirm%"=="y" goto menu

git checkout -- .
echo ✅ 工作区修改已撤销
echo.
pause
goto menu

:exit
echo 再见！
exit /b 0
